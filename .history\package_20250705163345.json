{"name": "bolt-expo-starter", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "db:check": "npx tsx scripts/check-database.ts", "db:sync": "npx tsx scripts/check-database.ts", "db:migrate": "npx tsx scripts/migrate-database.ts", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "supabase:status": "npx supabase status", "supabase:migration:new": "npx supabase migration new", "supabase:db:push": "npx supabase db push"}, "dependencies": {"@expo-google-fonts/ma-shan-zheng": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "^2.1.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.39.0", "expo": "^53.0.0", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-router": "~5.0.2", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "leaflet": "^1.9.4", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-leaflet": "^4.2.1", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "react-toastify": "^10.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/leaflet": "^1.9.8", "@types/react": "~19.0.10", "cross-env": "^7.0.3", "tsx": "^4.20.3", "typescript": "~5.8.3"}}