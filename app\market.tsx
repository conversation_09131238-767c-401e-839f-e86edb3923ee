import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  MapPin,
  Navigation,
  Store,
  Eye,
  EyeOff,
  Star,
  ChevronRight,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';

const { width, height } = Dimensions.get('window');

// Web-only imports
let MapContainer: any = null;
let TileLayer: any = null;
let Marker: any = null;
let Popup: any = null;
let L: any = null;

if (Platform.OS === 'web') {
  try {
    const leaflet = require('react-leaflet');
    MapContainer = leaflet.MapContainer;
    TileLayer = leaflet.TileLayer;
    Marker = leaflet.Marker;
    Popup = leaflet.Popup;
    L = require('leaflet');

    // Fix for default markers in Leaflet
    delete (L.Icon.Default.prototype as any)._getIconUrl;
    L.Icon.Default.mergeOptions({
      iconRetinaUrl:
        'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
      iconUrl:
        'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
      shadowUrl:
        'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
    });
  } catch (error) {
    console.warn('Leaflet not available:', error);
  }
}

interface Shop {
  id: string;
  name: string;
  description: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  isOpen: boolean;
  ownerId: string;
  ownerName: string;
  rating: number;
}

function MarketContent() {
  const { isDark } = useTheme();
  const { user, profile } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Mock shops data
  const shops: Shop[] = [
    {
      id: '1',
      name: '香草药铺',
      description: '专营各种珍贵草药和治愈药剂',
      location: {
        latitude: 54.2781,
        longitude: -0.4053,
        address: '斯卡布罗集市东区第三街',
      },
      isOpen: true,
      ownerId: 'owner1',
      ownerName: '草药师艾琳',
      rating: 4.8,
    },
    {
      id: '2',
      name: '铁匠铺',
      description: '制作精良的武器和护甲',
      location: {
        latitude: 54.2891,
        longitude: -0.3721,
        address: '斯卡布罗集市西区铁匠街',
      },
      isOpen: true,
      ownerId: 'owner2',
      ownerName: '铁匠托马斯',
      rating: 4.6,
    },
    {
      id: '3',
      name: '珍宝阁',
      description: '收藏各种稀有宝石和装饰品',
      location: {
        latitude: 54.2567,
        longitude: -0.3945,
        address: '斯卡布罗集市中央广场',
      },
      isOpen: false,
      ownerId: 'owner3',
      ownerName: '珠宝商莉莉安',
      rating: 4.9,
    },
  ];

  const openShops = shops.filter((shop) => shop.isOpen);

  const createCustomIcon = (shopName: string) => {
    if (Platform.OS !== 'web' || !L) return null;

    const iconHtml = `
      <div style="
        background-color: #4A7043;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 3px solid #F5E8C7;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        cursor: pointer;
      ">
        🏪
      </div>
    `;

    return L.divIcon({
      html: iconHtml,
      className: 'custom-shop-marker',
      iconSize: [40, 40],
      iconAnchor: [20, 20],
    });
  };

  const renderMap = () => {
    if (Platform.OS !== 'web' || !MapContainer) {
      return (
        <View style={styles.mapFallback}>
          <Navigation size={40} color={colors.secondary} />
          <Text style={[styles.mapFallbackText, { color: colors.text }]}>
            地图功能仅在网页版可用
          </Text>
          <Text
            style={[styles.mapFallbackSubtext, { color: colors.textSecondary }]}
          >
            请在浏览器中查看完整地图体验
          </Text>
        </View>
      );
    }

    return (
      <MapContainer
        center={[54.2781, -0.4053]} // Scarborough area
        zoom={13}
        style={{ height: '100%', width: '100%', borderRadius: '12px' }}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        {openShops.map((shop) => (
          <Marker
            key={shop.id}
            position={[shop.location.latitude, shop.location.longitude]}
            icon={createCustomIcon(shop.name)}
          >
            <Popup>
              <div style={{ textAlign: 'center', minWidth: '200px' }}>
                <h3 style={{ margin: '0 0 8px 0', color: '#4A7043' }}>
                  {shop.name}
                </h3>
                <p
                  style={{
                    margin: '0 0 8px 0',
                    fontSize: '12px',
                    color: '#8B7355',
                  }}
                >
                  {shop.description}
                </p>
                <p
                  style={{
                    margin: '0 0 8px 0',
                    fontSize: '11px',
                    color: '#8B7355',
                  }}
                >
                  店主: {shop.ownerName}
                </p>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '4px',
                    marginBottom: '8px',
                  }}
                >
                  <span style={{ color: '#D4A017' }}>⭐</span>
                  <span style={{ fontSize: '12px', color: '#8B7355' }}>
                    {shop.rating}
                  </span>
                </div>
                <div
                  style={{
                    display: 'inline-block',
                    padding: '4px 8px',
                    backgroundColor: shop.isOpen ? '#4A704320' : '#DC354520',
                    borderRadius: '6px',
                    fontSize: '10px',
                    color: shop.isOpen ? '#4A7043' : '#DC3545',
                    fontWeight: 'bold',
                  }}
                >
                  {shop.isOpen ? '营业中' : '已关闭'}
                </div>
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    );
  };

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    header: {
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    merchantContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 20,
      width: '100%',
    },
    merchantFrame: {
      width: 70,
      height: 70,
      borderRadius: 35,
      borderWidth: 3,
      borderColor: colors.secondary,
      overflow: 'hidden',
      position: 'relative',
      marginRight: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.5 : 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    merchantImage: {
      width: '100%',
      height: '100%',
    },
    mapBadge: {
      position: 'absolute',
      bottom: -5,
      right: -5,
      backgroundColor: colors.secondary,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.background,
    },
    merchantInfo: {
      flex: 1,
    },
    merchantTitle: {
      fontSize: 18,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 2,
    },
    merchantSubtitle: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    titleContainer: {
      alignItems: 'center',
    },
    mainTitle: {
      fontSize: 28,
      color: colors.text,
      textAlign: 'center',
      marginBottom: 8,
      fontWeight: '700',
      textShadowColor: isDark
        ? 'rgba(255, 255, 255, 0.1)'
        : 'rgba(0, 0, 0, 0.2)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    titleUnderline: {
      width: 80,
      height: 3,
      backgroundColor: colors.secondary,
      borderRadius: 2,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
  });

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{
          uri: isDark
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg'
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg',
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary,
          ]}
          style={dynamicStyles.overlay}
        >
          <SafeAreaView style={dynamicStyles.safeArea}>
            {/* Header */}
            <Animated.View
              style={[
                dynamicStyles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }],
                },
              ]}
            >
              <View style={dynamicStyles.merchantContainer}>
                <View style={dynamicStyles.merchantFrame}>
                  <Image
                    source={{
                      uri:
                        profile?.avatar_url ||
                        'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg',
                    }}
                    style={dynamicStyles.merchantImage}
                    resizeMode="cover"
                  />
                  <View style={dynamicStyles.mapBadge}>
                    <MapPin size={16} color={colors.background} />
                  </View>
                </View>
                <View style={dynamicStyles.merchantInfo}>
                  <Text style={dynamicStyles.merchantTitle}>集市地图</Text>
                  <Text style={dynamicStyles.merchantSubtitle}>
                    探索开放的商店
                  </Text>
                </View>
              </View>

              <View style={dynamicStyles.titleContainer}>
                <Text style={dynamicStyles.mainTitle}>斯卡布罗市场</Text>
                <View style={dynamicStyles.titleUnderline} />
                <Text style={dynamicStyles.subtitle}>
                  发现营业中的商店和商品
                </Text>
              </View>
            </Animated.View>

            <ScrollView
              style={{ flex: 1 }}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: 30 }}
            >
              {/* Map */}
              <Animated.View
                style={[
                  {
                    paddingHorizontal: 20,
                    marginBottom: 20,
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                <BlurView
                  intensity={30}
                  style={{
                    height: height * 0.4,
                    borderRadius: 16,
                    overflow: 'hidden',
                    borderWidth: 1,
                    borderColor: colors.border,
                  }}
                >
                  {renderMap()}
                </BlurView>
              </Animated.View>

              {/* Open Shops List */}
              <Animated.View
                style={[
                  {
                    paddingHorizontal: 20,
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                <Text
                  style={{
                    fontSize: 20,
                    fontWeight: '600',
                    color: colors.text,
                    marginBottom: 16,
                    textAlign: 'center',
                  }}
                >
                  营业中的商店
                </Text>

                {openShops.map((shop) => (
                  <BlurView
                    key={shop.id}
                    intensity={30}
                    style={{
                      marginBottom: 12,
                      borderRadius: 16,
                      overflow: 'hidden',
                      borderWidth: 1,
                      borderColor: colors.border,
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        flexDirection: 'row',
                        padding: 16,
                      }}
                      activeOpacity={0.8}
                    >
                      <View
                        style={{
                          width: 60,
                          height: 60,
                          borderRadius: 30,
                          backgroundColor: colors.secondary + '20',
                          justifyContent: 'center',
                          alignItems: 'center',
                          marginRight: 16,
                        }}
                      >
                        <Store size={30} color={colors.secondary} />
                      </View>
                      <View style={{ flex: 1 }}>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: 4,
                          }}
                        >
                          <Text
                            style={{
                              fontSize: 16,
                              fontWeight: '600',
                              color: colors.text,
                              flex: 1,
                            }}
                          >
                            {shop.name}
                          </Text>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              paddingHorizontal: 8,
                              paddingVertical: 4,
                              borderRadius: 12,
                              marginLeft: 8,
                              backgroundColor: shop.isOpen
                                ? colors.success + '20'
                                : colors.error + '20',
                            }}
                          >
                            {shop.isOpen ? (
                              <Eye size={12} color={colors.success} />
                            ) : (
                              <EyeOff size={12} color={colors.error} />
                            )}
                            <Text
                              style={{
                                fontSize: 10,
                                fontWeight: '600',
                                marginLeft: 4,
                                color: shop.isOpen
                                  ? colors.success
                                  : colors.error,
                              }}
                            >
                              {shop.isOpen ? '营业中' : '已关闭'}
                            </Text>
                          </View>
                        </View>
                        <Text
                          style={{
                            fontSize: 14,
                            color: colors.textSecondary,
                            marginBottom: 8,
                          }}
                        >
                          {shop.description}
                        </Text>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                          }}
                        >
                          <Text
                            style={{
                              fontSize: 12,
                              color: colors.textSecondary,
                            }}
                          >
                            店主: {shop.ownerName}
                          </Text>
                          <View
                            style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}
                          >
                            <Star
                              size={14}
                              color={colors.warning}
                              fill={colors.warning}
                            />
                            <Text
                              style={{
                                fontSize: 12,
                                color: colors.textSecondary,
                                marginLeft: 4,
                              }}
                            >
                              {shop.rating}
                            </Text>
                          </View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  </BlurView>
                ))}
              </Animated.View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

const styles = StyleSheet.create({
  mapFallback: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapFallbackText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 12,
    textAlign: 'center',
  },
  mapFallbackSubtext: {
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
  },
});

export default function MarketScreen() {
  return (
    <ProtectedRoute>
      <MarketContent />
    </ProtectedRoute>
  );
}
