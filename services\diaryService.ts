import { supabase } from '@/lib/supabase';
import {
  DiaryEntry,
  HerbEntry,
  CreateDiaryEntryInput,
  UpdateDiaryEntryInput,
  CreateHerbEntryInput,
  UpdateHerbEntryInput,
  DiaryListParams,
  HerbListParams,
} from '@/types/diary';

export class DiaryService {
  // 日记条目相关方法
  static async createDiaryEntry(
    userId: string,
    input: CreateDiaryEntryInput
  ): Promise<{ data: DiaryEntry | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('diary_entries')
        .insert({
          user_id: userId,
          ...input,
        })
        .select()
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  static async updateDiaryEntry(
    input: UpdateDiaryEntryInput
  ): Promise<{ data: DiaryEntry | null; error: any }> {
    try {
      const { id, ...updateData } = input;
      const { data, error } = await supabase
        .from('diary_entries')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  static async deleteDiaryEntry(
    entryId: string
  ): Promise<{ error: any }> {
    try {
      const { error } = await supabase
        .from('diary_entries')
        .delete()
        .eq('id', entryId);

      return { error };
    } catch (error) {
      return { error };
    }
  }

  static async getDiaryEntry(
    entryId: string
  ): Promise<{ data: DiaryEntry | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('diary_entries')
        .select('*')
        .eq('id', entryId)
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  static async getDiaryEntries(
    userId: string,
    params: DiaryListParams = {}
  ): Promise<{ data: DiaryEntry[] | null; error: any; count?: number }> {
    try {
      let query = supabase
        .from('diary_entries')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // 应用过滤器
      if (params.mood) {
        query = query.eq('mood', params.mood);
      }
      if (params.weather) {
        query = query.eq('weather', params.weather);
      }
      if (params.tag) {
        query = query.contains('tags', [params.tag]);
      }
      if (params.search) {
        query = query.or(`title.ilike.%${params.search}%,content.ilike.%${params.search}%`);
      }
      if (params.start_date) {
        query = query.gte('created_at', params.start_date);
      }
      if (params.end_date) {
        query = query.lte('created_at', params.end_date);
      }

      // 分页
      const page = params.page || 1;
      const limit = params.limit || 10;
      const from = (page - 1) * limit;
      const to = from + limit - 1;

      query = query.range(from, to);

      const { data, error, count } = await query;

      return { data, error, count: count || 0 };
    } catch (error) {
      return { data: null, error, count: 0 };
    }
  }

  // 草药条目相关方法
  static async createHerbEntry(
    userId: string,
    input: CreateHerbEntryInput
  ): Promise<{ data: HerbEntry | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('herb_entries')
        .insert({
          user_id: userId,
          ...input,
        })
        .select()
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  static async updateHerbEntry(
    input: UpdateHerbEntryInput
  ): Promise<{ data: HerbEntry | null; error: any }> {
    try {
      const { id, ...updateData } = input;
      const { data, error } = await supabase
        .from('herb_entries')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  static async deleteHerbEntry(
    entryId: string
  ): Promise<{ error: any }> {
    try {
      const { error } = await supabase
        .from('herb_entries')
        .delete()
        .eq('id', entryId);

      return { error };
    } catch (error) {
      return { error };
    }
  }

  static async getHerbEntry(
    entryId: string
  ): Promise<{ data: HerbEntry | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('herb_entries')
        .select('*')
        .eq('id', entryId)
        .single();

      return { data, error };
    } catch (error) {
      return { data: null, error };
    }
  }

  static async getHerbEntries(
    userId: string,
    params: HerbListParams = {}
  ): Promise<{ data: HerbEntry[] | null; error: any; count?: number }> {
    try {
      let query = supabase
        .from('herb_entries')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // 应用过滤器
      if (params.rarity) {
        query = query.eq('rarity', params.rarity);
      }
      if (params.property) {
        query = query.contains('properties', [params.property]);
      }
      if (params.location) {
        query = query.ilike('location', `%${params.location}%`);
      }
      if (params.search) {
        query = query.or(`name.ilike.%${params.search}%,description.ilike.%${params.search}%,scientific_name.ilike.%${params.search}%`);
      }

      // 分页
      const page = params.page || 1;
      const limit = params.limit || 10;
      const from = (page - 1) * limit;
      const to = from + limit - 1;

      query = query.range(from, to);

      const { data, error, count } = await query;

      return { data, error, count: count || 0 };
    } catch (error) {
      return { data: null, error, count: 0 };
    }
  }

  // 图片上传相关方法
  static async uploadImage(
    file: File,
    bucket: string = 'diary-images'
  ): Promise<{ data: { path: string } | null; error: any }> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `${fileName}`;

      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(filePath, file);

      if (error) {
        return { data: null, error };
      }

      // 获取公共URL
      const { data: urlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      return { data: { path: urlData.publicUrl }, error: null };
    } catch (error) {
      return { data: null, error };
    }
  }

  static async deleteImage(
    imagePath: string,
    bucket: string = 'diary-images'
  ): Promise<{ error: any }> {
    try {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([imagePath]);

      return { error };
    } catch (error) {
      return { error };
    }
  }
}
