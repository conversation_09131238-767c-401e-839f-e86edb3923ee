{"_id": "write-file-atomic", "_rev": "94-8863ad516e48d9dd9a8da40104ba34e0", "name": "write-file-atomic", "dist-tags": {"latest": "6.0.0"}, "versions": {"1.0.0": {"name": "write-file-atomic", "version": "1.0.0", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.0.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "20750a1710e6cd09de5911d3043fa14e4a948445", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.0.0.tgz", "integrity": "sha512-YTzFJHmA40RP8NXofYRaFG54SaN0Wtoo5UIyTWsy1i/fYXjBVU7NlibyjcrBg+jiYpnQXf71SsFOPWTKapdkrQ==", "signatures": [{"sig": "MEYCIQDZyjRufL/1TQbuSJng1nALBD+79hDl3EKM+Rn7CmZfvQIhAJYYw3CL3YPuFHcPztNegW+HK7DkH9V6em/px/sxJ/G8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "20750a1710e6cd09de5911d3043fa14e4a948445", "gitHead": "5ef6f374dfd1b6034f11d80a0d5616e7aa5bcaeb", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "**************:iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "dependencies": {"slide": "^1.1.5"}, "devDependencies": {"tap": "^0.4.12", "require-inject": "^1.1.0"}}, "1.0.1": {"name": "write-file-atomic", "version": "1.0.1", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.0.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "0592ccdc2cee973e7427525f4cd5f67d352afeb9", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.0.1.tgz", "integrity": "sha512-ZJK0UP9+4+N+SR6pOYxYgT2IKMzeWFyiG7VbPA+aq0TkEdVETypLNdpAXq+QwPltVHYMCJNjJU++JAzaWP8dSQ==", "signatures": [{"sig": "MEQCIEk6+enTfpi0DQzdtRwHduFayimpxqYm3kJ3NKlE4DtFAiAVB5u3nXIFYTRttOb5T+hFA7AeJ4VZwD6D02u0y5CFFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0592ccdc2cee973e7427525f4cd5f67d352afeb9", "gitHead": "93f18a172fa79d0fb67c6c1dc37be62a106f13a5", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "**************:iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "dependencies": {"slide": "^1.1.5"}, "devDependencies": {"tap": "^0.4.12", "require-inject": "^1.1.0"}}, "1.0.2": {"name": "write-file-atomic", "version": "1.0.2", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.0.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "080b0439cf29b3abdcf321a86fc47e9429b9984e", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.0.2.tgz", "integrity": "sha512-YygOy8BFyFeyQjbo1cSHfVCJOEG0lmhmvoflSse8p3GKYmARxE40OwhxMCtcz/SjunmrTJmKN2DqVWLmHDzwIw==", "signatures": [{"sig": "MEYCIQDTZkVKu+toVUHSM92rU4+HMWTM7VSTtOMduA9Gb+Q1igIhAOxKkzSfl4B+XWk5jh3gUrqk1WLxhX/d5R8GtVHabs9c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "080b0439cf29b3abdcf321a86fc47e9429b9984e", "gitHead": "84058cea16834f9f53ae1948fbb828937f9ab697", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "**************:iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "dependencies": {"slide": "^1.1.5"}, "devDependencies": {"tap": "^0.4.12", "require-inject": "^1.1.0"}}, "1.0.3": {"name": "write-file-atomic", "version": "1.0.3", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.0.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "597fcb824a0d1ad337db90b7028d2772e9e8e7af", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.0.3.tgz", "integrity": "sha512-sjKqLWndsbTNVKX3nfyvRTvrLBe0rN0kdyQ3oReLgu3YZc3Q1N5lY2X/QvUeoWdcvxphFBJevuCcBxtBS1eO3Q==", "signatures": [{"sig": "MEQCIG6Wg/nll3TFF+XS8707TTX97fARvXIPbwzvEhcpt1UrAiBlzUCvgjUVf3/wA6Ohl2HQmkfJ8iI72m542USF6W/p/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "597fcb824a0d1ad337db90b7028d2772e9e8e7af", "gitHead": "5e85cfc7b38e4f0659b15b1c1d979e6a4a7bf6d5", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "**************:iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "dependencies": {"slide": "^1.1.5"}, "devDependencies": {"tap": "^0.4.12", "require-inject": "^1.1.0"}}, "1.1.0": {"name": "write-file-atomic", "version": "1.1.0", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.1.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "e114cfb8f82188353f98217c5945451c9b4dc060", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.1.0.tgz", "integrity": "sha512-hBzSvmsCxfn7IHmgGWYFiR1EaRO+I1wQL6ELLNV/wBi6jPO3tz+mYd8wPxQTSRNAbCv6a/rLlQU8Hkfq/q/Row==", "signatures": [{"sig": "MEUCIQCJUEyB6OY7YRA4vZiT56HEnIkqS1BfuW10FUcR0cSJEwIgCxrRWSCE1Q78iw9tU8MXnAjgtr77SEfEJ5vzN22SpuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e114cfb8f82188353f98217c5945451c9b4dc060", "gitHead": "28e4df86547c6728eab0b51bca6f00cf44ef392c", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "**************:iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "dependencies": {"slide": "^1.1.5", "graceful-fs": "^3.0.2"}, "devDependencies": {"tap": "^0.4.12", "require-inject": "^1.1.0"}}, "1.1.1": {"name": "write-file-atomic", "version": "1.1.1", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.1.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "cf085df5f415638d9bc9e56ad0f33349f18ba2e4", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.1.1.tgz", "integrity": "sha512-9b8/p180qkY2o+p1P7HDd1LnbBT7qIToiuL13COcY/Ia6dKkm9OsRw1BWokm91ndfem85s9O5V7NTSIumOCwJw==", "signatures": [{"sig": "MEUCIQDIUkmBPBbQZnRcfdgktn/ewg5BCANxS2ULTkmAIfilOQIgbTFUNHkC9+BtMyrLncb75Uc8Mbnzp2Q/OWRl3++IN90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cf085df5f415638d9bc9e56ad0f33349f18ba2e4", "gitHead": "2e85f44a3d176e5e1ed453c016adbe4bcc25eb5c", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "**************:iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^3.0.2"}, "devDependencies": {"tap": "^0.4.12", "require-inject": "^1.1.0"}}, "1.1.2": {"name": "write-file-atomic", "version": "1.1.2", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.1.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "ff3d61f1c2f5bb71e8ebe491a7157bf7d60435a4", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.1.2.tgz", "integrity": "sha512-v74MFElvm262ej04D/7ZtpbrD94n61tCjObcMJPSsW3cuPWtONbitTQOGp1ZEeP0pCZJ4WKKhVP7SADKaiW7uQ==", "signatures": [{"sig": "MEQCIAGUMeaLxTHoGVzb5BAYPPZadSNXvCywRM9wDg0+mMGqAiAYa4s9C/wt/MfEznDNtgr1lX5trLtZjYM08PAaDVHdYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ff3d61f1c2f5bb71e8ebe491a7157bf7d60435a4", "gitHead": "b721f8a71223bcf162f1ee4ff4677f31de1c061f", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "**************:iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "2.7.6", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^3.0.2"}, "devDependencies": {"tap": "^0.4.12", "require-inject": "^1.1.0"}}, "1.1.3": {"name": "write-file-atomic", "version": "1.1.3", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.1.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "60eaca258a0b559b37aca82b21d64a293b4b90d0", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.1.3.tgz", "integrity": "sha512-e5nX+j6V/58iDNVqB+9JC0FeDVnz+I10jZQ7R+qMjtxbgx2W0fdeBSaoTNRO34Y4EgNgHc3kMd0p5bd0DO4yaw==", "signatures": [{"sig": "MEUCICwMt8+FKEYsMF5vV4GMLUok9zbJv4k/rir61xn/JruGAiEA9mZC1HuV3nt+txkQbCJCBR8F5HRsafZMEzvklaWUmLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "60eaca258a0b559b37aca82b21d64a293b4b90d0", "gitHead": "65a1e2e156c0d0bfb7acac2e039b943d6ec9876d", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "3.3.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "3.1.0", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^4.1.2"}, "devDependencies": {"tap": "^0.4.12", "require-inject": "^1.1.0"}}, "1.1.4": {"name": "write-file-atomic", "version": "1.1.4", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.1.4", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "b1f52dc2e8dc0e3cb04d187a25f758a38a90ca3b", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.1.4.tgz", "integrity": "sha512-c5qespPIeoD/YQTLgdOTe9mcjhK0MhK/URjnIlpuF+4Hoec1flfMRcZY+SWrqGHHRC1oGY1VyNC44wiLQgJMiw==", "signatures": [{"sig": "MEUCIFGHX8XrkEtIcljGbmAob4MSM0spjR/P+CB4oAsdccu4AiEA9ZeQ5ntZoLCsm7Vkoo000EYfaUmHSmA8q0MSwaNGooA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b1f52dc2e8dc0e3cb04d187a25f758a38a90ca3b", "gitHead": "42dc04a17af96ac045f4979c8c951ee5a14a8b8b", "scripts": {"test": "standard && tap --coverage test/*.js"}, "_npmUser": {"name": "othiym23", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "3.5.1", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^4.1.2", "imurmurhash": "^0.1.4"}, "devDependencies": {"tap": "^2.3.1", "standard": "^5.4.1", "require-inject": "^1.1.0"}}, "1.2.0": {"name": "write-file-atomic", "version": "1.2.0", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.2.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "14c66d4e4cb3ca0565c28cf3b7a6f3e4d5938fab", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.2.0.tgz", "integrity": "sha512-sF5pW8ltbsziEH1LOXylQqZc970+21ryQmxAY/jtxc8L6MNpDRnY2ME30HTEIGhyfGyNh8YDq0uVWes3j54vDQ==", "signatures": [{"sig": "MEUCIGW5p2TkQ7IJiPSEKy7KmKdHVBZVUxG3Urkap6dzA0lOAiEAzvWiYihkceax2Vy03xJHZT9jsbfhR6r9MHnT2hi3z14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "14c66d4e4cb3ca0565c28cf3b7a6f3e4d5938fab", "gitHead": "c29f37cb5955f597066ad7aedea7aa6f7408a5b7", "scripts": {"test": "standard && tap --coverage test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^4.1.2", "imurmurhash": "^0.1.4"}, "devDependencies": {"tap": "^2.3.1", "tmp": "0.0.28", "standard": "^5.4.1", "require-inject": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic-1.2.0.tgz_1471552371956_0.5829780481290072", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.0": {"name": "write-file-atomic", "version": "1.3.0", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.3.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "d13e4831d52ee4e3d9a266ee1c9a1592f7fbbf3d", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.3.0.tgz", "integrity": "sha512-KS/Xb2t9HcQxjsC6q1aCmRiSUBEGoLsRClN7o55hnvBQu5BCHzmt/FPa1iNm3xRUbqr4HPqwBbKFN04BDSfz3g==", "signatures": [{"sig": "MEYCIQC3GWLJtjOBQWEbqDq66/7stgMnyu98t1U2anrqo8rDbwIhAOCioZmiTaLjPBdmOLE8DdUHteK7yc2YonbX1lDI+nWO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "d13e4831d52ee4e3d9a266ee1c9a1592f7fbbf3d", "gitHead": "8c382bc10b903ca92b36fcaeb19e2630bb6c2c58", "scripts": {"test": "standard && tap --coverage test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "4.1.1", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "4.6.1", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4"}, "devDependencies": {"tap": "^2.3.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^5.4.1", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic-1.3.0.tgz_1483663174188_0.5587220122106373", "host": "packages-18-east.internal.npmjs.com"}}, "1.3.1": {"name": "write-file-atomic", "version": "1.3.1", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.3.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "7d45ba32316328dd1ec7d90f60ebc0d845bb759a", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.3.1.tgz", "integrity": "sha512-RCTmbZJFENrUmJVmdaf3SiIDlP1YQGFub6P/WbrTxKHKLWmhnSgaM/cYsjxDwnzD0gVE2tlTUpX6Zr/9V4+DQg==", "signatures": [{"sig": "MEQCIFyEwhLZQUwW1yfRcWjn1Ma8Y6bDZ9W1mOkMJDKizJ8XAiBI+Xo5Sx933672Np0p9s254HR47Rfb9Yg+0RX1jeF+lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "7d45ba32316328dd1ec7d90f60ebc0d845bb759a", "gitHead": "56fef5763513905d7e43aa685adad80002445474", "scripts": {"test": "standard && tap --coverage test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "4.1.1", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "7.3.0", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4"}, "devDependencies": {"tap": "^2.3.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^5.4.1", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic-1.3.1.tgz_1483776142964_0.11427561868913472", "host": "packages-18-east.internal.npmjs.com"}}, "1.3.2": {"name": "write-file-atomic", "version": "1.3.2", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.3.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "f80ac5e06d3a38996ab51b5d310db57102deb902", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.3.2.tgz", "integrity": "sha512-+oKsjAwslFT7dnTE43USg70xJloTbVQiLit9itkbZbIpWOuYEjhu607+Qh7A0znA/vyVry6T5pdaNMNrJJenVg==", "signatures": [{"sig": "MEUCIQDBJiYTLNRZUMMA32uWjaGvqq1PREekhaCypoAFLvdkMgIgQzgrcuHXKs8QNJlQzfCdDapc6DKs2mF0Gt/fzsQHhSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "gitHead": "d9c5f54df01043671f4ce6542cf7ebcf770e6d43", "scripts": {"test": "standard && tap --coverage test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "7.7.4", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4"}, "devDependencies": {"tap": "^2.3.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^5.4.1", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic-1.3.2.tgz_1492743137041_0.5345788700506091", "host": "packages-18-east.internal.npmjs.com"}}, "1.3.3": {"name": "write-file-atomic", "version": "1.3.3", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.3.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "831dd22d491bdc135180bb996a0eb3f8bf587791", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.3.3.tgz", "integrity": "sha512-FDy1rVWascTeXXe4z2vAl/bFj/+LWYko3Wvl/6Jsv+J5n8ZGiQn7ci/Nmfsn3zdTkG++1LKCOw621qIK/44cog==", "signatures": [{"sig": "MEUCIQDWyQgIm0z/O/UFT2DI4Sf6GbMd9JnEWWjHoZRhmFY7TwIgFYLGFt1/VVKQDiDj6bI2oL0w7Ql0aBElzw2Pxi7Lw8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "831dd22d491bdc135180bb996a0eb3f8bf587791", "gitHead": "fce59c7e3675131712b3965d52e5880e2a5df2ca", "scripts": {"test": "standard && tap --coverage test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "4.5.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "4.6.1", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4"}, "devDependencies": {"tap": "^2.3.1", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^5.4.1", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic-1.3.3.tgz_1492826308726_0.5064767252188176", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.4": {"name": "write-file-atomic", "version": "1.3.4", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@1.3.4", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "f807a4f0b1d9e913ae7a48112e6cc3af1991b45f", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-1.3.4.tgz", "integrity": "sha512-SdrHoC/yVBPpV0Xq/mUZQIpW2sWXAShb/V4pomcJXh92RuaO+f3UTWItiR3Px+pLnV2PvC2/bfn5cwr5X6Vfxw==", "signatures": [{"sig": "MEUCIQCXwkjUYXpMoOzL+fzU7MLIqPhWZuAyu/7GXCE9gigy5gIgKcep6/xC6D0QoQEuyZXXThVFyzv0DCkgw/vL8Zeh35w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "gitHead": "8f7d56f6a62600a38e816a8276a128883f4e7436", "scripts": {"test": "standard && tap --coverage test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "7.7.4", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4"}, "devDependencies": {"tap": "^10.3.2", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^9.0.2", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic-1.3.4.tgz_1493249998004_0.7851631820667535", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "write-file-atomic", "version": "2.0.0", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@2.0.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "bb99a5440d0d31dd860a68da392bffeef66251a1", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.0.0.tgz", "integrity": "sha512-fdiCHCFsKXHKnx7pLSj5S2cTK2R86TDdOrZ+9boV14j9+FBxUQNEK3Hfj4RMRDJT55oLa91t9Tcvhs4be59fvA==", "signatures": [{"sig": "MEQCIAc3Mr1+G29PXC2jlefScTtOEut1SY3t+K12qfFOR0DYAiAdWG0qyMCHWrSciu53FDWSSMSCivnfFtQwvs/RY3kqpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "gitHead": "eabc44b01044a78bcc7b4e41f42ece6bf9d1ca5d", "scripts": {"test": "standard && tap --coverage test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "7.7.4", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4"}, "devDependencies": {"tap": "^10.3.2", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^9.0.2", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic-2.0.0.tgz_1493250025531_0.17179739149287343", "host": "packages-18-east.internal.npmjs.com"}}, "2.1.0": {"name": "write-file-atomic", "version": "2.1.0", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@2.1.0", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "1769f4b551eedce419f0505deae2e26763542d37", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.1.0.tgz", "integrity": "sha512-0TZ20a+xcIl4u0+Mj5xDH2yOWdmQiXlKf9Hm+TgDXjTMsEYb+gDrmb8e8UNAzMCitX8NBqG4Z/FUQIyzv/R1JQ==", "signatures": [{"sig": "MEQCIANhkSJJlu8pDMKRUjYWm1wWigICBy1l8TVDHKLSbZGkAiBulhmyCLuyPG21JQb6wj+SOmQiWkhAXboM2Zmo3uBMEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "gitHead": "4c63f8e504845ceca4a11cfff779e5b4839243f9", "scripts": {"test": "standard && tap --100 test/*.js"}, "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"slide": "^1.1.5", "graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4"}, "devDependencies": {"tap": "^10.3.2", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^10.0.2", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic-2.1.0.tgz_1494704753639_0.7767078222241253", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.0": {"name": "write-file-atomic", "version": "2.3.0", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@2.3.0", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "1ff61575c2e2a4e8e510d6fa4e243cce183999ab", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.3.0.tgz", "integrity": "sha512-xuPeK4OdjWqtfi59ylvVL0Yn35SF3zgcAcv7rBPFHVaEapaDr4GdGgm3j7ckTwH9wHL7fGmgfAnb0+THrHb8tA==", "signatures": [{"sig": "MEUCIEE+f0UnGZwyvsy97mUkMt/sCqEuuy+PwiVfccWUt/6KAiEA7t2TA4f8AxU8gzyb66VHru91mZRRDfBqvzI3BVWX9qI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "gitHead": "1597785cb9e1152056d905a357b3891b3295506d", "scripts": {"test": "standard && tap --100 test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "8.3.0", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "devDependencies": {"tap": "^10.3.2", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^10.0.2", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic-2.3.0.tgz_1503096898166_0.22769020055420697", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "write-file-atomic", "version": "2.4.0", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@2.4.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "cf0f9bc37c2d26c455161c3b75f407f5bd1cad64", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.4.0.tgz", "fileCount": 5, "integrity": "sha512-rpYM0txDxd2GV1RJeyAEhDasBPUG51fFpSaW22JKulFA6mKrOYfht5mkq62TbTRfKdUDFj0b8TAz95+F5sc+Ow==", "signatures": [{"sig": "MEYCIQDVDNSC11LlobwmRqClg8ALpPw2rHKbScxah6k0iGKGdAIhAMw5tw2wWEk+nZd8c0DKnmvzkdkDDzaKxvx6ZJbT3JLG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11293, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcR6zBCRA9TVsSAnZWagAAHPcP/jV1fYdreaeFXpo9x+5z\nF9btxkpfPeSHv8GITUoHg+thLVNPyIATPyK6Dt+TO/IYeubheL3A7IrAQgKU\nzyuiTvsHZsluOsJ6lWvT4t3QdTKy7fNF9XtAF99QTSY+yyqGJSHrOUDxICGu\nUN04is4jgamCHFS0JTjxsmCuiLbu3zGle18fhckZg0bacC3JII2Zy8Jipfn+\n/CIMdNqiwltD5TXUWON7U7hsoaGncbHK97xXtKmRZZlbAV5WlSbKDCAra+Jy\npmAGG222tIwDvQxmKKrUCzYlq/C9CNzZNB+5aFrk9fCWvx/YeYEIISZsbSY8\nolGv6bFyiCYFkNXiGf4Ji8KHSjiKalQY7FSKkcr41QM6TEzUq0SVfF0wEAtp\nBGM/lMndX3yUyE5+IB1JX6UOp7xHWxFN8j/BGQc73Ue0zyHqyWtMgxIwzw26\nxE10CPaJnAX7UanI3JN+Kmt0ljedlcfXJb9MqbjLoXbsW+h+UO/0ZEerYU4H\nhGXVIV1IqZYTh9dCr7sQsRDWtKf/IMvQf354uE6A0LQmcFDp2zLxTnA2JjvE\nupOLyTInnaNEO+wUH9A4GUZrT+qF0kHur1d2fzUe0scO4mdB/LrZuJAJL0bc\n7q/py54xOSpHtDSJFUhqcxRo8I/BeRDhvr+0DGsQmC+jaGN0Bure064a0v8o\nmLq3\r\n=TzH1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "152992ae232070bb25951a7b0484a3bd31160460", "scripts": {"test": "standard && tap --100 test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "6.6.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^12.0.1", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_2.4.0_1548201153104_0.7017856282373696", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "write-file-atomic", "version": "2.4.1", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@2.4.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "d0b05463c188ae804396fd5ab2a370062af87529", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.4.1.tgz", "fileCount": 5, "integrity": "sha512-TGHFeZEZMnv+gBFRfjAcxL5bPHrsGKtnb4qsFAws7/vlh+QfwAaySIw4AXP9ZskTTh5GWu3FLuJhsWVdiJPGvg==", "signatures": [{"sig": "MEUCIQDTH2zvyvED4IjDcKLITBM/pxcYZO8NPA+CLyqG4MWMewIgApke2InbN6fszqGUarayUGgGQfsnrNYvVRXyxFuY2MM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcR7T2CRA9TVsSAnZWagAAYXkP/1Czylm7ZB3NG/YDhmPV\nD4sdHRlzuB7YI4hLVzzYou0ArKCXHo5Na84ZRn7yusGfX0w36T6c+lngwDuN\nvsiF+cEmdWJQLNLPqxrAh9F/6ey4kV2XOopEu3+T91sWpVf/LRq+GW62QU9i\nVytmy8u0EuYQ0lEBrady3Y4wBBs37PZ/9AOedpM3Fn857QfvB20sxduzpELJ\nOulpvzGMbEV3Cs5TGh+RNsTn55ajgteU2hl8x+XGNn3lfEleSmS9KjnvoATa\nW+NTkw5M5Xsv1SoYVoFFK6KvNZiUIFk677RNcbZmThW/CvvsISJBm3sG1Bx5\noULMPDLHFfbyMEGC1H8H5svm/ChlLBpZLp9PwRASkTxnA2F53MWsVpQbcKWW\nKBCiFSGlR6hc6fjUdBQMmMxar+qGvh/x48+9gNMB9GyEITsmm3R7urgJHC6r\nNEDt6zqhrEaYRHUw9xEL5y7WOklXP4DFXqm69g3N30j9qZLVs13/a+peqZWt\nql3Vm3eW0LmOiOtafOznZxmWGMjz3o7+E124XThHScXBuVG//i4n3MsfR3gm\nW9t//0AVtOiiwD0K8+xqP+3beVdbmhAVJD4WlEfYFjvndyte0BKnB2UwhBTE\nlcCdjMxd60V5CpjaaH8FuNlWbBU0wBjlwS71PyuSi+vcx8R/AOqUxwQycAtw\nyRh1\r\n=I1Gh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "8ea1b4ab6e2e9630fc495118084058fbeb09bcbc", "scripts": {"test": "standard && tap --100 test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "6.5.0-next.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "11.6.0", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^12.0.1", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_2.4.1_1548203254097_0.2775843084072045", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "write-file-atomic", "version": "2.4.2", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@2.4.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "a7181706dfba17855d221140a9c06e15fcdd87b9", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.4.2.tgz", "fileCount": 5, "integrity": "sha512-s0b6vB3xIVRLWywa6X9TOMA7k9zio0TMOsl9ZnDkliA/cfJlpHXAscj0gbHVJiTdIuAYpIyqS5GW91fqm6gG5g==", "signatures": [{"sig": "MEUCIQCAf8i5YNjkVs2giTeIwIw7f5C3lcNLSELgkz4qid7ahAIgM+K1bOXW+j/********************************=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcR8IdCRA9TVsSAnZWagAA2sMP/Ak3zz3ypibJ7iszwuES\nuDJZrda3zlNFGqWehFDMc6TD/9YxPd6Rgh0Up2LD0+Y6DbddxmHiaPo7H4n7\n12NT3s886JrL3fJ6Nwfd0u0P2JXyA49f8pTfwL8kAUbV/MR5ietvf9wxCAcs\nadjmiow1pwuiJ6KHHoIoDMRd6EmNXe8sEAD/Jj57fAeoTa/UD5K94M7uz1w0\nw7iuXZ/zFT/cL+l0KdMEnBCgj8gDgqVI0rlTh1spUf9PYtppU0ju6OVGlKrS\nfTo+kGOIHAydyzzD7hlELAWtcrI9ZGpn0MtrjCpvlXGonVHnLxz9PKjZlEhN\n3mVTnyELCN7osex52J6hpIL3PA26ta4uV/HsUR+fx1hWoNhdPGOdX05ochiu\n0rQ4EcHSg/hDp0n3jmKNj9LtteDNebGlugNiZLlmR8Cdzgjy4muKmZP+Io8e\nLuQq0IzToxj/C8Kq2mAEIa+zHDo8GZaKOwNow3pbcarZ+gP5gXLgSXvX0lpV\nSNP5+PeoXQo+z0S3iS2wYpwrO45Ugh4ZwAZ/ZeeN9IcSWlKdyxjKZKVULJ66\nJgXjl7W5Hhh9pBwm2WQz66a8oUYwk+r+/+msfu59jX6687EhFIq1EA23b9jN\nwpnXh/tocqtNbnvDZ2pJThV8HJAakSzV8SLAK057oh1Mli1t84t4QrVMvHV+\nsa4w\r\n=MkZv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2191d03e97ecf447af30057d752b25fa07c1f32a", "scripts": {"test": "standard && tap --100 test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "11.3.0", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^12.0.1", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_2.4.2_1548206621021_0.3144530936854546", "host": "s3://npm-registry-packages"}}, "2.4.3": {"name": "write-file-atomic", "version": "2.4.3", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@2.4.3", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "othiym23", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/iarna/write-file-atomic", "bugs": {"url": "https://github.com/iarna/write-file-atomic/issues"}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-2.4.3.tgz", "fileCount": 5, "integrity": "sha512-GaETH5wwsX+GcnzhPgKcKjJ6M2Cq3/iZp1WyY/X1CSqrW+jVNM9Y7D8EC2sM4ZG/V8wZlSniJnCKWPmBYAucRQ==", "signatures": [{"sig": "MEYCIQCDcXjq3im6qCUpA++ccqwB8Q8+CErIRE+dYjsH/H4fQAIhALqO6tullA5GZeuAc9RhEVIkXhxosP76EfDSfHJ61Jbn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6CB1CRA9TVsSAnZWagAAKv8P/0XmSzv2zNvqfeBtBqqm\nFfvpCZHVOGb4jYPW87fIF1BsFCqGILhDEhUhW2tSkckNW4a7SB+ZmmAVzh4w\ngS8JL0FAkgftjvS1lkVQ5fMJvhw0HoKMVGGKGgOv2LN2G3genJPn7iU8oOOP\nCQVrEjBUyL9no1ebU6hDNdp2v8FcltyFbshwYJtVtCHLek80AREhtkpe0uHg\nc7zYwszOPT5g/AJf47QiY1m7jr0KH6BLSDx2loqx+uaS3qazVcflUG8N7ezy\nRSyTybXScz+qc0Puickqzg8ZTEloc5CQAGU4cgIYDyCYjB36HFxJoCjQShVa\nBAWuv7UrnIxqCvVCJmZYbEehoEkLh0C8tKTn+DJBM8BIrxWKklMeV4hNUNJ8\n1zQamBc19YcDKqOqBH2g3ggZNDYZ0s8Cr726v4wzaDMipWFor196A3HSBy9i\nLKiE/f0/4O9FyKKvodMGTvmmJIkdiHnt6nAX8XiBJExzQKY8r+ZXsE6NrZwH\n8NgA77VuxHdppfc5pURyVEFRFa1xWDVo1M445CvFo+ijqehtJ41uWOftBbrE\nuJ//zGzq88uK2gv9RbUvJTTPCEB61IKeUBsqIswmbiS8i0xP/KmKR0Vtg7uN\nOMye9XJOJNV0OpCJrokCB7A8WpFq55o0KBDA5yMYXGEskLTbL6jYicIhC45U\nk3qn\r\n=Ai3b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "f3b025d7ec066b3579c0737a400e668dbed42262", "scripts": {"test": "standard && tap --100 test/*.js", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/iarna/write-file-atomic.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "standard": "^12.0.1", "require-inject": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_2.4.3_1558716532556_0.2880615077337976", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "write-file-atomic", "version": "3.0.0", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@3.0.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/write-file-atomic", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "tap": {"100": true}, "dist": {"shasum": "1b64dbbf77cb58fd09056963d63e62667ab4fb21", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-EIgkf60l2oWsffja2Sf2AL384dx328c0B+cIYPTQq5q2rOYuDV00/iPFBOUiDKKwKMOhkymH8AidPaRvzfxY+Q==", "signatures": [{"sig": "MEUCIQCclUIiAsVF31txVFuSUTP70ccTBLCOzIF+Og4eTBan3gIgYF0u7wZo9F6JhcYqTzUNH8wgWSyqKkwjap8U5rk1kag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6EP1CRA9TVsSAnZWagAAXGkP/1h6tmcgs0V3sCPl+3ky\nPdYUhRTOih9EMnJQhs3H4ySEsxD3fnIApnqC+HWoCoQCXqfDl/u9vKsxVla7\nPfEk3ACvFc4yzLOAbPzQheCK8wenvV+QmbPfJh1HzrG27CkUVbrApreg1vjs\nSoQjHp3K9tfYwLn21QaRdb7qSI2fw+RD/PlZedjII7tb1K4ovRM0gr62ZZGC\nCbKn7zrbrdL1/Yu+OYuLE7anmOG6LI+ATXLlGDCJYjFMFQDHGUtBTq80BfDF\n19UxhR1tkwbYo5FE/vfkSO+yAyDS6CcAr1yDtrOfbK9dPW8iDSapEF0jSQJD\n/j9Mr2aouhqtOQxYhK2GvOF7yZ1HaWuJy9fVgSYDdIMbpsrei0XQ6AkhleXh\nq4Mn9bMBZyj5yjXqxCAIZwKAEq1drOg4c6t6eMiEUWy0bPvRwuCBPDE8tvQ2\n4e5A7cmaRMo+N+OU9WA95DIXe8lQXv40xz/iPrY2QpiMzsj2v0y6zqSVt+aV\n1kwgMFIKqMyaOiBnmvb1hUeAuWVDBuNw+fJb7LF47h1m5eNd+QWkF/3Q9ID+\nIoMj9DNQoQmungI8ldX05rissICsmD5TgtJpKA9nRCjaQv8MJB+syyNsJfXf\npo06XR0oxwlJfDEhtvisLPjod3miI2hrw3WGtw/fYdg7MZW6GQJv+s5c+Mxt\nEuPJ\r\n=7kpW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "73c3b6f94ce7d57f63d30d4b7b33f17d4004bf1e", "scripts": {"test": "tap", "pretest": "standard", "posttest": "rimraf chowncopy good nochmod nochown nofsync nofsyncopt noopen norename \"norename nounlink\" nowrite", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/write-file-atomic.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "12.2.0", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.2", "is-typedarray": "^1.0.0", "typedarray-to-buffer": "^3.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.1.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "standard": "^12.0.1", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_3.0.0_1558725620595_0.8040002107095305", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "write-file-atomic", "version": "3.0.1", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@3.0.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/write-file-atomic", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "tap": {"100": true}, "dist": {"shasum": "558328352e673b5bb192cf86500d60b230667d4b", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-JPStrIyyVJ6oCSz/691fAjFtefZ6q+fP6tm+OS4Qw6o+TGQxNp1ziY2PgS+X/m0V8OWhZiO/m4xSj+Pr4RrZvw==", "signatures": [{"sig": "MEUCIQCgIsVTs/9a8dNDKTMLfgCmgjJnWghDfJeQbJDLe2UrwAIgQBL7Nmoh75XIlGzRQdidVv2fDdLy4S9TZv5cNW9+Cj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdppsqCRA9TVsSAnZWagAAYm0P/27IQscOGat9p23GecLP\nTAtgm331gpeaHznpk8CAWLmSXZ7Cz51p2DqH1YnsLxLJDsTBCnUrj2SuJq3f\nAAGzvBjxp87SrFtd7PvNt1r5eu7CINrVlSPx+i4AIeNgPoZW0YYacLrJkJla\nDTM9M8Z+sSTA9MbtW23Gdh40WDeMxCOu7O7BKHGTNj6ydgLF2Tx08Y+LTbw7\n8qbRmPCrUo5KCoBX6P0RCZoXss59T/UZhpO/TxHkCHvrDYeiRDlca3kwfUiX\nZQHwPe7qIDVjPAGnfGitme3qOzYWPQmi9lbI00uCQEITu2Qg6alU9JNKK4dc\nAFmWVOAzJ19mGMdrJxQpCt5GVSrbGzuVtBZR8k9hU2Q/w5gIL2fuvJodc4Sp\n5lVWW35lkl4Pu0+7S1I9nMs4fq3/wdIR/mtKBLVPMnh86ryHSfltuBDFtAAn\n3B8rnJ1dckSsggsfCdeixCLazoE8D9CYH5vTSFfapRN4y/l6AySaQhBCsOny\n7WOAA84eoNzgAOf9Xi4szMtvZSredbSMSbb/eQ7xd3Y48rUSbjVn0BLoW6qI\nh79kFc0jv5tGH5cxoHrxPwhEAn6JN7hyakWwZMqmDh9IS0kgLL423uLZNRg7\nWhMRPHsNCOhGHq8FRehZiHX01TVqQSZM5d13lK2GFKnECrJ4Pd44ZPqD4C7b\n4dJ1\r\n=500D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "4eeacc621736d9d1f4ad58f4ac6084a630b7ddc8", "scripts": {"test": "tap", "pretest": "standard", "posttest": "rimraf chowncopy good nochmod nochown nofsync nofsyncopt noopen norename \"norename nounlink\" nowrite", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/write-file-atomic.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.2", "is-typedarray": "^1.0.0", "typedarray-to-buffer": "^3.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.1.1", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "standard": "^12.0.1", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_3.0.1_1571199785525_0.9882528754344773", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "write-file-atomic", "version": "3.0.2", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@3.0.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/write-file-atomic", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "tap": {"100": true}, "dist": {"shasum": "0c55003305e31dee5e62b3693dcb553410ac11e3", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-w+Lx4LO0eSgy3FypOovx1+vXrW+MilikDICu4sTe0YSmBT2D4MQmQ6X3wQ/gaP6TwAcsQgu03zhIfYdjYioVZQ==", "signatures": [{"sig": "MEUCIBuGMtIb5addgSTf4cs7LJmBPHJpdtm4Z9gvYlXDWjIVAiEA99yGdFMvHl1YdNlGhG4Wc9CNVnAiJtEVvBzXQj4eauo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVDYOCRA9TVsSAnZWagAAjOMP/RKjMDeh7vBeQ31XNb/V\njlHMoOkfDl6QSF2/81HxLsD3/f4qtNrTsrUV5yrN0BJpHJiAYrwIirCEDRWq\nggNuqBphT4JJu7UIRTHKZ1fHTLzG5Tu6+nkXfbzpNmwqLuRSuZaCT2cN+SGd\n6mpc+a12okIadFAT3bMKEScdvbKc2M6z2HpBe57UMt7ChhNUPjsvb+emrH8X\n/FDxiH+nwTiQGWQEvHKJONHoH+J3OSm5YKt6QrZ1Ry8HI0VCcZmo2fFXW325\ntG0lTtcx5wQGENjwjJxl9nj2WkBskswe0GGGpoZV6AwFafRkUbBaiwT/qD0w\nYtobkSyF4LY499CSBPlEaTCs5VDmbyRczlvFBFAwKg/OA8Dwk1SeBsyxOStk\ndEiy6xZ0J0KVcUNrfdcKWpdJBbBTdrmEeSvYt+C0SUh3qTd14/lskg/AbHxI\nlO7SIHk/y6NR1fIZY6HhgAhWpAw1eRxFwhPtdhGoxUylL3r00MFFBXUM3jQ2\nV9ouw3VzDCMX3krwcw0p6FO4rAMFv0bOBu9Le2uooF1oXB/t63i5Y8uUYr/K\n866EOJAjqCAEFCZTOnZhrCEcqSquWPMXYJe/fXtO20m4NFCu5Lzn7rKivmVo\n2Mewl91RzQjkupc4BHaA7ecmlgFH7Eh2DeF8clPz8o01/jhKncQQRJyhV4r6\naQWP\r\n=b93n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "e322288d985d54992142b144ed2e6366f8865a45", "scripts": {"lint": "standard", "test": "tap", "postlint": "rimraf chowncopy good nochmod nochown nofsync nofsyncopt noopen norename \"norename nounlink\" nowrite", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/write-file-atomic.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.2", "is-typedarray": "^1.0.0", "typedarray-to-buffer": "^3.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "standard": "^14.3.1", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_3.0.2_1582577166119_0.8984137433054395", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "write-file-atomic", "version": "3.0.3", "keywords": ["writeFile", "atomic"], "author": {"url": "http://re-becca.org", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "write-file-atomic@3.0.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/write-file-atomic", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "tap": {"100": true}, "dist": {"shasum": "56bd5c5a5c70481cd19c571bd39ab965a5de56e8", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz", "fileCount": 5, "integrity": "sha512-AvHcyZ5JnSfq3ioSyjrBkH9yW4m7Ayk8/9My/DD9onKeu/94fwrMocemO2QAJFAlnnDN+ZDS+ZjAR5ua1/PV/Q==", "signatures": [{"sig": "MEUCIFTHZ0a4a77TWvBA4fr8ZPlnaJf2WMDAcKr7eASANB0qAiEAnBOzLyLfsZya9CAH5Iti6LIv/3plhHjHEI9S5gjeCx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12787, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVE24CRA9TVsSAnZWagAAZ1oP/izZvazxVzxm9iG6jaq6\nFuYMxO/dmoTAml/sAnFtEhP/XxCn8bqzR96FCde+yeFFLHY96kRsd4fbMD6E\nJtGTVyvGxIv9T4gcjwesw/XRs28bTL8ustWDG/rFxiwR2DsPpjmmgA76LnWK\n9u0FSzz3j+XbclQxbwnjBmS+0fwZpe4JBK6ig9fp8RLc6gI8QKgvVgMgzEl4\ncPwEzDs5YrPgMnM+obzbQEegnG3JRpdcPiAY4dZEchKXfgorbxUpaGV5wZ/5\nLNqAxpIHy7mlIQAAmmbroezfOR4jq417+Ue6sjU0mz6jcGHCmM2R2tRSRaCf\nGPHKjdHA0jctoayAWZIs7wIUtQhOiOsyVqdD9JtNE431oIbzgvX//GCVJE/U\noMWKI/51WpT/X2VzU6DO7WaHwOInEykeR+EEW2TXKrWcJUu/gDoe3VL/fvTJ\njR+EUINXWWn9BC19ZNx0mOLx4HH0r6EYgGnPPeuwzaVXumc5zFYgLCOoigMi\nO19OWetKThqA0+evO7MpoZs2ea05AvIzBQYwq4cnDLsRIaeSJsYhwWB4wetY\n6yPytApnjAw6DW9Wcqu2KTeHIJ1nbCuU7p8M1wcnDq5oMoW65UrBcP2LMhSp\nNDWcmssNk0ayubl4QEZ2oKyMty9EXLj6hiXBYUk1k5NnCsYAL9LFzIiukixH\n80WP\r\n=wDSu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "eb8dff15f83f16be1e0b89be54fa80200356614a", "scripts": {"lint": "standard", "test": "tap", "postlint": "rimraf chowncopy good nochmod nochown nofsync nofsyncopt noopen norename \"norename nounlink\" nowrite", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/write-file-atomic.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.2", "is-typedarray": "^1.0.0", "typedarray-to-buffer": "^3.1.5"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "standard": "^14.3.1", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_3.0.3_1582583223897_0.8414533271003164", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "write-file-atomic", "version": "4.0.0", "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "write-file-atomic@4.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/write-file-atomic", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "dist": {"shasum": "0eff5dc687d3e22535ca3fca8558124645a4b053", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-<PERSON><PERSON><PERSON>WoKffJNF7ivO9yflBhc7tn3wKnokMUfWpBriM9yCXj4ePQnRPcWglBkkg1AHC8nsW/EfxwwhqsLtOy59djA==", "signatures": [{"sig": "MEUCIEnKrNgBeB3CM605hrk91fBsLNe1jvAx23FO9OScuWAlAiEAu70qt4pOiBtp1hkOwZCMUaQbyD27I3tUOnmFnGrlHRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ynNCRA9TVsSAnZWagAAzogP/2AVgMqY2GR9SXLrnvgH\nrJCd/EcoNK0oGxJEWALxtOIcu8xxU2oBVrwObQCTN7i7vRXiIztndUBwKuMM\nb4JSz1MDfrd76JPtJc5azCGbGt0/tMZKRBrwIyiIhyhq4ff0DO2Eq2faxWJ5\nIKFSsqZ+THBWRWQyczBiL/ZkqNMg/nu1lo3sjOv/gC0oGCMMDt2D0VLVksU0\nW9lt6v/7jKQL6DX/Y+U2kq2Z110wpuIGEs6+lCHgCmTvShn0GIHfk/5fAl+j\nWfTu8gIEx+3UslxfgtAiDRN0StdIYLzenFTF+k8nVeQDI8KLj5TPM/5wGfP3\nI/9UCF+ts6+g324PVkcm9071MWEUxk2wu/sVMg2n95N8xZNm9U/vI0GbF/Kr\nQH7kedVYBgvsoR47z+LXMDwZ71iq14ZJUxne84yG2f8xWXVQ5WjKaYUBhD/R\nBwKu13ywR6F7T1F4jN49gtS3citFMxoZAOtQ7Bb4+Wkm/r4KeVM7UfPz9D47\nwASEoVv5JDYNS/F3uMG+lzbcENWrSyUkgM3yuh8u6L8V0WvYgqRGLXo4h8ok\ntdc6ffwTMcJmnwlg+xHyEeQy5IbOr8wmSu8S5L9X2aFU5qttwyJJ0yiTSeAm\nN047t9NMv6oFvz3oy4WceTqpnzcj85BPRVB30jEo5Bvix0MUa5Kpm/wwtkG2\n2F+M\r\n=T5ZW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "5c14a2f20a3dbceb55413270fe8d9982c239fb3f", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/write-file-atomic.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "templateOSS": {"version": "2.5.1", "windowsCI": false}, "_nodeVersion": "16.13.0", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.2", "is-typedarray": "^1.0.0", "typedarray-to-buffer": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "@npmcli/template-oss": "^2.5.1"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_4.0.0_1642539469227_0.11293768248957403", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "write-file-atomic", "version": "4.0.1", "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "write-file-atomic@4.0.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/write-file-atomic", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "dist": {"shasum": "9faa33a964c1c85ff6f849b80b42a88c2c537c8f", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.1.tgz", "fileCount": 4, "integrity": "sha512-nSKUxgAbyioruk6hU87QzVbY279oYT6uiwgDoujth2ju4mJ+TZau7SQBhtbTmUyuNYTuXnSyRn66FV0+eCgcrQ==", "signatures": [{"sig": "MEQCIEso1cJdy/56AukXaMtuMv8xy0QcOtGavHXmEuiU5CawAiBaW4AoPeeXqbN4FxszAc8tBzxado6vzXYNDOpxFvhF0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBSUhCRA9TVsSAnZWagAAMuwP/0r0E6bCPezsTlGykNy0\nedF/WiAMTCBTaqhXPnWVIWafdKcvHEkKd8lwNWG4FuG1C4kCS1d+rowIgt7H\ny18aAUYYkM/Rc4iSwg/S0jQOSk/0Laalo9dZSQdNvP9XMRRiVhTAPyVZk6VO\nl/EV0NM8cBlxeTULAbiYgxWl0ombYG4IDuP41W6DLhwgTqoQjbWbzCCtpkO0\nrcGdf0uvRXl0JiRt0A0TypMD31C23YEAd0pODmzHvf3jnWNPZSlaoeGSIbZl\nojbaPIHXl3uRspffJhQ7tq331RtbMT7JnblR3dXy0Ua7OwqOQfi9jG7fdyCx\nOd76t6NDrCftvu4+TbUbWmeFL/+LvPKLMqSe6tAdQnPZBOoxE4qjfb4brq1U\nmhtqSNaqvJKkz2brpj11FBbfxqe8PC0RclMiEoCuLS3dlg5XytOaS6RW1ivN\nulRG0E9OwdNONt1JYQTGSk9a9P7RMc235rXWmCdeR0JBM4S+sfhjOuLPR1Mg\nyFDE7Etr06iVy0t+P0NQRvQzRQfx5vDKNOY4JblsHYAV2BI+ajWSe7aZjfOo\n2hcVkybjv2oZ90XgzfCF8cme4yRJt3A0FhgxP9f5oMgv948FajTJNonf5yHo\nUH5RF2pywmm8G6NeosTaA6MYLMoCSjwzD+85YS8qAtaXTVFzfQLgfqILmbf6\nGxhT\r\n=ltxU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "bf8ca7f9f5e6b7f955c97fd0e9fdbc2d6adb1e3c", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "template-copy": "npm-template-copy --force", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/write-file-atomic.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "templateOSS": {"version": "2.7.1", "windowsCI": false}, "_nodeVersion": "16.14.0", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "@npmcli/template-oss": "^2.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_4.0.1_1644504353063_0.04931648875822048", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "write-file-atomic", "version": "4.0.2", "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "write-file-atomic@4.0.2", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/write-file-atomic", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "dist": {"shasum": "a9df01ae5b77858a027fd2e80768ee433555fcfd", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "fileCount": 4, "integrity": "sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==", "signatures": [{"sig": "MEQCIFN0rOXzb7vlZ/Ne3ZvDXqdi5kBnplBRC9V4av29mFsWAiBMVWOVfqqC6a/LHIYhD3RQS5zZkwd3Sb6ysZQcemlJmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+9AVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrD1w/+LfZiCgVS2tRWWLxCSoeZZzdH8PwknIv4CDmjof2jguBbQPOI\r\nozz3mQP9CWxREtHuQ5/CwAzE03/mKBp+DZbY3WbgfK9OdXWGQERBLS+M3Bwp\r\nDHGK3PPrWfqMnLrbsO+uWKxsXcoNCAk8+F/A9meLUb9ymeSa62LRfViZeLFn\r\nQGVkNSAFhD7EHbEz7QlIZZWdJmkr87mGXiS6BhOSK9yIBf5FoZofcq6whbTU\r\nyJDc1AX7JBbw/B7dCE3GpNDajx7j4sJ7ZEL4bqllrc3Bocb9rxVCBG0GrGJ7\r\n6RXE3zzS0sxWnf2LSluNKQ8VtGUtCzbZII9qr+lw5JryqdwXw11CIHmprz/j\r\nm2UH79gIGBZejE0SG0vtW3Y3QCJ5rvjcPdvez0woA76jVXq37j1MMoNCwx9p\r\npWdDQVTE47T5X17bU8fcF3sT4doTfUFqbRaWjKSyj1kiJCYAGWArTxBaOIIb\r\n1azEV5mSMZQ/kzS7EAApstgC8ZM2VXgMmBS8ET1sfkRnPCnlD5AxAQbwWY4F\r\nyyGJzSGz04ZlJ+N7FUfIWumZcfNtZRH5GJzDhoph2kL7Xd8rwdc8AVOB62fP\r\nGdGhVpfmIM93At+jvgxTiecFeKlEap0gCRy7IyFsWdSHVoXppTWPIZrpsid5\r\nhAsbdCCGxU1wvxdzebk9Tn6OUOwBdFqS2ek=\r\n=5qqS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "9688d63c4013595dd6a2b2128273b67544be3ca4", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/write-file-atomic.git", "type": "git"}, "_npmVersion": "8.17.0", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "templateOSS": {"version": "3.5.0", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.6.0", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_4.0.2_1660669973372_0.12770302065333472", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "write-file-atomic", "version": "5.0.0", "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "write-file-atomic@5.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/write-file-atomic", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "54303f117e109bf3d540261125c8ea5a7320fab0", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-5.0.0.tgz", "fileCount": 4, "integrity": "sha512-R7NYMnHSlV42K54lwY9lvW6MnSm1HSJqZL3xiSgi9E7//FYaI74r2G0rd+/X6VAMkHEdzxQaU5HUOXWUz5kA/w==", "signatures": [{"sig": "MEQCIE6PBSPffISfxPWaCbEz4dcFN45vbBu2uedNlaK0tSWhAiBgZjEiNxukcAgoY9bNxCHzCEGta7HlvrUePfSvkap3IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSPIeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpegQ//UwQTHr9v/4PTaG+mVdV03IzOUAd0wvIbN1yTR5CuKIUP3saF\r\nHy3vETe3e9WSmV9PQ5ha+lCQvZ0GHukyMQSgjEsZzfYYepLnx2OqalG8XQzt\r\nXQS1OB19ywQsP+ZckdfkvYSfBk9jPqU950a22NcK3N2o8QQj9C3N14NnNJqU\r\nYLV+WZ6KYnhNVzdkCqX1eOj+HzPDXK1wOKbcj96g58z/rulNP56rBiSwPUoL\r\nVzkBstZxQp9zea7YpeLuaOSYJjeLtBQHBzCcxdNN3ys9PyT5Dr/AICM3TTDK\r\nksXF5A9wCm+qmQYH/uKk98NEjj6FifDAyoJcD41SYvxdsR5x/UOzfqOprlWC\r\n7w9jJMKlV4XaFLMbedwFQQ8nRMIAUpxOuRk6ndMNBvX2Kp24nfc7YNWN4jit\r\nsIB5JnfxwnLjH5uPzpo/PvGUomq/SaaWpPDM3DH8zAGU+JC3CDB1tGa2SVAI\r\nnAWa9v7Xyk7b5G+AvjGcA+9/IVerkFfBLbNU0ZgcoYxDWx/TydwhKvwhIsc4\r\nCHo9XMI4MUvXYkbppAtnXvUbF5POHpa/RZJviu5fgJsVwfGrQNL/8FsF4dGV\r\npqT1WIMOxYOL9RqhcgZYZF7gWvMLAx5X2iAGxJk5rzfG8egR2Lseqt7z8c9j\r\nHua4O8tqYVCnSr1STVazJ85oB4aJcz+aX/o=\r\n=azqX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "af873aab3169b7362d990532b3e60889d71d28b2", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/write-file-atomic.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "templateOSS": {"version": "4.5.1", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.10.0", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_5.0.0_1665724958752_0.9578277453246526", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "write-file-atomic", "version": "5.0.1", "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "write-file-atomic@5.0.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/write-file-atomic", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "68df4717c55c6fa4281a7860b4c2ba0a6d2b11e7", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-5.0.1.tgz", "fileCount": 4, "integrity": "sha512-+QU2zd6OTD8XWIJCbffaiQeH9U73qIqafo1x6V1snCWYGJf6cVE0cDR4D8xRzcEnfI21IFrUPzPGtcPf8AC+Rw==", "signatures": [{"sig": "MEYCIQDqqykBTtym4VY08wDXnXSrulqKJQKw8QI+L+2OL6BWmwIhAPoR81vEraEZI16hWXBL2MXCu9w7HZIe1CJOl340rbz0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/write-file-atomic@5.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 12155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSXt6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpm7g//Uq4rbtZOYVoM9VWATgOeIek0XxGsP/Tli7fgAIg1ExYKdCcZ\r\n5olEczsvVgYj7KUJRvuYY59Zbj36OgxbgRlmkhZt4vLh7O/7MCsuHlZ+SrTs\r\npsO/mTMXtXgDvlQc2bxVwkRJ0srluC0vhLyhn4JQh2wFM4JwO/os4CG4ihs1\r\ndY3MGEIJ5VWp2FDKEVfMAsliPnU0VwTuFlrqt9lFodwQtPDM8HzTePkdtJ0O\r\nS9Fsd0ynmVyBMusKc09ZPE8mfhCZDqcN8zv2hl/PqHBuxqZlQbcFTvenIQuV\r\nNgI7GOoMgOozsuv/Tu+JU/qFaHHp2iSa9H/F1FSt56ZhVytohCoHMI2KAiUe\r\niUMheHsfz7Ego3FqTWh7pJ+vc3jVKUx5x+tqQ1Rg6HfKqUI72O87CDdOEC8p\r\nkTSkhBbEShvw5YsQf5j/oc0W3gyiWLWawb7BhQ2WIAfdNvu/0JqKiy17+vMZ\r\nUlseyYXPPJZOG5e4m564MxqyLv8vjeEO1H+xC08IRG1cK3kW2bxMvH3I3q2+\r\nuUMRptb2nBBLp++Vfq4cahtd8O0xbjmABoyVu0GDCrlsor3ntUNWyPg+nwoW\r\ndsDp0xkrZYD7vh50KAxfqjP68Pm10J6vONFlApiglDLcrGtIzkQeSHLTbPsy\r\nC3CCzfk3m4JHWszKOTKUerZSOeJ9Sv3SZtU=\r\n=oyQz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "042cff721108a0c1d8a831ba9fa8dca2e5e390da", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/write-file-atomic.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "templateOSS": {"publish": "true", "version": "4.14.1", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_5.0.1_1682537338540_0.8323328526091589", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "write-file-atomic", "version": "6.0.0", "keywords": ["writeFile", "atomic"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "write-file-atomic@6.0.0", "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/write-file-atomic", "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "e9c89c8191b3ef0606bc79fb92681aa1aa16fa93", "tarball": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-6.0.0.tgz", "fileCount": 4, "integrity": "sha512-GmqrO8WJ1NuzJ2DrziEI2o57jKAVIQNf8a18W3nCYU3H7PNWqCCVTeH6/NQE93CIllIgQS98rrmVkYgTX9fFJQ==", "signatures": [{"sig": "MEUCIHRVsY6kOqYyc5+lzTFlZ3XeUCFCezFPnZzqsK5HVEuxAiEAncU2Wq7vuEmGjBtWbUc7oI+mavj8AzUhEHXn8/YYtvk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/write-file-atomic@6.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 12202}, "main": "./lib/index.js", "engines": {"node": "^18.17.0 || >=20.5.0"}, "gitHead": "9fcd4021b8a0c86bf54deded4905aec68d968161", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/write-file-atomic.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Write files in an atomic fashion w/configurable ownership", "directories": {}, "templateOSS": {"publish": "true", "version": "4.23.3", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.9.0", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^4.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/write-file-atomic_6.0.0_1727285807414_0.2588495531603523", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-09-10T23:26:27.142Z", "modified": "2025-05-14T20:04:28.405Z", "1.0.0": "2014-09-10T23:26:27.142Z", "1.0.1": "2014-09-10T23:27:54.551Z", "1.0.2": "2014-09-11T23:53:33.815Z", "1.0.3": "2014-09-15T23:33:40.116Z", "1.1.0": "2014-09-19T23:32:52.849Z", "1.1.1": "2015-05-22T01:40:50.343Z", "1.1.2": "2015-05-22T01:53:50.418Z", "1.1.3": "2015-09-08T21:17:40.905Z", "1.1.4": "2015-12-03T22:04:24.581Z", "1.2.0": "2016-08-18T20:32:52.191Z", "1.3.0": "2017-01-06T00:39:35.969Z", "1.3.1": "2017-01-07T08:02:24.984Z", "1.3.2": "2017-04-21T02:52:18.885Z", "1.3.3": "2017-04-22T01:58:28.962Z", "1.3.4": "2017-04-26T23:39:58.237Z", "2.0.0": "2017-04-26T23:40:28.497Z", "2.1.0": "2017-05-13T19:45:53.878Z", "2.3.0": "2017-08-18T22:54:58.294Z", "2.4.0": "2019-01-22T23:52:33.211Z", "2.4.1": "2019-01-23T00:27:34.206Z", "2.4.2": "2019-01-23T01:23:41.137Z", "2.4.3": "2019-05-24T16:48:52.689Z", "3.0.0": "2019-05-24T19:20:20.733Z", "3.0.1": "2019-10-16T04:23:05.682Z", "3.0.2": "2020-02-24T20:46:06.260Z", "3.0.3": "2020-02-24T22:27:04.013Z", "4.0.0": "2022-01-18T20:57:49.368Z", "4.0.1": "2022-02-10T14:45:53.214Z", "4.0.2": "2022-08-16T17:12:53.613Z", "5.0.0": "2022-10-14T05:22:38.937Z", "5.0.1": "2023-04-26T19:28:58.689Z", "6.0.0": "2024-09-25T17:36:47.579Z"}, "bugs": {"url": "https://github.com/npm/write-file-atomic/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/write-file-atomic", "keywords": ["writeFile", "atomic"], "repository": {"url": "git+https://github.com/npm/write-file-atomic.git", "type": "git"}, "description": "Write files in an atomic fashion w/configurable ownership", "maintainers": [{"email": "<EMAIL>", "name": "gar"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "owlstronaut"}], "readme": "write-file-atomic\n-----------------\n\nThis is an extension for node's `fs.writeFile` that makes its operation\natomic and allows you set ownership (uid/gid of the file).\n\n### `writeFileAtomic(filename, data, [options], [callback])`\n\n#### Description:\n\nAtomically and asynchronously writes data to a file, replacing the file if it already\nexists.  data can be a string or a buffer.\n\n#### Options:\n* filename **String**\n* data **String** | **Buffer**\n* options **Object** | **String**\n  * chown **Object** default, uid & gid of existing file, if any\n    * uid **Number**\n    * gid **Number**\n  * encoding **String** | **Null** default = 'utf8'\n  * fsync **Boolean** default = true\n  * mode **Number** default, from existing file, if any\n  * tmpfileCreated **Function** called when the tmpfile is created\n* callback **Function**\n\n#### Usage:\n\n```js\nvar writeFileAtomic = require('write-file-atomic')\nwriteFileAtomic(filename, data, [options], [callback])\n```\n\nThe file is initially named `filename + \".\" + murmurhex(__filename, process.pid, ++invocations)`.\nNote that `require('worker_threads').threadId` is used in addition to `process.pid` if running inside of a worker thread.\nIf writeFile completes successfully then, if passed the **chown** option it will change\nthe ownership of the file. Finally it renames the file back to the filename you specified. If\nit encounters errors at any of these steps it will attempt to unlink the temporary file and then\npass the error back to the caller.\nIf multiple writes are concurrently issued to the same file, the write operations are put into a queue and serialized in the order they were called, using Promises. Writes to different files are still executed in parallel.\n\nIf provided, the **chown** option requires both **uid** and **gid** properties or else\nyou'll get an error.  If **chown** is not specified it will default to using\nthe owner of the previous file.  To prevent chown from being ran you can\nalso pass `false`, in which case the file will be created with the current user's credentials.\n\nIf **mode** is not specified, it will default to using the permissions from\nan existing file, if any.  Expicitly setting this to `false` remove this default, resulting\nin a file created with the system default permissions.\n\nIf options is a String, it's assumed to be the **encoding** option. The **encoding** option is ignored if **data** is a buffer. It defaults to 'utf8'.\n\nIf the **fsync** option is **false**, writeFile will skip the final fsync call.\n\nIf the **tmpfileCreated** option is specified it will be called with the name of the tmpfile when created.\n\nExample:\n\n```javascript\nwriteFileAtomic('message.txt', 'Hello Node', {chown:{uid:100,gid:50}}, function (err) {\n  if (err) throw err;\n  console.log('It\\'s saved!');\n});\n```\n\nThis function also supports async/await:\n\n```javascript\n(async () => {\n  try {\n    await writeFileAtomic('message.txt', 'Hello Node', {chown:{uid:100,gid:50}});\n    console.log('It\\'s saved!');\n  } catch (err) {\n    console.error(err);\n    process.exit(1);\n  }\n})();\n```\n\n### `writeFileAtomicSync(filename, data, [options])`\n\n#### Description:\n\nThe synchronous version of **writeFileAtomic**.\n\n#### Usage:\n```js\nvar writeFileAtomicSync = require('write-file-atomic').sync\nwriteFileAtomicSync(filename, data, [options])\n```\n\n", "readmeFilename": "README.md", "users": {"jden": true, "iarna": true, "ierceg": true, "jsdnxx": true, "monsterkodi": true, "bluejeansandrain": true}}