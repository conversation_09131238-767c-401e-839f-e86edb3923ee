// 日记相关的类型定义

export interface DiaryEntry {
  id: string;
  user_id: string;
  title: string;
  content: string;
  mood: 'happy' | 'sad' | 'excited' | 'calm' | 'adventurous' | 'mysterious' | 'peaceful';
  weather: 'sunny' | 'cloudy' | 'rainy' | 'snowy' | 'foggy' | 'stormy';
  location: string;
  tags: string[];
  images: string[]; // 图片URL数组
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

export interface HerbEntry {
  id: string;
  user_id: string;
  name: string;
  scientific_name: string;
  description: string;
  location: string;
  date_found: string;
  properties: string[];
  rarity: 'common' | 'uncommon' | 'rare' | 'legendary';
  uses: string[];
  notes: string;
  images: string[];
  illustration: string;
  created_at: string;
  updated_at: string;
}

export interface DiaryImage {
  id: string;
  diary_entry_id: string;
  image_url: string;
  caption?: string;
  order_index: number;
  created_at: string;
}

// 心情选项
export const MoodOptions = {
  happy: { label: '开心', emoji: '😊', color: '#FFD700' },
  sad: { label: '忧伤', emoji: '😢', color: '#87CEEB' },
  excited: { label: '兴奋', emoji: '🤩', color: '#FF6347' },
  calm: { label: '平静', emoji: '😌', color: '#98FB98' },
  adventurous: { label: '冒险', emoji: '🗺️', color: '#DDA0DD' },
  mysterious: { label: '神秘', emoji: '🔮', color: '#9370DB' },
  peaceful: { label: '宁静', emoji: '🕊️', color: '#F0F8FF' },
} as const;

// 天气选项
export const WeatherOptions = {
  sunny: { label: '晴朗', emoji: '☀️', color: '#FFD700' },
  cloudy: { label: '多云', emoji: '☁️', color: '#D3D3D3' },
  rainy: { label: '下雨', emoji: '🌧️', color: '#4682B4' },
  snowy: { label: '下雪', emoji: '❄️', color: '#F0F8FF' },
  foggy: { label: '雾天', emoji: '🌫️', color: '#C0C0C0' },
  stormy: { label: '暴风雨', emoji: '⛈️', color: '#2F4F4F' },
} as const;

// 稀有度选项
export const RarityOptions = {
  common: { label: '常见', color: '#90EE90', icon: '🌿' },
  uncommon: { label: '不常见', color: '#87CEEB', icon: '🌸' },
  rare: { label: '稀有', color: '#DDA0DD', icon: '🌺' },
  legendary: { label: '传说', color: '#FFD700', icon: '🌟' },
} as const;

// 创建日记条目的输入类型
export interface CreateDiaryEntryInput {
  title: string;
  content: string;
  mood: DiaryEntry['mood'];
  weather: DiaryEntry['weather'];
  location: string;
  tags: string[];
  images: string[];
  is_public: boolean;
}

// 更新日记条目的输入类型
export interface UpdateDiaryEntryInput extends Partial<CreateDiaryEntryInput> {
  id: string;
}

// 创建草药条目的输入类型
export interface CreateHerbEntryInput {
  name: string;
  scientific_name: string;
  description: string;
  location: string;
  date_found: string;
  properties: string[];
  rarity: HerbEntry['rarity'];
  uses: string[];
  notes: string;
  images: string[];
  illustration: string;
}

// 更新草药条目的输入类型
export interface UpdateHerbEntryInput extends Partial<CreateHerbEntryInput> {
  id: string;
}

// 日记列表查询参数
export interface DiaryListParams {
  page?: number;
  limit?: number;
  mood?: DiaryEntry['mood'];
  weather?: DiaryEntry['weather'];
  tag?: string;
  search?: string;
  start_date?: string;
  end_date?: string;
}

// 草药列表查询参数
export interface HerbListParams {
  page?: number;
  limit?: number;
  rarity?: HerbEntry['rarity'];
  property?: string;
  search?: string;
  location?: string;
}
