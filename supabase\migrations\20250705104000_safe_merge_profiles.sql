-- 安全版本：分步骤合并 profiles 表和创建新表
-- 每个步骤都有错误处理

-- 步骤 1：创建枚举类型
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM (
          'herb_merchant',  -- 草本商人
          'wanderer',       -- 流浪者
          'lone_hunter'     -- 伶仃猎手
        );
        RAISE NOTICE '✅ 创建 user_role 枚举类型';
    ELSE
        RAISE NOTICE 'ℹ️  user_role 枚举类型已存在';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'organization') THEN
        CREATE TYPE organization AS ENUM (
          'dark_brotherhood',    -- 暗转兄弟会
          'stormcloaks',        -- 风暴斗篷
          'thieves_syndicate',  -- 盗赋公司
          'winterhold_academy', -- 冬保学院
          'imperial_legion',    -- 帝国军团
          'independent'         -- 独立
        );
        RAISE NOTICE '✅ 创建 organization 枚举类型';
    ELSE
        RAISE NOTICE 'ℹ️  organization 枚举类型已存在';
    END IF;
END $$;

-- 步骤 2：为 profiles 表添加缺失字段
DO $$
BEGIN
    -- 添加 email 字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'email') THEN
        ALTER TABLE profiles ADD COLUMN email TEXT;
        RAISE NOTICE '✅ 添加 email 字段';
    ELSE
        RAISE NOTICE 'ℹ️  email 字段已存在';
    END IF;

    -- 添加 role 字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'role') THEN
        ALTER TABLE profiles ADD COLUMN role user_role;
        RAISE NOTICE '✅ 添加 role 字段';
    ELSE
        RAISE NOTICE 'ℹ️  role 字段已存在';
    END IF;

    -- 添加 organization 字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'organization') THEN
        ALTER TABLE profiles ADD COLUMN organization organization DEFAULT 'independent';
        RAISE NOTICE '✅ 添加 organization 字段';
    ELSE
        RAISE NOTICE 'ℹ️  organization 字段已存在';
    END IF;

    -- 添加 organization_rank 字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'organization_rank') THEN
        ALTER TABLE profiles ADD COLUMN organization_rank TEXT;
        RAISE NOTICE '✅ 添加 organization_rank 字段';
    ELSE
        RAISE NOTICE 'ℹ️  organization_rank 字段已存在';
    END IF;

    -- 添加 tasks_completed 字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'tasks_completed') THEN
        ALTER TABLE profiles ADD COLUMN tasks_completed INTEGER DEFAULT 0;
        RAISE NOTICE '✅ 添加 tasks_completed 字段';
    ELSE
        RAISE NOTICE 'ℹ️  tasks_completed 字段已存在';
    END IF;

    -- 添加 shop_id 字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'shop_id') THEN
        ALTER TABLE profiles ADD COLUMN shop_id UUID;
        RAISE NOTICE '✅ 添加 shop_id 字段';
    ELSE
        RAISE NOTICE 'ℹ️  shop_id 字段已存在';
    END IF;
END $$;

-- 步骤 3：创建 shops 表
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'shops') THEN
        CREATE TABLE shops (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          owner_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          name TEXT NOT NULL,
          description TEXT,
          latitude DECIMAL(10, 8) NOT NULL,
          longitude DECIMAL(11, 8) NOT NULL,
          is_open BOOLEAN DEFAULT false,
          is_approved BOOLEAN DEFAULT false,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        RAISE NOTICE '✅ 创建 shops 表';
    ELSE
        RAISE NOTICE 'ℹ️  shops 表已存在';
    END IF;
END $$;

-- 步骤 4：创建 products 表
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        CREATE TABLE products (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          shop_id UUID NOT NULL REFERENCES shops(id) ON DELETE CASCADE,
          name TEXT NOT NULL,
          description TEXT NOT NULL,
          price DECIMAL(10, 2) NOT NULL CHECK (price >= 0),
          image_url TEXT,
          stock INTEGER DEFAULT 0 CHECK (stock >= 0),
          is_available BOOLEAN DEFAULT true,
          discovered_year INTEGER DEFAULT 1200,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        RAISE NOTICE '✅ 创建 products 表';
    ELSE
        RAISE NOTICE 'ℹ️  products 表已存在';
    END IF;
END $$;

-- 步骤 5：创建 orders 表
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        CREATE TABLE orders (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          buyer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          seller_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
          product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
          quantity INTEGER NOT NULL CHECK (quantity > 0),
          total_price DECIMAL(10, 2) NOT NULL CHECK (total_price >= 0),
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        RAISE NOTICE '✅ 创建 orders 表';
    ELSE
        RAISE NOTICE 'ℹ️  orders 表已存在';
    END IF;
END $$;

-- 步骤 6：创建 organization_strongholds 表
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'organization_strongholds') THEN
        CREATE TABLE organization_strongholds (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          organization organization NOT NULL,
          name TEXT NOT NULL,
          latitude DECIMAL(10, 8) NOT NULL,
          longitude DECIMAL(11, 8) NOT NULL,
          description TEXT,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        RAISE NOTICE '✅ 创建 organization_strongholds 表';
    ELSE
        RAISE NOTICE 'ℹ️  organization_strongholds 表已存在';
    END IF;
END $$;

-- 步骤 7：创建触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 步骤 8：创建触发器
DO $$
BEGIN
    -- shops 表触发器
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'shops') THEN
        DROP TRIGGER IF EXISTS update_shops_updated_at ON shops;
        CREATE TRIGGER update_shops_updated_at
            BEFORE UPDATE ON shops
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        RAISE NOTICE '✅ 创建 shops 表更新触发器';
    END IF;

    -- products 表触发器
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        DROP TRIGGER IF EXISTS update_products_updated_at ON products;
        CREATE TRIGGER update_products_updated_at
            BEFORE UPDATE ON products
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        RAISE NOTICE '✅ 创建 products 表更新触发器';
    END IF;

    -- orders 表触发器
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        DROP TRIGGER IF EXISTS update_orders_updated_at ON orders;
        CREATE TRIGGER update_orders_updated_at
            BEFORE UPDATE ON orders
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        RAISE NOTICE '✅ 创建 orders 表更新触发器';
    END IF;
END $$;

-- 步骤 9：插入示例数据
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'organization_strongholds') THEN
        -- 插入组织据点数据
        INSERT INTO organization_strongholds (organization, name, latitude, longitude, description) 
        SELECT 'dark_brotherhood', '暗影圣所', 54.2781, -1.4360, '暗转兄弟会的秘密据点，隐藏在斯卡伯勒的地下'
        WHERE NOT EXISTS (SELECT 1 FROM organization_strongholds WHERE name = '暗影圣所');

        INSERT INTO organization_strongholds (organization, name, latitude, longitude, description) 
        SELECT 'stormcloaks', '风暴要塞', 54.2850, -1.4250, '风暴斗篷的训练场所，位于海岸悬崖'
        WHERE NOT EXISTS (SELECT 1 FROM organization_strongholds WHERE name = '风暴要塞');

        INSERT INTO organization_strongholds (organization, name, latitude, longitude, description) 
        SELECT 'thieves_syndicate', '盗贼公会', 54.2750, -1.4400, '盗赋公司的总部，伪装成普通商铺'
        WHERE NOT EXISTS (SELECT 1 FROM organization_strongholds WHERE name = '盗贼公会');

        INSERT INTO organization_strongholds (organization, name, latitude, longitude, description) 
        SELECT 'winterhold_academy', '冬保学院', 54.2900, -1.4300, '魔法学院的分院，研究古老的符文'
        WHERE NOT EXISTS (SELECT 1 FROM organization_strongholds WHERE name = '冬保学院');

        INSERT INTO organization_strongholds (organization, name, latitude, longitude, description) 
        SELECT 'imperial_legion', '帝国军营', 54.2800, -1.4200, '帝国军团的驻地，维护市场秩序'
        WHERE NOT EXISTS (SELECT 1 FROM organization_strongholds WHERE name = '帝国军营');

        RAISE NOTICE '✅ 插入组织据点示例数据';
    END IF;
END $$;

-- 步骤 10：创建索引
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_organization ON profiles(organization);
CREATE INDEX IF NOT EXISTS idx_shops_owner_id ON shops(owner_id);
CREATE INDEX IF NOT EXISTS idx_shops_is_open ON shops(is_open);
CREATE INDEX IF NOT EXISTS idx_products_shop_id ON products(shop_id);
CREATE INDEX IF NOT EXISTS idx_products_is_available ON products(is_available);
CREATE INDEX IF NOT EXISTS idx_orders_buyer_id ON orders(buyer_id);
CREATE INDEX IF NOT EXISTS idx_orders_seller_id ON orders(seller_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_strongholds_organization ON organization_strongholds(organization);

-- 完成提示
DO $$
BEGIN
    RAISE NOTICE '🎉 数据库迁移完成！';
    RAISE NOTICE '📊 请运行 npm run db:structure 验证表结构';
END $$;
