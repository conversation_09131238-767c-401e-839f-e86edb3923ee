import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
  TextInput,
  Modal,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Store, Plus, CreditCard as Edit3, Save, X, MapPin, Coins, Package, Star, Eye, EyeOff, CircleCheck as CheckCircle, Clock, CircleAlert as AlertCircle, Camera } from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';

const { width, height } = Dimensions.get('window');

interface Shop {
  id: string;
  name: string;
  description: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  isOpen: boolean;
  status: 'pending' | 'approved' | 'rejected';
  ownerId: string;
  createdAt: string;
  products: Product[];
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  imageUrl: string;
  category: string;
  inStock: boolean;
  shopId: string;
}

function ShopContent() {
  const { isDark } = useTheme();
  const { user, profile } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  const [shop, setShop] = useState<Shop | null>(null);
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [showProductModal, setShowProductModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [applicationForm, setApplicationForm] = useState({
    name: '',
    description: '',
    latitude: '',
    longitude: '',
    address: '',
  });
  const [productForm, setProductForm] = useState({
    name: '',
    description: '',
    price: '',
    imageUrl: '',
    category: 'herbs',
  });

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Load user's shop if exists
    loadUserShop();
  }, []);

  const loadUserShop = () => {
    // Mock data - in real app, fetch from database
    const mockShop: Shop = {
      id: '1',
      name: '香草药铺',
      description: '专营各种珍贵草药和治愈药剂的古老药铺',
      location: {
        latitude: 54.2781,
        longitude: -0.4053,
        address: '斯卡布罗集市东区第三街',
      },
      isOpen: false,
      status: 'approved',
      ownerId: user?.id || '',
      createdAt: new Date().toISOString(),
      products: [
        {
          id: '1',
          name: '治愈药剂',
          description: '由新鲜香芹制成的强效治愈药剂',
          price: 150,
          imageUrl: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
          category: 'potions',
          inStock: true,
          shopId: '1',
        },
        {
          id: '2',
          name: '百里香精华',
          description: '珍贵的百里香精华，提升魔法抗性',
          price: 300,
          imageUrl: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
          category: 'herbs',
          inStock: true,
          shopId: '1',
        },
      ],
    };
    
    if (profile?.level && profile.level >= 1) {
      setShop(mockShop);
    }
  };

  const handleShopApplication = () => {
    if (!applicationForm.name || !applicationForm.description || !applicationForm.address) {
      Alert.alert('错误', '请填写所有必填字段');
      return;
    }

    const newShop: Shop = {
      id: Date.now().toString(),
      name: applicationForm.name,
      description: applicationForm.description,
      location: {
        latitude: parseFloat(applicationForm.latitude) || 54.2781,
        longitude: parseFloat(applicationForm.longitude) || -0.4053,
        address: applicationForm.address,
      },
      isOpen: false,
      status: 'pending',
      ownerId: user?.id || '',
      createdAt: new Date().toISOString(),
      products: [],
    };

    setShop(newShop);
    setShowApplicationModal(false);
    setApplicationForm({ name: '', description: '', latitude: '', longitude: '', address: '' });
    Alert.alert('成功', '商店申请已提交，等待审核中...');
  };

  const handleApproveShop = () => {
    if (shop) {
      setShop({ ...shop, status: 'approved' });
      Alert.alert('恭喜', '您的商店申请已通过审核！现在可以开始营业了。');
    }
  };

  const toggleShopStatus = () => {
    if (shop && shop.status === 'approved') {
      const newStatus = !shop.isOpen;
      setShop({ ...shop, isOpen: newStatus });
      Alert.alert(
        newStatus ? '商店开启' : '商店关闭',
        newStatus ? '您的商店现在开始营业！' : '您的商店已关闭营业。'
      );
    }
  };

  const handleAddProduct = () => {
    if (!productForm.name || !productForm.description || !productForm.price) {
      Alert.alert('错误', '请填写所有必填字段');
      return;
    }

    const newProduct: Product = {
      id: Date.now().toString(),
      name: productForm.name,
      description: productForm.description,
      price: parseFloat(productForm.price),
      imageUrl: productForm.imageUrl || 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
      category: productForm.category,
      inStock: true,
      shopId: shop?.id || '',
    };

    if (shop) {
      setShop({ ...shop, products: [...shop.products, newProduct] });
    }

    setShowProductModal(false);
    setProductForm({ name: '', description: '', price: '', imageUrl: '', category: 'herbs' });
    Alert.alert('成功', '商品已添加到您的商店！');
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setProductForm({
      name: product.name,
      description: product.description,
      price: product.price.toString(),
      imageUrl: product.imageUrl,
      category: product.category,
    });
    setShowProductModal(true);
  };

  const handleUpdateProduct = () => {
    if (!editingProduct || !shop) return;

    const updatedProducts = shop.products.map(p => 
      p.id === editingProduct.id 
        ? {
            ...p,
            name: productForm.name,
            description: productForm.description,
            price: parseFloat(productForm.price),
            imageUrl: productForm.imageUrl,
            category: productForm.category,
          }
        : p
    );

    setShop({ ...shop, products: updatedProducts });
    setShowProductModal(false);
    setEditingProduct(null);
    setProductForm({ name: '', description: '', price: '', imageUrl: '', category: 'herbs' });
    Alert.alert('成功', '商品信息已更新！');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return colors.success;
      case 'pending': return colors.warning;
      case 'rejected': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return '已通过';
      case 'pending': return '审核中';
      case 'rejected': return '已拒绝';
      default: return '未知';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return CheckCircle;
      case 'pending': return Clock;
      case 'rejected': return AlertCircle;
      default: return AlertCircle;
    }
  };

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    header: {
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    merchantContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 20,
      width: '100%',
    },
    merchantFrame: {
      width: 70,
      height: 70,
      borderRadius: 35,
      borderWidth: 3,
      borderColor: colors.secondary,
      overflow: 'hidden',
      position: 'relative',
      marginRight: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.5 : 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    merchantImage: {
      width: '100%',
      height: '100%',
    },
    shopBadge: {
      position: 'absolute',
      bottom: -5,
      right: -5,
      backgroundColor: colors.secondary,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.background,
    },
    merchantInfo: {
      flex: 1,
    },
    merchantTitle: {
      fontSize: 18,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 2,
    },
    merchantSubtitle: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    titleContainer: {
      alignItems: 'center',
    },
    mainTitle: {
      fontSize: 28,
      color: colors.text,
      textAlign: 'center',
      marginBottom: 8,
      fontWeight: '700',
      textShadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.2)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    titleUnderline: {
      width: 80,
      height: 3,
      backgroundColor: colors.secondary,
      borderRadius: 2,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 30,
    },
    noShopContainer: {
      paddingHorizontal: 20,
      alignItems: 'center',
    },
    noShopCard: {
      padding: 30,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
      alignItems: 'center',
    },
    noShopIcon: {
      marginBottom: 16,
    },
    noShopTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
      textAlign: 'center',
    },
    noShopDescription: {
      fontSize: 14,
      color: colors.textSecondary,
      textAlign: 'center',
      marginBottom: 20,
      lineHeight: 20,
    },
    applyButton: {
      borderRadius: 12,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    applyButtonGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      paddingHorizontal: 24,
    },
    applyButtonText: {
      color: colors.background,
      fontSize: 18,
      fontWeight: '700',
      marginLeft: 8,
    },
    shopContainer: {
      paddingHorizontal: 20,
    },
    shopCard: {
      marginBottom: 20,
      borderRadius: 16,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    shopHeader: {
      padding: 20,
    },
    shopTitleRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    shopName: {
      fontSize: 22,
      fontWeight: '700',
      color: colors.text,
      flex: 1,
    },
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 20,
      marginLeft: 12,
    },
    statusText: {
      fontSize: 12,
      fontWeight: '600',
      marginLeft: 4,
    },
    shopDescription: {
      fontSize: 14,
      color: colors.textSecondary,
      lineHeight: 20,
      marginBottom: 12,
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    locationText: {
      fontSize: 14,
      color: colors.textSecondary,
      marginLeft: 6,
    },
    shopControls: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    toggleButton: {
      borderRadius: 12,
      overflow: 'hidden',
      flex: 0.48,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 4,
    },
    toggleButtonGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
    },
    toggleButtonText: {
      color: colors.background,
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 6,
    },
    approveButton: {
      borderRadius: 12,
      overflow: 'hidden',
      flex: 0.48,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 4,
    },
    productsSection: {
      paddingHorizontal: 20,
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    addProductButton: {
      borderRadius: 12,
      overflow: 'hidden',
      marginBottom: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 4,
    },
    addProductGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
    },
    addProductText: {
      color: colors.background,
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },
    productCard: {
      marginBottom: 12,
      borderRadius: 16,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    productContent: {
      flexDirection: 'row',
      padding: 16,
    },
    productImage: {
      width: 80,
      height: 80,
      borderRadius: 12,
      marginRight: 16,
    },
    productInfo: {
      flex: 1,
    },
    productName: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 4,
    },
    productDescription: {
      fontSize: 14,
      color: colors.textSecondary,
      marginBottom: 8,
      lineHeight: 18,
    },
    productFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    productPrice: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    priceText: {
      fontSize: 16,
      fontWeight: '700',
      color: colors.text,
      marginLeft: 4,
    },
    editButton: {
      padding: 8,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      width: width * 0.9,
      maxHeight: height * 0.8,
      borderRadius: 20,
      overflow: 'hidden',
    },
    modalContent: {
      padding: 20,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: colors.text,
    },
    closeButton: {
      padding: 8,
    },
    formContainer: {
      marginBottom: 20,
    },
    inputGroup: {
      marginBottom: 16,
    },
    inputLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    textInput: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: colors.text,
    },
    textArea: {
      height: 80,
      textAlignVertical: 'top',
    },
    saveButton: {
      borderRadius: 12,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    saveButtonGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      paddingHorizontal: 24,
    },
    saveButtonText: {
      color: colors.background,
      fontSize: 18,
      fontWeight: '700',
      marginLeft: 8,
    },
  });

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{ 
          uri: isDark 
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary
          ]}
          style={dynamicStyles.overlay}
        >
          <SafeAreaView style={dynamicStyles.safeArea}>
            {/* Header */}
            <Animated.View 
              style={[
                dynamicStyles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
                }
              ]}
            >
              <View style={dynamicStyles.merchantContainer}>
                <View style={dynamicStyles.merchantFrame}>
                  <Image
                    source={{ uri: profile?.avatar_url || 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                    style={dynamicStyles.merchantImage}
                    resizeMode="cover"
                  />
                  <View style={dynamicStyles.shopBadge}>
                    <Store size={16} color={colors.background} />
                  </View>
                </View>
                <View style={dynamicStyles.merchantInfo}>
                  <Text style={dynamicStyles.merchantTitle}>商店管理</Text>
                  <Text style={dynamicStyles.merchantSubtitle}>管理您的集市商店</Text>
                </View>
              </View>

              <View style={dynamicStyles.titleContainer}>
                <Text style={dynamicStyles.mainTitle}>我的商店</Text>
                <View style={dynamicStyles.titleUnderline} />
                <Text style={dynamicStyles.subtitle}>在斯卡布罗集市开设您的商店</Text>
              </View>
            </Animated.View>

            <ScrollView 
              style={dynamicStyles.scrollView} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={dynamicStyles.scrollContent}
            >
              {!shop ? (
                // No Shop - Application Form
                <Animated.View 
                  style={[
                    dynamicStyles.noShopContainer,
                    {
                      opacity: fadeAnim,
                      transform: [{ translateY: slideAnim }]
                    }
                  ]}
                >
                  <BlurView intensity={30} style={dynamicStyles.noShopCard}>
                    <Store size={60} color={colors.secondary} style={dynamicStyles.noShopIcon} />
                    <Text style={dynamicStyles.noShopTitle}>开设您的商店</Text>
                    <Text style={dynamicStyles.noShopDescription}>
                      在斯卡布罗集市申请开设您的专属商店，销售草药、药剂和珍贵物品，成为受人尊敬的商人！
                    </Text>
                    <TouchableOpacity
                      style={dynamicStyles.applyButton}
                      onPress={() => setShowApplicationModal(true)}
                      activeOpacity={0.8}
                    >
                      <LinearGradient
                        colors={[colors.secondary, colors.accent]}
                        style={dynamicStyles.applyButtonGradient}
                      >
                        <Plus size={20} color={colors.background} />
                        <Text style={dynamicStyles.applyButtonText}>申请开店</Text>
                      </LinearGradient>
                    </TouchableOpacity>
                  </BlurView>
                </Animated.View>
              ) : (
                // Shop Management
                <>
                  {/* Shop Info */}
                  <Animated.View 
                    style={[
                      dynamicStyles.shopContainer,
                      {
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }]
                      }
                    ]}
                  >
                    <BlurView intensity={30} style={dynamicStyles.shopCard}>
                      <View style={dynamicStyles.shopHeader}>
                        <View style={dynamicStyles.shopTitleRow}>
                          <Text style={dynamicStyles.shopName}>{shop.name}</Text>
                          <View style={[
                            dynamicStyles.statusBadge,
                            { backgroundColor: `${getStatusColor(shop.status)}20` }
                          ]}>
                            {React.createElement(getStatusIcon(shop.status), {
                              size: 16,
                              color: getStatusColor(shop.status)
                            })}
                            <Text style={[
                              dynamicStyles.statusText,
                              { color: getStatusColor(shop.status) }
                            ]}>
                              {getStatusText(shop.status)}
                            </Text>
                          </View>
                        </View>
                        
                        <Text style={dynamicStyles.shopDescription}>{shop.description}</Text>
                        
                        <View style={dynamicStyles.locationContainer}>
                          <MapPin size={16} color={colors.textSecondary} />
                          <Text style={dynamicStyles.locationText}>{shop.location.address}</Text>
                        </View>

                        <View style={dynamicStyles.shopControls}>
                          {shop.status === 'approved' ? (
                            <TouchableOpacity
                              style={dynamicStyles.toggleButton}
                              onPress={toggleShopStatus}
                              activeOpacity={0.8}
                            >
                              <LinearGradient
                                colors={shop.isOpen ? [colors.error, '#FF6B6B'] : [colors.success, colors.secondary]}
                                style={dynamicStyles.toggleButtonGradient}
                              >
                                {shop.isOpen ? <EyeOff size={16} color={colors.background} /> : <Eye size={16} color={colors.background} />}
                                <Text style={dynamicStyles.toggleButtonText}>
                                  {shop.isOpen ? '关店' : '开店'}
                                </Text>
                              </LinearGradient>
                            </TouchableOpacity>
                          ) : shop.status === 'pending' ? (
                            <TouchableOpacity
                              style={dynamicStyles.approveButton}
                              onPress={handleApproveShop}
                              activeOpacity={0.8}
                            >
                              <LinearGradient
                                colors={[colors.success, colors.secondary]}
                                style={dynamicStyles.toggleButtonGradient}
                              >
                                <CheckCircle size={16} color={colors.background} />
                                <Text style={dynamicStyles.toggleButtonText}>模拟审核通过</Text>
                              </LinearGradient>
                            </TouchableOpacity>
                          ) : null}
                        </View>
                      </View>
                    </BlurView>
                  </Animated.View>

                  {/* Products Section */}
                  {shop.status === 'approved' && (
                    <Animated.View 
                      style={[
                        dynamicStyles.productsSection,
                        {
                          opacity: fadeAnim,
                          transform: [{ translateY: slideAnim }]
                        }
                      ]}
                    >
                      <Text style={dynamicStyles.sectionTitle}>商品管理</Text>
                      
                      <TouchableOpacity
                        style={dynamicStyles.addProductButton}
                        onPress={() => setShowProductModal(true)}
                        activeOpacity={0.8}
                      >
                        <LinearGradient
                          colors={[colors.accent, colors.warning]}
                          style={dynamicStyles.addProductGradient}
                        >
                          <Plus size={20} color={colors.background} />
                          <Text style={dynamicStyles.addProductText}>添加商品</Text>
                        </LinearGradient>
                      </TouchableOpacity>

                      {shop.products.map((product) => (
                        <BlurView key={product.id} intensity={30} style={dynamicStyles.productCard}>
                          <View style={dynamicStyles.productContent}>
                            <Image
                              source={{ uri: product.imageUrl }}
                              style={dynamicStyles.productImage}
                              resizeMode="cover"
                            />
                            <View style={dynamicStyles.productInfo}>
                              <Text style={dynamicStyles.productName}>{product.name}</Text>
                              <Text style={dynamicStyles.productDescription} numberOfLines={2}>
                                {product.description}
                              </Text>
                              <View style={dynamicStyles.productFooter}>
                                <View style={dynamicStyles.productPrice}>
                                  <Coins size={16} color={colors.warning} />
                                  <Text style={dynamicStyles.priceText}>{product.price}</Text>
                                </View>
                                <TouchableOpacity
                                  style={dynamicStyles.editButton}
                                  onPress={() => handleEditProduct(product)}
                                >
                                  <Edit3 size={20} color={colors.textSecondary} />
                                </TouchableOpacity>
                              </View>
                            </View>
                          </View>
                        </BlurView>
                      ))}
                    </Animated.View>
                  )}
                </>
              )}
            </ScrollView>

            {/* Shop Application Modal */}
            <Modal
              visible={showApplicationModal}
              animationType="slide"
              transparent={true}
              onRequestClose={() => setShowApplicationModal(false)}
            >
              <View style={dynamicStyles.modalOverlay}>
                <BlurView intensity={50} style={dynamicStyles.modalContainer}>
                  <ScrollView style={dynamicStyles.modalContent} showsVerticalScrollIndicator={false}>
                    <View style={dynamicStyles.modalHeader}>
                      <Text style={dynamicStyles.modalTitle}>商店申请</Text>
                      <TouchableOpacity
                        style={dynamicStyles.closeButton}
                        onPress={() => setShowApplicationModal(false)}
                      >
                        <X size={24} color={colors.text} />
                      </TouchableOpacity>
                    </View>

                    <View style={dynamicStyles.formContainer}>
                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>商店名称 *</Text>
                        <TextInput
                          style={dynamicStyles.textInput}
                          placeholder="输入您的商店名称"
                          placeholderTextColor={colors.textSecondary}
                          value={applicationForm.name}
                          onChangeText={(text) => setApplicationForm({...applicationForm, name: text})}
                        />
                      </View>

                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>商店描述 *</Text>
                        <TextInput
                          style={[dynamicStyles.textInput, dynamicStyles.textArea]}
                          placeholder="描述您的商店特色和经营范围"
                          placeholderTextColor={colors.textSecondary}
                          multiline
                          numberOfLines={4}
                          value={applicationForm.description}
                          onChangeText={(text) => setApplicationForm({...applicationForm, description: text})}
                        />
                      </View>

                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>商店地址 *</Text>
                        <TextInput
                          style={dynamicStyles.textInput}
                          placeholder="输入商店地址"
                          placeholderTextColor={colors.textSecondary}
                          value={applicationForm.address}
                          onChangeText={(text) => setApplicationForm({...applicationForm, address: text})}
                        />
                      </View>

                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>纬度 (可选)</Text>
                        <TextInput
                          style={dynamicStyles.textInput}
                          placeholder="54.2781"
                          placeholderTextColor={colors.textSecondary}
                          value={applicationForm.latitude}
                          onChangeText={(text) => setApplicationForm({...applicationForm, latitude: text})}
                          keyboardType="numeric"
                        />
                      </View>

                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>经度 (可选)</Text>
                        <TextInput
                          style={dynamicStyles.textInput}
                          placeholder="-0.4053"
                          placeholderTextColor={colors.textSecondary}
                          value={applicationForm.longitude}
                          onChangeText={(text) => setApplicationForm({...applicationForm, longitude: text})}
                          keyboardType="numeric"
                        />
                      </View>

                      <TouchableOpacity
                        style={dynamicStyles.saveButton}
                        onPress={handleShopApplication}
                      >
                        <LinearGradient
                          colors={[colors.secondary, colors.accent]}
                          style={dynamicStyles.saveButtonGradient}
                        >
                          <Save size={20} color={colors.background} />
                          <Text style={dynamicStyles.saveButtonText}>提交申请</Text>
                        </LinearGradient>
                      </TouchableOpacity>
                    </View>
                  </ScrollView>
                </BlurView>
              </View>
            </Modal>

            {/* Product Modal */}
            <Modal
              visible={showProductModal}
              animationType="slide"
              transparent={true}
              onRequestClose={() => setShowProductModal(false)}
            >
              <View style={dynamicStyles.modalOverlay}>
                <BlurView intensity={50} style={dynamicStyles.modalContainer}>
                  <ScrollView style={dynamicStyles.modalContent} showsVerticalScrollIndicator={false}>
                    <View style={dynamicStyles.modalHeader}>
                      <Text style={dynamicStyles.modalTitle}>
                        {editingProduct ? '编辑商品' : '添加商品'}
                      </Text>
                      <TouchableOpacity
                        style={dynamicStyles.closeButton}
                        onPress={() => {
                          setShowProductModal(false);
                          setEditingProduct(null);
                          setProductForm({ name: '', description: '', price: '', imageUrl: '', category: 'herbs' });
                        }}
                      >
                        <X size={24} color={colors.text} />
                      </TouchableOpacity>
                    </View>

                    <View style={dynamicStyles.formContainer}>
                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>商品名称 *</Text>
                        <TextInput
                          style={dynamicStyles.textInput}
                          placeholder="输入商品名称"
                          placeholderTextColor={colors.textSecondary}
                          value={productForm.name}
                          onChangeText={(text) => setProductForm({...productForm, name: text})}
                        />
                      </View>

                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>商品描述 *</Text>
                        <TextInput
                          style={[dynamicStyles.textInput, dynamicStyles.textArea]}
                          placeholder="描述商品的特点和用途"
                          placeholderTextColor={colors.textSecondary}
                          multiline
                          numberOfLines={3}
                          value={productForm.description}
                          onChangeText={(text) => setProductForm({...productForm, description: text})}
                        />
                      </View>

                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>价格 *</Text>
                        <TextInput
                          style={dynamicStyles.textInput}
                          placeholder="输入价格"
                          placeholderTextColor={colors.textSecondary}
                          value={productForm.price}
                          onChangeText={(text) => setProductForm({...productForm, price: text})}
                          keyboardType="numeric"
                        />
                      </View>

                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>图片链接</Text>
                        <TextInput
                          style={dynamicStyles.textInput}
                          placeholder="输入图片URL (可选)"
                          placeholderTextColor={colors.textSecondary}
                          value={productForm.imageUrl}
                          onChangeText={(text) => setProductForm({...productForm, imageUrl: text})}
                        />
                      </View>

                      <TouchableOpacity
                        style={dynamicStyles.saveButton}
                        onPress={editingProduct ? handleUpdateProduct : handleAddProduct}
                      >
                        <LinearGradient
                          colors={[colors.accent, colors.warning]}
                          style={dynamicStyles.saveButtonGradient}
                        >
                          <Save size={20} color={colors.background} />
                          <Text style={dynamicStyles.saveButtonText}>
                            {editingProduct ? '更新商品' : '添加商品'}
                          </Text>
                        </LinearGradient>
                      </TouchableOpacity>
                    </View>
                  </ScrollView>
                </BlurView>
              </View>
            </Modal>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

export default function ShopScreen() {
  return (
    <ProtectedRoute>
      <ShopContent />
    </ProtectedRoute>
  );
}