import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  SafeAreaView,
  Dimensions,
  Animated,
  Share,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  ArrowLeft,
  Edit3,
  Share2,
  Trash2,
  MapPin,
  Calendar,
  Heart,
  MessageCircle,
  MoreHorizontal,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { router, useLocalSearchParams } from 'expo-router';
import { DiaryEntry, MoodOptions, WeatherOptions } from '@/types/diary';
import { DiaryService } from '@/services/diaryService';

const { width, height } = Dimensions.get('window');

function DiaryDetailContent() {
  const { isDark } = useTheme();
  const { user } = useAuth();
  const colors = getColors(isDark);
  const { id } = useLocalSearchParams<{ id: string }>();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  const [entry, setEntry] = useState<DiaryEntry | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadEntry();
    }
    
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, [id]);

  const loadEntry = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const { data, error } = await DiaryService.getDiaryEntry(id);
      if (error) {
        Alert.alert('错误', '加载日记失败');
        router.back();
      } else {
        setEntry(data);
      }
    } catch (error) {
      Alert.alert('错误', '加载日记失败');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/diary/edit/${id}`);
  };

  const handleDelete = () => {
    Alert.alert(
      '确认删除',
      '确定要删除这篇日记吗？此操作无法撤销。',
      [
        { text: '取消', style: 'cancel' },
        { text: '删除', style: 'destructive', onPress: confirmDelete },
      ]
    );
  };

  const confirmDelete = async () => {
    if (!id) return;
    
    try {
      const { error } = await DiaryService.deleteDiaryEntry(id);
      if (error) {
        Alert.alert('错误', '删除失败，请重试');
      } else {
        Alert.alert('成功', '日记已删除', [
          { text: '确定', onPress: () => router.back() }
        ]);
      }
    } catch (error) {
      Alert.alert('错误', '删除失败，请重试');
    }
  };

  const handleShare = async () => {
    if (!entry) return;
    
    try {
      await Share.share({
        message: `${entry.title}\n\n${entry.content}\n\n来自斯卡布罗集市日记`,
        title: entry.title,
      });
    } catch (error) {
      console.error('分享失败:', error);
    }
  };

  if (loading || !entry) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
        <Text style={{ color: colors.text }}>加载中...</Text>
      </View>
    );
  }

  const mood = MoodOptions[entry.mood];
  const weather = WeatherOptions[entry.weather];

  const dynamicStyles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'space-between' as const,
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    headerActions: {
      flexDirection: 'row' as const,
      gap: 12,
    },
    actionButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    contentContainer: {
      padding: 20,
    },
    titleContainer: {
      marginBottom: 20,
    },
    title: {
      fontSize: 28,
      fontWeight: '700' as const,
      color: colors.text,
      marginBottom: 12,
      lineHeight: 36,
    },
    metaContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      flexWrap: 'wrap' as const,
      gap: 16,
    },
    metaItem: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      gap: 6,
    },
    metaText: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    emoji: {
      fontSize: 16,
    },
    contentCard: {
      borderRadius: 16,
      overflow: 'hidden' as const,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: 20,
    },
    content: {
      padding: 20,
    },
    contentText: {
      fontSize: 18,
      color: colors.text,
      lineHeight: 28,
    },
    locationContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginBottom: 16,
      padding: 16,
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 12,
    },
    locationText: {
      fontSize: 16,
      color: colors.text,
      marginLeft: 8,
      fontWeight: '600' as const,
    },
    tagsContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 8,
      marginBottom: 20,
    },
    tag: {
      backgroundColor: colors.primary + '20',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    tagText: {
      fontSize: 14,
      color: colors.primary,
      fontWeight: '600' as const,
    },
    imagesContainer: {
      marginBottom: 20,
    },
    imagesGrid: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 12,
    },
    imageItem: {
      width: (width - 64) / 2,
      height: (width - 64) / 2,
      borderRadius: 12,
      overflow: 'hidden' as const,
    },
    image: {
      width: '100%',
      height: '100%',
    },
    singleImage: {
      width: width - 40,
      height: 240,
    },
    actionsContainer: {
      flexDirection: 'row' as const,
      justifyContent: 'space-around' as const,
      paddingVertical: 20,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    actionItem: {
      alignItems: 'center' as const,
      gap: 8,
    },
    actionText: {
      fontSize: 14,
      color: colors.textSecondary,
    },
  };

  return (
    <View style={dynamicStyles.container}>
      <LinearGradient
        colors={[colors.backgroundSecondary, colors.backgroundTertiary]}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          {/* Header */}
          <Animated.View style={[dynamicStyles.header, { opacity: fadeAnim }]}>
            <TouchableOpacity
              style={dynamicStyles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={20} color={colors.text} />
            </TouchableOpacity>
            
            <View style={dynamicStyles.headerActions}>
              <TouchableOpacity
                style={dynamicStyles.actionButton}
                onPress={handleShare}
              >
                <Share2 size={20} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity
                style={dynamicStyles.actionButton}
                onPress={handleEdit}
              >
                <Edit3 size={20} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity
                style={dynamicStyles.actionButton}
                onPress={handleDelete}
              >
                <Trash2 size={20} color={colors.error} />
              </TouchableOpacity>
            </View>
          </Animated.View>

          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={dynamicStyles.contentContainer}
            showsVerticalScrollIndicator={false}
          >
            <Animated.View
              style={{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }}
            >
              {/* 标题和元信息 */}
              <View style={dynamicStyles.titleContainer}>
                <Text style={dynamicStyles.title}>{entry.title}</Text>
                <View style={dynamicStyles.metaContainer}>
                  <View style={dynamicStyles.metaItem}>
                    <Calendar size={16} color={colors.textSecondary} />
                    <Text style={dynamicStyles.metaText}>
                      {new Date(entry.created_at).toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}
                    </Text>
                  </View>
                  <View style={dynamicStyles.metaItem}>
                    <Text style={dynamicStyles.emoji}>{mood?.emoji}</Text>
                    <Text style={dynamicStyles.metaText}>{mood?.label}</Text>
                  </View>
                  <View style={dynamicStyles.metaItem}>
                    <Text style={dynamicStyles.emoji}>{weather?.emoji}</Text>
                    <Text style={dynamicStyles.metaText}>{weather?.label}</Text>
                  </View>
                </View>
              </View>

              {/* 地点 */}
              {entry.location && (
                <View style={dynamicStyles.locationContainer}>
                  <MapPin size={20} color={colors.primary} />
                  <Text style={dynamicStyles.locationText}>{entry.location}</Text>
                </View>
              )}

              {/* 内容 */}
              <BlurView intensity={30} style={dynamicStyles.contentCard}>
                <View style={dynamicStyles.content}>
                  <Text style={dynamicStyles.contentText}>{entry.content}</Text>
                </View>
              </BlurView>

              {/* 标签 */}
              {entry.tags.length > 0 && (
                <View style={dynamicStyles.tagsContainer}>
                  {entry.tags.map((tag, index) => (
                    <View key={index} style={dynamicStyles.tag}>
                      <Text style={dynamicStyles.tagText}>#{tag}</Text>
                    </View>
                  ))}
                </View>
              )}

              {/* 图片 */}
              {entry.images.length > 0 && (
                <View style={dynamicStyles.imagesContainer}>
                  <View style={dynamicStyles.imagesGrid}>
                    {entry.images.map((image, index) => (
                      <View
                        key={index}
                        style={[
                          dynamicStyles.imageItem,
                          entry.images.length === 1 && dynamicStyles.singleImage,
                        ]}
                      >
                        <Image source={{ uri: image }} style={dynamicStyles.image} />
                      </View>
                    ))}
                  </View>
                </View>
              )}

              {/* 操作按钮 */}
              <BlurView intensity={30} style={dynamicStyles.contentCard}>
                <View style={dynamicStyles.actionsContainer}>
                  <TouchableOpacity style={dynamicStyles.actionItem}>
                    <Heart size={24} color={colors.textSecondary} />
                    <Text style={dynamicStyles.actionText}>喜欢</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={dynamicStyles.actionItem}>
                    <MessageCircle size={24} color={colors.textSecondary} />
                    <Text style={dynamicStyles.actionText}>评论</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={dynamicStyles.actionItem} onPress={handleShare}>
                    <Share2 size={24} color={colors.textSecondary} />
                    <Text style={dynamicStyles.actionText}>分享</Text>
                  </TouchableOpacity>
                </View>
              </BlurView>
            </Animated.View>
          </ScrollView>
        </SafeAreaView>
      </LinearGradient>
    </View>
  );
}

export default function DiaryDetailScreen() {
  return (
    <ProtectedRoute>
      <DiaryDetailContent />
    </ProtectedRoute>
  );
}
