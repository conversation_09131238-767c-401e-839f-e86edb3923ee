{"_id": "agent-base", "_rev": "38-9932c01841710ea2f7cdc0ee1022af74", "name": "agent-base", "dist-tags": {"latest": "7.1.3"}, "versions": {"0.0.1": {"name": "agent-base", "version": "0.0.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@0.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "6821bd0b228447562378e3560a923ace3eedc3c5", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-0.0.1.tgz", "integrity": "sha512-lWxlsr/w2jOOeN2GsYsZdhXjyvWZ6waQRFhqIoxQbrGICUzK9SdSiyjSnX1YMMF4ueoJwwmp+0CwQ5o/4k+JhA==", "signatures": [{"sig": "MEYCIQDt/msj6RLGHoKAIDqtQ1LZdYhMSmBzLFVkTR4P/HIaQgIhAPlf7cX4FuiKSwMr6q+qZeM3JjlHKCOUU3q4a1BPjJYj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "Barebone `http.Agent` implementation", "directories": {}, "devDependencies": {"mocha": "~1.12.0"}}, "1.0.0": {"name": "agent-base", "version": "1.0.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@1.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "e4e0b974fe9d250340d3f7b7aaa48284076b2f8b", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-1.0.0.tgz", "integrity": "sha512-JQT6IZTOMohZ86n0YDDoSTSAaD2K8VbSnsxTpHm6jSxNqi011Ht6/9gLBgjK/jxv4X3dLsBqEejhR1keGNkAiQ==", "signatures": [{"sig": "MEUCIBc5keEWk7LlO/a636uxdpilYSEalUcKEHQdVTYFGoZRAiEAtpL++xFIxdyptMf/Ad4R8E0zo/qldu6NsV67WNT7Yi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "devDependencies": {"mocha": "~1.12.0"}}, "1.0.1": {"name": "agent-base", "version": "1.0.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@1.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "806dbee16f2f27506730e2eb78f537192706ccc3", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-1.0.1.tgz", "integrity": "sha512-1cEV+azwttRTWAxkcCiqUiVyGxfOTIahKuHbHMfMQtQTc+QGXRKK7Ls0JUMat92Tdvow+TAXbT35/0s5Lk91zA==", "signatures": [{"sig": "MEYCIQD0jqpqq6tJgRXUx0Vn7raiK1PuAaaj5mUWuS0iET+xuwIhAL7SGkQgPzZnth4DMc5+dZpQh3iSP63fVs+Hg13F2njL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "agent.js", "_from": ".", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "devDependencies": {"mocha": "~1.12.0"}}, "1.0.2": {"name": "agent-base", "version": "1.0.2", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@1.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "6890d3fb217004b62b70f8928e0fae5f8952a706", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-1.0.2.tgz", "integrity": "sha512-IrdRInle5l28T2DjBsOojXniN91mXYkt9piDyPbPEoA/X+f7kjd0qiIb18vZThIZCJdLk2Zq/ukXxZp8NkcFsw==", "signatures": [{"sig": "MEQCIGxD6zM9DbYbRH9CxWQRZFoCqazgKFrK+5ifiHxyjhPnAiAIZuRRUcNUsJ99Vwem7vV6OsvgS9U/0pi/YRIih7OvgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "agent.js", "_from": ".", "_shasum": "6890d3fb217004b62b70f8928e0fae5f8952a706", "gitHead": "7be263ca09bc9b0f78384bb248006fe01fcbe21a", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"mocha": "2"}}, "2.0.0": {"name": "agent-base", "version": "2.0.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@2.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "1120e1f8efed7a6b2fe60ea60ea4a52a9d5c80e1", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-2.0.0.tgz", "integrity": "sha512-SQHLdjIjfq64aWzOtHF4hNSsaqmCirZ7xWvw5qQPFCPeBDksBxGhxhJwo0MqiiB+hN9/043+svfdbdYJimJ09g==", "signatures": [{"sig": "MEUCIH4bU6wQKlrHTMm+eZhiG9/V5IvWmljsqtnENNDc9o7NAiEA+QEgP8w0/4DvDwnxAYjCNOLx2Hz/VIkalOsZt9nG2Zk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "agent.js", "_from": ".", "_shasum": "1120e1f8efed7a6b2fe60ea60ea4a52a9d5c80e1", "gitHead": "5598d7a64b59479135172670b91cec013b8b4037", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "2.11.2", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "0.12.6", "dependencies": {"extend": "~3.0.0", "semver": "~4.3.6"}, "devDependencies": {"mocha": "2"}}, "2.0.1": {"name": "agent-base", "version": "2.0.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@2.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "bd8f9e86a8eb221fffa07bd14befd55df142815e", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-2.0.1.tgz", "integrity": "sha512-9FEVRFHQZjAD2eP+9nBfnOTT3ts3BnnkqAR+szAPWd9Blque8VmlyoyLsEahb/rvFRb4nWCIFBrguF2amz53FQ==", "signatures": [{"sig": "MEUCIAXH0FoaLIsaUzqyG3oi0QYOj4IGm+SP3PFH4SFcUHV4AiEAq/tZV9Mkz12UaElML9RgmNOpx80IhZCmNR0jPoeCZMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "agent.js", "_from": ".", "_shasum": "bd8f9e86a8eb221fffa07bd14befd55df142815e", "gitHead": "b46938339bcecd261939dc55798270d0398ad8f0", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"extend": "~3.0.0", "semver": "~5.0.1"}, "devDependencies": {"mocha": "2"}}, "2.1.0": {"name": "agent-base", "version": "2.1.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@2.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "193455e4347bca6b05847cb81e939bb325446da8", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-2.1.0.tgz", "integrity": "sha512-pV5UClQPXnjQ5WOnMYYr6PKp9yuuNBhVKedxqIuW1e4MHVZwVxCx44y5CzdPB9vdZ/PBaMa3ioOhJsw9nlVoaw==", "signatures": [{"sig": "MEMCIB/64BiGKD2mR0wecj09BA6kEo7zmMmatWtRg9CVgkt6Ah8znYDnodtVIhvYVXD7edFMBMbYeLFhG0yoSAlCHlZv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "agent.js", "_from": ".", "_shasum": "193455e4347bca6b05847cb81e939bb325446da8", "gitHead": "5b981ee88def6dee042c10bbfdc02585f2af2faf", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"extend": "~3.0.0", "semver": "~5.0.1"}, "devDependencies": {"ws": "0.8.0", "mocha": "2"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base-2.1.0.tgz_1495816641892_0.3964533843100071", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "agent-base", "version": "2.1.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@2.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "d6de10d5af6132d5bd692427d46fc538539094c7", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-2.1.1.tgz", "integrity": "sha512-oDtZV740o3fr5oJtPLOsgH2hl2TRPscNXIx4VzzBwVlXVkv8RHm7XXqGAYg8t20+Gwu6LNDnx8HRMGqVGPZ8Vw==", "signatures": [{"sig": "MEUCIHBi+0U1rh7rDblRWvs/hT6GRkUlfe1EVsWf1gHS2+WfAiEA45ARzuSheCWJ7e1JLFxZndWgByDHu59592pLOmidEC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "agent.js", "_from": ".", "_shasum": "d6de10d5af6132d5bd692427d46fc538539094c7", "gitHead": "b6eecacecb3708181992c31bf7e6fcc96bbedc06", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"extend": "~3.0.0", "semver": "~5.0.1"}, "devDependencies": {"ws": "0.8.0", "mocha": "2"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base-2.1.1.tgz_1496182121313_0.05445550032891333", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "agent-base", "version": "3.0.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@3.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "fae2fa0429e960af7885d4c1278a0084412cddaa", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-3.0.0.tgz", "integrity": "sha512-M9K9N6u3MdyZ4b46CTYaANA5P1vmjW+Hay6gvleP8RH3Kk1qO6ClrqRgUGTHjUx9VhQVz1odhRy6fYCoIQc9wA==", "signatures": [{"sig": "MEUCIQDL2z1w7bDrYThcLr5KgCO/BAnOLbtfJKwShiDiMikzYQIgQy61RdWUpiCyo/9KPKvoU00YntTjMBGPY+Zegs24eAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 0.12.0"}, "gitHead": "9fa40bab2f665e0688cd0e4155f579644ebd9114", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"extend": "~3.0.0", "semver": "~5.0.1", "es6-promisify": "^5.0.0"}, "devDependencies": {"ws": "0.8.0", "mocha": "3"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base-3.0.0.tgz_1496439258689_0.737877310719341", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "agent-base", "version": "4.0.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@4.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "8c318459c1eae6561396e7bfed70b27b13e8957a", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-4.0.0.tgz", "integrity": "sha512-ccZqgUwkDCqy4vA9D9H5zFTjvEqgOZ/+A240u01qkQDMK2g+iv/U2TVkVIMqzHrqizbUA+dGVak282PR/Tbckw==", "signatures": [{"sig": "MEUCICeMEp0SiAlQyAVyztGceBRJGLgbMk3pAmfKkHz/o1IPAiEAw74O8wQKXc8dwc0xiz5QKEdET4gWSH12URoRw13E9eI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "757cc8a6c589775c0af4e6c50c20603f81480362", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"es6-promisify": "^5.0.0"}, "devDependencies": {"ws": "0.8.0", "mocha": "3"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base-4.0.0.tgz_1496791950846_0.8519535600207746", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "agent-base", "version": "4.0.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@4.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "b478185cc6774fdc8c4f70ee8caadf856afd1b34", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-4.0.1.tgz", "integrity": "sha512-VA96h2SVV7dBuKed5kvWTdpY6HVcBwIjiOZquseX/3xVg23GbUHUSuiJCN0+8KBjdhyJEkO5oBWyXhekUiMPdw==", "signatures": [{"sig": "MEUCIQCWr34cZpLJ5JwohJ5uS8gg7gExAW36vdYkuwBMe+M4sQIgXc8VI1H3N6lICYJ0FL/Vw37IXRkQYWTMAsBxKfF1Th0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "c4b3bb7381eadf04272d684c258c4b337e9675f4", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"es6-promisify": "^5.0.0"}, "devDependencies": {"ws": "^3.0.0", "mocha": "^3.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base-4.0.1.tgz_1497385225896_0.1452008062042296", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "agent-base", "version": "4.1.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@4.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "20e17401cd49b3c076bf56a4bc6c5b436ffa8d55", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-4.1.0.tgz", "integrity": "sha512-BzHx9oIyF/K2RsLeWgi+C55/GZU2R9YoREachgWOHPVSJloKuCw9n9Aqlhz5bOe16UgWwoRBK/yQp4hERFwyKw==", "signatures": [{"sig": "MEUCIHJ07V8o5IvPDgFBlaZ6MaI3GpWkCbjtF3bKi5uy7P+JAiEAs+ygR9lSw/Q/5DrwVeR2sQEqWKuGAcvyxJ9a57Hi0Yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "_from": ".", "_shasum": "20e17401cd49b3c076bf56a4bc6c5b436ffa8d55", "engines": {"node": ">= 4.0.0"}, "gitHead": "6df3dba945c63d5b57a09b9b559942bd8e2b6946", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"es6-promisify": "^5.0.0"}, "devDependencies": {"ws": "^3.0.0", "mocha": "^3.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base-4.1.0.tgz_1498522512817_0.28704919456504285", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "agent-base", "version": "4.1.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@4.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "92d8a4fc2524a3b09b3666a33b6c97960f23d6a4", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-4.1.1.tgz", "integrity": "sha512-yWGUUmCZD/33IRjG2It94PzixT8lX+47Uq8fjmd0cgQWITCMrJuXFaVIMnGDmDnZGGKAGdwTx8UGeU8lMR2urA==", "signatures": [{"sig": "MEUCIQDlpkt/YTyW2wdZO4YTrNPKMWi1QMzZ32xQjIjNNuU8FQIgfFHt7xmVgsbgRaWgkfxqzdC0Xzd/gorhu+j3PSVrwxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "e66f64cb58f2132d390711ce2dda8c05825cfecc", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "8.1.2", "dependencies": {"es6-promisify": "^5.0.0"}, "devDependencies": {"ws": "^3.0.0", "mocha": "^3.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base-4.1.1.tgz_1500600507309_0.9737169749569148", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "agent-base", "version": "4.1.2", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@4.1.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "80fa6cde440f4dcf9af2617cf246099b5d99f0c8", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-4.1.2.tgz", "integrity": "sha512-VE6QoEdaugY86BohRtfGmTDabxdU5sCKOkbcPA6PXKJsRzEi/7A3RCTxJal1ft/4qSfPht5/iQLhMh/wzSkkNw==", "signatures": [{"sig": "MEQCIFP+zvGZ//Tk4NSx93dl3c21I388ZBQgS+6/rVW5unYGAiB2kyrS670wHTlhm59OJkgsphlIAKA1HWHHeZqbLVNfrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "1b3c8c9bf228ee4f3fe085a454e4912cee55b739", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "9.2.0", "dependencies": {"es6-promisify": "^5.0.0"}, "devDependencies": {"ws": "^3.0.0", "mocha": "^3.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base-4.1.2.tgz_1511200549900_0.9417378406506032", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "agent-base", "version": "4.2.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@4.2.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "9838b5c3392b962bad031e6a4c5e1024abec45ce", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-4.2.0.tgz", "integrity": "sha512-c+R/U5X+2zz2+UCrCFv6odQzJdoqI+YecuhnAJLa1zYaMc13zPfwMwZrr91Pd1DYNo/yPRbiM4WVf9whgwFsIg==", "signatures": [{"sig": "MEQCIGjsuvDouDcO6k4nstdEsJ9htt5ITm43VmoFPu7g10JwAiAA33WPpmc8214wtePT3SlLc4ewIjNRJWgnf/gc4JpDjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./index.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "35b49daefc0e0cb165dd1b235d8d125413fc4dfe", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"es6-promisify": "^5.0.0"}, "devDependencies": {"ws": "^3.0.0", "mocha": "^3.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base-4.2.0.tgz_1516059967997_0.4326384493615478", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "agent-base", "version": "4.2.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@4.2.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "d89e5999f797875674c07d87f260fc41e83e8ca9", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-4.2.1.tgz", "fileCount": 9, "integrity": "sha512-JVwXMr9nHYTUXsBFKUqhJwvlcYU/blreOEUkhNR2eXZIvwd+c+o5V4MgDPKWnMS/56awN3TRzIP+KoPn+roQtg==", "signatures": [{"sig": "MEYCIQCsY26F/rKqiaueSwcjwYQcWKguEcI8I4a6lK+l8QMTyQIhAIVwmQJzLW2MXL86joHZuKy2UbDtG2OazaZtcCqLoPtQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPoNuCRA9TVsSAnZWagAADkAP/jVkd+qSiDjZzESvBxvj\nkFIQ2QAjQ/NtrZFhth5Nw9ZHVKtr3EcTpIVNqCnVKZnVJ31gSIpeN/87nSTw\niFRmPrzUZQaq08y0JUVlRUQUdbj4NZCoL2BVbYn50YC+rDR1jbB5DpAdZjYy\n7TlxHdhKz92Mpdg120lmP7z6hN7aBDXti5dZKGj+Nr+sa4w2J6qxAJDs0Uo8\nzJNNRy7BfOWMNjRZ76nP58i4ueejpfdbSJ1rJW/s4/x+O9W3zb2zdVGVm1aU\nEFIsTK81O6hz65S90HqrMNQPccoNf/rs7RuxyK8PIuBllwvivAmQACt4eyl8\n9VYpTghhKZCy2o22NUhkuCE8nACszRuG2U/hPpBjXatcXCbtJc3k8GraAD/M\neDiv2wE0fSXdQGS1E4A2mOJxYOXd3aavASNjl0z6GcYSphrKLMMsUJTOLZUy\nZq0mGxYm4YPZjbSf0uRIRbhR5OoR3GQuwuGvGzUH0K0UWXSiAq6Bwq/mngjL\nBndx4jPYtHIT0dgAwBcftwmaQ/EIfN6sSBTwPw5eGxWcbM+KO8SxqOT8puYw\nDo30IjtAnyu5Hemrja4krzZNbu37/TUvOkk6USiBTAz4EnTDzms2KhEs6mqm\nnORFN/RReht4/clrYz9+sKAgqSIplYp5eqFXD16/snxNvdJ0/C1Ip2LL/NIB\ncl34\r\n=eyWQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "7ea2dde4c21f2f5cfe071e99335d35b9c0a1403e", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"es6-promisify": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.0.0", "mocha": "^3.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_4.2.1_1530823534659_0.9208504260347914", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "agent-base", "version": "4.3.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@4.3.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "8165f01c436009bccad0b1d122f05ed770efc6ee", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-4.3.0.tgz", "fileCount": 10, "integrity": "sha512-salcGninV0nPrwpGNn4VTXBb1SOuXQBiqbrNXoeizJsHrsL6ERFM2Ne3JUSBWRE6aeNJI2ROP/WEEIDUiDe3cg==", "signatures": [{"sig": "MEQCICODKqOZW0ZAdDJOenTuYIXQKUUlUIvFHQRDrL9BgjpIAiBBKv5rsJpLAXKba0ge2HFI3gVmZSQYzMaxsyD67C6gvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37456, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+CNNCRA9TVsSAnZWagAATykP/jACri9CPvfpESr+U//k\nP9WghQ7B6034z+DFn8MHJRkJca8eo1vC89gxc0kAxX747jIwehSbI6iL5cGh\n91YNmvDXBOvoN6v0LBM6HGaug3qUIVBYBYrAD32awzMXpqOx7QG8ZF+kssID\nPFSgAuwQqsYVxWM+K7KbTWwZT+SRLTAxK+/n2+RjpdPj/06Un6ZzRxMZcGLB\njMyQbgcjmi47WywScu7DvGppw55LwmtO8C6pw9ppM2EhJR4e4vPaJHpxyRoS\n8xEFnWuC3JTl5Vl8ESBSHmjtez7M/FKJMlGyYweJiYpEX98gvo8IDoMNz1+s\n4JhBog80hcS9/N/R/SVb9Y+fRB9QSPeXeyAHc8mmoWqDrqomxaXR3nT+choK\ne8jVhTQwN1SEaG/NwmApzIBnWnnKiJQx61uw8kQ6x/lYMDs7bON47I2ZWtSD\nVn7+5CZQDdNcINfOZoBICuFI+Fcj9+WUAO0kHhYtgySLj8OvZnEtxERJsbBd\nO+2wpMvJ3h74rVcs2sRVmy0bAyqxVTzHgyHrxRgq0aWjQh9hwGe40Kqz41eD\naxl12qLG/E88asD9Yc9zi1rgw6JXDDdQtC371Gw6yzB0D5vqSQ5Anidqu9ez\nkchYbnciPp8JcpeWMakfkMz8rZolWxwtqkhC2RBSO0zk1b48IlTbCBhek42b\nmXAz\r\n=M/FZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./index.js", "engines": {"node": ">= 4.0.0"}, "gitHead": "560f111674af84dec46a4c3070ddf3b22edd3e76", "scripts": {"test": "mocha --reporter spec"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"es6-promisify": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.0.0", "mocha": "^3.4.2", "@types/node": "^10.5.3", "@types/es6-promisify": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_4.3.0_1559765836773_0.2712931340824465", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "agent-base", "version": "5.0.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@5.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "9b1fbf3e74b43f3cf42691d855f46886d7f80a60", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-bx4LotUmXlmHABuGlTIwhcVP5U8FDqFVNGzAscDDIDf+2jfpTt219tj0XigIa9K8z1zuPCYjU6emS9YW/yExBQ==", "signatures": [{"sig": "MEUCIQDyVDK8ipXXSoMOYc7XaAuYh0QcozhV5rtOJ0dExFxLZgIgI8wVAokCvs0jLT4fVSBF+UhmjrGujWsjVMF6QaCgN+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7/S7CRA9TVsSAnZWagAAkjIP+wca+zKmbs0jVDo1FKzp\nPIzB1yciZRQJ1SMFRFYmEeG3+7MBM2jmQo1XW/4F4wZLocZLnUpCGwiA++SH\nuu3RDPX5cJ4edhleRTUvmkyPYUxuO/FrowW/HoBzjG7C5Aqr6MsTJjhh9i4F\n+rxH8dCTkYVsZ+Jlqpw/62ssB0qhIkDbZ58jyPYHbDRd8RRcSSuvLp+0IQy0\nrlMff7bWcGDpf5xDy/bHvtTNgvbUxPcTSeC09xYgyb8TAHOYor2RWnpZKmW8\nkneVCeNToj2OkC7HB4CBk0ZpO/u221JEW0GAwVt3O3Fx9LQSuW1llJ6DvIfg\nOxE1yA93O1ks1cBgXJmE/uBagF+rpuAbJvucYV+xkBWsc27pghTzAj26xLn7\nt7yxg0nEMprwTo0HfZr3iLgZCkPSiWBvfTw2cGe5jOmjHyebDWQBRgchkckT\nwcTVFMbKMwiMKEDnC69jPr3TThDkh8taDNrer2pKuQVIwFMJicIZGkW69cef\nXpZtcqIRFkbx6fTuvlCPfv9+P7AISQ4qZkImVeJnGd1643pZMAzVx6rmAUSp\n3sRvp0W4OyzYeatF0O1hJQVAxaW6ORsd1jvsCItqMaGc3mPhUU2GPZ4Pgjni\nJSf/SYI4HKFGHNKWUON0cAHIvl7Xv45Mwhar0bC4pXELZP0rLqPUD70XoN/H\nd4mX\r\n=eEgY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/src/index", "engines": {"node": ">= 6.0.0"}, "gitHead": "512557bf07f6088594b8deb3315a4f934c81af47", "scripts": {"test": "mocha --reporter spec dist/test/*.js", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/src/index", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"es6-promisify": "^5.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.0.0", "mocha": "^6.2.0", "eslint": "5.16.0", "rimraf": "^3.0.0", "cpy-cli": "^2.0.0", "@types/ws": "^6.0.3", "typescript": "^3.5.3", "@types/node": "^10.5.3", "@types/mocha": "^5.2.7", "async-listen": "^1.2.0", "eslint-plugin-react": "7.12.4", "@types/es6-promisify": "^5.0.0", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_5.0.0_1576006842669_0.3213814953446261", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "agent-base", "version": "5.1.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@5.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "57a72a28613bcd5d2935a13dc7bd049c403ceb75", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-5.1.0.tgz", "fileCount": 8, "integrity": "sha512-7Fpt67pAkCNkDZZOKDKJJWPVaq2qCiDgU0n0abvGPWr3IiSuCrPtLrDPqUKWsGoUxQ0Lh7AEZ7VWZxgDyZ6uRA==", "signatures": [{"sig": "MEUCIQCerAUPxw/8iPwGfd1h1Se8cXZMpcqDbQotoPOtx6z5iQIgAit6ee7B7yC6u6JJW6El+7xkOrM7H/59CT9Zyp7WZI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8Gk5CRA9TVsSAnZWagAAfB8P/i0IcehImv9R9UNxBCCf\nzR1h9jAJux4FveNnLrAupE3uCk4rxd9sKakpk49sk/v1/kxlxYuldHm8wyl2\nw9D1OXy11tkrvWIm3ylEbVb1dOiEmeg5ku1HMP2obJCuOw/CKY+5oXtADl9t\nEH21Xh7bKrEq8kUj+PfdoqFFNaDDg7MoPJUK22VYPiDYJQh9LAFB0HARIqOl\ntDlcpByBEbHmFGJgqcTO9sbPy8p+sUPskm+R+AM0wH/BBm6RjpDZnbWCk3+i\nRXEzNyrQ3dv8vWKFIdpTloc73PNBVDy/RJRalxqIY2NKKlbidQi7E/yKrhgL\nac482IuFUbJ4cKZZSz0LQxC1voBIQeFlUwO3goiUwzt9yysl3j1cd5X9xxwc\n3t+0QQFT5eEU3HdZGOAZQ8wf0U+FAMiguibqHJnAZcl7BV7fgmGBfFsHOJmN\nL9BJOHnIH5q4K62zij2sUV1jHMcwOqX8N/1FHWtU2+0Mrl4tAFPry7CBQobr\nMeyKbUifCiGg+IplMA8GNQOgR9BtHkRFnZNlDpjHf0KvikCL6LJSmV2oBWg0\naT/cpgNdMnFIgzVamx03uY8Q6HrABxzvy4k288FeCaLaHS3l5WnxYjMnZpGJ\n4IU4NKG3ObGNQTuFSKgtmoI+6dnhuiPLGObGg/vBmPKBkvfq7xaeQ7WJHWxX\nDaml\r\n=6EDX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/src/index", "engines": {"node": ">= 6.0.0"}, "gitHead": "3d122284f21c59917946458e56086f78ecc5add5", "scripts": {"test": "mocha --reporter spec dist/test/*.js", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/src/index", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "8.16.2", "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.0.0", "mocha": "^6.2.0", "eslint": "5.16.0", "rimraf": "^3.0.0", "cpy-cli": "^2.0.0", "@types/ws": "^6.0.3", "typescript": "^3.5.3", "@types/node": "^10.5.3", "@types/mocha": "^5.2.7", "async-listen": "^1.2.0", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_5.1.0_1576036665194_0.28542412483549007", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "agent-base", "version": "5.1.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@5.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "e8fb3f242959db44d63be665db7a8e739537a32c", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-5.1.1.tgz", "fileCount": 8, "integrity": "sha512-TMeqbNl2fMW0nMjTEPOwe3J/PRFP4vqeoNuQMG0HlMrtm5QxKqdvAkZ1pRBQ/ulIyDD5Yq0nJ7YbdD8ey0TO3g==", "signatures": [{"sig": "MEYCIQDQjFNj0Sk/Cxo6jwhaI7ZZALcapEPRMp2iHpiT2T3m7QIhAIUbjd0M0b2dhsu0jYahrHvXNPC07u90fXpOLuUVMGOX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8HudCRA9TVsSAnZWagAAFtwQAJbUWcPzZ2TH8I4hkAJ9\nV/dYfcEr+Ac1NHDLQGsvqFwCUM2agAJGie/eYtCXIM4YldJtR/PMHoIFyUg7\nQ+craNE0QJcW+H+9VYUvy1LDGDiQUiEol1G0twzR7kf0yVTrp+Dnqa+RiE7n\nc5EycFDxdsi3op/ce5beKTmoMv95aMfOipQN9dE9GvW890CdCiQIFemi+mTA\nLJr4/9mA63IoPk36OU010W1ldM+uOfzDx06zy6vIc/sOiLuRATICYjlxWUCV\nTG4v/YO/BVYPJhLlFuApBm0hKMBOAd9EOeYIWWW9Rhxi1cc2Zm8ksoC00d/W\nGjq1aRCIljjL+hOhTrD35Icymtx6fNMVTxwmvonZoQkj/BpKDhREZnq2Fa1z\nBZrlNfjTngBY0c6GY1vvp++2mcuYQRBgRZrRxv73eZs8WvXJSm8iJNnAdclN\nc6eC4rD5ZMRfQedyJnQ/5y8p1SRmhnIvDTIa2QXO1jOmERzENMNORhwKKaV4\n1XljKt6XZ50FGFVE3B/SpiBW6cCGG0oGiGGw0O0UqmSIT5CCICZFLJIbkvKN\n3gnk5TulzAHWyw+DQfFZMOxx6DCS5H9B2oJQSMmVqGsh9xTnyeCxKQ079fwY\nkyqVm64NV8nXDDp2GdkIIsTEaI3fAJZmqDW6ahuJyqGH7XLZqfiL61eBMAlT\n48Ep\r\n=z976\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/src/index", "engines": {"node": ">= 6.0.0"}, "gitHead": "dc309f8a5dbfc9cbc7a860118324759f7ab0818c", "scripts": {"test": "mocha --reporter spec dist/test/*.js", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/src/index", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "8.16.2", "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.0.0", "mocha": "^6.2.0", "eslint": "5.16.0", "rimraf": "^3.0.0", "cpy-cli": "^2.0.0", "@types/ws": "^6.0.3", "typescript": "^3.5.3", "@types/node": "^10.5.3", "@types/mocha": "^5.2.7", "async-listen": "^1.2.0", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_5.1.1_1576041372880_0.4870527925173762", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "agent-base", "version": "6.0.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@6.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "5d0101f19bbfaed39980b22ae866de153b93f09a", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.0.tgz", "fileCount": 8, "integrity": "sha512-j1Q7cSCqN+AwrmDd+pzgqc0/NpC655x2bUf5ZjRIO77DcNBFmh+OgRNzF6OKdCC9RSCb19fGd99+bhXFdkRNqw==", "signatures": [{"sig": "MEYCIQCl4Vhn1VN5ayeKRd4hUHIe/Pe9nijQa9+qxkRazhDAEwIhAKPUctL3PBRuG82aKEAjrDrYh893to/0wRVeS7xALB50", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeKgIYCRA9TVsSAnZWagAAsLoP/is4+bg5etzrkAURpdaV\n9Zf4M8wTo+FueSpeSZUuMGUjTLERqxPEPm+KFOZO2qNICWSlOXnFhhjspDuv\nrx2OR4O+A7y86xEwdP8o/zwcxqHiZFqnS1vv4VOR3mjtkTrtswY2anX0hGF/\nYyeitnZOVELsLrSBMpifjfXP7tmd+KD/gLUQG3wymFq8imSu+XxIa+Te4rj0\niCBAvCCbJBkval6KYpJdHr10+QzBqMQY7gsQdNGplyzUFK+XPJK5jxWuCs5e\nppWKVWRUNKu3wl6uWkB9jVyre/AWKXPWbtoKh9pnj6nBwpmoxPRLPr+RtL81\nXctlyCgAD5mVGPT+f36FWJOLJSsZafsywMtWDSMYadE4xLugO4WYCPWboIf3\nhJZoyvcLgf8k/5a1abmInYsJgi8odgULrVahbYcWl9dW9UGh8aHrnugEXyPp\nxDubm0gYz+/GgVfCo/RVocIWp2Qz9EG6uyANP0aBexlwRUAgJ+fx0ObehRbz\nYs5sqF9+6jH2e2dfqUj92gXKvt3cPQWd0M2UEzK1yhOP0tZ5V+uShKi+aKhQ\nDQc7vJvVL/8Y6t0t3IwTULj+nGpFdl8lKsS3PPVhsIYs+jkU5qkVICrAeX86\nvsdm5uu708BhmXhVO9nLF7FGf66y4zaYJGN+/bEvulXRfDUPwxgTwizuklBV\nNklM\r\n=y6Qh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/src/index", "engines": {"node": ">= 6.0.0"}, "gitHead": "428a7b7df1d42d609c43acc072fb898fb56376d5", "scripts": {"test": "mocha --reporter spec dist/test/*.js", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/src/index", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"debug": "4"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.0.0", "mocha": "^6.2.0", "eslint": "5.16.0", "rimraf": "^3.0.0", "cpy-cli": "^2.0.0", "@types/ws": "^6.0.3", "typescript": "^3.5.3", "@types/node": "^12.12.17", "@types/debug": "4", "@types/mocha": "^5.2.7", "async-listen": "^1.2.0", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_6.0.0_1579811352189_0.537918813505835", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "agent-base", "version": "6.0.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@6.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "808007e4e5867decb0ab6ab2f928fbdb5a596db4", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.1.tgz", "fileCount": 10, "integrity": "sha512-01q25QQDwLSsyfhrKbn8yuur+JNw0H+0Y4JiGIKd3z9aYk/w/2kxD/Upc+t2ZBBSUNff50VjPsSW2YxM8QYKVg==", "signatures": [{"sig": "MEUCIFVGEjvuk5HvNFwOc5jUmY6X3llyJ+E4zlHZHUEhR27MAiEAnYn+KYys0HrlobUFgU4RKRNQBgOfIRrmBrzy6Ft9Ui4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/nRlCRA9TVsSAnZWagAABeMP/iZOX6GDhsplRUdNd7Jd\nTnfzCYROcvxdXyFSi8y/nB43ZN2RYuq5/mo8NiEI9pj4mzPpHmuJkuiMFTAn\nHHJiUJEFVrFafkopcImFE5KJi3hT5Q653WbfAm6V8wFBOaKOz5CoX3kqNXql\nzh08PhWYfbYDxZR/61uWr95H4l7x2y8YnlDMD/xSF9ERpQBrZqi/Nd+PK7dp\nPxJh7inRE7oOflJxaz//0oI3fD0nYTs0MN6hnnhNkZpTgGv5u5YRXt8glMy6\ncLbVzYQGXq126WVH1XnybNppdjul4kGmh+Try9wS+kpgXg1qFx3e367vmkfP\neMe3+CQKGW8RSRrQYw+UkvOZ/aImMNNDbm4q9dhIgoYpmnhsmbDvZh3Hh0JR\nsq6xMGfZrI8h9gArYT3AGB1JybQOrr54QTOqy0lsxTK7uV/9Smd6wN2S3HhS\naf7RFK2GjCW7cs5tfbNB0B1LZJJZ5Z5NpQ2ez9UEyZXSA+i+6mvbjDq+2Vpz\nQIyv5AnVEbChYVqCuNemXj3wJ5vlHVx+to/iJmuz61RVJ5q4lGBLM0esxKnx\n/0hHYLXRq9Gb+QrnF6XBknDoZDgjdCATDwISXVHbhG6myPvgNC3U2NVxHdXg\nR+AKZnxe6V++gj5e9Uqt4mvMWjofIVMa9Pm3E3xqPFEyJFvsnWaAhXMoAs01\nBLSt\r\n=IMKN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/src/index", "engines": {"node": ">= 6.0.0"}, "gitHead": "ad9a326a01d1423390fc882b0d827918243f2093", "scripts": {"test": "mocha --reporter spec dist/test/*.js", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/src/index", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "12.18.0", "dependencies": {"debug": "4"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.0.0", "mocha": "^6.2.0", "eslint": "5.16.0", "rimraf": "^3.0.0", "semver": "^7.1.2", "cpy-cli": "^2.0.0", "@types/ws": "^6.0.3", "typescript": "^3.5.3", "@types/node": "^12.12.17", "@types/debug": "4", "@types/mocha": "^5.2.7", "async-listen": "^1.2.0", "@types/semver": "^7.1.0", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_6.0.1_1593734244794_0.18562676500612962", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "agent-base", "version": "6.0.2", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@6.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/node-agent-base#readme", "bugs": {"url": "https://github.com/TooTallNate/node-agent-base/issues"}, "dist": {"shasum": "49fff58577cfee3f37176feab4c22e00f86d7f77", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz", "fileCount": 10, "integrity": "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==", "signatures": [{"sig": "MEQCICcZFCIuaqix48fxTrpk49ZLiOSzWZ3bWiA1Z7NkEKqsAiA394SmOxhYsFAnx1DkpW9DQS7xqR5d7gVHKEfLC2vEYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfky/7CRA9TVsSAnZWagAAyoAQAIQvms9ni58rEgX+SVrB\nw9QrEQza6zP6q1Ht2RQ7D+uZNGeBHW/2wxv9o1iH/Rb4IWHj77qsv081nc0G\naRl/MOnEfHNqYL+b/jq5lcaNvAK6NRcdA5ZZm1AOju4AsfqkUAyU+AqnsCO/\nIffojTBTMdYL258Jwrkqqrx3LM9lGnax9T/uzPEK2b3jpQU3krIci2ngNE08\n/FFehjfy1ZztGviEPLarQKHeDG0Vso77SEypUHPjNhZsX4a+7I/i0GVnw68X\nCwBdmywE1gSlhLNFZH7hS6pQEI5fJ+Ih7mcSjnlnntXtWfmEA1a/VAQIUxvo\nmSyZDjhv/jYrscOdaFjHd3HRloT8ShI9ScVFp+1ML2U4jn3Brxf5Gupojf/A\nnLuiUp8bJ9UMZnl8jJPrzlGRiZm9KJlIBdhIMNpV5eUfr4/p0LohP1Zr1L4t\nCHthvf22lAP19Zqt11KYswsRLO+RIh4FjHWGNZJ1HNDgUywr/DByY9oJqbpF\nQlrUQxtGyToC66FBpZcSHFY5SPAd0EurdkMOTGT6m221c5986bv8ZoUpT+Ws\nHBHkuELBuH7JcIT74bWo+cxFJa/thZUrGG2WpD5O2sup7EIuuTlCa5E+Yz8g\nBK8QpjBqEMwU2sDDtaLJw51pVORISuM/fcIFG86Qm5USV8ismCVmxILYJ7kp\n6EGs\r\n=o4Fm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/src/index", "engines": {"node": ">= 6.0.0"}, "gitHead": "c4b8ea2e1a11bae023bb09b708050a50418204e9", "scripts": {"test": "mocha --reporter spec dist/test/*.js", "build": "tsc", "prebuild": "<PERSON><PERSON><PERSON> dist", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}, "typings": "dist/src/index", "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/TooTallNate/node-agent-base.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "15.0.1", "dependencies": {"debug": "4"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.0.0", "mocha": "^6.2.0", "eslint": "5.16.0", "rimraf": "^3.0.0", "semver": "^7.1.2", "cpy-cli": "^2.0.0", "@types/ws": "^6.0.3", "typescript": "^3.5.3", "@types/node": "^14.0.20", "@types/debug": "4", "@types/mocha": "^5.2.7", "async-listen": "^1.2.0", "@types/semver": "^7.1.0", "eslint-plugin-react": "7.12.4", "eslint-config-airbnb": "17.1.0", "eslint-plugin-import": "2.16.0", "eslint-config-prettier": "4.1.0", "eslint-plugin-jsx-a11y": "6.2.1", "@typescript-eslint/parser": "1.1.0", "@typescript-eslint/eslint-plugin": "1.6.0", "eslint-import-resolver-typescript": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_6.0.2_1603481595243_0.4891369499229372", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "agent-base", "version": "7.0.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@7.0.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "3424dd96658fbbcc0b090e41aaca2deb9a89f9cf", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-7.0.0.tgz", "fileCount": 15, "integrity": "sha512-awaqsf16R0tAUMxWiVikaBDKrbt0im7XdzPMh3I8TFC097G4ZowjGgLBfXt+tGPsE+1U1FyLBGuWMd/EPVblWg==", "signatures": [{"sig": "MEUCIQDJD7uU+y+uy2jUMTunFNhkxisqymPyHxzQYucUPZB2rgIgcDF1oqhgsU/EvhKy4ug/o58cgP9f4PHdft18pnJW7kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkVBaAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZJxAAmgyiKkyKMUKOoPB+I0Js0vDWUxkQOSWvKNxxER0iD8so1+Pa\r\nCu9XXb9fesW+iG3KqAdQRrolhdNJGLrvBsqcFoFc0k4nzDdT54aFsSwtmj1j\r\nM9Y2980QEiaLF+aSVsbEEpmckNQZxZQMnzlRt6KxoBGDTzHtkmObgOfqRS7n\r\nvW6sSn5d5KpwZsWkMy5hzwuRL+9PTN/DnJlYUqUGRRFMafGPZFhS8s091Sy4\r\nSgYwZU3OWQi9Tk9j2xdcf6be7BJ9NF7LhK6x96OT0vjDGTmaghGBTRFLwXkv\r\nNKATfu4TrKeQ2eFGhkGUWYZa98QXx2+NXsEXDb8SlAZFpuXzAtNUff1OsNSB\r\nsdYSq9uuCR2pg0KSprw1tvmSRyfMR8hZetHHRhQR6ROWG+CSevIVGarBJ6vB\r\nUMKB4T2Ix4T/fsO7rjnubbnLfY4culUenQ9FYYzVuPh0Tt8T46VQBtBo8BLi\r\nVBJLfNgE9dKKBg26++Pji0t+nTXb9zZ9DSK+sN+6TyeXXOm34V15ESx5EQGe\r\nkMMIaB3Q07YXNGNBZPD6UwRkCpDoSBoLxpxcO+m5pGNfelYdQqqQjy7Zwt+t\r\n+sRYqj69WP4TYkmFE20Fj+IGWb3UiVcJYK513PJc0COWHPZTkvX/qN7HSYTn\r\nS4kpeUU/0+SKZj9BlE2+CbAJ/IHceATyhng=\r\n=39vP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "gitHead": "4d75e6cc974b2614a575e6f7a3d3c79741727dbc", "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/agent-base"}, "_npmVersion": "8.19.4", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "16.20.0", "dependencies": {"debug": "^4.3.4"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "workspace:*", "@types/ws": "^6.0.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.43", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/semver": "^7.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_7.0.0_1683232383919_0.7242806518001645", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "agent-base", "version": "7.0.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@7.0.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "ec4df4e6406bdf71490ade302ea45f86bf365ea9", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-7.0.1.tgz", "fileCount": 11, "integrity": "sha512-V9to8gr2GK7eA+xskWGAFUX/TLSQKuH2TI06c/jGLL6yLp3oEjtnqM7a5tPV9fC1rabLeAgThZeBwsYX+WWHpw==", "signatures": [{"sig": "MEUCIQCub43qOrgL1ECB9peiOsrbviOLLcQhA9hWMR/kLM3pCQIgcUfHmBMsJ9eLYNAYQMMFYDUZDZqsncl4cmtztuH5t4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20416}, "main": "./dist/index.js", "_from": "file:agent-base-7.0.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/04dd9e5f64167b47be3df4a78e581305/agent-base-7.0.1.tgz", "_integrity": "sha512-V9to8gr2GK7eA+xskWGAFUX/TLSQKuH2TI06c/jGLL6yLp3oEjtnqM7a5tPV9fC1rabLeAgThZeBwsYX+WWHpw==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/agent-base"}, "_npmVersion": "9.6.4", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"debug": "^4.3.4"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "@types/ws": "^6.0.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/semver": "^7.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_7.0.1_1683324250254_0.9390646513749472", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "agent-base", "version": "7.0.2", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@7.0.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "d6c854c21fe5b8c8f1c69ac12a7d21a3d1be2859", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-7.0.2.tgz", "fileCount": 11, "integrity": "sha512-k2/tQ1+8Zf50dEUJWklUP80LcE/+Ph+OJ6cf2Ff2fD/c/TtCe6ofnCoNMz9UnyxOQYlaAALZtEWETzn+1JjfHg==", "signatures": [{"sig": "MEUCIBHy3g7QH1m/A/E1Lvd+HSsiJRLcXdNCoR+CSan8btZhAiEA2PBA2H3C7gZPtXNlcQILGrmEw2GsCAt05q+yP9q537w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23174}, "main": "./dist/index.js", "_from": "file:agent-base-7.0.2.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/7068fe54e9376f4e4643a69370d03902/agent-base-7.0.2.tgz", "_integrity": "sha512-k2/tQ1+8Zf50dEUJWklUP80LcE/+Ph+OJ6cf2Ff2fD/c/TtCe6ofnCoNMz9UnyxOQYlaAALZtEWETzn+1JjfHg==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/agent-base"}, "_npmVersion": "9.6.6", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"debug": "^4.3.4"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "@types/ws": "^6.0.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^2.1.0", "@types/semver": "^7.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_7.0.2_1684438283925_0.9084338374824403", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "agent-base", "version": "7.1.0", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@7.1.0", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "536802b76bc0b34aa50195eb2442276d613e3434", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.0.tgz", "fileCount": 11, "integrity": "sha512-o/zjMZRhJxny7OyEF+Op8X+efiELC7k7yOjMzgfzVqOzXqkBkWI79YoTdOtsuWd5BWhAGAuOY/Xa6xpiaWXiNg==", "signatures": [{"sig": "MEUCIG59UY1uwFDbQsfoWZdqXmc183pdjBzFVU/NIEjHtYbKAiEAvswRuxPXjS6p46waDQ1b1Sj/B3fEaMtnn4xWFodqJGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23496}, "main": "./dist/index.js", "_from": "file:agent-base-7.1.0.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/c6754f3ac9048574dd63be70310eecbe/agent-base-7.1.0.tgz", "_integrity": "sha512-o/zjMZRhJxny7OyEF+Op8X+efiELC7k7yOjMzgfzVqOzXqkBkWI79YoTdOtsuWd5BWhAGAuOY/Xa6xpiaWXiNg==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/agent-base"}, "_npmVersion": "9.6.6", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"debug": "^4.3.4"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "@types/ws": "^6.0.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "@types/semver": "^7.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_7.1.0_1684966197571_0.20313114786357644", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "agent-base", "version": "7.1.1", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@7.1.1", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "bdbded7dfb096b751a2a087eeeb9664725b2e317", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.1.tgz", "fileCount": 12, "integrity": "sha512-H0TSyFNDMomMNJQBn8wFV5YC/2eJ+VXECwOadZJT554xP6cODZHPX3H9QMQECxvrgiSOP1pHjy1sMWQVYJOUOA==", "signatures": [{"sig": "MEQCICukUv7At8DBG9zvB3grqS9q18Bw9PPJwQIZpQq0tHZaAiBhiCvJ0ot1d9WsVT3+zqexp8BR4hufEl57A+jsa6gIsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31249}, "main": "./dist/index.js", "_from": "file:agent-base-7.1.1.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/49926a570127159653e7b70d101bcbba/agent-base-7.1.1.tgz", "_integrity": "sha512-H0TSyFNDMomMNJQBn8wFV5YC/2eJ+VXECwOadZJT554xP6cODZHPX3H9QMQECxvrgiSOP1pHjy1sMWQVYJOUOA==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/agent-base"}, "_npmVersion": "10.2.4", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"debug": "^4.3.4"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^3.3.3", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "@types/ws": "^6.0.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "@types/semver": "^7.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_7.1.1_1711762299983_0.6642759080604241", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "agent-base", "version": "7.1.2", "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"url": "http://n8.io/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "agent-base@7.1.2", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "dist": {"shasum": "c83b029791b07a5301dce3ef825e6a328b5391cd", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.2.tgz", "fileCount": 12, "integrity": "sha512-JVzqkCNRT+VfqzzgPWDPnwvDheSAUdiMUn3NoLXpDJF5lRqeJqyC9iGsAxIOAW+mzIdq+uP1TvcX6bMtrH0agg==", "signatures": [{"sig": "MEYCIQDohLCl4KKVFq1r4bUIymtlvn51BqBKTdYjL0WWu3hXsgIhAIA71jk7fB3Xc+7zgK+ciYf+qNv3SFnqexl+1XUqW5rh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31595}, "main": "./dist/index.js", "_from": "file:agent-base-7.1.2.tgz", "types": "./dist/index.d.ts", "engines": {"node": ">= 14"}, "scripts": {"lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs", "test": "jest --env node --verbose --bail", "build": "tsc"}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "_resolved": "/tmp/fe81417e0d3b4ac9647c9bdce788daff/agent-base-7.1.2.tgz", "_integrity": "sha512-JVzqkCNRT+VfqzzgPWDPnwvDheSAUdiMUn3NoLXpDJF5lRqeJqyC9iGsAxIOAW+mzIdq+uP1TvcX6bMtrH0agg==", "repository": {"url": "git+https://github.com/TooTallNate/proxy-agents.git", "type": "git", "directory": "packages/agent-base"}, "_npmVersion": "10.8.2", "description": "Turn a function into an `http.Agent` instance", "directories": {}, "_nodeVersion": "20.18.1", "dependencies": {"debug": "^4.3.4"}, "_hasShrinkwrap": false, "devDependencies": {"ws": "^5.2.4", "jest": "^29.5.0", "ts-jest": "^29.1.0", "tsconfig": "0.0.0", "@types/ws": "^6.0.4", "typescript": "^5.0.4", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/debug": "^4.1.7", "async-listen": "^3.0.0", "@types/semver": "^7.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/agent-base_7.1.2_1733542327483_0.35557837083053245", "host": "s3://npm-registry-packages"}}, "7.1.3": {"name": "agent-base", "version": "7.1.3", "description": "Turn a function into an `http.Agent` instance", "main": "./dist/index.js", "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/agent-base"}, "keywords": ["http", "agent", "base", "barebones", "https"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "devDependencies": {"@types/debug": "^4.1.7", "@types/jest": "^29.5.1", "@types/node": "^14.18.45", "@types/semver": "^7.3.13", "@types/ws": "^6.0.4", "async-listen": "^3.0.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4", "ws": "^5.2.4", "tsconfig": "0.0.0"}, "engines": {"node": ">= 14"}, "scripts": {"build": "tsc", "test": "jest --env node --verbose --bail", "lint": "eslint . --ext .ts", "pack": "node ../../scripts/pack.mjs"}, "_id": "agent-base@7.1.3", "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "_integrity": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==", "_resolved": "/tmp/0708188976c1b804614f97606b4b448d/agent-base-7.1.3.tgz", "_from": "file:agent-base-7.1.3.tgz", "_nodeVersion": "20.18.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==", "shasum": "29435eb821bc4194633a5b89e5bc4703bafc25a1", "tarball": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz", "fileCount": 12, "unpackedSize": 31548, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGPXvJWij+sL0uBz+jG5jqDD/Cq6tbJ47abDAe1nuBMJAiEAyYLnJ1AjcCuGMxYYrsmaFfrdKAk52YVoZqK886o44PA="}]}, "_npmUser": {"name": "tootallnate", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/agent-base_7.1.3_1733630651319_0.4296383757785176"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-07-09T20:14:39.860Z", "modified": "2024-12-08T04:04:11.685Z", "0.0.1": "2013-07-09T20:14:41.054Z", "1.0.0": "2013-09-09T23:12:46.790Z", "1.0.1": "2013-09-10T04:20:58.341Z", "1.0.2": "2015-06-28T01:24:07.948Z", "2.0.0": "2015-07-10T22:19:46.188Z", "2.0.1": "2015-09-10T18:55:12.806Z", "2.1.0": "2017-05-26T16:37:21.989Z", "2.1.1": "2017-05-30T22:08:41.402Z", "3.0.0": "2017-06-02T21:34:18.841Z", "4.0.0": "2017-06-06T23:32:31.040Z", "4.0.1": "2017-06-13T20:20:26.000Z", "4.1.0": "2017-06-27T00:15:13.928Z", "4.1.1": "2017-07-21T01:28:27.403Z", "4.1.2": "2017-11-20T17:55:50.004Z", "4.2.0": "2018-01-15T23:46:08.065Z", "4.2.1": "2018-07-05T20:45:34.771Z", "4.3.0": "2019-06-05T20:17:16.954Z", "5.0.0": "2019-12-10T19:40:42.838Z", "5.1.0": "2019-12-11T03:57:45.316Z", "5.1.1": "2019-12-11T05:16:13.169Z", "6.0.0": "2020-01-23T20:29:12.352Z", "6.0.1": "2020-07-02T23:57:24.943Z", "6.0.2": "2020-10-23T19:33:15.354Z", "7.0.0": "2023-05-04T20:33:04.125Z", "7.0.1": "2023-05-05T22:04:10.433Z", "7.0.2": "2023-05-18T19:31:24.134Z", "7.1.0": "2023-05-24T22:09:57.729Z", "7.1.1": "2024-03-30T01:31:40.164Z", "7.1.2": "2024-12-07T03:32:07.648Z", "7.1.3": "2024-12-08T04:04:11.531Z"}, "bugs": {"url": "https://github.com/TooTallNate/proxy-agents/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/"}, "license": "MIT", "homepage": "https://github.com/TooTallNate/proxy-agents#readme", "keywords": ["http", "agent", "base", "barebones", "https"], "repository": {"type": "git", "url": "git+https://github.com/TooTallNate/proxy-agents.git", "directory": "packages/agent-base"}, "description": "Turn a function into an `http.Agent` instance", "maintainers": [{"name": "tootallnate", "email": "<EMAIL>"}], "readme": "agent-base\n==========\n### Turn a function into an [`http.Agent`][http.Agent] instance\n\nThis module is a thin wrapper around the base `http.Agent` class.\n\nIt provides an abstract class that must define a `connect()` function,\nwhich is responsible for creating the underlying socket that the HTTP\nclient requests will use.\n\nThe `connect()` function may return an arbitrary `Duplex` stream, or\nanother `http.Agent` instance to delegate the request to, and may be\nasynchronous (by defining an `async` function).\n\nInstances of this agent can be used with the `http` and `https`\nmodules. To differentiate, the options parameter in the `connect()`\nfunction includes a `secureEndpoint` property, which can be checked\nto determine what type of socket should be returned.\n\n#### Some subclasses:\n\nHere are some more interesting uses of `agent-base`.\nSend a pull request to list yours!\n\n * [`http-proxy-agent`][http-proxy-agent]: An HTTP(s) proxy `http.Agent` implementation for HTTP endpoints\n * [`https-proxy-agent`][https-proxy-agent]: An HTTP(s) proxy `http.Agent` implementation for HTTPS endpoints\n * [`pac-proxy-agent`][pac-proxy-agent]: A PAC file proxy `http.Agent` implementation for HTTP and HTTPS\n * [`socks-proxy-agent`][socks-proxy-agent]: A SOCKS proxy `http.Agent` implementation for HTTP and HTTPS\n\nExample\n-------\n\nHere's a minimal example that creates a new `net.Socket` or `tls.Socket`\nbased on the `secureEndpoint` property. This agent can be used with both\nthe `http` and `https` modules.\n\n```ts\nimport * as net from 'net';\nimport * as tls from 'tls';\nimport * as http from 'http';\nimport { Agent } from 'agent-base';\n\nclass MyAgent extends Agent {\n  connect(req, opts) {\n    // `secureEndpoint` is true when using the \"https\" module\n    if (opts.secureEndpoint) {\n      return tls.connect(opts);\n    } else {\n      return net.connect(opts);\n    }\n  }\n});\n\n// Keep alive enabled means that `connect()` will only be\n// invoked when a new connection needs to be created\nconst agent = new MyAgent({ keepAlive: true });\n\n// Pass the `agent` option when creating the HTTP request\nhttp.get('http://nodejs.org/api/', { agent }, (res) => {\n  console.log('\"response\" event!', res.headers);\n  res.pipe(process.stdout);\n});\n```\n\n[http-proxy-agent]: ../http-proxy-agent\n[https-proxy-agent]: ../https-proxy-agent\n[pac-proxy-agent]: ../pac-proxy-agent\n[socks-proxy-agent]: ../socks-proxy-agent\n[http.Agent]: https://nodejs.org/api/http.html#http_class_http_agent\n", "readmeFilename": "README.md", "users": {"tsxuehu": true}}