0 verbose cli E:\Node\node.exe E:\Nvm\nvm\v20.15.1\node_modules\npm\bin\npm-cli.js
1 info using npm@10.7.0
2 info using node@v20.15.1
3 silly config:load:file:E:\Nvm\nvm\v20.15.1\node_modules\npm\npmrc
4 silly config:load:file:D:\.0000\scarboroughfair\.npmrc
5 silly config:load:file:C:\Users\<USER>\.npmrc
6 silly config:load:file:e:\Node\etc\npmrc
7 verbose title npm exec supabase init
8 verbose argv "exec" "--" "supabase" "init"
9 verbose logfile logs-max:10 dir:d:\.0000\scarboroughfair\_logs\2025-07-05T08_28_41_937Z-
10 verbose logfile d:\.0000\scarboroughfair\_logs\2025-07-05T08_28_41_937Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly logfile done cleaning log files
13 http fetch GET 200 https://registry.npmjs.org/supabase 4941ms (cache miss)
14 silly idealTree buildDeps
15 silly fetch manifest supabase@2.30.4
16 http fetch GET 200 https://registry.npmjs.org/supabase 924ms (cache revalidated)
17 silly placeDep ROOT supabase@2.30.4 OK for:  want: 2.30.4
18 silly fetch manifest tar@7.4.3
19 silly fetch manifest bin-links@^5.0.0
20 silly fetch manifest node-fetch@^3.3.2
21 silly fetch manifest https-proxy-agent@^7.0.2
22 http fetch GET 200 https://registry.npmjs.org/tar 314ms (cache revalidated)
23 http fetch GET 200 https://registry.npmjs.org/node-fetch 3997ms (cache revalidated)
24 http fetch GET 200 https://registry.npmjs.org/bin-links 4007ms (cache revalidated)
25 http fetch GET 200 https://registry.npmjs.org/https-proxy-agent 4008ms (cache revalidated)
26 silly placeDep ROOT bin-links@5.0.0 OK for: supabase@2.30.4 want: ^5.0.0
27 silly placeDep ROOT https-proxy-agent@7.0.6 OK for: supabase@2.30.4 want: ^7.0.2
28 silly placeDep ROOT node-fetch@3.3.2 OK for: supabase@2.30.4 want: ^3.3.2
29 silly placeDep ROOT tar@7.4.3 OK for: supabase@2.30.4 want: 7.4.3
30 silly fetch manifest cmd-shim@^7.0.0
31 silly fetch manifest proc-log@^5.0.0
32 silly fetch manifest read-cmd-shim@^5.0.0
33 silly fetch manifest write-file-atomic@^6.0.0
34 silly fetch manifest npm-normalize-package-bin@^4.0.0
35 silly fetch manifest agent-base@^7.1.2
36 silly fetch manifest debug@4
37 silly fetch manifest data-uri-to-buffer@^4.0.0
38 silly fetch manifest fetch-blob@^3.1.4
39 silly fetch manifest formdata-polyfill@^4.0.10
40 silly fetch manifest @isaacs/fs-minipass@^4.0.0
41 http fetch GET 200 https://registry.npmjs.org/cmd-shim 337ms (cache revalidated)
42 silly fetch manifest chownr@^3.0.0
43 http fetch GET 200 https://registry.npmjs.org/npm-normalize-package-bin 337ms (cache revalidated)
44 silly fetch manifest minipass@^7.1.2
45 http fetch GET 200 https://registry.npmjs.org/read-cmd-shim 340ms (cache revalidated)
46 silly fetch manifest minizlib@^3.0.1
47 http fetch GET 200 https://registry.npmjs.org/proc-log 343ms (cache revalidated)
48 silly fetch manifest mkdirp@^3.0.1
49 http fetch GET 200 https://registry.npmjs.org/minizlib 344ms (cache revalidated)
50 silly fetch manifest yallist@^5.0.0
51 http fetch GET 200 https://registry.npmjs.org/chownr 351ms (cache revalidated)
52 http fetch GET 200 https://registry.npmjs.org/mkdirp 345ms (cache revalidated)
53 http fetch GET 200 https://registry.npmjs.org/minipass 362ms (cache revalidated)
54 http fetch GET 200 https://registry.npmjs.org/yallist 331ms (cache revalidated)
55 http fetch GET 200 https://registry.npmjs.org/formdata-polyfill 4061ms (cache revalidated)
56 http fetch GET 200 https://registry.npmjs.org/data-uri-to-buffer 4063ms (cache revalidated)
57 http fetch GET 200 https://registry.npmjs.org/write-file-atomic 4068ms (cache revalidated)
58 http fetch GET 200 https://registry.npmjs.org/debug 4072ms (cache revalidated)
59 http fetch GET 200 https://registry.npmjs.org/fetch-blob 4074ms (cache revalidated)
60 http fetch GET 200 https://registry.npmjs.org/agent-base 4079ms (cache revalidated)
61 http fetch GET 200 https://registry.npmjs.org/@isaacs%2ffs-minipass 4079ms (cache revalidated)
62 silly placeDep ROOT cmd-shim@7.0.0 OK for: bin-links@5.0.0 want: ^7.0.0
63 silly placeDep ROOT npm-normalize-package-bin@4.0.0 OK for: bin-links@5.0.0 want: ^4.0.0
64 silly placeDep ROOT proc-log@5.0.0 OK for: bin-links@5.0.0 want: ^5.0.0
65 silly placeDep ROOT read-cmd-shim@5.0.0 OK for: bin-links@5.0.0 want: ^5.0.0
66 silly placeDep ROOT write-file-atomic@6.0.0 OK for: bin-links@5.0.0 want: ^6.0.0
67 silly fetch manifest imurmurhash@^0.1.4
68 silly fetch manifest signal-exit@^4.0.1
69 http fetch GET 200 https://registry.npmjs.org/signal-exit 308ms (cache revalidated)
70 http fetch GET 200 https://registry.npmjs.org/imurmurhash 310ms (cache revalidated)
71 silly placeDep ROOT agent-base@7.1.3 OK for: https-proxy-agent@7.0.6 want: ^7.1.2
72 silly placeDep ROOT debug@4.4.1 OK for: https-proxy-agent@7.0.6 want: 4
73 silly fetch manifest ms@^2.1.3
74 http fetch GET 200 https://registry.npmjs.org/ms 314ms (cache revalidated)
75 silly placeDep ROOT ms@2.1.3 OK for: debug@4.4.1 want: ^2.1.3
76 silly placeDep ROOT data-uri-to-buffer@4.0.1 OK for: node-fetch@3.3.2 want: ^4.0.0
77 silly placeDep ROOT fetch-blob@3.2.0 OK for: node-fetch@3.3.2 want: ^3.1.4
78 silly placeDep ROOT formdata-polyfill@4.0.10 OK for: node-fetch@3.3.2 want: ^4.0.10
79 silly fetch manifest node-domexception@^1.0.0
80 silly fetch manifest web-streams-polyfill@^3.0.3
81 http fetch GET 200 https://registry.npmjs.org/web-streams-polyfill 302ms (cache revalidated)
82 http fetch GET 200 https://registry.npmjs.org/node-domexception 304ms (cache revalidated)
83 silly placeDep ROOT node-domexception@1.0.0 OK for: fetch-blob@3.2.0 want: ^1.0.0
84 silly placeDep ROOT web-streams-polyfill@3.3.3 OK for: fetch-blob@3.2.0 want: ^3.0.3
85 silly placeDep ROOT @isaacs/fs-minipass@4.0.1 OK for: tar@7.4.3 want: ^4.0.0
86 silly placeDep ROOT chownr@3.0.0 OK for: tar@7.4.3 want: ^3.0.0
87 silly placeDep ROOT minipass@7.1.2 OK for: tar@7.4.3 want: ^7.1.2
88 silly placeDep ROOT minizlib@3.0.2 OK for: tar@7.4.3 want: ^3.0.1
89 silly placeDep ROOT mkdirp@3.0.1 OK for: tar@7.4.3 want: ^3.0.1
90 silly placeDep ROOT yallist@5.0.0 OK for: tar@7.4.3 want: ^5.0.0
91 silly fetch manifest minipass@^7.0.4
92 silly placeDep ROOT imurmurhash@0.1.4 OK for: write-file-atomic@6.0.0 want: ^0.1.4
93 silly placeDep ROOT signal-exit@4.1.0 OK for: write-file-atomic@6.0.0 want: ^4.0.1
94 silly reify moves {}
95 silly audit bulk request {
95 silly audit   supabase: [ '2.30.4' ],
95 silly audit   'bin-links': [ '5.0.0' ],
95 silly audit   'https-proxy-agent': [ '7.0.6' ],
95 silly audit   'node-fetch': [ '3.3.2' ],
95 silly audit   tar: [ '7.4.3' ],
95 silly audit   'cmd-shim': [ '7.0.0' ],
95 silly audit   'npm-normalize-package-bin': [ '4.0.0' ],
95 silly audit   'proc-log': [ '5.0.0' ],
95 silly audit   'read-cmd-shim': [ '5.0.0' ],
95 silly audit   'write-file-atomic': [ '6.0.0' ],
95 silly audit   'agent-base': [ '7.1.3' ],
95 silly audit   debug: [ '4.4.1' ],
95 silly audit   ms: [ '2.1.3' ],
95 silly audit   'data-uri-to-buffer': [ '4.0.1' ],
95 silly audit   'fetch-blob': [ '3.2.0' ],
95 silly audit   'formdata-polyfill': [ '4.0.10' ],
95 silly audit   'node-domexception': [ '1.0.0' ],
95 silly audit   'web-streams-polyfill': [ '3.3.3' ],
95 silly audit   '@isaacs/fs-minipass': [ '4.0.1' ],
95 silly audit   chownr: [ '3.0.0' ],
95 silly audit   minipass: [ '7.1.2' ],
95 silly audit   minizlib: [ '3.0.2' ],
95 silly audit   mkdirp: [ '3.0.1' ],
95 silly audit   yallist: [ '5.0.0' ],
95 silly audit   imurmurhash: [ '0.1.4' ],
95 silly audit   'signal-exit': [ '4.1.0' ]
95 silly audit }
96 warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead
97 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 1866ms
98 silly audit report {}
99 info run supabase@2.30.4 postinstall node_modules/supabase node scripts/postinstall.js
100 info run supabase@2.30.4 postinstall { code: 0, signal: null }
101 verbose exit 0
102 info ok
