import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { BookOpen, Compass, CircleCheck as CheckCircle, Clock, Star, Leaf, Map, Scroll, Crown, ChevronRight } from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

interface Task {
  id: string;
  title: string;
  description: string;
  type: 'herb' | 'exploration';
  progress: number;
  maxProgress: number;
  reward: string;
  difficulty: 'easy' | 'medium' | 'hard';
  timeRemaining?: string;
  completed: boolean;
  icon: any;
}

export default function TasksScreen() {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'herb' | 'exploration'>('all');

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const tasks: Task[] = [
    {
      id: '1',
      title: '收集香芹',
      description: '在集市周围寻找新鲜的香芹叶子',
      type: 'herb',
      progress: 7,
      maxProgress: 10,
      reward: '50金币 + 经验值',
      difficulty: 'easy',
      timeRemaining: '2小时',
      completed: false,
      icon: Leaf,
    },
    {
      id: '2',
      title: '百里香探索',
      description: '深入森林寻找稀有的百里香品种',
      type: 'herb',
      progress: 3,
      maxProgress: 5,
      reward: '100金币 + 稀有种子',
      difficulty: 'medium',
      timeRemaining: '4小时',
      completed: false,
      icon: Leaf,
    },
    {
      id: '3',
      title: '古老图书馆',
      description: '探索被遗忘的古老图书馆',
      type: 'exploration',
      progress: 1,
      maxProgress: 3,
      reward: '200金币 + 古老卷轴',
      difficulty: 'hard',
      timeRemaining: '6小时',
      completed: false,
      icon: BookOpen,
    },
    {
      id: '4',
      title: '神秘洞穴',
      description: '调查山脉中的神秘洞穴',
      type: 'exploration',
      progress: 2,
      maxProgress: 4,
      reward: '150金币 + 宝石',
      difficulty: 'medium',
      timeRemaining: '3小时',
      completed: false,
      icon: Compass,
    },
    {
      id: '5',
      title: '完成的草药任务',
      description: '成功收集了薄荷叶',
      type: 'herb',
      progress: 5,
      maxProgress: 5,
      reward: '75金币',
      difficulty: 'easy',
      completed: true,
      icon: CheckCircle,
    },
  ];

  const filteredTasks = tasks.filter(task => {
    if (selectedCategory === 'all') return true;
    return task.type === selectedCategory;
  });

  const getProgressColor = (progress: number, maxProgress: number) => {
    const percentage = (progress / maxProgress) * 100;
    if (percentage === 100) return '#4A7043';
    if (percentage >= 70) return '#D4A017';
    if (percentage >= 40) return '#E6B82A';
    return '#B0B7A4';
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#4A7043';
      case 'medium': return '#D4A017';
      case 'hard': return '#DC3545';
      default: return '#B0B7A4';
    }
  };

  const handleCompleteTask = (taskId: string) => {
    console.log('Complete task:', taskId);
    // Here you would implement task completion logic
  };

  return (
    <View style={styles.container}>
      <ImageBackground
        source={{ uri: 'https://images.pexels.com/photos/1370295/pexels-photo-1370295.jpeg' }}
        style={styles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['rgba(245, 232, 199, 0.95)', 'rgba(245, 232, 199, 0.9)', 'rgba(245, 232, 199, 0.85)']}
          style={styles.overlay}
        >
          <SafeAreaView style={styles.safeArea}>
            {/* Header with Sage */}
            <Animated.View 
              style={[
                styles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
                }
              ]}
            >
              <View style={styles.sageContainer}>
                <View style={styles.sageFrame}>
                  <Image
                    source={{ uri: 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                    style={styles.sageImage}
                    resizeMode="cover"
                  />
                  <View style={styles.robeOverlay}>
                    <LinearGradient
                      colors={['rgba(169, 169, 169, 0.4)', 'rgba(169, 169, 169, 0.7)']}
                      style={styles.robeGradient}
                    />
                  </View>
                  <View style={styles.wisdomBadge}>
                    <Scroll size={16} color="#F5E8C7" />
                  </View>
                </View>
                <View style={styles.sageInfo}>
                  <Text style={styles.sageTitle}>智者的任务</Text>
                  <Text style={styles.sageSubtitle}>完成任务获得奖励</Text>
                </View>
              </View>

              <View style={styles.titleContainer}>
                <Text style={styles.mainTitle}>任务大厅</Text>
                <View style={styles.titleUnderline} />
                <Text style={styles.subtitle}>探索与收集的冒险之旅</Text>
              </View>
            </Animated.View>

            {/* Category Filter */}
            <Animated.View 
              style={[
                styles.categoryContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}
            >
              <BlurView intensity={25} style={styles.categoryBar}>
                <TouchableOpacity
                  style={[
                    styles.categoryButton,
                    selectedCategory === 'all' && styles.categoryButtonActive
                  ]}
                  onPress={() => setSelectedCategory('all')}
                >
                  <Text style={[
                    styles.categoryText,
                    selectedCategory === 'all' && styles.categoryTextActive
                  ]}>
                    全部任务
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.categoryButton,
                    selectedCategory === 'herb' && styles.categoryButtonActive
                  ]}
                  onPress={() => setSelectedCategory('herb')}
                >
                  <Leaf size={16} color={selectedCategory === 'herb' ? '#6B4E71' : '#B0B7A4'} />
                  <Text style={[
                    styles.categoryText,
                    selectedCategory === 'herb' && styles.categoryTextActive
                  ]}>
                    草本任务
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.categoryButton,
                    selectedCategory === 'exploration' && styles.categoryButtonActive
                  ]}
                  onPress={() => setSelectedCategory('exploration')}
                >
                  <Map size={16} color={selectedCategory === 'exploration' ? '#6B4E71' : '#B0B7A4'} />
                  <Text style={[
                    styles.categoryText,
                    selectedCategory === 'exploration' && styles.categoryTextActive
                  ]}>
                    探索任务
                  </Text>
                </TouchableOpacity>
              </BlurView>
            </Animated.View>

            <ScrollView 
              style={styles.scrollView} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.scrollContent}
            >
              {/* Task List */}
              <Animated.View 
                style={[
                  styles.tasksContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                {filteredTasks.map((task, index) => (
                  <BlurView key={task.id} intensity={30} style={styles.taskCard}>
                    <View style={styles.taskContent}>
                      {/* Task Header */}
                      <View style={styles.taskHeader}>
                        <View style={styles.taskLeft}>
                          <View style={[
                            styles.taskIconContainer,
                            { backgroundColor: task.completed ? 'rgba(74, 112, 67, 0.2)' : 'rgba(176, 183, 164, 0.2)' }
                          ]}>
                            <task.icon 
                              size={24} 
                              color={task.completed ? '#4A7043' : '#B0B7A4'} 
                            />
                          </View>
                          <View style={styles.taskInfo}>
                            <Text style={[
                              styles.taskTitle,
                              task.completed && styles.taskTitleCompleted
                            ]}>
                              {task.title}
                            </Text>
                            <Text style={styles.taskDescription}>
                              {task.description}
                            </Text>
                          </View>
                        </View>
                        <View style={styles.taskRight}>
                          <View style={[
                            styles.difficultyBadge,
                            { backgroundColor: `${getDifficultyColor(task.difficulty)}20` }
                          ]}>
                            <Text style={[
                              styles.difficultyText,
                              { color: getDifficultyColor(task.difficulty) }
                            ]}>
                              {task.difficulty === 'easy' ? '简单' : 
                               task.difficulty === 'medium' ? '中等' : '困难'}
                            </Text>
                          </View>
                        </View>
                      </View>

                      {/* Progress Bar */}
                      <View style={styles.progressContainer}>
                        <View style={styles.progressInfo}>
                          <Text style={styles.progressText}>
                            进度: {task.progress}/{task.maxProgress}
                          </Text>
                          {task.timeRemaining && !task.completed && (
                            <View style={styles.timeContainer}>
                              <Clock size={12} color="#8B7355" />
                              <Text style={styles.timeText}>{task.timeRemaining}</Text>
                            </View>
                          )}
                        </View>
                        <View style={styles.progressBarContainer}>
                          <View style={styles.progressBarBackground}>
                            <View 
                              style={[
                                styles.progressBarFill,
                                { 
                                  width: `${(task.progress / task.maxProgress) * 100}%`,
                                  backgroundColor: getProgressColor(task.progress, task.maxProgress)
                                }
                              ]}
                            />
                          </View>
                          <Text style={styles.progressPercentage}>
                            {Math.round((task.progress / task.maxProgress) * 100)}%
                          </Text>
                        </View>
                      </View>

                      {/* Reward and Action */}
                      <View style={styles.taskFooter}>
                        <View style={styles.rewardContainer}>
                          <Star size={16} color="#D4A017" />
                          <Text style={styles.rewardText}>{task.reward}</Text>
                        </View>
                        {!task.completed ? (
                          <TouchableOpacity
                            style={[
                              styles.completeButton,
                              task.progress < task.maxProgress && styles.completeButtonDisabled
                            ]}
                            onPress={() => handleCompleteTask(task.id)}
                            disabled={task.progress < task.maxProgress}
                          >
                            <LinearGradient
                              colors={
                                task.progress >= task.maxProgress 
                                  ? ['#6B4E71', '#8B6F8B']
                                  : ['#B0B7A4', '#C0C7B4']
                              }
                              style={styles.completeButtonGradient}
                            >
                              <Text style={styles.completeButtonText}>
                                {task.progress >= task.maxProgress ? '完成' : '进行中'}
                              </Text>
                            </LinearGradient>
                          </TouchableOpacity>
                        ) : (
                          <View style={styles.completedBadge}>
                            <CheckCircle size={16} color="#4A7043" />
                            <Text style={styles.completedText}>已完成</Text>
                          </View>
                        )}
                      </View>
                    </View>
                  </BlurView>
                ))}
              </Animated.View>

              {/* Daily Summary */}
              <Animated.View 
                style={[
                  styles.summaryContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <BlurView intensity={30} style={styles.summaryCard}>
                  <View style={styles.summaryHeader}>
                    <Crown size={24} color="#D4A017" />
                    <Text style={styles.summaryTitle}>今日成就</Text>
                  </View>
                  <View style={styles.summaryStats}>
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>4</Text>
                      <Text style={styles.statLabel}>进行中</Text>
                    </View>
                    <View style={styles.statDivider} />
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>1</Text>
                      <Text style={styles.statLabel}>已完成</Text>
                    </View>
                    <View style={styles.statDivider} />
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>575</Text>
                      <Text style={styles.statLabel}>总奖励</Text>
                    </View>
                  </View>
                </BlurView>
              </Animated.View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: width,
    height: height,
  },
  overlay: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 15,
  },
  sageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  sageFrame: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 3,
    borderColor: '#8B7355',
    overflow: 'hidden',
    position: 'relative',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  sageImage: {
    width: '100%',
    height: '100%',
  },
  robeOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  robeGradient: {
    flex: 1,
  },
  wisdomBadge: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    backgroundColor: '#6B4E71',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#F5E8C7',
  },
  sageInfo: {
    flex: 1,
  },
  sageTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 2,
  },
  sageSubtitle: {
    fontSize: 14,
    color: '#8B7355',
  },
  titleContainer: {
    alignItems: 'center',
  },
  mainTitle: {
    fontSize: 28,
    color: '#4A7043',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  titleUnderline: {
    width: 80,
    height: 3,
    backgroundColor: '#4A7043',
    borderRadius: 2,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8B7355',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  categoryContainer: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  categoryBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(176, 183, 164, 0.3)',
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  categoryButtonActive: {
    backgroundColor: 'rgba(107, 78, 113, 0.15)',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#B0B7A4',
    marginLeft: 4,
  },
  categoryTextActive: {
    color: '#6B4E71',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  tasksContainer: {
    paddingHorizontal: 20,
  },
  taskCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  taskContent: {
    padding: 16,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  taskLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  taskIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B4E71',
    marginBottom: 4,
  },
  taskTitleCompleted: {
    color: '#4A7043',
  },
  taskDescription: {
    fontSize: 14,
    color: '#8B7355',
    lineHeight: 20,
  },
  taskRight: {
    marginLeft: 12,
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '600',
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B4E71',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    color: '#8B7355',
    marginLeft: 4,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBarBackground: {
    flex: 1,
    height: 8,
    backgroundColor: 'rgba(176, 183, 164, 0.3)',
    borderRadius: 4,
    overflow: 'hidden',
    marginRight: 8,
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressPercentage: {
    fontSize: 12,
    fontWeight: '600',
    color: '#8B7355',
    minWidth: 35,
    textAlign: 'right',
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rewardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rewardText: {
    fontSize: 14,
    color: '#8B7355',
    marginLeft: 6,
    fontWeight: '500',
  },
  completeButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  completeButtonDisabled: {
    opacity: 0.6,
  },
  completeButtonGradient: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  completeButtonText: {
    color: '#F5E8C7',
    fontSize: 14,
    fontWeight: '600',
  },
  completedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(74, 112, 67, 0.15)',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  completedText: {
    fontSize: 14,
    color: '#4A7043',
    fontWeight: '600',
    marginLeft: 4,
  },
  summaryContainer: {
    paddingHorizontal: 20,
    marginTop: 10,
  },
  summaryCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B4E71',
    marginLeft: 8,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#8B7355',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(139, 115, 85, 0.3)',
  },
});