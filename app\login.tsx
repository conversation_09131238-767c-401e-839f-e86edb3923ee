import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ImageBackground,
  Image,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Eye, EyeOff, Shield, Sword, UserPlus } from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { router } from 'expo-router';

const { width, height } = Dimensions.get('window');

export default function LoginScreen() {
  const { isDark } = useTheme();
  const { signIn, signUp, loading } = useAuth();
  const colors = getColors(isDark);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Email validation function
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleAuth = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('错误', '请填写邮箱和密码');
      return;
    }

    // Validate email format
    if (!isValidEmail(email.trim())) {
      Alert.alert('错误', '请输入有效的邮箱地址');
      return;
    }

    if (isSignUp && !username.trim()) {
      Alert.alert('错误', '请填写用户名');
      return;
    }

    setIsLoading(true);

    try {
      let result;
      if (isSignUp) {
        result = await signUp(email.trim(), password, username);
      } else {
        result = await signIn(email.trim(), password);
      }

      if (result.error) {
        Alert.alert('错误', result.error.message);
      } else {
        if (isSignUp) {
          Alert.alert('成功', '注册成功！请查看邮箱验证链接');
        } else {
          router.replace('/(tabs)/home');
        }
      }
    } catch (error) {
      Alert.alert('错误', '操作失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 40,
      paddingHorizontal: 20,
    },
    characterContainer: {
      alignItems: 'center',
      marginBottom: 30,
      position: 'relative',
    },
    characterFrame: {
      width: 120,
      height: 120,
      borderRadius: 60,
      borderWidth: 4,
      borderColor: colors.primary,
      overflow: 'hidden',
      position: 'relative',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.5 : 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    characterImage: {
      width: '100%',
      height: '100%',
    },
    badgeContainer: {
      position: 'absolute',
      bottom: -5,
      right: -5,
      backgroundColor: colors.secondary,
      borderRadius: 15,
      width: 30,
      height: 30,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.background,
    },
    mistEffect: {
      position: 'absolute',
      bottom: -20,
      left: -20,
      right: -20,
      height: 40,
    },
    mistBlur: {
      flex: 1,
      borderRadius: 20,
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 40,
    },
    logoText: {
      fontFamily: 'MaShanZheng-Regular',
      fontSize: 36,
      color: colors.text,
      textShadowColor: isDark
        ? 'rgba(255, 255, 255, 0.1)'
        : 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 2, height: 2 },
      textShadowRadius: 4,
    },
    logoUnderline: {
      width: 80,
      height: 3,
      backgroundColor: colors.secondary,
      marginTop: 8,
      borderRadius: 2,
    },
    formContainer: {
      width: '100%',
      maxWidth: 350,
      borderRadius: 20,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    formInner: {
      padding: 30,
    },
    welcomeText: {
      fontSize: 24,
      fontWeight: '600',
      color: colors.text,
      textAlign: 'center',
      marginBottom: 30,
      fontFamily: 'MaShanZheng-Regular',
    },
    inputContainer: {
      marginBottom: 20,
    },
    inputWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surface,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: colors.border,
      paddingHorizontal: 15,
      paddingVertical: 12,
    },
    inputIcon: {
      marginRight: 12,
    },
    textInput: {
      flex: 1,
      fontSize: 16,
      color: colors.text,
      fontWeight: '500',
    },
    passwordInput: {
      paddingRight: 40,
    },
    eyeButton: {
      position: 'absolute',
      right: 15,
      padding: 5,
    },
    authButton: {
      marginTop: 10,
      marginBottom: 20,
      borderRadius: 12,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    authButtonDisabled: {
      opacity: 0.7,
    },
    authButtonGradient: {
      paddingVertical: 16,
      alignItems: 'center',
    },
    authButtonText: {
      color: colors.background,
      fontSize: 18,
      fontWeight: '700',
      fontFamily: 'MaShanZheng-Regular',
    },
    switchContainer: {
      alignItems: 'center',
    },
    switchText: {
      fontSize: 16,
      color: colors.textSecondary,
    },
    switchLink: {
      color: colors.secondary,
      fontWeight: '600',
      textDecorationLine: 'underline',
    },
    decorativeElements: {
      position: 'absolute',
      top: 100,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: 20,
    },
    ornament: {
      width: 40,
      height: 40,
      backgroundColor: colors.secondary + '4D',
      borderRadius: 20,
      transform: [{ rotate: '45deg' }],
    },
    ornamentRight: {
      backgroundColor: colors.primary + '4D',
    },
  });

  // Redirect if already authenticated
  React.useEffect(() => {
    if (!loading && router.canGoBack()) {
      router.replace('/(tabs)/home');
    }
  }, [loading]);

  if (loading) {
    return (
      <View
        style={[
          dynamicStyles.container,
          { justifyContent: 'center', alignItems: 'center' },
        ]}
      >
        <Text style={{ color: colors.text }}>加载中...</Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={dynamicStyles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ImageBackground
        source={{
          uri: isDark
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg'
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg',
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.primary + 'CC',
          ]}
          style={dynamicStyles.overlay}
        >
          <ScrollView
            contentContainerStyle={dynamicStyles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Character Illustration */}
            <View style={dynamicStyles.characterContainer}>
              <View style={dynamicStyles.characterFrame}>
                <Image
                  source={{
                    uri: 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg',
                  }}
                  style={dynamicStyles.characterImage}
                  resizeMode="cover"
                />
                <View style={dynamicStyles.badgeContainer}>
                  <Shield size={24} color={colors.accent} />
                </View>
              </View>
              <View style={dynamicStyles.mistEffect}>
                <BlurView intensity={20} style={dynamicStyles.mistBlur} />
              </View>
            </View>

            {/* Logo */}
            <View style={dynamicStyles.logoContainer}>
              <Text style={dynamicStyles.logoText}>斯卡布罗集市</Text>
              <View style={dynamicStyles.logoUnderline} />
            </View>

            {/* Auth Form */}
            <BlurView intensity={30} style={dynamicStyles.formContainer}>
              <View style={dynamicStyles.formInner}>
                <Text style={dynamicStyles.welcomeText}>
                  {isSignUp ? '加入集市' : '欢迎回到集市'}
                </Text>

                {/* Email Field */}
                <View style={dynamicStyles.inputContainer}>
                  <View style={dynamicStyles.inputWrapper}>
                    <Sword
                      size={20}
                      color={colors.primary}
                      style={dynamicStyles.inputIcon}
                    />
                    <TextInput
                      style={dynamicStyles.textInput}
                      placeholder="邮箱"
                      placeholderTextColor={colors.textSecondary}
                      value={email}
                      onChangeText={setEmail}
                      autoCapitalize="none"
                      autoCorrect={false}
                      keyboardType="email-address"
                    />
                  </View>
                </View>

                {/* Username Field (Sign Up Only) */}
                {isSignUp && (
                  <View style={dynamicStyles.inputContainer}>
                    <View style={dynamicStyles.inputWrapper}>
                      <UserPlus
                        size={20}
                        color={colors.primary}
                        style={dynamicStyles.inputIcon}
                      />
                      <TextInput
                        style={dynamicStyles.textInput}
                        placeholder="用户名"
                        placeholderTextColor={colors.textSecondary}
                        value={username}
                        onChangeText={setUsername}
                        autoCapitalize="none"
                        autoCorrect={false}
                      />
                    </View>
                  </View>
                )}

                {/* Password Field */}
                <View style={dynamicStyles.inputContainer}>
                  <View style={dynamicStyles.inputWrapper}>
                    <Shield
                      size={20}
                      color={colors.primary}
                      style={dynamicStyles.inputIcon}
                    />
                    <TextInput
                      style={[
                        dynamicStyles.textInput,
                        dynamicStyles.passwordInput,
                      ]}
                      placeholder="密码"
                      placeholderTextColor={colors.textSecondary}
                      value={password}
                      onChangeText={setPassword}
                      secureTextEntry={!showPassword}
                      autoCapitalize="none"
                      autoCorrect={false}
                    />
                    <TouchableOpacity
                      style={dynamicStyles.eyeButton}
                      onPress={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff size={20} color={colors.primary} />
                      ) : (
                        <Eye size={20} color={colors.primary} />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>

                {/* Auth Button */}
                <TouchableOpacity
                  style={[
                    dynamicStyles.authButton,
                    isLoading && dynamicStyles.authButtonDisabled,
                  ]}
                  onPress={handleAuth}
                  disabled={isLoading}
                >
                  <LinearGradient
                    colors={[colors.primary, colors.primaryLight]}
                    style={dynamicStyles.authButtonGradient}
                  >
                    <Text style={dynamicStyles.authButtonText}>
                      {isLoading ? '处理中...' : isSignUp ? '注册' : '登录'}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>

                {/* Switch Auth Mode */}
                <TouchableOpacity
                  style={dynamicStyles.switchContainer}
                  onPress={() => setIsSignUp(!isSignUp)}
                >
                  <Text style={dynamicStyles.switchText}>
                    {isSignUp ? '已有账户？' : '还没有账户？'}
                    <Text style={dynamicStyles.switchLink}>
                      {isSignUp ? ' 登录' : ' 注册'}
                    </Text>
                  </Text>
                </TouchableOpacity>
              </View>
            </BlurView>

            {/* Decorative Elements */}
            <View style={dynamicStyles.decorativeElements}>
              <View style={dynamicStyles.ornament} />
              <View
                style={[dynamicStyles.ornament, dynamicStyles.ornamentRight]}
              />
            </View>
          </ScrollView>
        </LinearGradient>
      </ImageBackground>
    </KeyboardAvoidingView>
  );
}
