import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
  RefreshControl,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  BookOpen,
  Plus,
  Calendar,
  MapPin,
  Star,
  Leaf,
  Flower,
  Sparkles,
  Clock,
  ChevronRight,
  Edit3,
  Camera,
  Search,
  Filter,
  Heart,
  MessageCircle,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { router } from 'expo-router';
import {
  DiaryEntry,
  HerbEntry,
  MoodOptions,
  WeatherOptions,
  RarityOptions,
} from '@/types/diary';
import { DiaryService } from '@/services/diaryService';

const { width, height } = Dimensions.get('window');

function DiaryContent() {
  const { isDark } = useTheme();
  const { user } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  
  const [activeTab, setActiveTab] = useState<'diary' | 'herbs'>('diary');
  const [diaryEntries, setDiaryEntries] = useState<DiaryEntry[]>([]);
  const [herbEntries, setHerbEntries] = useState<HerbEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<string | null>(null);

  // 加载数据
  const loadData = useCallback(async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const [diaryResult, herbResult] = await Promise.all([
        DiaryService.getDiaryEntries(user.id, { limit: 20 }),
        DiaryService.getHerbEntries(user.id, { limit: 20 })
      ]);

      if (diaryResult.data) {
        setDiaryEntries(diaryResult.data);
      }
      if (herbResult.data) {
        setHerbEntries(herbResult.data);
      }
    } catch (error) {
      console.error('Error loading diary data:', error);
      Alert.alert('错误', '加载数据失败，请重试');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // 刷新数据
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  }, [loadData]);

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // 加载数据
    loadData();
  }, [loadData]);

  // 导航到创建页面
  const handleCreateDiary = () => {
    router.push('/diary/create');
  };

  const handleCreateHerb = () => {
    router.push('/diary/create-herb');
  };

  // 导航到详情页面
  const handleDiaryPress = (entry: DiaryEntry) => {
    router.push(`/diary/${entry.id}`);
  };

  const handleHerbPress = (entry: HerbEntry) => {
    router.push(`/diary/herb/${entry.id}`);
  };

  // 渲染日记条目
  const renderDiaryEntry = (entry: DiaryEntry, index: number) => {
    const mood = MoodOptions[entry.mood];
    const weather = WeatherOptions[entry.weather];
    
    return (
      <Animated.View
        key={entry.id}
        style={[
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <BlurView intensity={30} style={dynamicStyles.entryCard}>
          <TouchableOpacity
            style={dynamicStyles.entryContent}
            onPress={() => handleDiaryPress(entry)}
            activeOpacity={0.8}
          >
            <View style={dynamicStyles.entryHeader}>
              <View style={dynamicStyles.entryMeta}>
                <Text style={dynamicStyles.entryTitle}>{entry.title}</Text>
                <View style={dynamicStyles.entryInfo}>
                  <View style={dynamicStyles.moodWeather}>
                    <Text style={dynamicStyles.emoji}>{mood?.emoji}</Text>
                    <Text style={dynamicStyles.emoji}>{weather?.emoji}</Text>
                  </View>
                  <Text style={dynamicStyles.entryDate}>
                    {new Date(entry.created_at).toLocaleDateString('zh-CN')}
                  </Text>
                </View>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </View>
            
            <Text style={dynamicStyles.entryPreview} numberOfLines={2}>
              {entry.content}
            </Text>
            
            {entry.location && (
              <View style={dynamicStyles.locationContainer}>
                <MapPin size={14} color={colors.textSecondary} />
                <Text style={dynamicStyles.locationText}>{entry.location}</Text>
              </View>
            )}
            
            {entry.tags.length > 0 && (
              <View style={dynamicStyles.tagsContainer}>
                {entry.tags.slice(0, 3).map((tag, tagIndex) => (
                  <View key={tagIndex} style={dynamicStyles.tag}>
                    <Text style={dynamicStyles.tagText}>#{tag}</Text>
                  </View>
                ))}
                {entry.tags.length > 3 && (
                  <Text style={dynamicStyles.moreTagsText}>+{entry.tags.length - 3}</Text>
                )}
              </View>
            )}
          </TouchableOpacity>
        </BlurView>
      </Animated.View>
    );
  };

  // 渲染草药条目
  const renderHerbEntry = (entry: HerbEntry, index: number) => {
    const rarity = RarityOptions[entry.rarity];
    
    return (
      <Animated.View
        key={entry.id}
        style={[
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <BlurView intensity={30} style={dynamicStyles.entryCard}>
          <TouchableOpacity
            style={dynamicStyles.entryContent}
            onPress={() => handleHerbPress(entry)}
            activeOpacity={0.8}
          >
            <View style={dynamicStyles.entryHeader}>
              <View style={dynamicStyles.herbInfo}>
                <Text style={dynamicStyles.herbIcon}>{entry.illustration}</Text>
                <View style={dynamicStyles.herbMeta}>
                  <Text style={dynamicStyles.entryTitle}>{entry.name}</Text>
                  <Text style={dynamicStyles.scientificName}>{entry.scientific_name}</Text>
                  <View style={[dynamicStyles.rarityBadge, { backgroundColor: rarity.color + '20' }]}>
                    <Text style={[dynamicStyles.rarityText, { color: rarity.color }]}>
                      {rarity.icon} {rarity.label}
                    </Text>
                  </View>
                </View>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </View>
            
            <Text style={dynamicStyles.entryPreview} numberOfLines={2}>
              {entry.description}
            </Text>
            
            {entry.location && (
              <View style={dynamicStyles.locationContainer}>
                <MapPin size={14} color={colors.textSecondary} />
                <Text style={dynamicStyles.locationText}>{entry.location}</Text>
              </View>
            )}
            
            {entry.properties.length > 0 && (
              <View style={dynamicStyles.tagsContainer}>
                {entry.properties.slice(0, 3).map((property, propIndex) => (
                  <View key={propIndex} style={dynamicStyles.propertyTag}>
                    <Text style={dynamicStyles.tagText}>{property}</Text>
                  </View>
                ))}
                {entry.properties.length > 3 && (
                  <Text style={dynamicStyles.moreTagsText}>+{entry.properties.length - 3}</Text>
                )}
              </View>
            )}
          </TouchableOpacity>
        </BlurView>
      </Animated.View>
    );
  };

  const dynamicStyles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
      backgroundColor: isDark ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.1)',
    },
    header: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'space-between' as const,
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: '700' as const,
      color: colors.text,
      fontFamily: 'MaShanZheng-Regular',
    },
    headerActions: {
      flexDirection: 'row' as const,
      gap: 12,
    },
    actionButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    tabContainer: {
      flexDirection: 'row' as const,
      marginHorizontal: 20,
      marginBottom: 20,
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 12,
      padding: 4,
    },
    tab: {
      flex: 1,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center' as const,
    },
    activeTab: {
      backgroundColor: colors.primary,
    },
    tabText: {
      fontSize: 16,
      fontWeight: '600' as const,
      color: colors.textSecondary,
    },
    activeTabText: {
      color: colors.background,
    },
    addButton: {
      position: 'absolute' as const,
      bottom: 30,
      right: 20,
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: colors.primary,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    entryCard: {
      marginHorizontal: 20,
      marginBottom: 16,
      borderRadius: 16,
      overflow: 'hidden' as const,
      borderWidth: 1,
      borderColor: colors.border,
    },
    entryContent: {
      padding: 16,
    },
    entryHeader: {
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'flex-start' as const,
      marginBottom: 12,
    },
    entryMeta: {
      flex: 1,
    },
    entryTitle: {
      fontSize: 18,
      fontWeight: '700' as const,
      color: colors.text,
      marginBottom: 4,
    },
    entryInfo: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'space-between' as const,
    },
    moodWeather: {
      flexDirection: 'row' as const,
      gap: 8,
    },
    emoji: {
      fontSize: 16,
    },
    entryDate: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    entryPreview: {
      fontSize: 16,
      color: colors.textSecondary,
      lineHeight: 22,
      marginBottom: 12,
    },
    locationContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginBottom: 8,
    },
    locationText: {
      fontSize: 14,
      color: colors.textSecondary,
      marginLeft: 4,
    },
    tagsContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 8,
    },
    tag: {
      backgroundColor: colors.primary + '20',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    propertyTag: {
      backgroundColor: colors.accent + '20',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    tagText: {
      fontSize: 12,
      color: colors.primary,
      fontWeight: '600' as const,
    },
    moreTagsText: {
      fontSize: 12,
      color: colors.textSecondary,
      alignSelf: 'center' as const,
    },
    herbInfo: {
      flexDirection: 'row' as const,
      alignItems: 'flex-start' as const,
      flex: 1,
    },
    herbIcon: {
      fontSize: 32,
      marginRight: 12,
    },
    herbMeta: {
      flex: 1,
    },
    scientificName: {
      fontSize: 14,
      color: colors.textSecondary,
      fontStyle: 'italic' as const,
      marginBottom: 4,
    },
    rarityBadge: {
      alignSelf: 'flex-start' as const,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    rarityText: {
      fontSize: 12,
      fontWeight: '600' as const,
    },
  };

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{
          uri: isDark
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg'
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg',
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary,
          ]}
          style={dynamicStyles.overlay}
        >
          <SafeAreaView style={{ flex: 1 }}>
            {/* Header */}
            <Animated.View
              style={[
                dynamicStyles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              <Text style={dynamicStyles.headerTitle}>我的日记</Text>
              <View style={dynamicStyles.headerActions}>
                <TouchableOpacity style={dynamicStyles.actionButton}>
                  <Search size={20} color={colors.text} />
                </TouchableOpacity>
                <TouchableOpacity style={dynamicStyles.actionButton}>
                  <Filter size={20} color={colors.text} />
                </TouchableOpacity>
              </View>
            </Animated.View>

            {/* Tab Selector */}
            <Animated.View
              style={[
                dynamicStyles.tabContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              <TouchableOpacity
                style={[
                  dynamicStyles.tab,
                  activeTab === 'diary' && dynamicStyles.activeTab,
                ]}
                onPress={() => setActiveTab('diary')}
              >
                <Text
                  style={[
                    dynamicStyles.tabText,
                    activeTab === 'diary' && dynamicStyles.activeTabText,
                  ]}
                >
                  📖 日记
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  dynamicStyles.tab,
                  activeTab === 'herbs' && dynamicStyles.activeTab,
                ]}
                onPress={() => setActiveTab('herbs')}
              >
                <Text
                  style={[
                    dynamicStyles.tabText,
                    activeTab === 'herbs' && dynamicStyles.activeTabText,
                  ]}
                >
                  🌿 草药
                </Text>
              </TouchableOpacity>
            </Animated.View>

            {/* Content */}
            <ScrollView
              style={{ flex: 1 }}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
              }
              contentContainerStyle={{ paddingBottom: 100 }}
            >
              {activeTab === 'diary' ? (
                diaryEntries.length > 0 ? (
                  diaryEntries.map(renderDiaryEntry)
                ) : (
                  <View style={{ padding: 40, alignItems: 'center' }}>
                    <BookOpen size={48} color={colors.textSecondary} />
                    <Text style={{ color: colors.textSecondary, marginTop: 16, textAlign: 'center' }}>
                      还没有日记条目{'\n'}点击右下角的 + 号开始记录吧！
                    </Text>
                  </View>
                )
              ) : (
                herbEntries.length > 0 ? (
                  herbEntries.map(renderHerbEntry)
                ) : (
                  <View style={{ padding: 40, alignItems: 'center' }}>
                    <Leaf size={48} color={colors.textSecondary} />
                    <Text style={{ color: colors.textSecondary, marginTop: 16, textAlign: 'center' }}>
                      还没有草药记录{'\n'}点击右下角的 + 号开始收集吧！
                    </Text>
                  </View>
                )
              )}
            </ScrollView>

            {/* Add Button */}
            <TouchableOpacity
              style={dynamicStyles.addButton}
              onPress={activeTab === 'diary' ? handleCreateDiary : handleCreateHerb}
              activeOpacity={0.8}
            >
              <Plus size={28} color={colors.background} />
            </TouchableOpacity>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

export default function DiaryScreen() {
  return (
    <ProtectedRoute>
      <DiaryContent />
    </ProtectedRoute>
  );
}
