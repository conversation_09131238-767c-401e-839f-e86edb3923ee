-- 添加 profiles 表的缺失字段
-- 这个迁移文件会添加 bio 和 location 字段到 profiles 表

-- 添加 bio 字段（个人简介）
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bio TEXT;
COMMENT ON COLUMN profiles.bio IS '用户个人简介';

-- 添加 location 字段（所在地）
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS location TEXT;
COMMENT ON COLUMN profiles.location IS '用户所在地';

-- 确保 avatar_url 字段存在（如果不存在则添加）
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS avatar_url TEXT;
COMMENT ON COLUMN profiles.avatar_url IS '用户头像URL';

-- 确保 updated_at 字段存在
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();
COMMENT ON COLUMN profiles.updated_at IS '最后更新时间';

-- 创建或更新 updated_at 触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 profiles 表创建触发器（如果不存在）
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();