{"_id": "bin-links", "_rev": "65-5203fee9b1c795039f27dfe9b9ad62b0", "name": "bin-links", "dist-tags": {"legacy": "1.1.8", "latest": "5.0.0"}, "versions": {"1.0.0": {"name": "bin-links", "version": "1.0.0", "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "_id": "bin-links@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "dist": {"shasum": "f71f48b2e3bbbe79865e68a9e8eef6017840130b", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-1.0.0.tgz", "integrity": "sha512-eRrGL/i19rIGpu8jMNz+C4YkFHSUTJu6Z7sCXj91dWJeC0yeeEEnmIvNpCw3wlDgXsH9tPIjk9tZc88yHSiHdQ==", "signatures": [{"sig": "MEUCIBttuZk8AvOXaDa7vjYVABa3JqM6F1yCZWKKeoIdDbP4AiEAvXqszE/6US4RV1QXEEWZ9TkCZxrAxtQVNBYvjhMGYlE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "gitHead": "e99da7ad177e677f48b218521df5b494bd2ed110", "scripts": {"test": "tap -J --nyc-arg=--all --coverage test/*.js", "pretest": "standard", "release": "standard-version -s", "prerelease": "npm t", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "postrelease": "npm publish && git push --follow-tags", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "8.5.0", "dependencies": {"slide": "^1.1.6", "bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "fs-write-stream-atomic": "^1.0.10"}, "devDependencies": {"nyc": "^11.1.0", "tap": "^10.7.2", "standard": "^10.0.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8", "standard-version": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links-1.0.0.tgz_1507340365047_0.9850793110672385", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "bin-links", "version": "1.1.0", "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "_id": "bin-links@1.1.0", "maintainers": [{"name": "zkat", "email": "<EMAIL>"}, {"name": "iarna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "dist": {"shasum": "e0a92cb57f01c4dc1088bca2bae6be110b9f64f9", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-1.1.0.tgz", "integrity": "sha512-3desjIEoSt86s+BRZlkLpBPPcHhr4vyUPL/+X1cQuE96NIlkELqnb4Yq+I5gZe47gHsZztA6cm38uMrT9+FWpA==", "signatures": [{"sig": "MEUCIQDlrr4pcQufZ9BNf9Hcm4dc37yIMtG+MsQudWSjyrg6rgIgGuUI6CJlfB5YotyCWq+37Czjg5fef8Rwjr15+d0hqrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "gitHead": "553e04318b40d2be342a9651a25ce6e7488c06e0", "scripts": {"test": "tap -J --nyc-arg=--all --coverage test/*.js", "pretest": "standard", "release": "standard-version -s", "prerelease": "npm t", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "postrelease": "npm publish && git push --follow-tags", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"slide": "^1.1.6", "bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "fs-write-stream-atomic": "^1.0.10"}, "devDependencies": {"nyc": "^11.1.0", "tap": "^10.7.2", "standard": "^10.0.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8", "standard-version": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links-1.1.0.tgz_1511222320787_0.32946322369389236", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "bin-links", "version": "1.1.1", "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "_id": "bin-links@1.1.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "dist": {"shasum": "374cd1635265efe884a2d00cf51b080045c01c75", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-1.1.1.tgz", "fileCount": 5, "integrity": "sha512-a0tIA2SEZeim5yjKZ5EJa6afsDtBLPFs+fer1fFfPjAyzXU/ZnLnlYIjAVzUxxjiowpHl0GiFBzK/wPf8QHjXA==", "signatures": [{"sig": "MEUCIQDE7lMypcbyt4NrnKAenu0OxbsVF0s41apcaXeSmXJxmAIgVkk3IC5WRODFUX1ODNznIFDozckdJD8P9mD5adUoGm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18900}, "main": "index.js", "files": ["index.js"], "gitHead": "7be31bd8a89b94d1180a35e33453fac5d6113f24", "scripts": {"test": "tap -J --nyc-arg=--all --coverage test/*.js", "pretest": "standard", "release": "standard-version -s", "prerelease": "npm t", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "postrelease": "npm publish && git push --follow-tags", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "write-file-atomic": "^2.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.1.0", "tap": "^10.7.2", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "standard": "^10.0.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8", "standard-version": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_1.1.1_1520459711797_0.025525801835520223", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "bin-links", "version": "1.1.2", "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "_id": "bin-links@1.1.2", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "dist": {"shasum": "fb74bd54bae6b7befc6c6221f25322ac830d9757", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-1.1.2.tgz", "fileCount": 5, "integrity": "sha512-8eEHVgYP03nILphilltWjeIjMbKyJo3wvp9K816pHbhP301ismzw15mxAAEVQ/USUwcP++1uNrbERbp8lOA6Fg==", "signatures": [{"sig": "MEUCIQCa4fbSmZkQZuV9cVW/vltcF751pl2zS2tzfqtZhpIZawIgWEwXT180hr8Q5xGIG/y2WsitxYdNGsp/bjbzhhV3N3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19134}, "main": "index.js", "files": ["index.js"], "gitHead": "3c7112104c3c4f68563a6b22b8abfee8ecb0707d", "scripts": {"test": "tap -J --nyc-arg=--all --coverage test/*.js", "pretest": "standard", "release": "standard-version -s", "prerelease": "npm t", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "postrelease": "npm publish && git push --follow-tags", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "5.8.0-next.0", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"bluebird": "^3.5.0", "cmd-shim": "^2.0.2", "gentle-fs": "^2.0.0", "graceful-fs": "^4.1.11", "write-file-atomic": "^2.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.1.0", "tap": "^10.7.2", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "standard": "^10.0.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8", "standard-version": "^4.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_1.1.2_1521762233444_0.20200400723633738", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "bin-links", "version": "1.1.3", "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "_id": "bin-links@1.1.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "dist": {"shasum": "702fd59552703727313bc624bdbc4c0d3431c2ca", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-1.1.3.tgz", "fileCount": 5, "integrity": "sha512-TEwmH4PHU/D009stP+fkkazMJgkBNCv60z01lQ/Mn8E6+ThHoD03svMnBVuCowwXo2nP2qKyKZxKxp58OHRzxw==", "signatures": [{"sig": "MEUCIDTCXyi4Pt2k8ffYnmxttODubiDHZZ1TMjVpcCqoT5jIAiEA1N77UMHkpn2U49CLH7qEzYNJIBZcGkJPFKITy6CC8Dw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVGS7CRA9TVsSAnZWagAAewoP/iPpCjW4EdYPuEWaBBcw\n8syzZ+O4+FoLmnaiusOaJVbW8Y9GR6eETZoyHf2HC9JSSyII/JGPFJFomIuj\ni6AduFwnTK53PPpAHt6ozIxvHcA0gBMVQSJom9AKzUiQdT99BmSCCCwhgHCX\nFZMcJy0NEwFoucA0wy2kr3wE01iqipKmts1WOBS6tzGNfZaDm7dwMG6U6CR2\nUVQ8kKikatHbg0DUCDg4nu1k1YBVVLxDGf316dOFvx4S5W2V6Of5hmQEY3RD\nqVDUB7lObmcb8OadOu72qF0UvSJFHTVEZnenzrwJ73IWRXOPJs3vW8GpCuj2\n2E1xmNFKhUOOGvmTr9eZx6IQKfMB1k87/XQrzQLt2Q4KuHGq4RNgu6v09mEg\nmiaUQ8cX3k1QfwzqmtxrxN0P67poz/NEOZ2P5SEnKJ8tozNobqH888m7J6ZL\n8muUXw4TTDr/NSHyGQHXpOjptCpqaGzdtXFyZeRdPmg3i/f/RKFuYtUQ3sCv\n7HMcTXIzOrvziWxKGTjfuMsck5aA7uQgdhOXkjT5js+DDqLfWNuhGnCS82NI\nex+9i3X6u6bQNk7Z7eeP0FTr3UAOcNTkz4kjMmFa2WN/pFQGsCUeKWFcdYmd\nYunJ8AgsF3t8HncOGWP3gETB9AdZwOgJfxF6gIzwhl0U5swGSWTgJ2Ew7Lhj\nJCfM\r\n=tPj4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "24324d3e3a8bf3d358b828c13016ba3446ce7fbc", "scripts": {"test": "tap -J --nyc-arg=--all --coverage test/*.js", "pretest": "standard", "release": "standard-version -s", "prerelease": "npm t", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "postrelease": "npm publish && git push --follow-tags", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.0.1", "graceful-fs": "^4.1.15", "write-file-atomic": "^2.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "tap": "^12.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "standard": "^10.0.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_1.1.3_1565811898578_0.8996417117421225", "host": "s3://npm-registry-packages"}}, "1.1.4": {"name": "bin-links", "version": "1.1.4", "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "_id": "bin-links@1.1.4", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "dist": {"shasum": "9570fcc0a75fcc214cd1c7972b16b43810b9a739", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-1.1.4.tgz", "fileCount": 5, "integrity": "sha512-F3lXH2K06zlFARLYA5rnJQb5i74/JNj8wIW+9UJQbuwyL62TUzPbMlnHHp/JxSAHfxBFx41aeY7SQd7Z5hIirw==", "signatures": [{"sig": "MEUCIENoFy++oSIilXiBrieW0h+/RWlMb4Rkk0yyCM/zOPBOAiEAonpSejufx+68eN0oKg6DyVNsT7aePyTgaAyFrayP+D0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7tQKCRA9TVsSAnZWagAAJ2YQAIWppaLPs+iJrNYPN1KK\nreQEO7JJB0p9bYDwRe1EE/Fvx/qZnUBfFUUYAUaU5z5DFcOtTcesGuiAEiIo\nXA4WJGWtl1UV/co5jfOmW/UdQx3Y/ihzNX77BwI75ig8R4PZj7TVKZ3HfHjk\nENbe5TDfneSa03IE7djsvHzvzSs5jWqOwoHo3DiWQQV4fvM+QFlkmc9PSNpo\nhdm9zir8YmAP+ZIpwWNZjn2+e+VTkpoUrbgCv6kGRzFPCjGdUMTMyxmdwtud\nOebWgKcxGXbmaYOsrJmtFQNGZn37SySkSeRy8hRBQBfp2FcGwFnZU+Yk5Sqq\n/vAxioyuyF72tDYK+JA8W3qWEt7pPnWs5sh0SVAAinyOPcrl+RSWXexS7osA\nJeSEb81fNZKWOJ0yJ2lKD5BzuH0EFCqX0kPO2TCPCgH3ySwhI/bGZi+snP0k\nJJ3Rrx2q+ieCvo2QH/dfLldvfYs9fUhJ8LKTNne9p7Wf57fR2iOmphJ95qcT\np9kmAe4QTkpRFngu8AwAPP9+uUMIktPfKdAhFyAzic9XArTL3u8yboVniecV\nInbQS8R2jXjfPc7OurOEmIRmqA6O+ECuNvzmPyDpBTv05+NFngxPnPaCaF+g\nUft1Xhw3pRmlGae4CswYVq2QbxTra5xc+u9YxQeBWd43gJsjqpjPBYhnYImZ\nScnO\r\n=sAuJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "fe39e10e447c395fd1a30b5ce20b1ddf7cc37dd0", "scripts": {"test": "tap -J --nyc-arg=--all --coverage test/*.js", "pretest": "standard", "release": "standard-version -s", "prerelease": "npm t", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "postrelease": "npm publish && git push --follow-tags", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.0.1", "graceful-fs": "^4.1.15", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "tap": "^12.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "standard": "^10.0.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_1.1.4_1575932937875_0.5722673211812359", "host": "s3://npm-registry-packages"}}, "1.1.5": {"name": "bin-links", "version": "1.1.5", "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "_id": "bin-links@1.1.5", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "dist": {"shasum": "bbbcd1d7a8101105b927d54aa895a4cc75138169", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-1.1.5.tgz", "fileCount": 5, "integrity": "sha512-GQuxeiclIjomNfQ7LSaERfIz2RSZrF44c0cDEV+Iu4uVhyYndSSwl1RjjunxHU1dYqh+QCP4S/1/DUsjquNRhQ==", "signatures": [{"sig": "MEUCIEpBoLL/TLlmnGw9h4nlbcCOVMuUw5CdBbax/zrOZXFXAiEAnTpaAGVd9BAMuXUw0hXWZEBPaOEIxGVXqf1hOpjm43g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7ubmCRA9TVsSAnZWagAACW4QAJHktTYeThBAgtnUGnuP\nQDIpinMBC96ysb308Bm/Uyp/USMO0H0lXdZJssIc2mfFGPe+XM3Io4WBdX4n\nZ1vWawtsJ037uIsbcCqp4VBRedxbS8Ne6jKhaWSQUlywxZ8meOANayjiDZMF\ngWl8P5N2Lu5CiVUyPQsQsjEBT4sMaj88ktOwTM/B8DYKl9LT/nHnLEf9XJWg\naEoBlZ6TPvavUyJQAheak79H1phQluF3LgCgf/75LvIACSyRLUBKCg+vKIyL\nOG/gDIe66bBIVxKaJ8Re37J8UbCkTxT5bwn/ixj2s9jl407GF/RxdhsQ0PtA\nfE1us67vEdYA/OccCTXehwQgdi3tzrIcBvjiohFHSCL7qUQx/FP/UwJ16KFO\ny1EoOuo1V0JNg3OZ0uFiX7eIEB5lzGGdVE2SaQqd83BN0KMwJjLkf62PDnBR\nL4WwaR128RVCJGz6VXL9Yw859jiBwsZLUy29BUB/sHOpE0zvU67VfztCwX7I\n3yNz5tWT5IAD6p4KV1Netyvwb+duxmEUurDcteGi3FZNDCyVs3FrOhy0gIlg\n/YoyNoALZgNM4WHDu7gxqn71FoKPYC9TNDaPRY6SMLn4PTsuHWiGaDs4cMTw\nIkaVzJWbjie9l66H2Kfkd2boK3A9LZm5Jw3XxTc942MFclltb/k1EUhT2zd4\na7JY\r\n=nmXW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "52e65255ea873d875a6bcef43d288fe2646362f0", "scripts": {"test": "tap -J --nyc-arg=--all --coverage test/*.js", "pretest": "standard", "release": "standard-version -s", "prerelease": "npm t", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "postrelease": "npm publish && git push --follow-tags", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.0.1", "graceful-fs": "^4.1.15", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "tap": "^12.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "standard": "^10.0.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_1.1.5_1575937766306_0.6836856166200178", "host": "s3://npm-registry-packages"}}, "1.1.6": {"name": "bin-links", "version": "1.1.6", "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "_id": "bin-links@1.1.6", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "dist": {"shasum": "30d33e810829305e5e61b90cfcb9a3a4f65eb516", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-1.1.6.tgz", "fileCount": 5, "integrity": "sha512-b5rV3uVyrlrJWLI3mawUUf5t2f9mCEQm/TqT5zNj6DPYhYDZaNp0AYaYd/CVASkSEklayNDLliZHVdo2J3niPw==", "signatures": [{"sig": "MEQCIAMG+nSzyLcvaftuG/p/xyWCMMpxjI9bLlLaKA8hlIG5AiAEei654BON9XYPLXCcy0g6vuX/tW1IfShuoGtK5ETWJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8ToiCRA9TVsSAnZWagAAGZcP/2Lq8EL0yjGWUl0ve08n\nQe4DdKTK8om7EVZqTgncH5HGKr6dpPcDaEhCcDT7YiSQ6cumf2Z6U/ZwwnKC\nv3i9G8nk+LfXpwzZp4DswQ79ST1lthkurw7EUgSzQyGa+gJ7ynCY1+zGJZhM\nZQ5ZnldkG6l18AAgrC3nNUqc0i0UX5HT5BEw2VJXm+xQ+BhMzldQELn/4Zgq\n9tW7yRgvkAuw3jUu0ZtWbg/pc8ri4Ck7cYrnrcK+Z8EQ+0qpAU9W371KcYZG\nmpjwDr4cTS8iJq7s+Fm0UjGmYVcpJ0/uHXByKiINS2QG4//0xVpiQkGH4diM\nZP3pdLp3hM4KKHbAC/XsVZFXU69yjT2ZtWx/vSjv+J2ijotqdGD49N1AcjeW\nteqqgnvgsJ13vZ9H9smH49wIfl3tKdfQfTdu2ItlfLfgRK9cl4O0x7MiQnUF\nRyRlp/5a5teMYsz1/CU3FEmhUHnH5o0t+MOSYL1ynF+8dWhVuVIyEHat9JY/\n1dR/G6r3tSLEXjO1sJSaPnrwt8MjeZuEn/vP0ZH/HIRBETHpiR9AV+SngaA8\nMW2sZ7OmKiGNZ8/jZ+v73iZblo2JK0z/DUHVXnKre9LwgZ/7BKQ+HdoS1V0e\n+ZNFzuW6GdkmqcsiSsp1Y14xLqR3YFh05l3eNV8xjEMnVfBPvWN1ZinC0/Ej\nEZgr\r\n=vFnm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "f315830130777dc0cae0c020a743b31b14b598ec", "scripts": {"test": "tap -J --nyc-arg=--all --coverage test/*.js --100", "release": "standard-version -s", "posttest": "standard", "prerelease": "npm t", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "postrelease": "npm publish && git push --follow-tags", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "6.13.3", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.3.0", "graceful-fs": "^4.1.15", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "tap": "^12.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "standard": "^10.0.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_1.1.6_1576090145624_0.9799907445951463", "host": "s3://npm-registry-packages"}}, "1.1.7": {"name": "bin-links", "version": "1.1.7", "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "_id": "bin-links@1.1.7", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "dist": {"shasum": "34b79ea9d0e575d7308afeff0c6b2fc24c793359", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-1.1.7.tgz", "fileCount": 5, "integrity": "sha512-/eaLaTu7G7/o7PV04QPy1HRT65zf+1tFkPGv0sPTV0tRwufooYBQO3zrcyGgm+ja+ZtBf2GEuKjDRJ2pPG+yqA==", "signatures": [{"sig": "MEYCIQDNlLYs/Sc6kvb9HgX1UQWEgSfz80Z69Cfwn52/EdKpXwIhAOG56inAEUTRqqixwGI/i3sJBoHadr+efs4oKGd9ENIj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeBCIUCRA9TVsSAnZWagAAe5sP/ig5DRMHQ6vA29EkDqXK\nP32AlxtZes3AuShq5fiiYiCsyu3/aoA/K2bJOim08wathaYzc+2s/xDjVnzc\nB0BhRQDU/pf+sppdsN2mgDamiH4BbfSM8qEpSXlgm9IIDXkNwk2TGEvgSNO0\n8iV455Zcqb3gww3tqEb2j/QuNI/RZ1Bb4Zwf/95M9rLhbnTo56RC9sdXBems\n0AzvhbVU6YPS13iWSvFFUBTCF0rFn/OT1zOLTPdy1teVE5Nf7sfoHPs8Tgbz\nsAVbBWSZxBl3+BUHMfs4giJpZaovvZrO1ZvPTyGIAq/jQNz5nI/3+nyEehIk\nIgFpcS7tU8rJHpJyW9Pb2FENYVo9tliR0dtYkW8Cx1PbUroPaYNamdr4XQy+\ny+qRNcZcSeWlIhP5TntrpH/OXWkJbpxIEbKZcfFOuu545iOAZAOJ1GVo2G9P\ntQN97VHjXH4C3CF+n/vmU/GH2OYQRW+it1E0EMdAuFlg8oA+/Ylu714sKYfW\nKJA+K/mZBMY6uFutOEaKVx76SgHcfXH+jO1zKXzIq1ysBgQj5FKxILP5+KR/\nrhFqNgsilaeyUZrazfA86JNtmXXJ9GqHTJW9Lkv7Wmi/vkWq9hq8+aGdNbtU\nW/NaRQkDQr34ncaoim7t0Ihxde238tIpCkH6b/87c6BBeJuCxC0m7ktIKcse\n49aB\r\n=XiJh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "32132c9855f5ad6d7abad970ed13e8c9472007bb", "scripts": {"test": "tap -J --nyc-arg=--all --coverage test/*.js --100", "release": "standard-version -s", "posttest": "standard", "prerelease": "npm t", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "postrelease": "npm publish && git push --follow-tags", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.3.0", "graceful-fs": "^4.1.15", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "tap": "^12.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "standard": "^10.0.3", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_1.1.7_1577329171959_0.2291732676844116", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "bin-links", "version": "2.0.0", "keywords": ["npm", "link", "bins"], "license": "ISC", "_id": "bin-links@2.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "7b281e1f6c2238ae809589490c2d61380c027a8b", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-2.0.0.tgz", "fileCount": 13, "integrity": "sha512-VvBk9YwChMjdn9rs1c9Yt0vRwbZ0SgDBsb9pT07DAmFvuTlxqAuazKtIa4be+6ChBGI+io8gYjT+vq586foYaw==", "signatures": [{"sig": "MEQCIF+LYQgBNe5j/F/UAieJQ0/ZTkE+iFpofMbhawPIbAvBAiAWkok/BxWck7oFu/Sxe0JbXdOfCJd8b9v4cunZNToi3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeM6k8CRA9TVsSAnZWagAAGP0P/iDfjevL82zL63FS/4F4\nHUqwFs/5PMGlfUTBYW4RpIBCErUSyvgHGe6XbM0vlE3dL7d8QMNOWPs2zryI\n0MPoXSh4qmPkP0DSLOf+8t1hkJp4e150HFbFXyAndQs4YnnZOVRr3Q3/I9z8\nk34tchNekfxg9fMUMNoOPVttIFNreVsfP3hyg6I3KRh224+GCw2fh33IzW00\nYrRfCBlQ/EGxmTr5UfIxf2SNDiJz+fzu+YFBdE/Kc+Rn0RBdWsAmbwdKv+sM\n5QnxZOK9ZzGCx6KPuwumSQCAtx6J8ISCvFeuaw+PSUfY1JGn1FiCP/rEYO/W\nEULEJWs18YPyrDVu3SX3ZfvvtevjW01EF6bGljPLbaIpHOoVQzMJOX3Vao9Y\nC0gxDiM9+V26YhyNNDT8pyRhHwCkXOmq2tGn7AU4XFS0yF0WSx7s7t/iu0pR\nXeHEmliHljOQkmXh2mhhjSzuq8SsTNxIWb+bSjUiZIsCWYaUr7NrlQs0kloP\nWrWEwTYuO9gFboQXq6/O9YAalMQx6m2+cBAag5PuYEIZ3+pliPRUCny+kaUc\nZSiA5kxZG7b0t7bAnKntSqIV+P030SYDOSOd1N5oq/bxBDzrK7RiUh+LKlUS\npZyPwZCCFB01MGiKNFR0iwKU7+q4UoUkaBFsJpRqG3Z31oz6YnmG/u3J4V10\na8kU\r\n=NTXR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "ca8aad242a50b9ac92393ed17c1a34e048690907", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "14.0.0-pre", "dependencies": {"mkdirp": "^1.0.3", "rimraf": "^3.0.0", "cmd-shim": "^4.0.1", "read-cmd-shim": "^2.0.0", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_2.0.0_1580443963761_0.5179872418786688", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "bin-links", "version": "2.1.0", "keywords": ["npm", "link", "bins"], "license": "ISC", "_id": "bin-links@2.1.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "b26bd6c0eac564ed7cc8e26199aaef2b070a5549", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-2.1.0.tgz", "fileCount": 19, "integrity": "sha512-hIZrZte31Aj+dcpOIlIuEbsBGkHHAtB8xV3evHloPkA/JUGW73w1GwG+a+XJlUNk22uS6+twqb8i3+eLp6KgBg==", "signatures": [{"sig": "MEYCIQD8zqh3WHbJKdvRTSn8VBBB4J9ZfqEp4Gc4+hAfEnRijAIhAJoafAX0t3nr4raoICiQ0kmN3tCoKd2nUVX9tXwNKAtL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUXdiCRA9TVsSAnZWagAAvIIP/3EG3C1cA7LzB/Ffb79G\n3GyQYeyzpW4qVKUIja/85OcLVulvCTmtp19n6bSYyTtRnxlNfDnU/S22aoli\nYTRZTuRxVM0sQ1rTEGd/r8a7OdrIWe8R2lHJsFZ55JZdywEOmgtTENPqfBAF\nI1GEz85trMSv/+2QRBeTmxJ5JGKAfDOWcJEZFZcEZ+B0jkpsRP1u2dxAl8vb\nTpZ18UnDt5M9qsOTFx9M5BXE/mkHZRbPZMw/NmqBcvl3rkbzXZQOe31qzqSt\nIniXg/5Y/9IBK7aTCdPhUcM8F85BzXhLCCfJ73Dz5M6m6UpdnW81AKDiZyCR\nchvBU9ZxovdGCpAzdA7po4fR2XFC78NSRfwwgkXyOktOxnyoMsJksGtJXvil\nqp0nm02fWv89g7Fw28XMs+psyrwzTIwTQH1UznksXh/XoqA27KOWs/D2/YKR\nN7h2KXFZL26Nt55eG+cITQXh4K9GXqkQZ+vn4sakSqAhzk1TvTlwA2JMtaon\n88IlGc17fUTgBxT298GJbLLgML4+/C7QlEF61W8gGtY4MCXCWLVGEX90VvFC\nnWC/Dlrbj4oJhElUeYq8PToluCY3TejG8HtFgVTDVgOp2pkB5oouCvZrpsk4\n9q7p2NU+aXH8G/JmcuFFBm3ZqNPiuse37iOgpB184Ad7bBpZH3h9FMmo1451\n/pzJ\r\n=UqXR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "ae52cdc6d64c284b00365d996c0b5599ebaf0a1d", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"mkdirp": "^1.0.3", "rimraf": "^3.0.0", "cmd-shim": "^4.0.1", "read-cmd-shim": "^2.0.0", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_2.1.0_1582397282382_0.2446218163487821", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "bin-links", "version": "2.1.1", "keywords": ["npm", "link", "bins"], "license": "ISC", "_id": "bin-links@2.1.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "22b354faf96b2a312eebb2ac7fe509ec0fd1de04", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-2.1.1.tgz", "fileCount": 17, "integrity": "sha512-CmKACOnH4T2T0Ja8kd7nbGyU6MqmK1IJoeywgqV8j2FAzOPrmUpDD4GNm0rPqoj409uxHtkP+puujN3Lfu+1Ng==", "signatures": [{"sig": "MEYCIQDTh123hZ315TfE7ZM/0CwUmFRdzt+It0olw/SPI6HjLwIhAObeMjF4e5OtWP65Us69NCXO3kBqWedlqfoLIfr4ZQvs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUXemCRA9TVsSAnZWagAAAAAP/2COYvQbJYyI+6ELVtml\njfH0BKrBFGa67DKfNj0IBiOfZSjAvCQrSkrfZ0pzRg5nFl9/Y6Y0MrnOaMe5\niW6Vtcq5nWwwTp+aVtcLgf6xjDkHa29it/FsPcWCYknrsRKsNltb9pVS3vvu\njYqSqNdXcwPd4ipKjZ8ceyH4vOaxOhY0bJayhRpfTcYAR0KdZ5cRALrf/XEb\nSsjYJjKDI9q+nrOOSjP57N1qUT7Pw5xm7YnVY1YjlaIsEsVUXJJkWj8ptcIM\nr0HQfj9Hwuxxo2l2Wvp6wKOlDVsA0SpnWpk4lOrrgysGMPLZSwAzkOTUiFAA\nacVE6qX4OAamvZVmoFrto0W6s9ZX8xB6dC13EGxEoqHmEb7QkyNf8dvBedqG\ncltpN0Xk3/0acj5bo51zf+k/urp6GPofSu2vqXWQKcC+LeWVGGG1O3lPK7vO\neJJrRnfh9s2Sq9Vpl4PsBkDBj3mu7vJ493cDDGNTYHD2R3R6oZTLo67lJCxE\ncJzBEOfKcZ6jv/e42Mhd3DLRCgZHBUciNbcU4pqkNX/JF72I19LMuKhIhc8R\n4FIsh6O2suTPbAICRj9TnpZ6wwVmCjou/8JccfMU2/3VmsOnplf8RkF/xi7S\nzo9dz9y0Ah5J5Vqqt/CfD0pzXu3gdw5vWkzRKK92/mesvjv2iPeFzeoNgSN6\npzEN\r\n=NpJe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "57a9c9c90e9cb10ceaeceb201e1766f896161656", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"mkdirp": "^1.0.3", "rimraf": "^3.0.0", "cmd-shim": "^4.0.1", "read-cmd-shim": "^2.0.0", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_2.1.1_1582397349783_0.06977994899571205", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "bin-links", "version": "2.1.2", "keywords": ["npm", "link", "bins"], "license": "ISC", "_id": "bin-links@2.1.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "110f82e5563caf66c4483c1eb5d84043ef571660", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-2.1.2.tgz", "fileCount": 17, "integrity": "sha512-mtspzc/umzMiZkIRIVm93WgyT7fFVXX302qswWwlHErBVM8BgAVvB0EVOdzmtrNzs1zIKEE0dE4RR5PwtBfy7g==", "signatures": [{"sig": "MEYCIQDsiZNzh2BQgrIC36DTR46vQjhNzQZjNPdoIoIHMgbmzQIhAO8RoDFZJdHxG8x6n5PzJQbb78Y9zT4vxt3B7hm6i36N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUaINCRA9TVsSAnZWagAA5aIQAIzk9KPKOwtxhSoP6OAn\n0BVGxUPkFk2tI/FAd+NcopTfMrdZckGaIu0mu+35dtXIoir2nfPxzmPBZER/\n6uC6KnRLWcGrmHlqHvMNY4WBHnC7gtguwetWzi0Ps5JEfUWveD07jSU1gn42\ngscTNbgC0v4cuUDqctdKkgk+/im2gFYAemWhqpq6ip2GjbaAB2ANiEEHIx8F\n+YRxdhdgbNYSLczwLhShaUh50g7frtTbyQE9u2SF6jPxVy5lE0XM9K0xhQKo\ndJCNUR3F1Xx8P/yZtkGg19WZ+W2eFD+mOAlrzCGCliSoGRwdHezq2yfZMp2b\nMgAfbXTTzrpIHX64f18TIjN97aQMt3zDCarTNe3akuqURb+Mn7MAAo6q4Reg\nix1UqinAYO8yAXD1q+/y2rtgMnsuJPcmFnkNCRYOISEyTJZNc/FBu6QTedlZ\nO1Obr3OPH0v3ba4bVt1NsbE1Y9vgdlAMsFUzN1F3fu82V5IZMl5Dn8SRIC8u\nikpvlAursZodwCVP/IlxQgtC4kwJmsp7ts9/UFD/wmAfAhoh0k0FdBsM+9YH\ndViBVlz0E7QNXtZz2P+0BeYvruJUxDggEEDu56uhR07onmdbDGhbUdWcbOi5\nhGeCfw2sJnvhz/1Yoi5B657eYf5epSzBRJN54VIsHCSUf00IOmBU0N42AuCr\nRABV\r\n=19Eb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "26f70cf508c3c1130d9eed31711d90ff6cf291fd", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"mkdirp": "^1.0.3", "rimraf": "^3.0.0", "cmd-shim": "^4.0.1", "read-cmd-shim": "^2.0.0", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_2.1.2_1582408205351_0.6082970235146452", "host": "s3://npm-registry-packages"}}, "1.1.8": {"name": "bin-links", "version": "1.1.8", "keywords": ["npm", "gentle", "fs"], "author": {"name": "<PERSON>"}, "license": "Artistic-2.0", "_id": "bin-links@1.1.8", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "dist": {"shasum": "bd39aadab5dc4bdac222a07df5baf1af745b2228", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-1.1.8.tgz", "fileCount": 5, "integrity": "sha512-KgmVfx+QqggqP9dA3iIc5pA4T1qEEEL+hOhOhNPaUm77OTrJoOXE/C05SJLNJe6m/2wUK7F1tDSou7n5TfCDzQ==", "signatures": [{"sig": "MEQCIA3PcTJ3MhqqvdcujMS2npNCqGOWzHFMHat1Ie4BaOn3AiBkcTNc4B/KM5IMfpZZzSqrXeM6+NxmafrwuElB+i4XEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeVG0CRA9TVsSAnZWagAAy1wP/RukWn5nxFKs80RefGre\nkB8oUz7VNZwBnNd+fp7Rpq8bGBH8NBgzZqKIHdjK+wmRVyGwq7C66+xBltup\nnwD6R0aMA25ZyHbNOEzWALYHcLqn3LFyFD9XefySmdP6xUehXdUzlKC3foVK\nL2FWTIw0oyCD8hZ7Orn+lox5UoxNlNgRsir0rcGFP6lwK8MAaK2yFjDgghAn\nZvNUqoR4cc+HQ+dlndOfAq94QMWdMIcIb5Eg0G6YMt4p2Su1spt5ZpznUpCB\n/2vcdYdIE+CK0vGu1Bhql8YXHRNc5y6dLbsBkHeaa7kCklhtCcmMo4UglzCh\nvYjYZPee7kJOVTKILgRiuOTdSQdrM+goVFALYCd+YVTzhEVu7AT3N1RIy//R\nNZ1fl6VRDWY+f8zB5mXbiPsIaTiW350DwKfBBFFw2U29RjwQVCP24g9wuFmk\nxKcz6AinOlmfkUIi+MAgalw7gRoLbDdfxs+Ymy+juUuTzwn9ntLerXmI0NJ2\nLG6burs90WN038h7F+ysvNTHYUu2bm0TMQ75rH8Tx2GkA+dr55h3twnkZw6z\nWAnnvnFFA/NYmRZmpm2LJxh2CzegC3I5lODY8pX+ppAfUe2C+5piua98moxN\nYENqZ0AbpbdCtkp16LltCYoc/0hjfFf5PXhmH4d50DlA3gJwFnomTAsy+qaF\npQjM\r\n=uBeP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "961e3bd65c75ec9a2df05c9204963f115fa7e2f9", "scripts": {"test": "tap -J --nyc-arg=--all --coverage test/*.js --100", "release": "standard-version -s", "posttest": "standard", "prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "13.10.1", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.3.0", "graceful-fs": "^4.1.15", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "publishConfig": {"tag": "legacy"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^13.1.0", "tap": "^12.1.3", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "standard": "^10.0.3", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_1.1.8_1585009075582_0.17092406550651673", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "bin-links", "version": "2.1.3", "keywords": ["npm", "link", "bins"], "license": "ISC", "_id": "bin-links@2.1.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "my<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "ec32f2335f2cbce716744137af1cdb6b0b1290fd", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-2.1.3.tgz", "fileCount": 17, "integrity": "sha512-SPLS96y6+liCdMYz7Jpfe/zhxyPZmk6MqOUThXJLhe2/F3rnJGehG8zjphqV3ZShbpudGms4X92Ue94gUW4M1w==", "signatures": [{"sig": "MEQCIHPzEZLZuhYWzKkuwI9usEvxiscQwtKI3T6EzsCJ4K2cAiBCrL3G5DuJBFcCIRn1MbLngLOT8UNs0jp0Mzw/mKSmLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKJvOCRA9TVsSAnZWagAAEKQP/28ZxeYcfP7LblrfFeVv\nvyfZAJRA4pdOq/gsI/lvbreS0tFV+2MumTFGVAdCETyqjC6RdJRtmJvk1Mwa\nxH2o7fpbZ4RZ89d3Dfn+p87Y0zuPKS3rXPCkIPJZTsAEaH5HWzfYoRBs5gtU\nSLVGoLdDFmR9ktpi0LFs2SuvxY+5jRrXWOmdnLIycdnle/OMbBEZrfRM14uu\n3IBh+gjMTnWYL0J7uF4CeDVZ4sqWQulPbZeeykrwlhHIxy9hlMGUGOge3JU+\nZq/7fTbCHd8xOuWhTutyRQ8Jw2DwyZmoiF5/lVfKdmt+y8Zyp3rYzDw+DYsc\n+WNSlK7LpptJ2EHI9bBLZ1aYOmhMxf0MOMr/Aa1CVPrJAOTAG7qwrx2+bP9g\no5My58vtMCD939sNeK/5z2oJtXnLJThWxJYwm3WvAs6eDiDTdNdN2cirEvF/\njx+KSZ32hKyp2MnEK6S1ylEqEBqcSmhkgb76Cm3noXgAXFsmRMmsRl7n1H+M\nzozHyHWsc3tgTYZwZ7xl3mrn3ETKKG40sAWDT127TZjMHOJFVxmt6UhHuifl\nSrpUKEU4bIpvh+CXwPiou0bwwuSdfLLUvQLlVnmRKKIVfXki7BDRwEfVUz2e\nHkrruEiR6ftVIC/vtwioM8MigeQ8JdkwMuhcfibrqE/kXExZ5QPtpW7Gw/fK\nn/cT\r\n=IC7O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "cf229973bbbaaa766bfa5484a3584473f9061885", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "7.0.0-beta", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "14.2.0", "dependencies": {"mkdirp": "^1.0.3", "rimraf": "^3.0.0", "cmd-shim": "^4.0.1", "read-cmd-shim": "^2.0.0", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_2.1.3_1596496845623_0.7291094317213467", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "bin-links", "version": "2.1.4", "keywords": ["npm", "link", "bins"], "license": "ISC", "_id": "bin-links@2.1.4", "maintainers": [{"name": "my<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "f2fb5c9f232c1e3e301aabf5337f9d7eafa95d16", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-2.1.4.tgz", "fileCount": 17, "integrity": "sha512-NIi7OWd1FelUfgNERBLpgSaRbbm6+wkMRfURuvfui31N0i9jmQYFXhPxL0d8rcnnbUB2Rw+DiCY4nU1Egdrugg==", "signatures": [{"sig": "MEQCIGzuzsys1WDWnd3qbErG66aKU8QAkKSLKkcO0BNykobjAiBMOFmwvib9D8jeQNeB/Shg7pLVbEilJJQUWZOwht3jlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYqGBCRA9TVsSAnZWagAA10kP/2IpxHrScQ7FSIdFvDB0\nEE/wWTQcSC4VudvLBssXDuhJJAYjzFeaG4S0CYh1idFFUevq6J8CwF9Zwo0x\nEUWIgPLxjIH5sSJ06dRBwqJb4xOMUyquFYlj7oKlhlHgTjBDEPb/3lfy/iF9\nZdXmHBibXEuK0bBLA1zIW4U+ispDfPm5u8S3VPtaPnEC6miA1vxi+wIpxq+/\nkQs99H+01pxTQ+hx/qYj5rRWupoRuaHb400DcOCDgECdNMdzgNr5WejPL6z3\nABzFuT+e06Nb+5tMwg7bvWc9tFMv9aG6/G6wM9Z92qqd4Qt/NbWXhTazgWfi\nHhkb+R0oPjs1HyBrhhOoO1CwGjck+1q8Ga6oxQs9KWAlqkCcXQiWB/cgrsz6\nWMow5akx7WfafpyvlcdDlGz8VEcI77BAWf3GhxyHML1UvMlJW84F/C6+KEp9\nut26boS/TWiQOG2gwiUP56YNg8xB6LwYlO2xy8NtV2hxqWFFEkfxB/3aWNqm\nfzQJOVRWtOf8nuI9K3j+55hr9paQg+eQZFJ8FnzQ+avMipY+HPrxVA9No1Jj\nrS5ziTZoYT8CtnmGXxGNjsZeKJLELLU7XVa/0R/3Mb749WJpdkm3JK7U+CCz\nL1+PZdU7zEDL0wA5IvqoLpbSJg3KewdXaWZm+QuDkrOyNUydg3Z4LTdjfLZO\nhwvV\r\n=nwVF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "6c8b04413e4b29de1f5cf86e2d74ca5bc472ce7f", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "7.0.0-beta.11", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"mkdirp": "^1.0.3", "rimraf": "^3.0.0", "cmd-shim": "^4.0.1", "read-cmd-shim": "^2.0.0", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_2.1.4_1600299393442_0.10872363729624479", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "bin-links", "version": "2.2.0", "keywords": ["npm", "link", "bins"], "license": "ISC", "_id": "bin-links@2.2.0", "maintainers": [{"name": "bonkydog", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "1d8478e51a51cc00c1ff342565c4d697aafaa442", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-2.2.0.tgz", "fileCount": 19, "integrity": "sha512-SmANP2yFm/fWCW/MSLXS2sFKjmQakc0gyQbUbgpf1jyr3qg+ep0FUgfJpcDeiXxTZ3R2eGAzcTf/mCsIes8+OA==", "signatures": [{"sig": "MEUCIQDASB9fHV9hC3iVi1gA0RalghqUQBpV/dVqgU5SwMmNggIgBeLw/wy4HVRcswgQkbz8vKFBk1ZFpePCqexF2K6R0DQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdLsLCRA9TVsSAnZWagAA2A0QAIpXyR2rQhq94/nVxKsP\n5saxmkgcDiV+OOY31YhihgtAun4yumhiJmwTwBokKzDP2K73BRAC8QVrmuk0\nsYuvJ32u6Rp+ELlblUvd6GXwxjMwU3XUClo22QVfD7Druzh0ycOsCVLY/Jfd\nyZkt7g+PM9IbsRGCD85Gj4DChD7+a76euq2UoR8CnkqYLrbmXNUmvt5EmP+e\nw5j5YGKMRmU/sRIadmoM4HoPbwiK7RSihRT9Ze4rIVUGDdgcZ1xZIgE/ql9m\nBDLBB1+aa0Pxcl+hHaii4VMkeGqnIu1uJfJ/TjcQOpgl3CVs4G7y5srCLh8W\nq4OW2m+uTNAk6Jg0enmHonKsmr4u3WjAbRCNb3mPy0qC9T7Z/yEt7c/jfNmb\n+Lc7AdORWym5seSJAT0FEU8RFsUa90z4s/57wB5FcwNoO33VF09+7wVNAr9U\nCYLYVScrBnU3A6hco2UKHuy0ZFG60bVoNY9LNHr67+fmQPURWKvpssxGAkVF\nQxUd/8MDQiaEeBrhbO6YUd05ZM8s8I1l+gXJHffY06NTfEIemW6DUkjJLX82\nf//OVugMmTpgR0fgEbVk5fTL8IJdrrHucVbhzP2OqIIQkgIoxx+xbFgobz3d\nX43VlbXgW7dMWQ4Yr4CtBl/qgy3ro+TIGx2BgOoZMB5dbYDOgS9DTp0sMnVs\nxBjg\r\n=VsJy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "6e100749521643409416406bfa913e481fec27c5", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "7.0.0-beta.13", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "14.8.0", "dependencies": {"mkdirp": "^1.0.3", "rimraf": "^3.0.0", "cmd-shim": "^4.0.1", "read-cmd-shim": "^2.0.0", "write-file-atomic": "^2.3.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_2.2.0_1601485579189_0.9860213653509886", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "bin-links", "version": "2.2.1", "keywords": ["npm", "link", "bins"], "license": "ISC", "_id": "bin-links@2.2.1", "maintainers": [{"name": "bonkydog", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "347d9dbb48f7d60e6c11fe68b77a424bee14d61b", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-2.2.1.tgz", "fileCount": 19, "integrity": "sha512-wFzVTqavpgCCYAh8SVBdnZdiQMxTkGR+T3b14CNpBXIBe2neJWaMGAZ55XWWHELJJ89dscuq0VCBqcVaIOgCMg==", "signatures": [{"sig": "MEUCIQC5zJs66fs/SSOGeHRlcByS07Fvz2D+aen00LPzBsWC4gIgWpFWwjpVgYifMx4HahoHnhyrpyrklRtIvF+DWM9E2jM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdMqcCRA9TVsSAnZWagAAjPoP/iLbObM7fJG4JSMUC0PA\nApjRJquSDoiDO4yQc8Q8wcy5KoHKhTLCIEJq8uXg8l6vHAGHg4syF1ltIsJy\no9XY0O6/77cYj8fePU8LOU67z6da1ZfNFhNQoxygAXKXuO9GZwmK52ClTNHX\nGPLkvW6i0oaQMBt7np/moxPzi9EtaThP2OKUALp2v79q7ZwA2K2ry935GEw6\np3vzqhBMUbrxBSZ9fqrMQJMJ5TYRcsbPyhx5ABohZR6D878pFlke9LM+7vKM\nf1+1OriLeRUzhCfbcoqNZ/laZraNvXNsML7PLCdSTqvYQwMrGMo2LnSn3hxA\nAKWQKAb7/l+yqL9XWZNapwOicAVDfSFAqv2JPL3AoWANGdyHXbX5nc0SCtvM\nBsTQMwbTcRtIpefX+CtmkQ6tTgzSRWudFIHvk12tNcm2VXqSlV5Z6bMAIVeP\nUl+y0ZWiLRvr4+4AcUcmx/TvWZz884DrloXaH6nkAagb1w11Iv8QySEp9woP\nrqlmVEIqxo/QqmyRRWA0cAc4CccJO/eEn8u9VVXlPalhVOvMVT4HW4jwo/i1\nukG6aDNlyfOA/lAuaIFl2w1WE1F0iNtdxhsafvHaGf3PF+7qslXLqq09L8do\n6A3xlfJXKbKb5jgjWcshQn+gpaFkINR2k034ETDQXVg7UV6Ju3nyRJtSCoNT\nVQD5\r\n=MapR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "47e3e535d595efb9cdd110f5e6936b68f34c4f60", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags"}, "_npmUser": {"name": "ruyadorno", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "7.0.0-beta.12", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "14.12.0", "dependencies": {"mkdirp": "^1.0.3", "rimraf": "^3.0.0", "cmd-shim": "^4.0.1", "read-cmd-shim": "^2.0.0", "write-file-atomic": "^3.0.3", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_2.2.1_1601489564236_0.20081205462657192", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "bin-links", "version": "2.3.0", "keywords": ["npm", "link", "bins"], "license": "ISC", "_id": "bin-links@2.3.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "1ff241c86d2c29b24ae52f49544db5d78a4eb967", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-2.3.0.tgz", "fileCount": 18, "integrity": "sha512-JzrOLHLwX2zMqKdyYZjkDgQGT+kHDkIhv2/IK2lJ00qLxV4TmFoHi8drDBb6H5Zrz1YfgHkai4e2MGPqnoUhqA==", "signatures": [{"sig": "MEYCIQCFvJVQGm9d+2+RcfbXynDuME3x15q0ustOMXhOQAhuUAIhAOdL+YW7vGfRzTN4SgzVuA5bb66U8P4Dfvq7y/j+bDVk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20639}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "f6506626f20019a255dcffa14e6093a5342440b2", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "8.0.0", "description": "JavaScript package binary linker", "directories": {}, "_nodeVersion": "14.18.0", "dependencies": {"rimraf": "^3.0.0", "cmd-shim": "^4.0.1", "read-cmd-shim": "^2.0.0", "write-file-atomic": "^3.0.3", "mkdirp-infer-owner": "^2.0.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.10", "mkdirp": "^1.0.3", "require-inject": "^1.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_2.3.0_1634219131576_0.37895621427131365", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "bin-links", "version": "3.0.0", "keywords": ["npm", "link", "bins"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "bin-links@3.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "8273063638919f6ba4fbe890de9438c1b3adf0b7", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-3.0.0.tgz", "fileCount": 18, "integrity": "sha512-fC7kPWcEkAWBgCKxmAMqZldlIeHsXwQy9JXzrppAVQiukGiDKxmYesJcBKWu6UMwx/5GOfo10wtK/4zy+Xt/mg==", "signatures": [{"sig": "MEYCIQD+lrZPP+fmWwUqxMLqg3yMEHJd/Q3tm0kg9egzI5qO5QIhAMS1Gd9TxQeuVs6CgstUGRqSOAhGmjhOAP+y214Njasj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5zm8CRA9TVsSAnZWagAAnqsP/0gOriNjxKeUcmRblhtC\n/Rau3OBydO6pRWJ9I7rhQTOpMi+bS8eqjTegXunR06HN/t+LLHSoc+ks7Whk\n9Zgf/JUHZwP4QfsALi3llgXC3UGofWVRJUzD0RzujgysWxv/WGZnIHnIXysy\nKvdFKpPoJhEu5+SInL7JAtVXTk8Fc892a4rQ/ObCpbsJNaTbDkUU885tNS8i\nrfpIG4XQIQruN38a5LI/qFukvkgIpIF5wno/uRYPiiM51ycncLjqAyNcUz9O\nqAQ9VkoKLu8rKKkHFk5nZ1DFCQGY3B28BXoKCCMznMCC2bSB/muNzWsdSGtE\n+T1turxEjj5Eid8wIeprye/XwzLn3lju5bu1jb/qDQX8+bI93sh1WEN+fDfG\ncNHvSxJR2FI7OHSnjHIJ1gp4KFbQUt6VOVBbZh0/hnivg0I0xmGDC8M96D10\nv0LR30H3qGh4HxhqjP/lZwSvhkNupIQ2/dNbDodT2m1eMbVqeP0ZyNT7BFZ5\nyuipQvLIWc3u6Q0MAuxaQHDSNEysoGLxUwFe314xUnvuyCg/MVo221mQT6DJ\n7yARQt1Sb34XI1kI9jlRJBMndQrjAUko+2rApnRbX0zM5Jx5SO8O7P58+luD\nYA6j06lYCatl+gpFu8Nm1JaQl7f5ZjByrIfm6ybEh0p7n+x1tWFT2Y37PWoo\nKQBf\r\n=2dsk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "674756242f36ff981d0979680c57dcafa59bc0d8", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "JavaScript package binary linker", "directories": {}, "templateOSS": {"version": "2.5.0", "windowsCI": false}, "_nodeVersion": "16.13.0", "dependencies": {"rimraf": "^3.0.0", "cmd-shim": "^4.0.1", "read-cmd-shim": "^2.0.0", "write-file-atomic": "^4.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.10", "mkdirp": "^1.0.3", "require-inject": "^1.4.4", "@npmcli/template-oss": "^2.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_3.0.0_1642543547999_0.9691668007751593", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "bin-links", "version": "3.0.1", "keywords": ["npm", "link", "bins"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "bin-links@3.0.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "cc70ffb481988b22c527d3e6e454787876987a49", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-3.0.1.tgz", "fileCount": 18, "integrity": "sha512-9vx+ypzVhASvHTS6K+YSGf7nwQdANoz7v6MTC0aCtYnOEZ87YvMf81aY737EZnGZdpbRM3sfWjO9oWkKmuIvyQ==", "signatures": [{"sig": "MEUCIQDiZGk4nR5BCggWVoF4Dcjshty0FYV2R8NYKL2z5CauuQIgLUh7mZP9STQSCGQZO8nvSJsiruG6JNkmSgNNCH50Eyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTJRxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqs1A/9FQowOBGdUhbOEqXc/4+42thNh3agpwEOMx6sFaPkcpL7GitL\r\nu21LoCKcjhPPqU2BBYo14Gn3fClRxT8sWr4TM2JPSMcJF00egbm3zuHU6X0I\r\nPSAblK5f96nzkxxkd43brkz/5ulykcAR0F+xoD7m8iAdpyzBZW7ZIia9hc54\r\nMzKbBdztY/lLLyhCyLyMd5IhHHgtL4G6P1JCD4fhv3LQT/S7eSf1X27TtX2o\r\nPbyoCn+zI2U0ULP5BlJrPZv9/sZB4xo/sdNukxxXYYixdg31qqfUDTZ6JWwn\r\nB5OPNu9GQ20xPoC5B5eG/Tfy09pgaf1bVXZcswrsyUtLuUFsi2IIX44etSZQ\r\nJEmJTvsiJdZiPGeYWYud84tmTzX4ZHqC8r2OhuNyXUbrD5TgUqi2aFX5rWAv\r\nXNcs8SkpfnwsTGBLMzxMoFac9lUzay8Te+KnfJoR3TBLwtUArconvmdhrlDn\r\n7iYh0oiI0CNshiIoINIPhJuP3ywrd3FxJG5gGl5hXwwKt35Ee7IANqjBHNrG\r\nOA8ga3szQFfkb0C58BnuE/qVR9TzStKUFGSEdGT5i/0yhEybK0NTtupjsd4u\r\nyaTR5QgO7L2+8wPNq++Mmn49npmJ49YP5gAQXopKY/CYUPhkA0ylLVNi7Em6\r\n2ce1P4lcH5VqfSylBFg8NYqHwnWv6oiwrHA=\r\n=stTN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "492a1be753239ef5ecac379ef916da1eebc18526", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "JavaScript package binary linker", "directories": {}, "templateOSS": {"version": "3.2.2", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.14.2", "dependencies": {"rimraf": "^3.0.0", "cmd-shim": "^5.0.0", "read-cmd-shim": "^3.0.0", "write-file-atomic": "^4.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.10", "mkdirp": "^1.0.3", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_3.0.1_1649185905845_0.6300105181506597", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "bin-links", "version": "3.0.2", "keywords": ["npm", "link", "bins"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "bin-links@3.0.2", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "5c40f14b0742faa2ae952caa76b4a29090befcbb", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-3.0.2.tgz", "fileCount": 18, "integrity": "sha512-+oSWBdbCUK6X4LOCSrU36fWRzZNaK7/evX7GozR9xwl2dyiVi3UOUwTyyOVYI1FstgugfsM9QESRrWo7gjCYbg==", "signatures": [{"sig": "MEQCIAK+MsnA3t4CL5gB4UgKJWtPvEYDZT8jtUvQCGQfH7weAiAQXQpzDi2gGLrY78gG4bUXVl0AjDThndiDXHJ5b37Ojw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9VEpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfTw//Q46BLuPF1Xm1EsdG20Xgh3Ad68TIoaBzouoQftBKkYq1wpu+\r\nbLZnZ+aAy4agjevgfNa2M0i350JcIzQ3FbXGodIt63BZbmdQaeFDZ6WM0Qa2\r\nJR/rGE7xr9s5vUirhUMn/Dozr8CKDjOC5KS7xbLnvo/NTbzwI8vrCqFJQ2BI\r\niGqRA4d33rtKN+DkNJaBoKvRL1mQv/ckEdVDRP+A2lqrEt8vDDiOliNemUUv\r\n6ChlesBsQAjxrqw19jHK1aeWpqYa1zgpDF7onVzC/dF5dMeQ1urlEcKqhjGa\r\nrkaP5mWdB0sqdgwQXoBnQq2a5FpN2o6mIe61whk7QvDJOeVWp3Pw1kAUsubp\r\nclCfLV3qkOox2X7u+niJj29aGxeX7dWZL2P/ktI9EWyd3VC+f6lvqJjOd5cO\r\ndfIasP6e8Rz9aNJcQEf+EqtUgYQYkDuq+z3Q+NZsebxdVGF7bJ4WavxB7RaW\r\n7pXCFGO1wmvdU09pl+OgW6eH6PGwlhSSn0yRh/tddkhRkNGBHEVrlVzJukln\r\nvBhIvTc88Gni8jdKf3TfXRv0CTDYKIDlO4nZvhtqE4+wVKXueJlZEp7jQqP0\r\nhfmyinZ5rJlXmlt3u0ajDecmjDpkm8y4K95rHwl3ERXLecTbevfd3LqPGzoK\r\nnpbVrRa+R1XtjwaCN4XlTTPwzq80EQU5wYs=\r\n=60o2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "a9e00099099ee1de40f62b859a733b16c7defc6c", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "8.17.0", "description": "JavaScript package binary linker", "directories": {}, "templateOSS": {"version": "3.5.0", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.6.0", "dependencies": {"rimraf": "^3.0.0", "cmd-shim": "^5.0.0", "read-cmd-shim": "^3.0.0", "write-file-atomic": "^4.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-normalize-package-bin": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "mkdirp": "^1.0.3", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_3.0.2_1660244264856_0.908511027796804", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "bin-links", "version": "3.0.3", "keywords": ["npm", "link", "bins"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "bin-links@3.0.3", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "3842711ef3db2cd9f16a5f404a996a12db355a6e", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-3.0.3.tgz", "fileCount": 18, "integrity": "sha512-zKdnMPWEdh4F5INR07/eBrodC7QrF5JKvqskjz/ZZRXg5YSAZIbn8zGhbhUrElzHBZ2fvEQdOU59RHcTG3GiwA==", "signatures": [{"sig": "MEQCIBVumPzvgztJXC4fvWBcamGJvyK0UCiq2h+JVXbUdedgAiBHvza7sPE//NBxVhWC2GlGfTuweMj6U3XZuYgo/8XLZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBTCEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqG5w//UpAHhU6L3+dqr0yZWKgK0ElpQQgN/SgaYkOwkfveV/MX5hUR\r\nI/J3VGDUKQNV6iz7DjWNWpoiZmFam6yVctAdUxveW59MMXMyc45PTEtyD3Ba\r\nYGPUoOjui7eCRyMbfPff4iRRi0NNgOE86PMjNgQnq7c31ROmtHlYewnLubaV\r\n3C3UZx2tB8kdONSSQkS1hJo7Buh7Qlhp6cEeZNP5SAT40qkSS+Y4PfalcO84\r\nafhD/V5i6jF1kpbGlsauPFr4tGz7cDm0rJ1fcDGDRwdRFgbPZGegSRm/J4tT\r\n3IAftJ17tIokcliqU4MaIfnOZc6orGvqLcod/4JWQgwJsWyCMuzB1i+bJSOk\r\n4Bc8a65D2cv/czzjIKsMLHKwjgQNjoMj288uzRX9qXS3YUF8X6PKEm+t3Yh1\r\nuwWvj9DMz3ucaqrMRIod/***************************+/zkzZ1CbkKX\r\nztd0XHHucWaC+TbJgViH4oQ48yg7hDzEwWfSTSq/vPKfcNZVMyaqS+AZLvaY\r\nWyUMpEmD6JHaeGo5MJlUMSTLq5QiDUeb8RuSQhWZbEvgFa6fzeurQg5bAKgj\r\nf7C34YT+WeAq2+TaqQHPzSnNLhAltGtphHsW0SdMheIg8K0qOuLfM2MbECaO\r\nn9rpRa551YfclXKeL3xY5f3PJxDklTxiNmA=\r\n=m4Hi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "947697c41abfc2aef68a5d7aa3cc8db3f80fe43e", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "JavaScript package binary linker", "directories": {}, "templateOSS": {"version": "3.5.0", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.7.0", "dependencies": {"rimraf": "^3.0.0", "cmd-shim": "^5.0.0", "read-cmd-shim": "^3.0.0", "write-file-atomic": "^4.0.0", "mkdirp-infer-owner": "^2.0.0", "npm-normalize-package-bin": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "mkdirp": "^1.0.3", "require-inject": "^1.4.4", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_3.0.3_1661284484752_0.30233725552549195", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "bin-links", "version": "4.0.0", "keywords": ["npm", "link", "bins"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "bin-links@4.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "f9122f8d0eddca07d014d6ced2a6021de7b4da1f", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-4.0.0.tgz", "fileCount": 18, "integrity": "sha512-myOlk+Sj0GCKAB+47BeuR08cv3YPtLnmDPFNpTuqjYh/2qOft42q+i0Alvfk+Lj3900iljb6jgDGl50oFN078A==", "signatures": [{"sig": "MEUCIGMCKiHcO1A50bb8SLwilXOk8KSbb/02YszM+VfRxbsYAiEAuPi5l7vaUdvR857MFtO/IIQ4erENHqCGSTDk2JoYJoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSF9uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojZw//SdJ2xyg/yEyl4DRVoUIn5CejW1q6mha7O2ArH1poWUQSbJ07\r\nn/MIxDRQ0CVyzRFw9M5fRwjD8siV6vetA6O8Hwiu4wmqMw+9drHfskGyzVWq\r\n83wgpdrxnYwcI0Jymii9s3BVrExT+J2oHRyUC2atamafloVoxOYYoFfwyRSz\r\noQvRpwHZk08nP7r5jpfzXggLC9/qUj3WxP5R9dEFPBkUIGztg0cHUBH/33Yn\r\nhACn4bEYao1zViI0RInVtgfHhBTirPwfZEKxm3cLJiSFyb3spbEaNJJZ+2YX\r\nKB+BE5FyDem9dEhqAylUfXlmsBJVEpGmX8gp2dR0WC/bqTTPvQ05rmDyBunv\r\nrPmJ0ofOg4ALOnVKVSqZdMva8vt0Di2mHerAKJjcLbJxG21f9ZxuDEF/lPFS\r\nkBKp0qkvuR2y4pHBbYdwFNRTf+mkz3ac6M/LbA2i1GGEM2ag9WxbMC7g8RtE\r\nPxmGdh84wJQ3uu3bTa1LRSTtCEngh+/a6pj2X0wIsEF6ptP5Ulw/eAlwlXU6\r\nvUHkYOrMwlUeTHlHqI6EFL3+4VJcncJcIlU+w4ro0bObeB5nlclojqYmotit\r\nm2jMdLHWN48P61nSORQdsHP4VoYSOyR70e7luDnMn00gBC62QGh1JpYmHRJr\r\noIImD6n9i4SQIbb7V4+HFkEpRhNDh57rzI8=\r\n=BPia\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "928858911af48d98c2dbde8dd6dbf0496e72b1b8", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "9.0.0-pre.4", "description": "JavaScript package binary linker", "directories": {}, "templateOSS": {"version": "4.5.1", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.16.0", "dependencies": {"cmd-shim": "^6.0.0", "read-cmd-shim": "^3.0.0", "write-file-atomic": "^4.0.0", "npm-normalize-package-bin": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_4.0.0_1665687406360_0.7436051913957751", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "bin-links", "version": "4.0.1", "keywords": ["npm", "link", "bins"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "bin-links@4.0.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "afeb0549e642f61ff889b58ea2f8dca78fb9d8d3", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-4.0.1.tgz", "fileCount": 18, "integrity": "sha512-bmFEM39CyX336ZGGRsGPlc6jZHriIoHacOQcTt72MktIjpPhZoP4te2jOyUXF3BLILmJ8aNLncoPVeIIFlrDeA==", "signatures": [{"sig": "MEUCIQCvcaigXRbW5q7m/LLGZU59WWsfcs/GpUVUZL6cd53eXQIgZdbevWYCG14RtGpWNcVmb8Rvh6xIwfJiNDx+RACANfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTa5/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCDA//aMd2kbrR29+083YLVlU62kuID476dAv5uwiYtwodZRRVfgls\r\n9pzsNmSoAhNJt0jMW8RLLow9Gw/enqrtJUePDRORp0wtRivMsdiWt8OWBZfr\r\nksppO+7QMbTa1sf558qVONJJ+FvNV0GgCL9XsFCNGmonFfp/N+mnOuTt+DM4\r\nLQDeEGw2926xUTqBxSPhaODoa2fG2K0GFHUhmINM4QE4sbyhKHdvuR686U17\r\n3lsJRkUcxZAYNJ2JP28LksB4ngQPUJToG1hKJiTjEu0Bkc2ByVXboUPVSB+J\r\n1uTR/XJ9eynZojJuw+YGNDPsvr4v+WJVj+p7WafHbS4kV56L2XF7BtkvdgQu\r\nT+Zq/oa2nvr7bvCxAa5CM3wTfsCNFea/6lbVJJTlJmhF1CRjUehCZkBXsDxZ\r\ntI+PAbF4zUZD9PJl6tK5anp3caTsODOr34Z7E69Dwck/SomQ1Rklx4o8adG7\r\nYqBv+P91NoUXWczQkhWFfzPkuIBUC3gLF9KSKN9AKBcVMHrPQAUzZEUH8WJL\r\ncuUc37pwm776LtEwn8myxA8q4MlhdinSNE4C8GeSnrgYmvFmdDHQKMYKGO6d\r\nDST7pdKj3UOkPxIHM7aSzbnGHacWDJ7zBkKiKjEtQ2zIrFJPJ4HFy/AXkhOZ\r\n5yifCayy2WvykDsHloHX+0Kb9m7uuopb/MU=\r\n=vTYK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "9f424c46dbba96fcf659a252e67b59fb026db61d", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "JavaScript package binary linker", "directories": {}, "templateOSS": {"version": "4.5.1", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.10.0", "dependencies": {"cmd-shim": "^6.0.0", "read-cmd-shim": "^4.0.0", "write-file-atomic": "^5.0.0", "npm-normalize-package-bin": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_4.0.1_1666035327573_0.07367852163957989", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "bin-links", "version": "4.0.2", "keywords": ["npm", "link", "bins"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "bin-links@4.0.2", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "13321472ea157e9530caded2b7281496d698665b", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-4.0.2.tgz", "fileCount": 18, "integrity": "sha512-jxJ0PbXR8eQyPlExCvCs3JFnikvs1Yp4gUJt6nmgathdOwvur+q22KWC3h20gvWl4T/14DXKj2IlkJwwZkZPOw==", "signatures": [{"sig": "MEQCIB8N8KpE43yr9qu0Bk1Fnyp7c2Et2nBaeDJaNdL3zZVmAiBZ69tgyI88/5ut2Swlo2i8pvfgh8Ivs6xRSjzdmvuzzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/bin-links@4.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20703}, "main": "./lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "0816cc0c1f5b8844496119f97cbd341a22465f04", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "JavaScript package binary linker", "directories": {}, "templateOSS": {"publish": true, "version": "4.15.1", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.1", "dependencies": {"cmd-shim": "^6.0.0", "read-cmd-shim": "^4.0.0", "write-file-atomic": "^5.0.0", "npm-normalize-package-bin": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.15.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_4.0.2_1689091057542_0.747121462709939", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "bin-links", "version": "4.0.3", "keywords": ["npm", "link", "bins"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "bin-links@4.0.3", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "9e4a3c5900830aee3d7f52178b65e01dcdde64a5", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-4.0.3.tgz", "fileCount": 18, "integrity": "sha512-obsRaULtJurnfox/MDwgq6Yo9kzbv1CPTk/1/s7Z/61Lezc8IKkFCOXNeVLXz0456WRzBQmSsDWlai2tIhBsfA==", "signatures": [{"sig": "MEYCIQCZ+uyV/rPGBMDcgwkiJHBnyhMBq8v0Nrct+6x7lW5kKAIhAPZb8vN2Hy3FUnaWiXVYsiZ52Y/fDz77+m7+j88JhKcn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/bin-links@4.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20646}, "main": "./lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "d4ccdb05cd9a1787f25f69cf2458b4926cbe65c8", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "JavaScript package binary linker", "directories": {}, "templateOSS": {"publish": true, "version": "4.19.0", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.18.0", "dependencies": {"cmd-shim": "^6.0.0", "read-cmd-shim": "^4.0.0", "write-file-atomic": "^5.0.0", "npm-normalize-package-bin": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_4.0.3_1697127766918_0.3816962899566816", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "bin-links", "version": "4.0.4", "keywords": ["npm", "link", "bins"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "bin-links@4.0.4", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "c3565832b8e287c85f109a02a17027d152a58a63", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-4.0.4.tgz", "fileCount": 18, "integrity": "sha512-cMtq4W5ZsEwcutJrVId+a/tjt8GSbS+h0oNkdl6+6rBuEv8Ot33Bevj5KPm40t309zuhVic8NjpuL42QCiJWWA==", "signatures": [{"sig": "MEQCIEqXyqZtaEtILbs2bLrhEp+Yo1QoeqNM6RxThp6OKsygAiBOc7e24SbE7cqEavFLygvXA6uhpBztto1tLW93HRoMzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/bin-links@4.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20665}, "main": "./lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "0b83a9de0816f85dbfe2f9be49b6bd444b3c1d03", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "JavaScript package binary linker", "directories": {}, "templateOSS": {"publish": true, "version": "4.22.0", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.1.0", "dependencies": {"cmd-shim": "^6.0.0", "read-cmd-shim": "^4.0.0", "write-file-atomic": "^5.0.0", "npm-normalize-package-bin": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_4.0.4_1714785010814_0.08736990214927287", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "bin-links", "version": "5.0.0", "keywords": ["npm", "link", "bins"], "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "bin-links@5.0.0", "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/bin-links#readme", "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "coverage-map": "map.js", "check-coverage": true}, "dist": {"shasum": "2b0605b62dd5e1ddab3b92a3c4e24221cae06cca", "tarball": "https://registry.npmjs.org/bin-links/-/bin-links-5.0.0.tgz", "fileCount": 18, "integrity": "sha512-sdleLVfCjBtgO5cNjA2HVRvWBJAHs4zwenaCPMNJAJU0yNxpzj80IpjOIimkpkr+mhlA+how5poQtt53PygbHA==", "signatures": [{"sig": "MEQCIBSKqLVgF/9BwRf3JsqR4ID/iKYgwsDBefgY+3uQHGUwAiATwAk/o2N8xa1sxTAjsCYuZKxm/8xs+yAuJ6QOh4AuYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/bin-links@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 20855}, "main": "./lib/index.js", "engines": {"node": "^18.17.0 || >=20.5.0"}, "gitHead": "21614a8d3f0479136a76648e2f9364aad3ffdd55", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/bin-links.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "JavaScript package binary linker", "directories": {}, "templateOSS": {"publish": true, "version": "4.23.3", "windowsCI": false, "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.9.0", "dependencies": {"cmd-shim": "^7.0.0", "proc-log": "^5.0.0", "read-cmd-shim": "^5.0.0", "write-file-atomic": "^6.0.0", "npm-normalize-package-bin": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "require-inject": "^1.4.4", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bin-links_5.0.0_1727363905995_0.617415631733452", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2017-10-07T01:39:25.999Z", "modified": "2025-05-14T20:03:28.741Z", "1.0.0": "2017-10-07T01:39:25.999Z", "1.1.0": "2017-11-20T23:58:40.904Z", "1.1.1": "2018-03-07T21:55:11.916Z", "1.1.2": "2018-03-22T23:43:53.505Z", "1.1.3": "2019-08-14T19:44:58.753Z", "1.1.4": "2019-12-09T23:08:57.998Z", "1.1.5": "2019-12-10T00:29:26.416Z", "1.1.6": "2019-12-11T18:49:05.771Z", "1.1.7": "2019-12-26T02:59:32.065Z", "2.0.0": "2020-01-31T04:12:43.897Z", "2.1.0": "2020-02-22T18:48:02.487Z", "2.1.1": "2020-02-22T18:49:09.891Z", "2.1.2": "2020-02-22T21:50:05.479Z", "1.1.8": "2020-03-24T00:17:55.754Z", "2.1.3": "2020-08-03T23:20:45.796Z", "2.1.4": "2020-09-16T23:36:33.559Z", "2.2.0": "2020-09-30T17:06:19.352Z", "2.2.1": "2020-09-30T18:12:44.386Z", "2.3.0": "2021-10-14T13:45:31.703Z", "3.0.0": "2022-01-18T22:05:48.230Z", "3.0.1": "2022-04-05T19:11:45.974Z", "3.0.2": "2022-08-11T18:57:45.068Z", "3.0.3": "2022-08-23T19:54:44.929Z", "4.0.0": "2022-10-13T18:56:46.530Z", "4.0.1": "2022-10-17T19:35:27.798Z", "4.0.2": "2023-07-11T15:57:37.751Z", "4.0.3": "2023-10-12T16:22:47.134Z", "4.0.4": "2024-05-04T01:10:10.982Z", "5.0.0": "2024-09-26T15:18:26.181Z"}, "bugs": {"url": "https://github.com/npm/bin-links/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/bin-links#readme", "keywords": ["npm", "link", "bins"], "repository": {"url": "git+https://github.com/npm/bin-links.git", "type": "git"}, "description": "JavaScript package binary linker", "maintainers": [{"email": "<EMAIL>", "name": "gar"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "owlstronaut"}], "readme": "# bin-links [![npm version](https://img.shields.io/npm/v/bin-links.svg)](https://npm.im/bin-links) [![license](https://img.shields.io/npm/l/bin-links.svg)](https://npm.im/bin-links) [![Travis](https://img.shields.io/travis/npm/bin-links.svg)](https://travis-ci.org/npm/bin-links) [![AppVeyor](https://ci.appveyor.com/api/projects/status/github/npm/bin-links?svg=true)](https://ci.appveyor.com/project/npm/bin-links) [![Coverage Status](https://coveralls.io/repos/github/npm/bin-links/badge.svg?branch=latest)](https://coveralls.io/github/npm/bin-links?branch=latest)\n\n[`bin-links`](https://github.com/npm/bin-links) is a standalone library that links\nbinaries and man pages for JavaScript packages\n\n## Install\n\n`$ npm install bin-links`\n\n## Table of Contents\n\n* [Example](#example)\n* [Features](#features)\n* [Contributing](#contributing)\n* [API](#api)\n  * [`binLinks`](#binLinks)\n  * [`binLinks.getPaths()`](#getPaths)\n  * [`binLinks.checkBins()`](#checkBins)\n\n### Example\n\n```javascript\nconst binLinks = require('bin-links')\nconst readPackageJson = require('read-package-json-fast')\nbinLinks({\n  path: '/path/to/node_modules/some-package',\n  pkg: readPackageJson('/path/to/node_modules/some-package/package.json'),\n\n  // true if it's a global install, false for local.  default: false\n  global: true,\n\n  // true if it's the top level package being installed, false otherwise\n  top: true,\n\n  // true if you'd like to recklessly overwrite files.\n  force: true,\n})\n```\n\n### Features\n\n* Links bin files listed under the `bin` property of pkg to the\n  `node_modules/.bin` directory of the installing environment.  (Or\n  `${prefix}/bin` for top level global packages on unix, and `${prefix}`\n  for top level global packages on Windows.)\n* Links man files listed under the `man` property of pkg to the share/man\n  directory.  (This is only done for top-level global packages on Unix\n  systems.)\n\n### Contributing\n\nThe npm team enthusiastically welcomes contributions and project participation!\nThere's a bunch of things you can do if you want to contribute! The [Contributor\nGuide](CONTRIBUTING.md) has all the information you need for everything from\nreporting bugs to contributing entire new features. Please don't hesitate to\njump in if you'd like to, or even ask us questions if something isn't clear.\n\n### API\n\n#### <a name=\"binLinks\"></a> `> binLinks({path, pkg, force, global, top})`\n\nReturns a Promise that resolves when the requisite things have been linked.\n\n#### <a name=\"getPaths\"></a> `> binLinks.getPaths({path, pkg, global, top })`\n\nReturns an array of all the paths of links and shims that _might_ be\ncreated (assuming that they exist!) for the package at the specified path.\n\nDoes not touch the filesystem.\n\n#### <a name=\"checkBins\"></a> `> binLinks.checkBins({path, pkg, global, top, force })`\n\nChecks if there are any conflicting bins which will prevent the linking of\nbins for the given package.  Returns a Promise that resolves with no value\nif the way is clear, and rejects if there's something in the way.\n\nAlways returns successfully if `global` or `top` are false, or if `force`\nis true, or if the `pkg` object does not contain any bins to link.\n\nNote that changes to the file system _may_ still cause the `binLinks`\nmethod to fail even if this method succeeds.  Does not check for\nconflicting `man` links.\n\nReads from the filesystem but does not make any changes.\n\n##### Example\n\n```javascript\nbinLinks({path, pkg, force, global, top}).then(() => console.log('bins linked!'))\n```\n", "readmeFilename": "README.md", "users": {"evocateur": true}}