import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { router } from 'expo-router';
import { useTheme } from '@/contexts/ThemeContext';
import { getColors } from '@/constants/Colors';

const { width, height } = Dimensions.get('window');

export default function WelcomeScreen() {
  const { isDark } = useTheme();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleSkip = () => {
    router.push('/(tabs)');
  };

  const handleEnter = () => {
    router.push('/(tabs)');
  };

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
      justifyContent: 'space-between',
      paddingVertical: 60,
      paddingHorizontal: 20,
    },
    charactersContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      marginTop: 40,
      paddingHorizontal: 20,
    },
    characterWrapper: {
      alignItems: 'center',
    },
    characterFrame: {
      width: 120,
      height: 120,
      borderRadius: 60,
      borderWidth: 4,
      borderColor: colors.secondary,
      overflow: 'hidden',
      position: 'relative',
      shadowColor: isDark ? '#000' : '#000',
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: isDark ? 0.5 : 0.3,
      shadowRadius: 12,
      elevation: 12,
    },
    characterImage: {
      width: '100%',
      height: '100%',
    },
    cloakOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    cloakGradient: {
      flex: 1,
    },
    embroideryDetail: {
      position: 'absolute',
      top: 10,
      right: 10,
      backgroundColor: colors.secondary + 'CC',
      borderRadius: 12,
      padding: 4,
    },
    embroideryText: {
      fontSize: 16,
    },
    backpackOverlay: {
      position: 'absolute',
      top: 5,
      right: 5,
    },
    backpack: {
      backgroundColor: colors.textSecondary + 'E6',
      borderRadius: 8,
      padding: 6,
      alignItems: 'center',
    },
    backpackIcon: {
      fontSize: 14,
    },
    thymeIcon: {
      fontSize: 10,
      marginTop: 2,
    },
    characterLabel: {
      marginTop: 12,
      fontSize: 14,
      fontWeight: '600',
      color: colors.text,
      textAlign: 'center',
      fontFamily: 'MaShanZheng-Regular',
    },
    marketElements: {
      position: 'absolute',
      top: 200,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: 30,
    },
    marketStall: {
      width: 50,
      height: 50,
      borderRadius: 25,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.border,
    },
    stallRight: {
      alignSelf: 'flex-end',
      marginTop: 40,
    },
    stallText: {
      fontSize: 24,
    },
    welcomeContainer: {
      alignItems: 'center',
      marginVertical: 40,
    },
    welcomeTextContainer: {
      paddingVertical: 30,
      paddingHorizontal: 40,
      borderRadius: 20,
      borderWidth: 2,
      borderColor: colors.border,
      alignItems: 'center',
    },
    welcomeText: {
      fontFamily: 'MaShanZheng-Regular',
      fontSize: 28,
      color: colors.text,
      textAlign: 'center',
      marginBottom: 12,
      textShadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.2)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    welcomeUnderline: {
      width: 120,
      height: 3,
      backgroundColor: colors.secondary,
      borderRadius: 2,
      marginBottom: 12,
    },
    welcomeSubtext: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    skipButton: {
      flex: 0.4,
      borderRadius: 25,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    enterButton: {
      flex: 0.4,
      borderRadius: 25,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.4,
      shadowRadius: 12,
      elevation: 12,
    },
    buttonGradient: {
      paddingVertical: 16,
      paddingHorizontal: 24,
      alignItems: 'center',
    },
    skipButtonText: {
      color: colors.primary,
      fontSize: 18,
      fontWeight: '600',
      fontFamily: 'MaShanZheng-Regular',
    },
    enterButtonText: {
      color: colors.primary,
      fontSize: 20,
      fontWeight: '700',
      fontFamily: 'MaShanZheng-Regular',
    },
    decorativeContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      pointerEvents: 'none',
    },
    floatingElement: {
      position: 'absolute',
      top: 150,
      left: 50,
    },
    floatingRight: {
      top: 300,
      left: undefined,
      right: 60,
    },
    floatingBottom: {
      top: undefined,
      bottom: 200,
      left: 80,
    },
    floatingIcon: {
      fontSize: 20,
      opacity: 0.7,
    },
  });

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{ 
          uri: isDark 
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary
          ]}
          style={dynamicStyles.overlay}
        >
          {/* Characters Section */}
          <Animated.View 
            style={[
              dynamicStyles.charactersContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
              }
            ]}
          >
            {/* Girl with Green Cloak */}
            <View style={dynamicStyles.characterWrapper}>
              <View style={dynamicStyles.characterFrame}>
                <Image
                  source={{ uri: 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                  style={dynamicStyles.characterImage}
                  resizeMode="cover"
                />
                <View style={dynamicStyles.cloakOverlay}>
                  <LinearGradient
                    colors={[colors.secondary + '4D', colors.secondary + '99']}
                    style={dynamicStyles.cloakGradient}
                  />
                </View>
                <View style={dynamicStyles.embroideryDetail}>
                  <Text style={dynamicStyles.embroideryText}>🌿</Text>
                </View>
              </View>
              <Text style={dynamicStyles.characterLabel}>香芹少女</Text>
            </View>

            {/* Boy with Thyme Backpack */}
            <View style={dynamicStyles.characterWrapper}>
              <View style={dynamicStyles.characterFrame}>
                <Image
                  source={{ uri: 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                  style={dynamicStyles.characterImage}
                  resizeMode="cover"
                />
                <View style={dynamicStyles.backpackOverlay}>
                  <View style={dynamicStyles.backpack}>
                    <Text style={dynamicStyles.backpackIcon}>🎒</Text>
                    <Text style={dynamicStyles.thymeIcon}>🌿</Text>
                  </View>
                </View>
              </View>
              <Text style={dynamicStyles.characterLabel}>百里香少年</Text>
            </View>
          </Animated.View>

          {/* Market Scene Elements */}
          <View style={dynamicStyles.marketElements}>
            <BlurView intensity={20} style={dynamicStyles.marketStall}>
              <Text style={dynamicStyles.stallText}>🏪</Text>
            </BlurView>
            <BlurView intensity={20} style={[dynamicStyles.marketStall, dynamicStyles.stallRight]}>
              <Text style={dynamicStyles.stallText}>🛒</Text>
            </BlurView>
          </View>

          {/* Welcome Text */}
          <Animated.View 
            style={[
              dynamicStyles.welcomeContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}
          >
            <BlurView intensity={25} style={dynamicStyles.welcomeTextContainer}>
              <Text style={dynamicStyles.welcomeText}>欢迎来到斯卡布罗集市</Text>
              <View style={dynamicStyles.welcomeUnderline} />
              <Text style={dynamicStyles.welcomeSubtext}>在这里开始你的奇幻冒险之旅</Text>
            </BlurView>
          </Animated.View>

          {/* Action Buttons */}
          <Animated.View 
            style={[
              dynamicStyles.buttonContainer,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}
          >
            <TouchableOpacity
              style={dynamicStyles.skipButton}
              onPress={handleSkip}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={[colors.accent, colors.accentLight]}
                style={dynamicStyles.buttonGradient}
              >
                <Text style={dynamicStyles.skipButtonText}>跳过</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              style={dynamicStyles.enterButton}
              onPress={handleEnter}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={[colors.accent, colors.accentLight, colors.accent + 'DD']}
                style={dynamicStyles.buttonGradient}
              >
                <Text style={dynamicStyles.enterButtonText}>进入</Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>

          {/* Decorative Elements */}
          <View style={dynamicStyles.decorativeContainer}>
            <View style={dynamicStyles.floatingElement}>
              <Text style={dynamicStyles.floatingIcon}>✨</Text>
            </View>
            <View style={[dynamicStyles.floatingElement, dynamicStyles.floatingRight]}>
              <Text style={dynamicStyles.floatingIcon}>🌟</Text>
            </View>
            <View style={[dynamicStyles.floatingElement, dynamicStyles.floatingBottom]}>
              <Text style={dynamicStyles.floatingIcon}>💫</Text>
            </View>
          </View>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}