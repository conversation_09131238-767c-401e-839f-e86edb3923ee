/*
  # Seed Initial Data for Scarborough Fair Market App

  1. Sample Herbs
    - Common herbs like parsley, thyme
    - Rare and legendary herbs
    
  2. Sample Products
    - Potions and decorations
    - Various rarities and prices
    
  3. Sample Tasks
    - Herb collection tasks
    - Exploration quests
    
  4. Sample Exploration Areas
    - Different difficulty levels
    - Various rewards
*/

-- Insert sample herbs
INSERT INTO herbs (name, scientific_name, description, rarity, base_price, effects, image_url) VALUES
('香芹', 'Petroselinum crispum', '一种常见但珍贵的草药，具有强大的治愈能力。叶片呈深绿色，散发着清新的香气。', 'common', 25, '["治愈", "净化", "增强活力"]', 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg'),
('百里香', 'Thymus vulgaris', '神秘的紫色小花草药，传说中能够增强魔法抗性和精神力量。', 'rare', 75, '["魔法抗性", "精神强化", "驱魔"]', 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg'),
('月光草', 'Lunaria mysticus', '极其稀有的银色草药，只在满月之夜才会发光。具有强大的魔法能量。', 'legendary', 200, '["魔法增幅", "时间感知", "预知能力"]', 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg'),
('火焰花', 'Ignis floralis', '炽热的红色花朵，触摸时会感到温暖。具有增强力量和勇气的特性。', 'uncommon', 50, '["力量增强", "勇气提升", "火焰抗性"]', 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg'),
('薄荷', 'Mentha piperita', '清香的绿色叶子，具有清神醒脑的功效。', 'common', 15, '["清神", "醒脑", "消化"]', 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg'),
('迷迭香', 'Rosmarinus officinalis', '芳香的常绿草本，能够增强记忆力和专注力。', 'common', 30, '["记忆增强", "专注力", "防腐"]', 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg');

-- Insert sample products
INSERT INTO products (name, description, category, price, original_price, rarity, effects, image_url) VALUES
('治愈药剂', '由新鲜香芹制成的强效治愈药剂，能够快速恢复生命值', 'potions', 150, 200, 'rare', '["恢复50HP", "持续5分钟", "无副作用"]', 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg'),
('百里香精华', '珍贵的百里香精华，提升魔法抗性和精神力', 'potions', 300, null, 'legendary', '["魔法抗性+20%", "精神力+15", "持续30分钟"]', 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg'),
('力量药剂', '增强体力和攻击力的草本药剂', 'potions', 120, null, 'common', '["攻击力+25%", "体力+10", "持续15分钟"]', 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg'),
('古老护身符', '神秘的古老护身符，提供持续的保护效果', 'decorations', 500, 650, 'legendary', '["防御力+30%", "幸运值+5", "永久效果"]', 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg'),
('水晶项链', '闪闪发光的水晶项链，增强魅力和交易能力', 'decorations', 250, null, 'rare', '["魅力+20", "交易折扣10%", "永久效果"]', 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg'),
('皇家徽章', '象征地位的皇家徽章，提升声望和影响力', 'decorations', 800, null, 'legendary', '["声望+50", "影响力+25", "永久效果"]', 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg'),
('铁剑', '坚固的铁制长剑，适合初级冒险者', 'equipment', 180, null, 'common', '["攻击力+15", "耐久度高"]', 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg'),
('皮甲', '柔软的皮制护甲，提供基础防护', 'equipment', 220, null, 'common', '["防御力+12", "移动速度不减"]', 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg');

-- Insert sample tasks
INSERT INTO tasks (title, description, type, difficulty, max_progress, reward_gold, reward_experience, reward_items) VALUES
('收集香芹', '在集市周围寻找新鲜的香芹叶子', 'herb_collection', 'easy', 10, 50, 20, '["香芹种子"]'),
('百里香探索', '深入森林寻找稀有的百里香品种', 'herb_collection', 'medium', 5, 100, 50, '["稀有种子", "经验药剂"]'),
('古老图书馆探索', '探索被遗忘的古老图书馆', 'exploration', 'hard', 3, 200, 100, '["古老卷轴", "智慧药剂"]'),
('神秘洞穴调查', '调查山脉中的神秘洞穴', 'exploration', 'medium', 4, 150, 75, '["宝石", "探索地图"]'),
('制作治愈药剂', '使用香芹制作基础治愈药剂', 'crafting', 'easy', 3, 75, 30, '["治愈药剂", "炼金经验"]'),
('交易任务', '与其他商人进行贸易交换', 'trading', 'medium', 5, 120, 40, '["贸易许可证", "声望点数"]');

-- Insert sample exploration areas
INSERT INTO exploration_areas (name, description, difficulty, coordinates, rewards, requirements, time_required) VALUES
('迷雾森林', '古老的森林中隐藏着珍贵的草药和神秘的生物', 'easy', '{"lat": 54.2781, "lng": -0.4053}', '["稀有草药", "50金币", "经验值+20"]', null, 30),
('暗影峡谷', '危险的峡谷中藏着古老的宝藏和强大的敌人', 'medium', '{"lat": 54.2341, "lng": -0.3892}', '["古老武器", "150金币", "稀有宝石"]', '需要等级5以上', 45),
('废弃城堡', '被遗忘的城堡中可能隐藏着王室的秘密', 'hard', '{"lat": 54.2891, "lng": -0.3721}', '["传说装备", "300金币", "皇家徽章"]', '需要完成暗影峡谷', 60),
('龙之巢穴', '传说中的龙族栖息地，蕴含无尽的财富和危险', 'legendary', '{"lat": 54.2456, "lng": -0.4234}', '["龙鳞护甲", "1000金币", "龙之精华"]', '需要等级15以上', 90),
('水晶洞窟', '充满魔法水晶的神秘洞窟', 'medium', '{"lat": 54.2567, "lng": -0.3945}', '["魔法水晶", "200金币", "法力药剂"]', '需要等级8以上', 50),
('古老遗迹', '远古文明留下的神秘遗迹', 'hard', '{"lat": 54.2723, "lng": -0.3678}', '["古老文物", "400金币", "考古经验"]', '需要考古技能', 75);