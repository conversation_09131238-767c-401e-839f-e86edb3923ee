import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  SafeAreaView,
  Dimensions,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  ArrowLeft,
  Save,
  Camera,
  MapPin,
  Tag,
  X,
  Plus,
  Image as ImageIcon,
  Leaf,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { router } from 'expo-router';
import { RarityOptions, CreateHerbEntryInput } from '@/types/diary';
import { DiaryService } from '@/services/diaryService';
import * as ImagePicker from 'expo-image-picker';

const { width, height } = Dimensions.get('window');

// 常见草药属性
const commonProperties = [
  '治疗', '解毒', '镇痛', '消炎', '止血', '安神', '提神', '增强体力',
  '美容', '护肤', '驱虫', '防腐', '香料', '染料', '魔法增强', '法力恢复'
];

// 常见用途
const commonUses = [
  '制作药剂', '烹饪调料', '魔法仪式', '护身符', '香薰', '茶饮',
  '外敷治疗', '内服药物', '驱邪避凶', '增强法术', '美容护理', '防腐保存'
];

// 草药表情图标
const herbEmojis = ['🌿', '🌱', '🍃', '🌸', '🌺', '🌻', '🌹', '🌷', '🌼', '🌾', '🍀', '🌵'];

function CreateHerbContent() {
  const { isDark } = useTheme();
  const { user } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const [formData, setFormData] = useState<CreateHerbEntryInput>({
    name: '',
    scientific_name: '',
    description: '',
    location: '',
    date_found: new Date().toISOString().split('T')[0],
    properties: [],
    rarity: 'common',
    uses: [],
    notes: '',
    images: [],
    illustration: '🌿',
  });
  const [newProperty, setNewProperty] = useState('');
  const [newUse, setNewUse] = useState('');
  const [loading, setLoading] = useState(false);

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleSave = async () => {
    if (!user) return;
    
    if (!formData.name.trim() || !formData.description.trim()) {
      Alert.alert('提示', '请填写草药名称和描述');
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await DiaryService.createHerbEntry(user.id, formData);
      
      if (error) {
        Alert.alert('错误', '保存失败，请重试');
      } else {
        Alert.alert('成功', '草药记录保存成功！', [
          { text: '确定', onPress: () => router.back() }
        ]);
      }
    } catch (error) {
      Alert.alert('错误', '保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleImagePicker = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('权限', '需要相册权限来选择图片');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      const imageUri = result.assets[0].uri;
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, imageUri]
      }));
    }
  };

  const handleAddProperty = () => {
    if (newProperty.trim() && !formData.properties.includes(newProperty.trim())) {
      setFormData(prev => ({
        ...prev,
        properties: [...prev.properties, newProperty.trim()]
      }));
      setNewProperty('');
    }
  };

  const handleAddUse = () => {
    if (newUse.trim() && !formData.uses.includes(newUse.trim())) {
      setFormData(prev => ({
        ...prev,
        uses: [...prev.uses, newUse.trim()]
      }));
      setNewUse('');
    }
  };

  const handleRemoveProperty = (propertyToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      properties: prev.properties.filter(prop => prop !== propertyToRemove)
    }));
  };

  const handleRemoveUse = (useToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      uses: prev.uses.filter(use => use !== useToRemove)
    }));
  };

  const handleRemoveImage = (imageToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter(img => img !== imageToRemove)
    }));
  };

  const dynamicStyles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'space-between' as const,
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '700' as const,
      color: colors.text,
      flex: 1,
      textAlign: 'center' as const,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    saveButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      backgroundColor: colors.primary,
    },
    saveButtonText: {
      color: colors.background,
      fontWeight: '600' as const,
    },
    formContainer: {
      padding: 20,
    },
    inputGroup: {
      marginBottom: 20,
    },
    label: {
      fontSize: 16,
      fontWeight: '600' as const,
      color: colors.text,
      marginBottom: 8,
    },
    textInput: {
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 12,
      padding: 16,
      fontSize: 16,
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
    },
    textArea: {
      minHeight: 120,
      textAlignVertical: 'top' as const,
    },
    row: {
      flexDirection: 'row' as const,
      gap: 12,
    },
    flex1: {
      flex: 1,
    },
    emojiSelector: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 12,
      marginTop: 8,
    },
    emojiItem: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      borderWidth: 2,
      borderColor: colors.border,
    },
    selectedEmoji: {
      borderColor: colors.primary,
      backgroundColor: colors.primary + '20',
    },
    emoji: {
      fontSize: 24,
    },
    selectorContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 12,
    },
    selectorItem: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 20,
      backgroundColor: colors.surfaceSecondary,
      borderWidth: 1,
      borderColor: colors.border,
    },
    selectedItem: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    selectorText: {
      fontSize: 14,
      color: colors.text,
    },
    selectedText: {
      color: colors.background,
    },
    quickAddContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 8,
      marginTop: 8,
    },
    quickAddItem: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      backgroundColor: colors.accent + '20',
      borderWidth: 1,
      borderColor: colors.accent,
    },
    quickAddText: {
      fontSize: 12,
      color: colors.accent,
    },
    inputContainer: {
      flexDirection: 'row' as const,
      gap: 12,
    },
    input: {
      flex: 1,
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 12,
      padding: 12,
      fontSize: 14,
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
    },
    addButton: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 12,
      backgroundColor: colors.primary,
      justifyContent: 'center' as const,
    },
    tagsContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 8,
      marginTop: 12,
    },
    tag: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      backgroundColor: colors.accent + '20',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      gap: 6,
    },
    tagText: {
      fontSize: 12,
      color: colors.accent,
      fontWeight: '600' as const,
    },
    imagesContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 12,
      marginTop: 12,
    },
    imageItem: {
      position: 'relative' as const,
    },
    image: {
      width: 80,
      height: 80,
      borderRadius: 12,
    },
    removeImageButton: {
      position: 'absolute' as const,
      top: -8,
      right: -8,
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: colors.error,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    addImageButton: {
      width: 80,
      height: 80,
      borderRadius: 12,
      backgroundColor: colors.surfaceSecondary,
      borderWidth: 2,
      borderColor: colors.border,
      borderStyle: 'dashed' as const,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
  };

  return (
    <View style={dynamicStyles.container}>
      <LinearGradient
        colors={[colors.backgroundSecondary, colors.backgroundTertiary]}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          {/* Header */}
          <Animated.View style={[dynamicStyles.header, { opacity: fadeAnim }]}>
            <TouchableOpacity
              style={dynamicStyles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={20} color={colors.text} />
            </TouchableOpacity>
            
            <Text style={dynamicStyles.headerTitle}>记录草药</Text>
            
            <TouchableOpacity
              style={dynamicStyles.saveButton}
              onPress={handleSave}
              disabled={loading}
            >
              <Text style={dynamicStyles.saveButtonText}>
                {loading ? '保存中...' : '保存'}
              </Text>
            </TouchableOpacity>
          </Animated.View>

          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={dynamicStyles.formContainer}
            showsVerticalScrollIndicator={false}
          >
            <Animated.View style={{ opacity: fadeAnim }}>
              {/* 草药名称和学名 */}
              <View style={dynamicStyles.row}>
                <View style={[dynamicStyles.inputGroup, dynamicStyles.flex1]}>
                  <Text style={dynamicStyles.label}>草药名称</Text>
                  <TextInput
                    style={dynamicStyles.textInput}
                    placeholder="草药的通用名称..."
                    placeholderTextColor={colors.textSecondary}
                    value={formData.name}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
                  />
                </View>
              </View>

              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>学名</Text>
                <TextInput
                  style={dynamicStyles.textInput}
                  placeholder="拉丁学名（可选）..."
                  placeholderTextColor={colors.textSecondary}
                  value={formData.scientific_name}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, scientific_name: text }))}
                />
              </View>

              {/* 图标选择 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>选择图标</Text>
                <View style={dynamicStyles.emojiSelector}>
                  {herbEmojis.map((emoji) => (
                    <TouchableOpacity
                      key={emoji}
                      style={[
                        dynamicStyles.emojiItem,
                        formData.illustration === emoji && dynamicStyles.selectedEmoji,
                      ]}
                      onPress={() => setFormData(prev => ({ ...prev, illustration: emoji }))}
                    >
                      <Text style={dynamicStyles.emoji}>{emoji}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* 描述 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>描述</Text>
                <TextInput
                  style={[dynamicStyles.textInput, dynamicStyles.textArea]}
                  placeholder="描述这种草药的外观、特征等..."
                  placeholderTextColor={colors.textSecondary}
                  value={formData.description}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
                  multiline
                />
              </View>

              {/* 稀有度 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>稀有度</Text>
                <View style={dynamicStyles.selectorContainer}>
                  {Object.entries(RarityOptions).map(([key, rarity]) => (
                    <TouchableOpacity
                      key={key}
                      style={[
                        dynamicStyles.selectorItem,
                        formData.rarity === key && dynamicStyles.selectedItem,
                      ]}
                      onPress={() => setFormData(prev => ({ ...prev, rarity: key as any }))}
                    >
                      <Text
                        style={[
                          dynamicStyles.selectorText,
                          formData.rarity === key && dynamicStyles.selectedText,
                        ]}
                      >
                        {rarity.icon} {rarity.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* 发现地点和日期 */}
              <View style={dynamicStyles.row}>
                <View style={[dynamicStyles.inputGroup, dynamicStyles.flex1]}>
                  <Text style={dynamicStyles.label}>发现地点</Text>
                  <TextInput
                    style={dynamicStyles.textInput}
                    placeholder="在哪里发现的？"
                    placeholderTextColor={colors.textSecondary}
                    value={formData.location}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, location: text }))}
                  />
                </View>
                <View style={[dynamicStyles.inputGroup, dynamicStyles.flex1]}>
                  <Text style={dynamicStyles.label}>发现日期</Text>
                  <TextInput
                    style={dynamicStyles.textInput}
                    placeholder="YYYY-MM-DD"
                    placeholderTextColor={colors.textSecondary}
                    value={formData.date_found}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, date_found: text }))}
                  />
                </View>
              </View>

              {/* 属性 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>属性</Text>
                <View style={dynamicStyles.inputContainer}>
                  <TextInput
                    style={dynamicStyles.input}
                    placeholder="添加属性..."
                    placeholderTextColor={colors.textSecondary}
                    value={newProperty}
                    onChangeText={setNewProperty}
                    onSubmitEditing={handleAddProperty}
                  />
                  <TouchableOpacity
                    style={dynamicStyles.addButton}
                    onPress={handleAddProperty}
                  >
                    <Plus size={16} color={colors.background} />
                  </TouchableOpacity>
                </View>
                
                <View style={dynamicStyles.quickAddContainer}>
                  {commonProperties.map((property) => (
                    <TouchableOpacity
                      key={property}
                      style={dynamicStyles.quickAddItem}
                      onPress={() => {
                        if (!formData.properties.includes(property)) {
                          setFormData(prev => ({
                            ...prev,
                            properties: [...prev.properties, property]
                          }));
                        }
                      }}
                    >
                      <Text style={dynamicStyles.quickAddText}>{property}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
                
                {formData.properties.length > 0 && (
                  <View style={dynamicStyles.tagsContainer}>
                    {formData.properties.map((property, index) => (
                      <View key={index} style={dynamicStyles.tag}>
                        <Text style={dynamicStyles.tagText}>{property}</Text>
                        <TouchableOpacity onPress={() => handleRemoveProperty(property)}>
                          <X size={12} color={colors.accent} />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                )}
              </View>

              {/* 用途 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>用途</Text>
                <View style={dynamicStyles.inputContainer}>
                  <TextInput
                    style={dynamicStyles.input}
                    placeholder="添加用途..."
                    placeholderTextColor={colors.textSecondary}
                    value={newUse}
                    onChangeText={setNewUse}
                    onSubmitEditing={handleAddUse}
                  />
                  <TouchableOpacity
                    style={dynamicStyles.addButton}
                    onPress={handleAddUse}
                  >
                    <Plus size={16} color={colors.background} />
                  </TouchableOpacity>
                </View>
                
                <View style={dynamicStyles.quickAddContainer}>
                  {commonUses.map((use) => (
                    <TouchableOpacity
                      key={use}
                      style={dynamicStyles.quickAddItem}
                      onPress={() => {
                        if (!formData.uses.includes(use)) {
                          setFormData(prev => ({
                            ...prev,
                            uses: [...prev.uses, use]
                          }));
                        }
                      }}
                    >
                      <Text style={dynamicStyles.quickAddText}>{use}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
                
                {formData.uses.length > 0 && (
                  <View style={dynamicStyles.tagsContainer}>
                    {formData.uses.map((use, index) => (
                      <View key={index} style={dynamicStyles.tag}>
                        <Text style={dynamicStyles.tagText}>{use}</Text>
                        <TouchableOpacity onPress={() => handleRemoveUse(use)}>
                          <X size={12} color={colors.accent} />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                )}
              </View>

              {/* 备注 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>备注</Text>
                <TextInput
                  style={[dynamicStyles.textInput, dynamicStyles.textArea]}
                  placeholder="其他需要记录的信息..."
                  placeholderTextColor={colors.textSecondary}
                  value={formData.notes}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, notes: text }))}
                  multiline
                />
              </View>

              {/* 图片 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>图片</Text>
                <View style={dynamicStyles.imagesContainer}>
                  {formData.images.map((image, index) => (
                    <View key={index} style={dynamicStyles.imageItem}>
                      <Image source={{ uri: image }} style={dynamicStyles.image} />
                      <TouchableOpacity
                        style={dynamicStyles.removeImageButton}
                        onPress={() => handleRemoveImage(image)}
                      >
                        <X size={12} color={colors.background} />
                      </TouchableOpacity>
                    </View>
                  ))}
                  <TouchableOpacity
                    style={dynamicStyles.addImageButton}
                    onPress={handleImagePicker}
                  >
                    <ImageIcon size={24} color={colors.textSecondary} />
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          </ScrollView>
        </SafeAreaView>
      </LinearGradient>
    </View>
  );
}

export default function CreateHerbScreen() {
  return (
    <ProtectedRoute>
      <CreateHerbContent />
    </ProtectedRoute>
  );
}
