-- 创建日记系统相关表

-- 创建心情枚举类型
DO $$ BEGIN
    CREATE TYPE mood_type AS ENUM (
        'happy', 'sad', 'excited', 'calm', 'adventurous', 'mysterious', 'peaceful'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建天气枚举类型
DO $$ BEGIN
    CREATE TYPE weather_type AS ENUM (
        'sunny', 'cloudy', 'rainy', 'snowy', 'foggy', 'stormy'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建稀有度枚举类型
DO $$ BEGIN
    CREATE TYPE rarity_type AS ENUM (
        'common', 'uncommon', 'rare', 'legendary'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建日记条目表
CREATE TABLE IF NOT EXISTS diary_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    mood mood_type,
    weather weather_type,
    location TEXT,
    tags TEXT[] DEFAULT '{}',
    images TEXT[] DEFAULT '{}',
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建草药条目表
CREATE TABLE IF NOT EXISTS herb_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    scientific_name TEXT,
    description TEXT NOT NULL,
    location TEXT,
    date_found TEXT,
    properties TEXT[] DEFAULT '{}',
    rarity rarity_type DEFAULT 'common',
    uses TEXT[] DEFAULT '{}',
    notes TEXT,
    images TEXT[] DEFAULT '{}',
    illustration TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建日记图片表
CREATE TABLE IF NOT EXISTS diary_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    diary_entry_id UUID NOT NULL REFERENCES diary_entries(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    caption TEXT,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建草药图片表
CREATE TABLE IF NOT EXISTS herb_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    herb_entry_id UUID NOT NULL REFERENCES herb_entries(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    caption TEXT,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为日记条目表创建更新时间触发器
DROP TRIGGER IF EXISTS update_diary_entries_updated_at ON diary_entries;
CREATE TRIGGER update_diary_entries_updated_at
    BEFORE UPDATE ON diary_entries
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为草药条目表创建更新时间触发器
DROP TRIGGER IF EXISTS update_herb_entries_updated_at ON herb_entries;
CREATE TRIGGER update_herb_entries_updated_at
    BEFORE UPDATE ON herb_entries
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_diary_entries_user_id ON diary_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_diary_entries_created_at ON diary_entries(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_diary_entries_mood ON diary_entries(mood);
CREATE INDEX IF NOT EXISTS idx_diary_entries_weather ON diary_entries(weather);
CREATE INDEX IF NOT EXISTS idx_diary_entries_tags ON diary_entries USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_diary_entries_is_public ON diary_entries(is_public);

CREATE INDEX IF NOT EXISTS idx_herb_entries_user_id ON herb_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_herb_entries_created_at ON herb_entries(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_herb_entries_rarity ON herb_entries(rarity);
CREATE INDEX IF NOT EXISTS idx_herb_entries_properties ON herb_entries USING GIN(properties);
CREATE INDEX IF NOT EXISTS idx_herb_entries_uses ON herb_entries USING GIN(uses);

CREATE INDEX IF NOT EXISTS idx_diary_images_diary_entry_id ON diary_images(diary_entry_id);
CREATE INDEX IF NOT EXISTS idx_diary_images_order_index ON diary_images(order_index);

CREATE INDEX IF NOT EXISTS idx_herb_images_herb_entry_id ON herb_images(herb_entry_id);
CREATE INDEX IF NOT EXISTS idx_herb_images_order_index ON herb_images(order_index);

-- 添加表注释
COMMENT ON TABLE diary_entries IS '日记条目表';
COMMENT ON TABLE herb_entries IS '草药条目表';
COMMENT ON TABLE diary_images IS '日记图片表';
COMMENT ON TABLE herb_images IS '草药图片表';

-- 添加字段注释
COMMENT ON COLUMN diary_entries.user_id IS '用户ID';
COMMENT ON COLUMN diary_entries.title IS '日记标题';
COMMENT ON COLUMN diary_entries.content IS '日记内容';
COMMENT ON COLUMN diary_entries.mood IS '心情';
COMMENT ON COLUMN diary_entries.weather IS '天气';
COMMENT ON COLUMN diary_entries.location IS '地点';
COMMENT ON COLUMN diary_entries.tags IS '标签数组';
COMMENT ON COLUMN diary_entries.images IS '图片URL数组';
COMMENT ON COLUMN diary_entries.is_public IS '是否公开';

COMMENT ON COLUMN herb_entries.user_id IS '用户ID';
COMMENT ON COLUMN herb_entries.name IS '草药名称';
COMMENT ON COLUMN herb_entries.scientific_name IS '学名';
COMMENT ON COLUMN herb_entries.description IS '描述';
COMMENT ON COLUMN herb_entries.location IS '发现地点';
COMMENT ON COLUMN herb_entries.date_found IS '发现日期';
COMMENT ON COLUMN herb_entries.properties IS '属性数组';
COMMENT ON COLUMN herb_entries.rarity IS '稀有度';
COMMENT ON COLUMN herb_entries.uses IS '用途数组';
COMMENT ON COLUMN herb_entries.notes IS '备注';
COMMENT ON COLUMN herb_entries.images IS '图片URL数组';
COMMENT ON COLUMN herb_entries.illustration IS '插图表情';

-- 插入一些示例数据
INSERT INTO diary_entries (user_id, title, content, mood, weather, location, tags, is_public)
SELECT 
    id,
    '我的第一篇日记',
    '今天是我在斯卡布罗集市的第一天，这里充满了神秘和魔法的气息。我遇到了很多有趣的人，也发现了一些珍贵的草药。希望明天能有更多的发现！',
    'excited',
    'sunny',
    '斯卡布罗集市',
    ARRAY['第一天', '冒险', '发现'],
    true
FROM profiles 
WHERE username IS NOT NULL
LIMIT 1;

-- 完成提示
DO $$
BEGIN
    RAISE NOTICE '✅ 日记系统数据库表创建完成！';
    RAISE NOTICE '📊 包含表：diary_entries, herb_entries, diary_images, herb_images';
    RAISE NOTICE '🔧 包含触发器、索引和示例数据';
END $$;
