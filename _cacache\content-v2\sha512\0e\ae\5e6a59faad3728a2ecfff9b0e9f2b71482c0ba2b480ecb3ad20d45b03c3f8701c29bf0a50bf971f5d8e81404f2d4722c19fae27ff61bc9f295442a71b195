{"_id": "@esbuild/win32-arm64", "_rev": "95-d6e9533788ca3a0f759882d2754d36ba", "name": "@esbuild/win32-arm64", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.15.18": {"name": "@esbuild/win32-arm64", "version": "0.15.18", "license": "MIT", "_id": "@esbuild/win32-arm64@0.15.18", "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "1f9b832b7fd373822d444529969b7886439b8f17", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.15.18.tgz", "fileCount": 4, "integrity": "sha512-nR8Nbrp7Kb5+s2eh6NF1UmRVV1V+S5syeE7tuobXpaEbMRsjb7mG5Gg3yFTGjTglbFPZBEnYmfpY38b1c9DBdg==", "signatures": [{"sig": "MEUCIDDr4xhbg6q7MraprubeMoHVdrzbhf4WPPJOFSVUVbiJAiEA3SOAmnc0ODnsQxm+EcFO46Jh4Z38oJljBUnbATGLDpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8150750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoXDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbiQ//bqKiIlPL8zvn/uFlOCBrFWSFwEFOp8wD1MMtTq+GA1vnwQ1f\r\no93AVoeYUgx+kUC6U98hE61mOlpYEfvVp0cKfAQo02fltWduQU3kvXJMMmvA\r\nsm+uyuYQ17Awfxddfhca7allz6IVbaSJ4mHlNMeLhYizBZa0OgIA08cl/uTS\r\njbrGg+jEK+fGeRURyrSwm+4n+PkzYvf1FzqmK2l+AU3zF/T6JdMTqsvuDc7U\r\niwm7Gb96Ys6yqALkk8er9wphMcpINjk/ZKsCbKS2Ee+la0a07G+fWlTPCCv2\r\ndy/BsALuBwLIRSDIZ8uP0DD3NoyQrOMWcWDLWlkYY4VRs4SNz8tivoeaiFGE\r\nIN2hAI5QIwzp6ldLAE65rWlCi9LmWzv66Z8ETdPA4Iv6cEWbSA7k4hFivIWn\r\n8R2DWBvk2BmQSbUobpzneEn8AQuscAajOtqoilPP/w2z00F610M+OHChKUTE\r\n5+2c9b0pNQErzidjVYRFMtnSK5E+tBR38SYwbsWK0FzOzi/pXQVDVZQcmMl6\r\nPFHBoWzTlYxMCodbELWE6ToCK9WGBkhmMD6qFLV2Awo4P1ME3taCxa1xWV6C\r\n/WH+NrnI0qlI5s4x3W7ZfoEGCG15u704FBtdEtqBAXWbhA5p0nJ1pBVgkACx\r\n58pBJ8wYO7LJcERfJqYZAmr8yQZADkFOEtI=\r\n=q+ZM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.15.18_1670284738281_0.1798623722388908", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@esbuild/win32-arm64", "version": "0.16.0", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d96b6c2bd1b8c507fab526c72948bc62d0ebedeb", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.0.tgz", "fileCount": 3, "integrity": "sha512-UrisFJyz6gNRPUWtPVVrxfpby1TAR4+vmOxDsXcz7l02ZRavuv5I5B1rknQuYEe5qAAOW8ZwIvpLD6XzeI7iCw==", "signatures": [{"sig": "MEYCIQDCTscazQBL0s4JDfnzdzj4iwNcfnDhXUoBMePghJGWugIhAPDIfqo+rrFyWPypcErARLb4SdbvcBDX2hf9d+KunJ1F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8152549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA6EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXGw//VpvkSNbvn2cfdMvDyGkMCuwaaCcqsH6Wxas4kwvKGi96bPm2\r\nJyiXEiLFu/FcpXziS9KnuxHLLqUxuFytVFhVR9n8ryFPavYkdidPWnT/tL01\r\ny53IwZLvYnyZHeYwIZJnIefgHZJuIgF1m6kwFInhM2qKMxWwYXf2K0xMaiZ3\r\nK1w6PsNic6k+yA/sxDJSm86O4691i+aNHk/HhJndLEoWYanXhmYTurdmGxdy\r\n0p3x6GI8FZFaoFrkKpuI3bJ7rZn+JKts6dmvgSxdGdtjiUz4Brloc/F63+5O\r\nnjXUxqTJgdL3YAuIqj/jznWL4sQluu7JR8CSaKBhg5hJjmlIfWIYsz/C9PPC\r\n9QJp133Tj1+1FsSDux6DWTyYYtBCEQvj42v8v02gqLU2WLRUv1PQWcXrvYPF\r\nQ+aPqQIgoJ6N3GFqKnBkd2EmlXHy2mMiP+Hk7Pi/XRYgHoSY3YNsGqKdqLOH\r\ndm2jv+iMHknVM/dF4YzAMzbHQAVqq7MG9OrerVP9+WhauA6TXw5Ja/OJEO70\r\nJlpSjumXUZrO6nvmiEwZAP9eOu+mivfdcEHxWC6dueWIyqT0rFDu3x4dG0I4\r\nNOyT0F6e2nlvk/p5nK2Qt9edIDps+9Y+bV2g9wBv07wm2MM8Swk04q6aZlSa\r\nd2Xd+uBTK5biu1lseL0heh24KSinSAL6wi8=\r\n=sNcj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.0_1670385284634_0.18492583309001231", "host": "s3://npm-registry-packages"}}, "0.16.1": {"name": "@esbuild/win32-arm64", "version": "0.16.1", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "4b63fdc90349053deaea6c3159837b85f7d73417", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.1.tgz", "fileCount": 3, "integrity": "sha512-UuKMH583a6epN+L6VxbXwYQ/RISJsz8NN05QlV2l0LY8aV79Wty23BkBz0WF5kOK22eXNavgb2sgcZer6Qg+KA==", "signatures": [{"sig": "MEUCIBruPBXN8Q2x+F2Bc8nw81gTlGjselIlDGl6AANGWBWyAiEAikucrqv6Vh00+RA5vf9Th+n6JU2RRYvOph1KH+TLCkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8152549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBr8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrauQ//X5N9jUvBkhKxyutyb1+UkFD6tpQefmh0zo/BPEqAsn/T2pi8\r\nZAugNDVzvTBoew9tMOpAkRhxPBb1YKjIpCci9EJQPhmxk+8llKOCQc7hWBza\r\n0hQFTh/5sYyZY8FPGySuhVQHa/OerMjza6JarTuKi/q/wpFEclS+fk81Qkvp\r\nuAYPKpTet/BwUfwVrq7ctDnVBje/2/Co+68uoEG8KmXIrJ/BRaEjUr81iLzM\r\nQGw3Z4sH0U9UEujXipZYC3wNm0LGKneJ/3dFc3Hlu5ZrpxhDzckdd26G+ucw\r\nt8cx5ECHZIBGnHJuKsjb10gQfMPU7tbUFG4b/f5m/X0Onk3ap9l+LYUtaxev\r\ngWOc+hPPgiMzPqAFMy5qiymR790oS6ufogL7tKZke9Y8okbiiCXPzyKIpULh\r\nDTi5wjF8ar1qnfZovQpv0WYVatbUkWab+YNdO8b9MJkvs3jq6mR8lig0T7xI\r\nXndvr5bgEYOmzsJZxm1rgMibVRd5Gfk9Ine+CcUlu5rUxIQFHQ+2dn2LsDDq\r\nz6KmCauU0PPwDSEBNFGdUToXsrEGkB2bqEjOE6i2bXZvB7H2uU3pDmvend44\r\nYEF64SMbD/KJEIxCHxDYIrRCR9M6tW3RPBxecJVf8c5+cHtor3SZNgtEuwBe\r\nrl8BXCzMqcKip4cnEWLMCNaTvYOP8TG+hAw=\r\n=/zfu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.1_1670388476689_0.3614448486566175", "host": "s3://npm-registry-packages"}}, "0.16.2": {"name": "@esbuild/win32-arm64", "version": "0.16.2", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "a548cff2e5bcd5cdfe1431320b9fee81440d26f1", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.2.tgz", "fileCount": 3, "integrity": "sha512-G9AWjsnVxGQj8z0WgaDwTKgXzwc9zLPYDFoLE4oAGI/TQnft0eQjc+CKiWRyoa+a/c3XIFGXoWnW+17kbibSfA==", "signatures": [{"sig": "MEYCIQDcQAl8FGessvx+mM05slc6fwCN8jvY7/f/p23iWs5m2wIhALolxvQRAcl2l3fjj1TmRZZGd5O+NCGQD5zq8C8873lq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8160229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYtUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNqxAAgLSntiJZGrbHyiw6Gkgx9ad6HIpapDwBX9VhMLA72VVkSTgx\r\nWDGRqgZ1lZNQRZVPxXin6lEgvuao9S9KEK1z6smMLqElHhnFGzgsAwbZoc8Z\r\nHKoX2C1iuMWQZupC13F8JWnfWPT2umHi3LwnWKES6PigxyE1L4jxHiQjQuTW\r\nY2riGulWtzqKPz8IeG6rbgGNgweIJM2cVFTLIZGZhyTrJrQP9Fmrqoi4litD\r\nNMQeTOnLXJzAJ0tr3op+PkebBnNGxGu5OVbXFP5Q1xHHSw1JRUdF7Q8c11DY\r\n3mi8h9EKtXldcLntxRehXvQ6e8WZuWGcSugU9jy19/ECPlkiVNynOmrm7ZlT\r\nkXUJLqq2UT2gMEzO6vtPs28WUIBk2XXfmdJ7Js6yov8OEBQTdPJhasOBSjUK\r\nazze8thCeO/pBy/5fo3GsI/sgHkR+gGLs7Ru2Zv1+6fSFbr3yDu/ZxrRNFOe\r\nr3DSVp68ouoShMKHtmI9fgoDEiZ9UuOUzedtTx+gz69REc+PVwhbFeDYHSeW\r\nP2v76GbFshea9/LbSehnGnTwV/Qa+48tOFPMIBW1e0pu9Z4oN5dt9RQBaAD0\r\n8dHt2CeSIotDfyceBvOL/9Zzfk0jlEzNkScQDdE5FQFa4RmmlJ7r1DJKFz5Q\r\nLQcaaB5CuCuFfS+iYEshBYnGcZ/BmusOyEM=\r\n=/8YL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.2_1670482772289_0.5649658486425302", "host": "s3://npm-registry-packages"}}, "0.16.3": {"name": "@esbuild/win32-arm64", "version": "0.16.3", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "bedd9bef5fb41f89ce2599f1761973cf6d6a67b6", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.3.tgz", "fileCount": 3, "integrity": "sha512-u5aBonZIyGopAZyOnoPAA6fGsDeHByZ9CnEzyML9NqntK6D/xl5jteZUKm/p6nD09+v3pTM6TuUIqSPcChk5gg==", "signatures": [{"sig": "MEQCIHocsi0/wuWQldsYu+THQFsCewyAAvkueFWICD35NG9UAiBCETrSaj8BawP2yNShm8jN51ig0KlGGV7oA6jaoynNDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8161765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkUxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQhQ/9GpamKHmNNIKtozTahn477+cbK4Fb1ZRw/LWQrUAYF0wEhVge\r\n5B7ke59azLiRlV1UKhdRdS1PjlRMsYMYcQUGYeDuNj99TqGK4iJ5GhALUvQS\r\n7pAKJhR1eeuZcIvOrLDtU+i7rSqoBi1e91k/5048owT7QTg5myz9kQETxPYW\r\nlekPfqEIH7oBeRgj0Izg3f/9CJH5XNE1X9kyec32Tlen6CjOd9mkOAegTmB8\r\nSH6os2nlGEDXKcJEc5qPhI7H6LBlmU03CMPzdOylV4dCq+rSAeliKLrtKiXi\r\nXIUXWdusgkV0uZ23cjYPYiAW6AEJrtc+qKC9C2k5h/wEokOrAb4t1JExTIsC\r\ny9XCQAHoBygVQ4llqoN6o9CQweSxExktsTyPa1lN2mN8LFRsV1KP8vS30qKp\r\nliuAksf7qMRpP7MMI3B+gMPAeVJuQ0g2K8vq9PMMnMQhrEMur4favK5DuDdz\r\nlrj0gRQ5p1GIZoFQZaw12uuA2T7MI2XEPjdOMGx6qy5CrlFAZg4EiK/fs/BK\r\nWQlxSj8fuYf4RnmLTDzVp5RlSyPUPrZ7fHPk9VOUaU02bI5NaddktAUvqIl8\r\noJcFWgHlwRJcccouh2ZhrE/2Jlh6Q8ysFYSRi/mnWUQOD5Y8RWsFA8kqVWOG\r\n3LHRYZHZVk7dXVb+xO7+bP4IsqBYre1GXq8=\r\n=mC6p\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.3_1670530352721_0.7369738888966051", "host": "s3://npm-registry-packages"}}, "0.16.4": {"name": "@esbuild/win32-arm64", "version": "0.16.4", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d1c93b20f17355ab2221cd18e13ae2f1b68013e3", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.4.tgz", "fileCount": 3, "integrity": "sha512-yYiRDQcqLYQSvNQcBKN7XogbrSvBE45FEQdH8fuXPl7cngzkCvpsG2H9Uey39IjQ6gqqc+Q4VXYHsQcKW0OMjQ==", "signatures": [{"sig": "MEYCIQCcclC+S8MjsRVG8vsVT/Z0Mg6kBjFg7x/iHRiWG8qF2gIhAMntOWk4vWuXRg/AOtllw/u/D0zYLJAHT6laWBjvVwKv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8162277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAH3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLZA//U8QxDprWneAXYM/JI/ILwnIynaJRlWLsR7rWLCoCcEHTjvvw\r\nXGdOsP6aklZuXwFQ8ChDtQBS+B79SOXK+JF15jycD/2pBsVTKHKrK58SuPD+\r\nVqscN7lW7pi+fm3Vfh6Peh1sveCgDQ5FY7a93/ETmpaPKG7WeGh8OV3nPgbx\r\nyURvgMuaC2cAmL5wV261DujxSGZnG0XZckWCDG3wOHTKGupX50mFBkDqpL7F\r\n5SstDTZRFx59QUZ2fpS69EJCU2/Tz60bUbWX6tKHJp5LfwmFAztf97AiQ1gb\r\nCtxBIDqoSh1qTHdiwtnlHLoj3J59pyFhDzvTgqvWvi5JvRHuIpC2Y8wATrLH\r\nhNQ/gY/5pAFMuTYhIz+wk1MJCT/lH1qD6G/LIDVaTQv+pSCaZszKZM5ZUcb1\r\n9O3Lel7NBYTNjzSPtDrMf0agVus6h3pCryDyyAiDGYS8j2wJXEcFpVoO/tRR\r\nClfvr0YYe6RYEdVqMCyZ3puCpb/Zv8d0y6jqJXYvHU7DaRouKmUm5wjSWZUB\r\nC5RA9C8E5eSZlixQHwW9RJEns0PoUPhnUeLXYp4XzJYgHXk3FS5d3RswIarQ\r\nmurAtOiqCcUSYFK11+Vye8WVLJWFUC9GRYfg5Gb9YFiwFupBVRl5SR9YxJGG\r\nHBZeKjSiCq8i2EXVCn1a2ASWuK0ItApegkE=\r\n=JjHA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.4_1670644215026_0.9138396345471522", "host": "s3://npm-registry-packages"}}, "0.16.5": {"name": "@esbuild/win32-arm64", "version": "0.16.5", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "5ebb50418ed224061a049a59c876283962134cee", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.5.tgz", "fileCount": 3, "integrity": "sha512-KcegNS7IgLm/cAcjIW3kZyLiZi/p8I+A2a6OonDA77em9xHewdA2yTA+9pO4gr77MkXATcnDAFBrWw5oLHIZkQ==", "signatures": [{"sig": "MEUCICqDAXuGWb+57KdVRyOC+6lJsF8s3YiTXmXIOyUAc2nhAiEAqjKpTSd7w8ekiTFSicCxqLnO1kpfsGrX9Gw4Xcp+0cE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8175077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLqwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEyhAAn6AUg1yh95ohN/GL8kYojXwNlH/amdm9k/R/Z1aZe1x1DmIQ\r\nlkIEJzFz8+6amkQfjgRg6V4jDUHq7PXGm8FGPU+1qtCvYRPl4flhU5LDYFsg\r\nUnXIzt1q5qC4KJb0qV6rSbo9u1efkHFngEHHc2dC6jCMtqfm/5sYrmWsgrvt\r\nQyx8Z9hHfq/c5frGtrksesSWdWA+qY+flwzrqYCSmD+GPFhe4kv4duKXmyzQ\r\n4bkOMMCupk+29TPNWTfgse+m0AaOzx/YtyX2uQL8VDn9lwc1E6hHOYJHFvME\r\nYa11a0yzzqbYgEo28HA1zUbyMc7N7yzrIXdd0hb3t+M2X7l6TwczhzWom811\r\nMYSpO2AcasvQQS5AOBYWcdIRiqJjB1NGqj7Bm6+oCga1zVrQaVZ2/ZMS+kqn\r\nzpkHhzSGlfj4eIR0nxZ6Wro2Z4HqKcSKTDkcrDUX1vo+piBly5X4KAUnR9yE\r\n3kIfd2ftTZ22ijh9jlbvOjEaXx3pok+eyJZiTleSVW/jC3nphF19PdTBvk2J\r\nI5YFLiowqk2wSiD2s8Fohkd4WPkod3YMlqTlUu9rr62BQEL7HJkMiR6Emu06\r\n43Xk6JvGxsdH1o6fdmBXPV9RQdCypgv7PDIand+/uOCEg4cmnqHVMBnGwBPV\r\n7L9kw1W4LklK/DJ1Igzb8mYmN/oihQgjlDc=\r\n=AnaN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.5_1670953647838_0.4956783899498274", "host": "s3://npm-registry-packages"}}, "0.16.6": {"name": "@esbuild/win32-arm64", "version": "0.16.6", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "8d913e77797c00e05922d46b6f74011bb0655bfd", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.6.tgz", "fileCount": 3, "integrity": "sha512-d+hveGvPLoGQHOKVDWfWSLUFnPtdpzWdtmz3PFq4t/iLg1MMTnPy48TrgC/JFTwcxDgKJdFw6ogTXjYN1tVALw==", "signatures": [{"sig": "MEUCIQDPFjhuRbNMdWT5gkDHo0XDkqB0DyR0V6l/HYl2mEGW3QIgQZTMilDYRGP9RJJ1EEGlJf/qjPDXALcA4IfkU0nWLUc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8180709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV2yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8Rw/5ASTKooBKm6jedSNHbpO8NfhTJT/J5WqIzQo30Tzmtc4ornIa\r\n2Hwpk4NxNLw6BZZmLt6Q2BWJN+I2q0zJbhbhmGEuOGXiBD2aGb28ag/7LUsR\r\nl9C+rztIQWA8YztdoTvxRKZ03XVBQzLxv9gE0Dk1w7bjxb6MYZK2LMm45lRQ\r\ndT8uqnUhA9ZVFpsrSCcCNTeevlRCieRdRg8H/UXq2/HR+8mCq5qMjzIq202J\r\nzsuYlZJVxoe0NaJbFfH9pV39zlTGXBalxfnJKe/GH4g1IX9bimZrZwMT0L9n\r\nnNCkGs5TLpGKIqC+yvxWSjucq6uMUzZYdqbkM1sh+ORu0DsWc2UxWqdCdo42\r\nY33jZgK/6Z7xZlvO4Jur65Ug+ApK/wOvYmD5gbHSjnZ129/eNCAtUOQWBlqO\r\nZInCa3MVoKVsenAoUOEuVDmHOlYP9n8XujuDjqpxb34O9f8dQD0oiPCHfwyh\r\nZ8I7pITWcv+2yZRkQIGMtcP3dIMJSAjGhOcZdCkmCd1/2McDW1lslu+Egf/3\r\nFzpzo++z1PP8ailk8ZIKEcT0A14c/hsoPUhXmfZIxdgqgcyYfOH3DpBcH066\r\nW+ozhQZGSsbcOR01mAts0Wd7cJU8T5IoRRQdAm2T9Q8g00lU3FJ3FBoIpKYr\r\n6RZTQgR3K5qsMvtHIKFnqVl5S10P+m0cnTQ=\r\n=idpH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.6_1670995377753_0.8002619727252802", "host": "s3://npm-registry-packages"}}, "0.16.7": {"name": "@esbuild/win32-arm64", "version": "0.16.7", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "95091269394f16352e318124790a3906bf370141", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.7.tgz", "fileCount": 3, "integrity": "sha512-m39UmX19RvEIuC8sYZ0M+eQtdXw4IePDSZ78ZQmYyFaXY9krq4YzQCK2XWIJomNLtg4q+W5aXr8bW3AbqWNoVg==", "signatures": [{"sig": "MEUCIQD099f5/oubyGidBbx+T9a0+4T0/jaPzALZd4iNsxiEvQIgYIsDEv7bOUuqYHRuvP5/KHJ8cMWazOV0U9K3QHQf1DE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8186341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlJTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodPhAAg7k4kFCqtWv0T+cw3K8Ibm1iY3DWGcNAlr5TFDWh8o761bcy\r\nc8k+DC4T/o4C8msBjkABq4FdfRs2j/w+G/Z/3p5PRpsZYtpuQXJnJdbsM4Gp\r\nf5DGQLb63R/yyIKjnI2B9ZtR5e1mIE0RqBQL3Cos2nR6dmJMqQgC95+fGnUi\r\nN8h3fz1RW57Dp4lpzCbj4HpsjUZB1lhPGVJe5Zd8RdguzZUJlAMc/vjaNoVV\r\n9BVahPslAj9+VPKLgXHoer1i0fio8C//cAndlh/VVk96aZVVE24tE6GAUDwb\r\nD/TKaC6ed9CKZDYBsyuuPudNGLwyPHyNJLQXyMsmokQgUMv+P/3FF4Xj+5z2\r\nd8n0P9TC8R9xP92yUuZv6AS3VLfQmHM039ZO+lguSLZIgNRYnciNX9oE1C9h\r\neBxaIdXkMcAuYwLAf9i4NvqFretoDVxPgSEDrNYvz//Pyv+RJLTNpppAbfi1\r\nR/AfYE4yajbiqkzT1o1XD2uCaqYX3aS+WkXEikhuIoo8vSub8Ao7FZglJveZ\r\nyx8ci4xyycyNpARA3xMJ8fWcUnJpZRlqcQALeIePuOgrw+GDKhzAeO4iNdM5\r\n5BK0n8S0wUGLzQoBUKkGUMDeTymr/YibewFccebg2ZZ/QbK9aHyl3N48CeYn\r\n0aBjcB1P51EDj+ve/wDGhW1itlbltRH9oMg=\r\n=XeTP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.7_1671058003015_0.8263944880447824", "host": "s3://npm-registry-packages"}}, "0.16.8": {"name": "@esbuild/win32-arm64", "version": "0.16.8", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f69f45591a0d5c54f52c9fdc124fb3c54c823b42", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.8.tgz", "fileCount": 3, "integrity": "sha512-M2BZhsa7z8kMGre96HTMXpm266cfJkbdtcZgVfAL8hY4ptkh5MwNDasl85CDo++ffW2issVT+W/xIGJOr0v2pg==", "signatures": [{"sig": "MEYCIQDtaumTyBrA1EkMe8p0v6hstUGDtIuBFQZGtYfQ0Fg1/AIhAPO6w3G2RZfvvVbjzf2dBOolk0Bzb+79y/iQ1dbDp79D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQFrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq21w/9EyuArTj4t4AEotqzqwqYNs4mmuG1+01e9mQVUfNLKqoOW1UX\r\nlOVcdtCvG93fZM+bwSLNN/Z8i8Efxl7VG2yTQJWNW5gVrJD27WBJbyLgY2aa\r\n3VIzI5yiBeqb46rBQLgoZRBiISqmolkKBFTYIE9OzIzhOVXyLyfWbwSnU1F6\r\nRovcoCjZTI1h7dy3vOKnwrazuwwSUfmEw3mFanWxbEv7UDO973qf6ONgQhZ5\r\nRTxWV60Hg4zSvfTMwCg6NZBnrDmoYqdxruNKTU1Q0GxJkBQnhIJ6++je4vDX\r\nKV+ebkrZZkhTP1Ehfke3kmYZUd8U6vesOO7FBW6cS31mg1La4Qfkqh3LL49H\r\nm+C9xHVoJ4cxo8X0maO5NMphC7KbjKdab39y8MYmmC/7IQJyHvhoepv2zenq\r\n5HJQSyhkxWDsFFTw1qFmyd8AUSzG2Ykd6p2uWNL1K7C0+vUINQRwbBa5Z3SH\r\nPEYehqu6FzjCYueVEBBiTA0bPXaDx/3lQJ9H9XLr9p7gCYOT5QVAPRh3bT2/\r\n/IcviYdZ0myDYmNpbZOcCD/F9y7q0fC0rjbqEJg6G1x+i8+1WP1r5wI7fTWX\r\nT4/1hIZJSohf89g+nvUHPUGkd9EUUyVbau2vgBG1Sm7hE2klw/B6Ehv4oy/8\r\nP+KFYp6hiLILIjK5OqLQ7axrOVX0kJhQG5Y=\r\n=81Gz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.8_1671233899404_0.0771459890844679", "host": "s3://npm-registry-packages"}}, "0.16.9": {"name": "@esbuild/win32-arm64", "version": "0.16.9", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "bf74d007d7f0fe1fe32c4fff82d27b271b3e1d58", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.9.tgz", "fileCount": 3, "integrity": "sha512-tKSSSK6unhxbGbHg+Cc+JhRzemkcsX0tPBvG0m5qsWbkShDK9c+/LSb13L18LWVdOQZwuA55Vbakxmt6OjBDOQ==", "signatures": [{"sig": "MEUCIDD5z7th/UdclTT4DFdkjHcacz2dhIMzHhNqRm5v2fcGAiEA2SRJmNajbHPRQl36e4Chtwbjtf0j9+wxsBmU91HYKKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8195557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpeMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlHRAAgnC6FSgJajTdCM2zmWo1sATShOcC8JREtu3eHByNBGaGDf5q\r\nrMPiOqoA0zaVzsW2PYkoWkUYYFPIhV1549AY3Am7J0k7ZJ87h8gHnh+r4Jzg\r\noVCy8JQRluc66OiobLEFx2eXQSa/fxLOBRSm7deHxz+MNFkneHse9k6AGODr\r\nUFqQTndZ9SCSDc08amx0NYEsQOpKoyzk/GJdov2MMOZ+hMngYe/aasubKfBT\r\nzmhNMzEXtofFPoVz1gelTuWzJ6Mf4xNa3pIRhP3DO9m3TpEK/OZvCN7L/sVm\r\nb5NK3mco2whGyUI4vXE44gEj/3mBRQfRx66buczBkrzHAuVqEZ8dXjPobTJw\r\nGxWWrXDcZStLx+72UUNrbwPtukQlRWUBT117aMd1kaO8qfbS6ATtcABMDbFV\r\n1l4aP0rYiic53gZnXQTNFIHM0/9Gty7GX7OFPTAfe92kn0XGP3+SUxrLXOF5\r\nZFxKK5syTt9XUs+w7DSgLl34YypE2s5ZOQX6jEDD8IEQDBZoPB+6/wzrQndz\r\nrPxXnmSsUPNv8CpextUsIBrp7PQuOiky7SU+YPxQxX8qPkFgCG8JsDrZ2m6P\r\n5OLbmuK80y8l4lrzRwQfcmhBe0zekc7inZlZa8H3agXo7UmbK3UqlZXQEg2+\r\ndXz6ZGpRKLAYlejdmSAw98/EmjP86kYZugg=\r\n=JXHM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.9_1671337867756_0.8763978768180527", "host": "s3://npm-registry-packages"}}, "0.16.10": {"name": "@esbuild/win32-arm64", "version": "0.16.10", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "ca58472dc03ca79e6d03f8a31113979ff253d94f", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.10.tgz", "fileCount": 3, "integrity": "sha512-+YYu5sbQ9npkNT9Dec+tn1F/kjg6SMgr6bfi/6FpXYZvCRfu2YFPZGb+3x8K30s8eRxFpoG4sGhiSUkr1xbHEw==", "signatures": [{"sig": "MEQCIGmFK1H4PoVTrCVm7XjFvekmIXIaITu4FJ/AoTaZPfIrAiBVzTLYnuAkvbtaPJk5w17UVFJEW1Z1Ga8a6L3AQ5xCaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8203750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPMbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrA0w/8Dsn+RCr4bnqn2p2s02PyaQn8NRUPo3qK2yoKuGFf784tM5u/\r\njgBkaICDvxyPX0WoGf5wb+Iv4kfhP7v/hRDd9Ims+kLPJxjXZ/4UtqPp9JaD\r\nN24G7suZRZHE6MrfWSsrrGpXbA5LyOschyVSarpwi3XqrcPNxqyb/GAydvVZ\r\nKBCTo7LWlP82Ok/y40wfce7zsYtOIZZhDxlvqQYRe/O9SNRsXJEpO2KOJKF1\r\nHKtoL1fRcDrT1SrByF8qq97QlhaAeQGA4/J37oiBlUFJ2Fxlozv99Pnv/KDq\r\nKDyLrIbVuyLCS/pjjA+M77uTEk2ph8knAGvgYZh3M/Srm8GwCPELWc1PFreo\r\nckXVJTXTJVhvsGMVR509ZEpmwgEoXOIzLiUFfVEZPdTTrIEYb6Gna4Fd/HwC\r\n2Cq4C1gCU5t2ZIdV7/fNwqUGJ4ZmSujM9QX0Hg4SFzwX8MkSwmDpdcqJzEw0\r\nwe1MinwbZiTz/yTxVPM8ZuAgtlF7szpX0W/tURYbdBumtVPfpTDvSUmN6o+I\r\nryJ37sL96WgWw3leeDiIXEjt/Gi1bZBxlw/vYPvX7pt3NZhQ6nRcKYSlj/td\r\niy9F4EPbnfOhVRzyqd94s91J2KM8qeDcPV6ddunAxoxIM7IDnFQBenA0/gp/\r\nSDX3wLrsnOHZdZpgkdlkYRFeXBafeYzxxDI=\r\n=qoIo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.10_1671492379186_0.8034751337250476", "host": "s3://npm-registry-packages"}}, "0.16.11": {"name": "@esbuild/win32-arm64", "version": "0.16.11", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "64302e50fab730e4f9b75cef5685b4f4415f76af", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.11.tgz", "fileCount": 3, "integrity": "sha512-vyTbfoEBn7cGXK8writbsB+G2wyRoOA+EbTNQ9cu5lyLU65sfWetCaL8T7mX338AN8tTbCYl6ce5YRKTonpA3w==", "signatures": [{"sig": "MEUCIDhahR1kJesVrpumgSJQWiyA7+wKjNlSNxK4ZQUq/x8nAiEA9LuejrsVWdJQ93qC9xHkPUwHfGYsdntkE8942+Kd85U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8203750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqkynACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreyQ//SR3x9PU8tOJR0daw5mqd3hJ9e9xn9ol13uohPJ4lPXkcSVmY\r\ncxpaxybDcY/PXdFdYLIjxpoRQDizDwq6FmMxaqxyfsmJTjwU0V4sFi3Xv7v0\r\nZEKSiV2BSrTu2dpxEnw+4NYzJAJp34VSTpWHG2OfI77puBkNJFl7Aysm00LW\r\ndciu/JoerTDkaPmGFuhj5rwtCCJJ0AMWCYd49q1+R9wrv8a5+YEujdHxll9S\r\nNpmUAO+f0Z9fUMl2YGhMaWQgINGeXRbrOS5j3uwniLLQLMIJIyo8wM6Z45wD\r\n8Z8RCG1YlH0buHLipsnT4q8/cQkQ9JzhJn5y0pl5RZCwEDRRJJKjy5IL8Y4e\r\nCZN8+N554aQpDglScQXvtZ9d2pMjI557DJVg0PeW1vsriVFKhvQ5SiDe7xQX\r\n2zTWSr/Joj5/g1rWSc84DbOvdqI+ILgxJ1oiFJ1Mm7UrKOI9iyuh8WZpELHN\r\nSgk8BT+7vWRmIl3xOvldpaD54CWgkl2xgvb+27yyWDZgjFzLfMtH4Ws6+O1L\r\n3cL9lejQ2H3pGxlgGxZmmHaKPh58HCTvvswguygPitA+F+gBlNYrV3IEazPR\r\n0b9+6sc7KFjEtPWJKAoQkbnH/iuYtflLBqx+Xl/IDRwGLKxKMVUbHBWqGh41\r\n1jqJzIAHll3WgLrzyAZgR/oKfnHQue4DkwQ=\r\n=8gv0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.11_1672105127414_0.09852710349977012", "host": "s3://npm-registry-packages"}}, "0.16.12": {"name": "@esbuild/win32-arm64", "version": "0.16.12", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "8c599a91f1c55b3df304c450ac0613855c10502e", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.12.tgz", "fileCount": 3, "integrity": "sha512-XEjeUSHmjsAOJk8+pXJu9pFY2O5KKQbHXZWQylJzQuIBeiGrpMeq9sTVrHefHxMOyxUgoKQTcaTS+VK/K5SviA==", "signatures": [{"sig": "MEUCID7JCYnj6EFZINHGFyacPVokhlAF7jK09k+bq9pTW07AAiEAuD9pTtthisWIRe7+p+QNGo9+c11MUB6qY1S4zgXp59E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8204774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6QtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgHg/8C/6AEdu1zHbkRpn6597YmjGIcoZSfb8wMGPw20cC9HkVxCvm\r\n/l/u8ivjeRgdI7wIzG31Fv/swnzM6X6ONfVdXxdl4pPxs8rCnCl6bF6LEJAR\r\nwmTXHXKNP03BQ2NsQtSO88hJYTULFZdmKfEYXZAH/A8C1gvKaJOwj065Zcz7\r\nNIsksBje5ezIWrkhsz/QewR/cRBwFMrsrdgdR2izglKEoAu+eJcRBul0uyw0\r\nKkCWXAagjLvf/zP4/wM6Lhf3l9fathiMNji0ltbZj2ZEc01dtGzlKWQdlKqS\r\nPs1LlSWRtJUJd2RKL7XzL41LR0lVZ1JQh7UuuTMRv2VUnPOfkdYXZWxf+23b\r\nqOpDX3BXE5vbedIQpWYyY3Tgn2HNvI5AXsLfaxMKEGHe1o1ds5C5q4qwC0bY\r\nQ9ojB9GdfLDLwvTkps2EFAwDzMDVpLZyQnJFWe1TeF8fw81jQhK0Rkv+FH1c\r\n6pqGKefo9THbKwK8xVRJ548e9UKzKcsfWvuA9eWTZ1/l5ZYpgfrxVs4n7x8e\r\nN9zL7dC/1XJAcdkHGXqttWQvBj1gzm42maxr4XD3GhQ4CMuRXqa57kSURRsc\r\nkANQ2IfAI1qUM9ye1Z3vd4mdP2XXHc/3e02zqUCVNZ+Sm9RQmXdFbyoGhrLO\r\nD3SzNqSOKEB4GyAIzPB/q62/F/4Sddlvn6U=\r\n=JC0U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.12_1672193069335_0.21478387852604697", "host": "s3://npm-registry-packages"}}, "0.16.13": {"name": "@esbuild/win32-arm64", "version": "0.16.13", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "965ebbe889e4221962250c55facaa1e48130c162", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.13.tgz", "fileCount": 3, "integrity": "sha512-Jmwbp/5ArLCiRAHC33ODfcrlIcbP/exXkOEUVkADNJC4e/so2jm+i8IQFvVX/lA2GWvK3GdgcN0VFfp9YITAbg==", "signatures": [{"sig": "MEYCIQCOekdz3gpPnISo+Bsisl8Ij4bk5QOUm7dB3KtMD3QeGQIhAOp3JIbEZUAzzKUjDP8jRp4T0ypGiqleevzFrvhuoWFF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8205286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2FCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/zw//T5D5B/3NesbQjFiNqEz3Xn6xu9CMC+MS9n9liO2pC360q/IW\r\n7AnhvSsUtG69ussxxNgHBONrdv20y6Lie7RUFtCy9ZY4LsgCOhal+e66QMPC\r\nRPQL2niy+PTG3SLa+5hZ7Pz8wGmAreyH7tkkCibTSmrNJiNfH/eKNoxU3L3x\r\nDRDS5tajkmqCRC9CUKYj0dH8ZaZg00IUIVj3M9la0+yx6HokG7QvpTZ/hMbX\r\nkbF0mIFiUAHdPZ2cVJ+ByQlf/cddiLmHqXs21sRu6Lsx7kWZIqGOgDmWCHSS\r\nohhyVk3E9MbAf0JlgZSJ5ZmNHomw5HwHVE0/x1FkhI4NK/6puDyQywIhttnu\r\nTwaVZK/lc8oSqPDK3PP9Z/nTViFrjdX2cVAuBDyOaxE02254scjxgYUcGu5x\r\nYHMLg9EL22CWPQNx2qD+GOgqZJndpbNLpwLEZZqlRmlL0UAKb5NNbYjHRXR0\r\nJe18Xd8fuMKRSUUDujzmBDDhNPLEYWruWFbu6di6Ue+1VmS0bOFj/mU3goQw\r\nBH41Y5T/b5boVAOoMkgwJs1S//0/7UJ8yK4+kK2F8ihrAxOu996RcoIAzUyc\r\nbSsZal9T4zzOzl+ith49Y/DJr+HEmTLx0P3es/kazey/CTFFIDEkwxgGQ/Dr\r\n2dq4cDVmMDq+DoIIzbQtTx+vYnUtswukv4s=\r\n=Fufg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.13_1672700226480_0.8971510265770626", "host": "s3://npm-registry-packages"}}, "0.16.14": {"name": "@esbuild/win32-arm64", "version": "0.16.14", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d6ed78742a6edd413e75796882ddaef8c1e23b93", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.14.tgz", "fileCount": 3, "integrity": "sha512-SjlM7AHmQVTiGBJE/nqauY1aDh80UBsXZ94g4g60CDkrDMseatiqALVcIuElg4ZSYzJs8hsg5W6zS2zLpZTVgg==", "signatures": [{"sig": "MEYCIQDXbFnT6P5WY6y4roy9HX6uwLuEtRtpbqu+134TayNAaQIhALhBTBXlOJz6quErw0Y4dUCqPxbDANfwr3GWto/Zj8eG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8230374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd22ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5ig//VWeaO02hWDxysFAtJ8b8/jKlKwSVMxo3rZ7jlcnb4krSTH2B\r\nRKWLUsjdsCWOwgl6cUeR+joBugBkg0vhO2omnVoqozLIYFHs2YnGo5F80XLg\r\nf2WKIedK2EDaryZGx7dGSOH6BtGNrBet8IkVMW7cj0wOGdozaPBGHDCiUghI\r\n1pduPHgpWqel0A7tU/pv1p19JimFPJ1KRb19GKch/CLTydlIQB+AMujlOsgg\r\nVM8XahbCa3rw4zJE4GEGwHYM1dGHWpHBLEhziWkvz7eDnK1f4Ij6XsHsySAf\r\nSmAb1OKEokGDMA68l2XaXjhgJQ0cNen2t7c/95fBxV40zzJd3m6Xb0bBRbkU\r\n2iyG2cB0PM7v0V9pXdLlaTQ+xtxZ3HXZyebSik34vtj3QwfLnbmEirLl0tuE\r\nIcTIWedOsmSPVD1sjztwK42ynIxtI7HVWsctOHXeB180IyFI8bIULoAc9C+B\r\nVhDIVXychL2T2jmyMNsTUzo/pfQoqtw921ffemc08BWpB1ffSjOZFoBKvnc6\r\nHQBdlpDcmiByEBEiCWz57xv/IFq8/UKX8QHRyGgYUTPHAnrmENNPd/4+/c2L\r\ngxe55I1AhnyfEvAt3fkc5rv6YGyNoj5/R4bvc87YVcMQSj/lacSXhaXb7pw/\r\nF3vJ4eA8kSi0eaLwUvc9SA9fp6uWalD0uc0=\r\n=Y1jb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.14_1672863158487_0.793965674319713", "host": "s3://npm-registry-packages"}}, "0.16.15": {"name": "@esbuild/win32-arm64", "version": "0.16.15", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "3fa7189ec92d1de87563ab9e73b3e0a4adbfd203", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.15.tgz", "fileCount": 3, "integrity": "sha512-YPaSgm/mm7kNcATB53OxVGVfn6rDNbImTn330ZlF3hKej1e9ktCaljGjn2vH08z2dlHEf3kdt57tNjE6zs8SzA==", "signatures": [{"sig": "MEUCIQCa/NmF3r12Id2wybZXMOgTCdNb8U5D8NCOUmpscjGq0QIgJ0jJrLDjsQFpjshspXLT6hDcQIyop/FUEnRBAQp13jM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8231398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPKOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoB9g//S4Oo1X82SeY/QZc4dwo05q1UYBKB/jhTR0rf7A+LijqaUrrb\r\nb6WMhfJmS783pSr8rgrNwCu+UjsaJPoKvDUbmWDW/xM2PdzSd1dAbyBcXFeL\r\nOD4fwGcsGeoFjMJGZDzcltv8+I1WNpqmU7EnJC7utFK1DoEuLlRcDGOM9fGn\r\nrd2ph9qXNOEK0pAfCAOPFey3yrgqcy/aaodYl2TR6AmKcTVIFzPpwFsUa/uo\r\n+hE7BabIgQFAURh7K8bauq5V0KjlLrE4meOdD97VvxFdj3KA5CNMJlsM5Dqp\r\n2NMolLWxHjvIZ+i3crWYXEIGKkhQMIGnMeDVYouuFrzm0YiK1UsrgSL2DA5H\r\nrYRiHAodiCDSgNXtxsxR/6W2C7Hcn/3kpGXjgdAWk8GboqlabbxR+lOJJ3Af\r\ny+GdJCNxyCNUs6rTCZIiRhbCps4007cI5j6yEKg0h7wU3TKhmeD5juEmXcjh\r\nOlPLW4jsm/OVCGaOe8d8p7aWiHmomDfeqNJN0yM1kRkdwGOu+80/c/Q49ZzX\r\nAMUIcg59oq/8JNgCJH5YjXy7HprljZetx14pHH0tNt3eywj9D9TZ821sBpcq\r\n7bWfw1YmOuIfZMfv4OCz7m1I0hgWsho7B6TqcXPsnBOTe1lmbMqojpL4DYc8\r\nJgDnmHOI9cmE8VHtK/D/KaUh/uyUP1OK35Q=\r\n=8DIc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.15_1673065102499_0.8016353830779839", "host": "s3://npm-registry-packages"}}, "0.16.16": {"name": "@esbuild/win32-arm64", "version": "0.16.16", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "404a9411d12533d0f2ce0a85df6ddb32e851ef04", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.16.tgz", "fileCount": 3, "integrity": "sha512-pdD+M1ZOFy4hE15ZyPX09fd5g4DqbbL1wXGY90YmleVS6Y5YlraW4BvHjim/X/4yuCpTsAFvsT4Nca2lbyDH/A==", "signatures": [{"sig": "MEUCIHsurltkmieFukb6oedy9wJKIzbN738I1vVz0h8YM80OAiEA/Kb/b780kuBHouiKglyrqg9PplXj9vytydE059bJYcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8232422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0cZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqshA//Xg7OFllnjBdkBfH9WPKpyRz85LHdpXgx8zycqwYI3wEUeuck\r\nkr7SQu/ZbQCVoLcEJ8PcrsDkPyvvxjpkloGl8DM0FY2FgFrnoJzxGHaGMkRt\r\nEK9Iy6bv0AbkxnNUztCqYN6YCXIGPzkHFd/i+s7F718Al0cGLeGqDi/F0HYb\r\ns4KkWyKz1M8FjvnRgaX0Ri1wFGW4E0TH7G9DFYwsMpopT4tJ58N6sSlrBVDg\r\nmaUZQ0TAaREW8fjXCS2wiq/+5D/hcgURa+zDBscmqLhzAy/ske+YL82/pEFS\r\nMQcljrfksLMGIKdLUf7JBaakXkelFWbHdn5UujGf8tIBDiF6f1k2Z81pdYBu\r\n1o2MkJL8o1IiQvXzOHIvN2uRa65K0s9Wb2N0HZYO/+PkAWjYJgz0rbU6Lpg1\r\nj36fesNBvl09+Iwk+fgkXeWTKHSXJgpJRr7poHQ/k3+nUZd1DUkKY1MqQPn9\r\nv7vtVlYxLhQCIGNJc1X5fXC6eTGgculW+09fxQeAnmCZdG/LkSBYPbMsv28u\r\nj9S0RtXaAZ7yCP0Plc4R57+cyt77+UL1JArAry1ty+WCAi0qarY2/I2WJGZA\r\nR6HVuiTFH4CPbejoCJxa9Rshb+qHw6j8h5WJ287wG6NJ1FkbzlBPVkjsGkAt\r\nEyX0Q819ECf80qNPMHqx7md6lhomFGbmeww=\r\n=anjy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.16_1673217816985_0.12935837837627617", "host": "s3://npm-registry-packages"}}, "0.16.17": {"name": "@esbuild/win32-arm64", "version": "0.16.17", "license": "MIT", "_id": "@esbuild/win32-arm64@0.16.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7b0ff9e8c3265537a7a7b1fd9a24e7bd39fcd87a", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.16.17.tgz", "fileCount": 3, "integrity": "sha512-ga8+JqBDHY4b6fQAmOgtJJue36scANy4l/rL97W+0wYmijhxKetzZdKOJI7olaBaMhWt8Pac2McJdZLxXWUEQw==", "signatures": [{"sig": "MEQCIGfqKwvgQwVPXYydECf6d+JHyk7hnwQo9ubRkkX73D6wAiB6U7UVkbxylZJ6Bi1Gfzxf2B8Qx+3mnkcyw36yzmuwXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8248294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzDZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEBhAAlzk45p2xRI0kbHp7c0w34b34LP/IuC3yd8B5tpI85ru0ea8+\r\nB/VxrJO8+eGQmh9iL2ZDNWjjKEQ8jbs4/X8ro/ROUOyQjsw302QHyN3WJzQU\r\nSZx9BkBFGazAlq7ziWCV+iSmxTUvDzWZy5shEPXtKh+y2QioYSVxmKLj1sFd\r\nTXhW8PSjh2IR9c4Wdn3ZMeq9emBzmUVqxuJJnvNBkRnUDKihCkkEYJoVTWlV\r\nJBzdAp8vRxWvmXe9jpaVavulJIUEpQcCrfYgjm/o6KX0HVI0WKxWJv1PHvX/\r\nRrY0eUBZned2d+Ienj7QCjbAcj2PlTDW0OojcM8y+Wis1oTm2Iol3wUunQnV\r\nX8ZbXtxKb0Y0J0j1ud8JQP+aZxNuWHgCiCWxyiU255zBGYcPrSD7wux/2SCF\r\ncS6zZMXBwRRkBsq+JChCmzD5U09P4yeyvBH87uvG3DiezgipcQMnwHK6XKnz\r\n/rt+hgES7oUxtSqy65uYd8gRlJt9tCtSTwGBO0aRpSRR/wvpDc6fyWBxTWr8\r\nstXubi/aSVFJhFQHL0CZk+txV9u4eb589eOngE3EmEDnj+bT+eMVU0UisS/G\r\nIZsBUMpt1HUpzpH+aCukdaRDJepRxhM9HUjb4htVB3H6An5poh8Dxrhc5sxA\r\nzG9TelG4HT1B6AVhKeWfF0Q/JXTOYQCknO0=\r\n=WZ+5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.16.17_1673474265084_0.11378245420457489", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@esbuild/win32-arm64", "version": "0.17.0", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "9b7cb6839240cd4408fbca6565c6a08e277d73bb", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.0.tgz", "fileCount": 3, "integrity": "sha512-hly6iSWAf0hf3aHD18/qW7iFQbg9KAQ0RFGG9plcxkhL4uGw43O+lETGcSO/PylNleFowP/UztpF6U4oCYgpPw==", "signatures": [{"sig": "MEUCIQDs5iDPx2ejHfEKJr47Gf8ykwTYyjWZsi0/Hjm5R8C8CwIgFgQhpYrmy5lsesuUgh6cgqrx8wHMA8eytp3G/Rs1lpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8495589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjCUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmCQ/+JNXfh5Jcsab+6UmAigtPPgGutcnwpdZMP5dYuaJaGC5y6COV\r\nay56MoxL942mimNle9BbcL0Ma4xaZX52sNrFJsdLGrNsI3Xj00zN3sADJXHr\r\n7kvTsdvRHiN7Klv3Kp6bF44tgzVDZsKG5drAbd3fYm7dN7eb9HN6fv9onbu8\r\nvb1+sAEBB8mGa8lnZoQ0N30u1BnR0P/rfSpSk1IZT3PBuGJUQCVgFtLNVr4h\r\nplEKgQ32jE7onhlclUoA3yPqUoGbvbZsEM7ZJ+d9DtGWB9Zfi9ElxaXJLt0p\r\nQP6g+ndwpLgPXiUe5AfWOZXbcjCfxIdETQEd1XwCzCnsHzlEHNhz5tVJA8h+\r\nizplx3MD4GpiX2obbnIIoEWst/xnHH4gOWnfR6YKS2FrfMpdT1/MTNJDnUKb\r\nF70WfOqQ8SmTZIecFlOwtfsTO7zxfifDMIkXiZwcUhqRwjq2nzJEsr3rMKMX\r\nUDVhAYZCxg/18/vuMXOo1tlv744LZqdGuuUDa2WctPXcw2ah2Fv+fHdGFng8\r\nDeVfVnGNicdrudc3DXsCWDOeEm0u9Ohmv7RXptabU11Ph41WvijZwdNAXw6I\r\nz8NpFNxZ8pkw1DuCNXw0BhYoOM10uxc44JbNvP3TYsLgzGnCnm7Mj7MqDbzk\r\nCYGuYwmTFj1DcCIpX1Inxncc/Mrrbjw5fqk=\r\n=a2df\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.0_1673670804334_0.40150544058763615", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@esbuild/win32-arm64", "version": "0.17.1", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "8c136879809cda60373ea45d680f75c7e34dd925", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.1.tgz", "fileCount": 3, "integrity": "sha512-mb1cy86vHBl1lxJhM3XiIGRKRgIO4WxS7/QW01feZuMkApL4RlMdlm95npTZInuh4gPeHEIxziifky5Okayupg==", "signatures": [{"sig": "MEQCIEVweaXA9t4QoQQLAFdvUcs94v5eBMth3a5g4Y2NAgKtAiBiANX2tdZH65cqneD9WhSbtluZXGcwC+aZRb1gz1gWag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8502757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZHhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWIw//ZdOKRksnMIM0q0knW/wGm4GnU4YyZX4Lih5K29/9R3zq6BSr\r\n5wdhORfqc7gWla6eyZelrarow+UEgMgSGJwJqpt5WcNVEPc41+Z8MPvwG3S/\r\nfc8ePKoza2O+ks1SliSv08gvwBpVz5Um82uJPhqCoCwJgEnD/JbBZzxaYFoq\r\nogc/horJrkgyp6frDboPbLmrn99Ogg5M+Friw2fkXv75dFf+6DQhM9VWBr4o\r\nAWG8cRnhYiseX+YaHHNYOcDNyPrPyMNECFTaO2xBP6GeGZtvd8ZniZJqrYH6\r\nCOKj3I4n1zk/dIblGlnuJp8c6AVfEP2WBjkUDDgzpGAzAKITve1SGtlVzDvZ\r\ngQ1btY3vavPBV39umVfNIdSGqBYt3nOku41zol5xiScKtpxZYaoiMb1nFMGO\r\nhN7DiaDM6noYsmPvl69nRAf3ZUQ+uAhbTqRk9gxb4fks05NEmpeoo0miTn1P\r\nPAyvNYYfvpNGE9cL/MOZdMctJWjIYYw+Hn7a96vPAhCuP2n9lOUmfvGRZBVf\r\n6HZb9SwsstbFgh7jbVV6h9UUlxXP5zgZw0pwswv589IxIoghoaxLX1vnVXkV\r\naxyMMdtNNtvC9FHoT545s915BwKZ+/hmIARqmoG6nySosb0f7fBGxYzqplOt\r\nfW2DH4B7oZpjkTMzS8KA1XI217tqPbmUUl8=\r\n=0m5B\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.1_1673892321626_0.9389270404686443", "host": "s3://npm-registry-packages"}}, "0.17.2": {"name": "@esbuild/win32-arm64", "version": "0.17.2", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6b6d31077cba24bd8bc9e173b9ae052b0bef5b0c", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.2.tgz", "fileCount": 3, "integrity": "sha512-rMbO3gPpxuENd+AnZLgo4J/g+BkwxT3NK7nYpSZ0KlYtSdlxYMIMG5pznX7a1ISZKo67aGStne+K41jdkBywpA==", "signatures": [{"sig": "MEYCIQDBQo7v9XYRAlz8d8qruxoLPn+EXqpvCRgTIyyA4maIlAIhAKEJfgxQtJGKqZfOmDOXbEz9u7IawtLmM4FOAwferPPv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8505829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkKiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhohAAgjHbLw2I648fiCerbyj9ri2t4ZXPnDLElsDWAaHkTeoaF4vh\r\nUYEl/Y4qTqyHa7mXnE+zmxMF23l8vSWuAT5dbDS8X11OunFHWi+SF32VopcL\r\n8/sRxu9sYQxUgJ3IhnhtKmoT35goYhLIhuQ63e1qbGBrt3X8l6zVQddXPCEE\r\ne+acsWKa2GbxYvgmhQlZdBzBCVX2/oV8V6cDBWZXfdpGdalqlVxOC9H5gOyI\r\noepe+OqlPcvp22HeOooIDC+waEec3O5euP9gCoXdGawazDMPMXdnfnOL8G/E\r\nTw1Yv3glJZ1e17Cw9ynACvQZh2smKpksChEV0381t8eR8HBsuEXKeMbUN+QL\r\nNaRCpjlQSwnlZB91my5So4AcM08YYwTfkzeTkAwWsRHiO9RUuI0JcqpO+734\r\nsbYPsAdgwmAY76O+9j0+4Ngz+26EMsA8ocvUQoUcIGim1AWjFrc5Bkj6I2CP\r\nzJmB2y3LOWutvcymfFsoj8mWcwaohsiKpwwb2GMk0BnHELb/xAcQ1F8XQ2dK\r\n3DRZmHJUL+2BxBAM1u3uTuNnDHsUnNqgaJYi6XBCVKOBFUjJXEl8pBHKh9Ei\r\n8Lz6qUp0UNMRkPyzrqfyLHuBjGT2AXLOXm5ku37fAxSGyLhXj3iqvvrTcBif\r\nzG/ZG8dR6vC6NtmqTZsCrpqb6dR1amFPIwk=\r\n=3q9P\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.2_1673937569977_0.610369578161408", "host": "s3://npm-registry-packages"}}, "0.17.3": {"name": "@esbuild/win32-arm64", "version": "0.17.3", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "14e9dd9b1b55aa991f80c120fef0c4492d918801", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.3.tgz", "fileCount": 3, "integrity": "sha512-0r36VeEJ4efwmofxVJRXDjVRP2jTmv877zc+i+Pc7MNsIr38NfsjkQj23AfF7l0WbB+RQ7VUb+LDiqC/KY/M/A==", "signatures": [{"sig": "MEQCIEyyx4zyphvpi6h7ItMweRdKm21/YHzBZAQz258sE8bvAiAG6UXzcOhXW8gsXItRQKQmcH8UbMnOJhQarMQ1xgI42w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8506341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEUCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorGBAAnOMTEAh+4/aUGsF77H5iD2Ud2HMe1PPCqVhYs1esE2nT2k42\r\nJPrHcLxXYG0A1KeX/PCm2QctRxagdvZWkNOBe7pzml3jTXn4GiYmKoxTvgSv\r\n6aQlMQDO46P0NASppOLc+zEVKSYByu9gZeYcefqdEBEGc5newTvzUABaQnk/\r\nM2tI7rDeoIl7F7YYdRGUJICEMEtN4cB8gqv912/eit7wkrsu5zLHJ38rlGLb\r\nTWzSYZDlOmBNop0JXRt2U9b4I8TngaYs1CEmohcWW/Je6BDAXa4nOM1AQwpv\r\n+LJrgndH+h9l03WU8ZUj1IyXhVarcJXpyaet8t4W7UcEH5XWHFmuDqrAL6B7\r\nxEhPI3FGHrSR79eji9O0K6SbjCf9e5LprqeEOfx8cxTiEiYP0JxBF5BlWjZc\r\nULQfkmv4CgOCzG+0CaU4lou5haekEI1Wxavv2LIHZ8yBc8vvi2w0e0gnWjn9\r\nv6UBUF8M05IvdMjUCDXnnaBzMLZOWH/vLjRYwbmmq676jsMW7n8M9sWO7KAc\r\nK0lI/3FdwIRXtInoLbLSDhX4x2EuhzDOtt/zngz6abuzCG2v8I73cxS9sQ0z\r\nwjcscIPwJN6GelL4MkZsHD/x+x1tO3UUWKduvXePnZIeb01xBiUcsNfWL46w\r\nc7JJsTybR372lXKcc1GsI9884N3ZIlqqYZo=\r\n=sWZf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.3_1674069250557_0.8553900684416478", "host": "s3://npm-registry-packages"}}, "0.17.4": {"name": "@esbuild/win32-arm64", "version": "0.17.4", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "424954b6d598f40e2c5a0d85e3af07147fb41909", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.4.tgz", "fileCount": 3, "integrity": "sha512-0HCu8R3mY/H5V7N6kdlsJkvrT591bO/oRZy8ztF1dhgNU5xD5tAh5bKByT1UjTGjp/VVBsl1PDQ3L18SfvtnBQ==", "signatures": [{"sig": "MEQCIHNVmIXj80LnUDrW9/lQd6vBkXAxwRXWkB6rzHAJSTDbAiAhsmdh9+W2tL2dsDFl6CaaV0224jMDsLkO51QnjzozwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8512997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+Lg//XEiMtfgZBhu0F10UbL9XnO9pLxhZdFv6TvsL6Ms7iNxb7i8g\r\n2z8FLg2epILEAKB6nDrttstdS8w7xatGyKquxklU1z/1tME2xqC1dgwsBG/m\r\nA0eavYYgE48iFBz8um5KhOqZ7Wr3rD11nMk9MiE9PoTL96h0pMd04umBYJR2\r\n4GigO2vq0PXyw+OiXCTQ0O4J5578owzMhLJIjemRem3tTF3VdyYYIxolXbvB\r\nEBGgokeTW5jOjJSctEr10sI5AI5njwjIYEMfsm/JFFbmx73p+BMZ5isSXScB\r\nxddvXWowuqdbHyILv/S9+1r8nza5OKsX4pF/dWx4ZtSVbMfVtjYN9JglfV3N\r\nwqyz9fpjtCRZrfReKCesHnRNcq6z16f8hTk8pgxgLAXW+L8hpxuVL+W8ABLs\r\nMtJVXp3M7/XJFO499UGwCjujSzrO6DqANjZBsL9ShiQRE2Voxn9fkHucJmTw\r\naHB7ZwUN0ruct/Oguf6W3qOGfZ3dv6rUqIMQuhS/3lPv4x8IfqUUFoZU7S4u\r\nyjnQ+Suws83PeKxdyypPCnTO8r5/nWNHbxpz+XvRHWYI3nkPXaQ1BABpVeiv\r\nAO7Oxe0Ss/7BlqQK4b4eEtUjRKChyIbH9g6kv7sUQ+BtIzqIwnthtrxDkt+0\r\nd/DI860lIVE78bLeniOaMCaBFy6uX9xaLNY=\r\n=kvu4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.4_1674368004497_0.3438698052422433", "host": "s3://npm-registry-packages"}}, "0.17.5": {"name": "@esbuild/win32-arm64", "version": "0.17.5", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "2a4c41f427d9cf25b75f9d61493711a482106850", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.5.tgz", "fileCount": 3, "integrity": "sha512-4u2Q6qsJTYNFdS9zHoAi80spzf78C16m2wla4eJPh4kSbRv+BpXIfl6TmBSWupD8e47B1NrTfrOlEuco7mYQtg==", "signatures": [{"sig": "MEQCIDBxJbwuf4aTaiHq2LdF8J15vuZ7sTXA9nnUrmFD4WE+AiB7CCPazonGZ3Y21qjXw5/SIOiW0573IyBX8K8DAf8C4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8517605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/3JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmYw/+LrcOz1t1JbhCg2mB5T2QlwjB4ZOgQ3DMyadKONkobqVJr0fb\r\nqS4+3q4FIgZ8NeDJfik7NE6ZqsfOmq9RE5Czch6enlOVfPH424DD9NyQ5N0A\r\nQBmuvMCnEKO6+gjs4xVdcINpvWVhl20hBIyelix7HgmbHNG8o62WCNf1e3lS\r\nlV/lL/aK8/L+hFdc6gmUMc3wHpSyKBRybltjkCdKHl9Afqxxj46Up/nAv9AL\r\nZB8u4r/qW27PWA0NVFGggy80BaPk7jYbT1oCGr4TB0f61bZ6FH9+yjlwu/rv\r\nfJn0JLndQP4kHxJSeZrBnPDlJGCi1O8ZgK567or5SvSfPb1tdLxBx+uuJTYI\r\nHMh13+Y+dxMpAhSez5EeK3I89T5bTyMlwp+Fx1O5s4ySbpNLVJ22ml/d/WK5\r\nPaJF1GYKh6ABCJcJw9McfcgVISO9IgSsB4SNIHuXYkU7t4HaD/L0EaV6dTFm\r\nM1NDaQ+tENWS1T9hPZKeKzKCySqrDtkHRCNQWWG5W/Rboy3i9xZaJogiXipe\r\nhAk+UcXXUCPkBNv3hIu9O64SlY6Qm14whyuu0CJiZLQRE7PXRAiuomS4FL1P\r\n4qzAWIEeGQ5oLcNADbJC0AvxrPEhaYEwkPdp6j9B2BrPqHEAorVr0D4UFnFG\r\n+WUcE9WPbk9lUZOu1KXEyTYVeqROb7ukFWs=\r\n=dfpP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.5_1674837449152_0.4866859143707598", "host": "s3://npm-registry-packages"}}, "0.17.6": {"name": "@esbuild/win32-arm64", "version": "0.17.6", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "ace0186e904d109ea4123317a3ba35befe83ac21", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.6.tgz", "fileCount": 3, "integrity": "sha512-G95n7vP1UnGJPsVdKXllAJPtqjMvFYbN20e8RK8LVLhlTiSOH1sd7+Gt7rm70xiG+I5tM58nYgwWrLs6I1jHqg==", "signatures": [{"sig": "MEUCIEiaRGaFO0ciHSN0meXDJgHQoCoBSiTjmRjckuR7sPcQAiEAxWtY3fnHgJJfbQvHQKXqzhzIOq6TK7uQsnV1C2DsTvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8437733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TItACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqydQ/5AX/Dq7B60wGig+QtS7VUF4pjZllfxEUkDu3/KN+kQf8TBMDv\r\nHIEoe+E4EmCspz+wTt1pRNhM5C5ALrUeC1ofAbWGyPs95fZpAB9h8QVfLVIW\r\nnbS9lt0Ihj/oaJT0qROqNE+LInLynFB80QD84inPLAAVsBnvBgSEe8+icGmP\r\nJrWNlncYpHNh8Kt1DuCnKRFK6qqpJsF4lBYBWjjdDfV/jp5RKiyOg0HD/yEf\r\n5tAuEbueLl8/biDdxGS/WmH5DLKRit/dIq18SoXWaQkmy4bLBbgwIXe6HWHs\r\nnzXuaqjVsSsbuMuzxIFzg4XDT/eLlGPtxwlcA9etK/pzzCDnP84QW16Ut+/t\r\nqaAuBTb94xe6O9XLV+BDGNx2dsI3v5vJ9/3APQ69Kxiy7Mep9wJOHLFaefqP\r\n67HsP3XHxI7Rb5sdQfYXYKuOogt1oNqGjid+hYSBc3gPEPeVjMmaRHvrdQ0L\r\nbmm2oaOt3dAKUGXK2JVkcH9R8eStzs4zhxCoja+/zITPW0Lsa/SZsAkfQepT\r\nGjx12vRTkL7jJYXVLy7Tpl3ATvL4p8M6xJ5gakk3zv1QawAOPmU4LLmPSvj6\r\n9mL35tACcIjZuEAOetEOTWHiQE7d0HnkhdN9CIMl/Vi2Eifc8DyihVp4GK7d\r\nvK44hmdbrh7Vd4O8g3rmwfPb87XPGGpkMNg=\r\n=avSF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.6_1675702829149_0.33750227859194126", "host": "s3://npm-registry-packages"}}, "0.17.7": {"name": "@esbuild/win32-arm64", "version": "0.17.7", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "70865d2332d7883e2e49077770adfe51c51343e3", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.7.tgz", "fileCount": 3, "integrity": "sha512-G/cRKlYrwp1B0uvzEdnFPJ3A6zSWjnsRrWivsEW0IEHZk+czv0Bmiwa51RncruHLjQ4fGsvlYPmCmwzmutPzHA==", "signatures": [{"sig": "MEYCIQCwMQQcq5LnBRC4pvxZvuZDxiQf2OIYTDy489huyCvbMgIhAIIP7zHvNzZdSYDkzuPZeR5zXJ65OeVUf54XoVwQ4H76", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8439269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XMXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKaw/+N8V3w0JrCiJA57RE03uq7iRaLajKONTa8dpBU5v0bTbKW7/Z\r\n6dquE4Lopqx14zY2JDRz+MxjZETQLWs6Q0apQLxBjprWw/AxByoNr6Guwc5L\r\nV8GYuQKA2YNkQvKWpBoMVvGwFzXLYQW0S1evJNLznVAVTE2+5Eq+WD1fH2GR\r\n+zilRl7Y/wWr20qraOYMKb1e7I1eFuZmBZ3dl8VtUakClcBtUFiUW44U3qUe\r\n2p+gO1PcB4OyozDghyAikVyt0DuyM7a/RIPCNIB+ThHxxYMUZc04SbdrR5j0\r\niXhZrYJ8rnVtn3xYqCRFINmtvH0MDbO+IQT42bVzO2MZwI++1cE63FRlXBRv\r\nSeTEsfYo5YpAVAlH1JldRBm2beJo6iEXGRobpk8lTGBSd4FuU3OfRGiX5cJP\r\n8SGXzsGcuvLSr3G0lM/jLOQoAG66Yp7usHHLvQgSSrXKWqazy17he4UXuv81\r\nfqMezfSHWKFX2YiTX7qaBVgf/HL3nZ6X5w7SgIPcF2Zcj9/LaO6qJysVzmIp\r\nZF2WGyDUgGnmVjiFsQ+lJqsidUaT+PMVSayEjgJeJ5I7ylScbEIol1FiAeoH\r\nmrPR3GH5sTei5UZOGPg5Vq1QgnX04o6vkVFLl4DNq0SsJG43d1f5oN0mFvqq\r\n+xvzqjYsp7MM7l7KngycDZPa/lgD711/c6w=\r\n=DoFQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.7_1675981590877_0.32816778890290865", "host": "s3://npm-registry-packages"}}, "0.17.8": {"name": "@esbuild/win32-arm64", "version": "0.17.8", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7c6ecfd097ca23b82119753bf7072bbaefe51e3a", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.8.tgz", "fileCount": 3, "integrity": "sha512-G0JQwUI5WdEFEnYNKzklxtBheCPkuDdu1YrtRrjuQv30WsYbkkoixKxLLv8qhJmNI+ATEWquZe/N0d0rpr55Mg==", "signatures": [{"sig": "MEQCIHzBW1e/hmH8oM2jWgH5Rz5iWmfaMtqdsBEvdNQ1brSVAiAma6HvHbVUcmRVni7Kt7xnM9aM7J5ijqaULTdLf+rQdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8443877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6doyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8NBAAiHNVtFk4paDuhXPH7wzTLCKFjcee3inZdRMtWTuqWvaeI9ia\r\nDY9iZXWKqb0ApoRYcPZp+4X/Kxtmb/b/r0nki83Rt6rkA0NmYqvGkiNJyL36\r\neMJejeLMfEniKUesWjV90qp0Qe2RpOlwujGug7xIX7Elb0Qdyw8gVLc1+Kj1\r\nJ8IVhcpQ6ObaG8oBVTVvEpLqk/E7ktYKLce6cfsmvXylYvDJuL2FqLmYR8Uu\r\nvE0Mg3Ep3wD4OGHNK/A7Ihug/unACtqKxfJd2qqpAaRRRjXi00aAQX32KBqz\r\ncxM4HdF64TbBVpZ45fCq5u1v8Cyurw14Uju3NviPEtDbVQ5/I6ChpSrJp+Ou\r\n4U/H1t5rPJynoyttJsTSnP/Qi/zgrxy84fAl0Rzxif5C8SFU4jqPSnC6DO+B\r\n8KygTNH3XP+7XIRv1RX8V4RK09/Yd4yoj2E4svwaSVUy4etczp+bxRzeJT8X\r\npVTTlllQbqdhXfVkoIeoDO4KZtO7YOSzQ1O85DLjCtQ4Y4PV+C0gCK6Xg496\r\nAt1Y+VqxXN1nFmkYxH2Y9GqYHu3gu6Z6YSzQPDFNZMLTZnwTtn8ElF4p6FTn\r\nFW5x2WE8sacuEjOygMkkL6akzwaaAuvc+W/DniilyXvFfpY2Mdt8N1U3hqRF\r\nmgvmi9TNzm552zmFkv+oJj02NcjyvkFPQ0M=\r\n=gQru\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.8_1676270129871_0.31085888089179625", "host": "s3://npm-registry-packages"}}, "0.17.9": {"name": "@esbuild/win32-arm64", "version": "0.17.9", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "85d396aa986f5671f151df5aea1d54fb7626979d", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.9.tgz", "fileCount": 3, "integrity": "sha512-jrU/SBHXc3NPS5mPgYeL8pgIrBTwdrnaoLtygkQtuPzz0oBjsTyxV46tZoOctv4Q1Jq06+4zsJWkTzVaoik8FQ==", "signatures": [{"sig": "MEMCIC0duYh/7ME3iQ34MG2D9gJBi2/Q/O9FSHrPF/Jz4323Ah8fXD39WiAxxuNUISJDaHh/Mqs07ibs8UbgLbUm6QYq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8446437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mAkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwYA//eIlrv0rH16BsfptbyeLZdxaUEEMQfr0itz9JLBHIeJPKt8tr\r\nZ4jrOB/GZT2WTFTqHUpCdVa6qsQBT6gFuKWFuFkbFxgWTfPMNcfJYT3TTHxc\r\n5XEPgOGV8+63IwZU+ffteLkg2lKWdv8JkW2jf0sRDU1QZLjC39Du8M77ip34\r\nypzaW6XgmujT3sG4k1mPL+mkY/lIhuJOXIScjlRmCdEgcxsj0D0s0sASLjRa\r\n/4P3aH1EbKhm99DUKl9TE39JaSsLaPtRwY6QI2VuFYsDCTfYeGw0FwXeblmL\r\n6LRIfODlulYCpw2wpbfc985XG1lbXUeRc8mAgfMm8UowMn7hGiFc6zNO+uos\r\ndsD9QDXIo+urvmurZiDL0hUcoigp5wK8eyMqiRYHB1y1wjzGffHUISx7SPkG\r\nhvMyk1qohXYESemjNDY0itB/hDIcKmp3m2OZDH6HTadGmZOtyPn4TQYbZK97\r\nETcwchHpT6fcxqRFH1woa/qes83gpfpv6TIIfg+ufQnY/1V2brf4pXU23/+M\r\nrrGiTE5ynFGEXzTToeVwu8V4Q8RVcF+u5vmjeuNqAL+Nsm2uNz00wDRgooGD\r\n0RMC7z9FIkSa2kRmG3ONazdV6d0KlCSPY1c8kYKPra7CiFL0UEoC670a42rX\r\npkqS0DrkKaHkcCRBZ2IcyXq9H9xQGRDeoRg=\r\n=Ni7U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.9_1676828708390_0.4336469847123847", "host": "s3://npm-registry-packages"}}, "0.17.10": {"name": "@esbuild/win32-arm64", "version": "0.17.10", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "1ec1ee04c788c4c57a83370b6abf79587b3e4965", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.10.tgz", "fileCount": 3, "integrity": "sha512-fXv+L+Bw2AeK+XJHwDAQ9m3NRlNemG6Z6ijLwJAAVdu4cyoFbBWbEtyZzDeL+rpG2lWI51cXeMt70HA8g2MqIg==", "signatures": [{"sig": "MEUCIBrekIUREf63r5dRzF62ET9BJtlNjSoc6TfzFQzj57Q+AiEAxIoy6+jQ97WieZ6gKkvf0WoXn2gj71McfT5/587J7d0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8446438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87PiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZjQ//ejELzr9d2Acxo9Ut29nLMVXDBqgxld/uUyc0TtNrPMa33pnA\r\nUwsem6DPNbZzFN2Df29HjxtaBCF2rUT//v8QM6i3sZ5C/h+5dgMyY9QNPlU9\r\nq+ugcpL8BYO80cDngOQiNnJHR1TKu+RKMXL1rfsXhot258qV4i30+S0jcH0v\r\nACy+XUwWwLwN9wRIqkPLplelFSdfamqfIzMtqbFuL1GOBEb+IcDFyj+rGx4s\r\ntW2di9ofE2aTotQZzT477IWi0iTd+WH/XOAiwFsntHLuGUbhtRx8PhlQK+K3\r\n0d6Ufdk15BYElo+XmEPVk3Oa3m0PHjMc3Lwsg4zCuzwmjWMiUzG85WyXbUW/\r\nB7F6Uf5A2c+Jc/YGcR1ukplSAX891txTarKz/5trFYNrc/F1M+kf0KuceadG\r\nUXc+LOltbM0Hj+tLotr/haehd6MskviQ8aNGu7XSQp7bKXFFDurvdDvYTaXN\r\ngXEhg2SyzrsLG+L1LkHexoAgfCZ3/sXMKZZJTweT4s8P8FqPCrLiXGbE+kZ/\r\ngcM52D5aBZgxHt6d1JCsE4aCw+Jp37/qOVgidfb0RdkDTlhgkGQC3O97ToXQ\r\nni7QpRtqFf8IQQ6XiAfmU6cGrcvZUlApql4aO0ixFiRsnebaVfeYqicMzh8i\r\n4DmUNnDolK5Ukx/V0odbol4Xwb3UgW/WsiI=\r\n=lkjx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.10_1676915681746_0.8548559753117275", "host": "s3://npm-registry-packages"}}, "0.17.11": {"name": "@esbuild/win32-arm64", "version": "0.17.11", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "47e6fdab17c4c52e6e0d606dd9cb843b29826325", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.11.tgz", "fileCount": 3, "integrity": "sha512-vtSfyx5yRdpiOW9yp6Ax0zyNOv9HjOAw8WaZg3dF5djEHKKm3UnoohftVvIJtRh0Ec7Hso0RIdTqZvPXJ7FdvQ==", "signatures": [{"sig": "MEUCIQDRa5wUXKoim9DuPDGt73gVaOHHwP1IGGwzxwZ9o0sJ/AIgEqPga4OMjg4WTZArdYsOZcby5Gg2oCh7VX3Q1GEOf+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8451046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAnc7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1HA/+Kpvthb+XC4RnoKz8kj6AZ1GE7GBksnp0hz6Pijf1H+IOnoUZ\r\nROicGdzgML5I0UkdWMr383wBMm7qj3a/QbdIjnkLEfwIx7zqP3si/G+PaJqc\r\nLD9HAlRZ6Afn/dCvpoCglBCwlNh6HqE4hc9oPLfGTEKiWo/aoFYnp4K9Ewt2\r\n944NmP3bXEOoejlxNJz2apFCJEFyJIxsklnQT0w2+5cvSmgd5szOMNRr3Bq/\r\nsZk1icTHq+rZNiHP3Z+1FyYTDplMvsjwidQJ2F2zVlXkm880MC1yAxkp7hzz\r\nRmpjXlGvFVmbDJH1kpzuw9m7pq9sHARS/YkCfYrMMeSAa+eCkHezYHWFthS5\r\nacuzfhUsgs3sn3DnGEDrqsmjr/gixKzRqrusmBdNEJ6A5EdZ7JWv477IM9E6\r\nY+79O2ERDbA96LMEGn1AI7HqPwZ3pmNeNJgGIOeU9nUvsrU+E5xYiDlv+S12\r\nimjUJiYo+4G1rzEQ7GgGRvJety8WPpcFXXeTjeBMfg35sQjtprnNj0QXUeom\r\nots45snYF9P44JnBBlwMYsZUT3d1BV9MP1esF94Cm8vp36OxnUgyqiR91Ld2\r\nybmr06Lf0CDuCj2sDuup34xidIo+fggjtpxdYXIb5SB9xb8h+wlCi5fvaEG6\r\nzip7cH41XzAjcGwcNltgJ1Gqkjp/0a/qD9E=\r\n=e5Cn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.11_1677883195185_0.2508447274722767", "host": "s3://npm-registry-packages"}}, "0.17.12": {"name": "@esbuild/win32-arm64", "version": "0.17.12", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "43dd7fb5be77bf12a1550355ab2b123efd60868e", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.12.tgz", "fileCount": 3, "integrity": "sha512-zsCp8Ql+96xXTVTmm6ffvoTSZSV2B/LzzkUXAY33F/76EajNw1m+jZ9zPfNJlJ3Rh4EzOszNDHsmG/fZOhtqDg==", "signatures": [{"sig": "MEYCIQC/1hh33N7dCF51CTavovL5nXdqc+xVrlEKTWk85WTu5QIhAIH7Dl2HzghYAvfa6z8NJ2vUH/vzBQVfdhnjE/TtETT+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8496614, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAWoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOiQ//QKDmmebURrq08qz6C5Lvl2FVYo4kqn86COeqRrUo1vlRVWcI\r\nIJ8pZbiHAVMU7/9epPn2i/SlYVEiaALDYLmUpUjCbaJXTTHc3xdTIvC6HWNz\r\n4+bFlt1Gz7A0sOKrCvzBv77mAlucYbJctC1wxLBQ6ef7p6C9l8LbTUixZtyn\r\nSi4zPJwmRXeiqkXBa07Mfj2UHxYK4zB5dAt617tTgbXOFaL3rzidrha3jU5N\r\nC62SqWkhvejJc+KxQwWCTMVIFnKqD0XQqPwUJeZPY+R/ootyI1TeY81ByRCI\r\nLgsUdJCL89iEuyxnMnq0JU2TDpZD3+bHpcJvqhymgyCXHIKFHHGjHk5ywM3j\r\nGTNUrG46ylg8RZY4R2hH2eV75g59LXLEftjcdX83/tI2ErlzF+t1ltgNufwq\r\nSj7HOb6HNUzyQ4cTtaXqfrAb5r+7Q4aROtvJR++fzpvyMEexJWOJCOgOm4y7\r\nPFKLkXiFR6hIthmFolOgtkQtElI0a2t0OiiyYWoirjQ0vLGQxlMPWhg3pLnT\r\nTB3xjTnHb2Z3bEAbmXXVSzCfqpbxJPTwnP/Pv3oNd3BCagsdEy+xa9Cg150H\r\nNh/kK04w4KQ1kRtk9J24b7eHb8gSWg9VMHitaOAbA2Y8JfroqOWaLZWckSO+\r\naszihUD9T5nFnrkjlVMqRIALzEiBmSdW+bg=\r\n=W1sY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.12_1679033768121_0.6661865161778739", "host": "s3://npm-registry-packages"}}, "0.17.13": {"name": "@esbuild/win32-arm64", "version": "0.17.13", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "e9527611f3c9d2c2be61c86d4eb9077c060cdb39", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.13.tgz", "fileCount": 3, "integrity": "sha512-FJBLYL4PkrZGeuHzEqme+0DjNetxkJ+XbB+Aoeow7aQ53JCwsA0/mo8sS5aPkDHgCnMkN4A5GLoFTlDj3BKDrQ==", "signatures": [{"sig": "MEUCIQDPJNw08L4BaKKjR0TbITiI3qrwerE6Vi+bHBTOl4m+MQIgctaHDTM5RGQ27hyM86XbbAReAAgGHqPW2cEYZbT3jVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8498662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfJ2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfYQ//eC2If8VV5KbtIc88cUKeLoKxqoNJlcyw87V+v4BHMml0+JQD\r\nlnUjunbXKL2bC47W8easQFU9pb5Jx4zEjBQSzCr19Sl1rF++DQHhFWCbPbhE\r\n+115BrXqXVlvHXTBCQYREoz3N4HiO+rgBUHbrKLPyT/UnV3iRzbSr7JQg8uk\r\nCBwpbK+vD21jAQe4hIQF8Zb4//cC19jP1TxHk8ChZX9T3KZ5fAanPM2hF3As\r\nKBrOHsdEREVLcVc+iHOw489HjyJnLonrWn6NfrQiE63yYmtfDhgm1NXojby2\r\nY0yp6KvY1LE8IqZC8Zsh3PJKZiBOSA7GkQhZLufqvJI6DxV18WulKvExgnPx\r\nUHhXUOuTqLMpolfslrIVomXQ3Y4Yg2p3EgjfXHVIRxDUgciTnFznuW+nPcwF\r\n//I9mLb9dr0Dq4BYqz0witvzgfgm6HB38cxCMupYeaQFuP0anahTD1vtfLRZ\r\nIYiSsb+VkVYkJ0qwnG4gF7k52xcsnzqU8rA151/aTY/d2enO2zs5ujA98qui\r\nBUR3RvGc83oYVN1vlw4+iPuHrRqrbLxH3JKtEYgm/2ngdpmUKxm8rVmcW47T\r\n5a+1CEdfX0Rsp5lbLADZTFdyxK0LbiaVoG4ilXsm5jqw34gVncB1aYH50btH\r\nTN05CxGyJr0wlSPF6XPNx4dUXK32ua4vjzs=\r\n=ljLo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.13_1679684214165_0.15662870471151735", "host": "s3://npm-registry-packages"}}, "0.17.14": {"name": "@esbuild/win32-arm64", "version": "0.17.14", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "dc36ed84f1390e73b6019ccf0566c80045e5ca3d", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.14.tgz", "fileCount": 3, "integrity": "sha512-xrD0mccTKRBBIotrITV7WVQAwNJ5+1va6L0H9zN92v2yEdjfAN7864cUaZwJS7JPEs53bDTzKFbfqVlG2HhyKQ==", "signatures": [{"sig": "MEUCIQD19pBwPuXjtO2xVTaD62/gm6Ak76hxtOm+Znhra8VvlAIgVcXpu4Dn054LXx+tU7OW9KjC/+hR306fF7kQLOhg+Hc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8533478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7I8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrttw//TV6Yu9ZBupl9F2rz6PVYogkfYxtcoJX0P/BfJFGCQx6Pc0zP\r\n0IxB7dMaUUk8HopxJrQAs8kNWMiKFCUGJCQ3ID/OpTJdnYOjlh/5KDr561UM\r\nLydiF7DGh7kGeBaEHBT/0G6EkihW0evkmvY7bPdpuOp9eU8IGcLA0z6ddSQd\r\ngUfwbpEkk+Q86jmoVd9AO5iYZeYA7VPdRQ+uMBIe400Kx+7nGRoxGMB3MR7N\r\nlSFBSXZzEsHUyCr18rGx5bWL/o1+u2fRT2it41axXLe30WlMStIGig1an/83\r\n+goDUaZ9YYS1Jhr9Pyjn6eKtTBKUZKv8G8HurhQO3qx5ELWXu4Uj16c5AngN\r\nDR7cfkCgXVwbn+ILv+4uvRS2Wok0jZYOyuSGrLD6ujSa+OzSyrxbiFbkywk/\r\nEGKaovT1ZiMp2TtaAT22/a8yj3p2c0m3zr2kgFaUgqLG5pcktmOzWoIqKr2p\r\nAA3hm8TGOGS/PpXeg+VNT/ri8HM9SwydOAED+WbbDVp4vACsfXwcK9aeJ6IH\r\nmO4j0EeQiDqBvV0cm1pB9cd+cJ0IhUd6aqgT0JRt6mk0QQ2+X2rlTkRgpSWZ\r\nYHBfavhXSfxQ/9Tis12/wY2e8b9jEL+OauTxiMz8RDBC1LnLu4ypxtgPHWRM\r\nGi4C6xily+8CyD35zVMYuLzuuEIVWQKW924=\r\n=YeWu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.14_1679798843815_0.7552792268676947", "host": "s3://npm-registry-packages"}}, "0.17.15": {"name": "@esbuild/win32-arm64", "version": "0.17.15", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "1f95b2564193c8d1fee8f8129a0609728171d500", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.15.tgz", "fileCount": 3, "integrity": "sha512-W+Z5F++wgKAleDABemiyXVnzXgvRFs+GVKThSI+mGgleLWluv0D7Diz4oQpgdpNzh4i2nNDzQtWbjJiqutRp6Q==", "signatures": [{"sig": "MEQCIFe5WiUmTzuPuvXqP/27E8rPjEmX1+bT1G7TN5Er0SDRAiBoakxKigMHVutbTzPtnjB71HeqFLWsHSd1BMdJKRPEPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8533990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK+YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNjw/+MbWLcLVw4bqo6s8RilXPhuvp89Zyt+ppVJwjjcHblSCRZ8Fx\r\nTAdMNSWrdoLgzvlZSyAwaOgfpaBl+19GVoQkMsQC3y5naJf6NkxUCMRq6Sfl\r\nc4F3UKLzln93oVeNaqtP4mnAAs3VLKpzTnAUQJMZ2FXTDLnKr/W8+qyLFm5r\r\nNulCF3cpeFZ/dsj1vrNw3vu9Vq7ow91R+7DE/GpZIRyIvuKwZBubOX674xSJ\r\nQWcNoTM88xxRd69oZ5NsCovY+pszyxUSst0SObGK7aaA2MmjmRYvfI55854z\r\nq04qVjFiCsIOl3W46bSoh7FwnXA5am4c+WQI39Jsj7yVqr1iSSeItEUEEpk0\r\nMcuzeYnjm1GcdLqIiALDBruFl4gvFTqXfJKK9xDUNyFtSvf16lXkGhiuEJs3\r\nCoFSbWfb+wa5E20ztPU4YeiJCvdTqpHLCNDipn7SYTiH4PBYuaqkGqvX8pKs\r\nI+FVeRrJVsgNP+6MAKi+QgpfoKp7WNBBiovZN8hUhkzO2NUfPv45Yo7XBsxr\r\nIc5kxN3xkZoBeiPBiuBQYbb4NST9dXyavsxma3+kQi+X31Wom24qiYxqDm4X\r\neT+BCYuy+8XwEB16MZzwKcpt6ZKZjzOP9mHEsf2WHx0513Iexp5S1Y5YDN1H\r\nwEGeki9d0ubJRa03fPRH0e6adxwb30zFDQA=\r\n=LIIa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.15_1680387991922_0.5722263688714075", "host": "s3://npm-registry-packages"}}, "0.17.16": {"name": "@esbuild/win32-arm64", "version": "0.17.16", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "423f46bb744aff897a5f74435469e1ef4952e343", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.16.tgz", "fileCount": 3, "integrity": "sha512-TzoU2qwVe2boOHl/3KNBUv2PNUc38U0TNnzqOAcgPiD/EZxT2s736xfC2dYQbszAwo4MKzzwBV0iHjhfjxMimg==", "signatures": [{"sig": "MEUCIQDHqcpUIOy+mZpD2XKkCC62H43SDcNCGzQaOuw1PsrqRgIgFEgoZYNyHsf4EAHoFSLsdTKn4EG8hlidpkt50FmshIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8536038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5HnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqe8w/7BPKqRNnyDOc89S6MXqrTNUy9D6BDohvlj72fuTABg7o+RWnc\r\nozuKUNHiQn9wk/8XhdXmuft1x5qo7XkPTGEQ7m8JnKoXxhMlJIDSLHmChLOm\r\njn+hZgXMWr7At7SVHu6xubk6AWH6yIofOnP4yT5dL1mGI38U3/D8RPPKotXR\r\nPm8HKC6OaVzVLHcdbyJ0LxpH2lT+4kyRK/9ZMDSnHfr52rgz+T15qk5lkalb\r\nqfzVfsZiLokTG42s6GcxCg3F5KH0X42HYuTWdYyAWDhaeSPO3zjAEd+vhrKX\r\n6stJY3PDS3rZNmdVT1nQNi5zdbK+7anu8YBgAzKGia9YOV4vW8p6BpNqndmW\r\n4AHAZYfarzYJ8MiJsE8nn8wQZVlxxGTT/MjTMzn7CKpBmE5YrTDrhP3W/+cn\r\nhG4N9Sw110Jjnwo3gXuNufBCwBg+8PLOQ7j7AtcKiPm7gggH8zJ1H/zGcIHz\r\nBpN6G9si44OE5tsUJkh8K87emmDfzA3uU8XTH0h5T/cbXYvILryj2FxKSDh5\r\nvQbzrGBQuA/DYMOtn9N6LY9YR3WV5VfV3jSuDOTDtLbMRmbdRYa1wFkFWUqJ\r\n8ECKQCH9F6I09PuOmjsig8bnCvZ57pPA+1KbhQ7C4viQcZzXtgBE7QxotcAq\r\nPfRj83FBFqKepdU42OkDQoWV4umvXDm+y8k=\r\n=06dK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.16_1681101286974_0.6501670682513727", "host": "s3://npm-registry-packages"}}, "0.17.17": {"name": "@esbuild/win32-arm64", "version": "0.17.17", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "521a6d97ee0f96b7c435930353cc4e93078f0b54", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.17.tgz", "fileCount": 3, "integrity": "sha512-YtUXLdVnd6YBSYlZODjWzH+KzbaubV0YVd6UxSfoFfa5PtNJNaW+1i+Hcmjpg2nEe0YXUCNF5bkKy1NnBv1y7Q==", "signatures": [{"sig": "MEYCIQDgOW1T+ZU0O2IoYZrQEf1nCUoh4STScQqMOCS4vYGNQwIhANr1KusqrwvvJJG+X32SZNKlCGV7pz0vZqDo/Qr07sBV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8540646, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGdKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojABAAh61UaenZ4pq1JaL0X8V8HkpF1URG1ls+hjO2orb+gV7aHees\r\nIaRMSRjeg16UStNAZaaPbfT0UDXefqH976Cbv6wfmR1D+6VsRT9Kubk4HIuH\r\nJ6GQCtxK5hwis3eYCiiK7Yqt3daJtQA6vtiQzWLYzla2aILLe9AlQrI8m8nA\r\nJBOoJ4rK6Lrg9vtqTnZobclFvlAB4fpu04T0MGRZMu//J9Hkt7XT9aRML11j\r\nMTeeTOK+tXKfb3gHnYKlJfLaR4/qR95BKRCdmhA2TpsRRz9OborcC1eGmFj1\r\n3nBpgww6B32reoA1u8zJmSc24UYjZIfDGuqMvzQRYE79B8zyp6RgGs6qJKIQ\r\njPofUVpzfbPHYZqb/PLWZoXPzHC+8bNzQC+m46ezuTJ/i2JFwUMzdOJVimtr\r\n8DJxkyhH5abHxxUrkv8BsobwiCRunAciU00KyP4bWpUTrL09VDKpa/8Pr6O+\r\n8WCTS8Ue+2SDMOpF+mvVlEVsExcEn0J1EljKJgFl/tNpJOCXE84erIEwQNAW\r\nCTrn5EY0oPOoDhiZ3oqbT05fhk1q3pTzACo06cyXfyDbFAoNx5rWWZ+xBLzN\r\nuDfxVdodBYpV+nh21msKwqRnc7OlNNcbBWsfbWXP1tpANfFwXHurxdb7K/Kj\r\nv6vNI/J5K9fbXR0Z02Ft9xEIw5BFihWqmKY=\r\n=ZhnQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.17_1681680201799_0.3373351214619935", "host": "s3://npm-registry-packages"}}, "0.17.18": {"name": "@esbuild/win32-arm64", "version": "0.17.18", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "58dfc177da30acf956252d7c8ae9e54e424887c4", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.18.tgz", "fileCount": 3, "integrity": "sha512-Kb3Ko/KKaWhjeAm2YoT/cNZaHaD1Yk/pa3FTsmqo9uFh1D1Rfco7BBLIPdDOozrObj2sahslFuAQGvWbgWldAg==", "signatures": [{"sig": "MEQCIACKOzYSvCNvsmo8A0uY8ybIjrFepodJ320Hw8/m6zG7AiB/2JPB82UmOvuYjVIwX0GjzjT1kPmFCP2mOpsSMzQHsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8543206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREZmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPgA//VoyLLlklCP0HQ0SXDBrI8WQKtaS381D/cHHNuyj7+6wRSkSD\r\naQQhnRw4URv37udhf/bDjzJmMOR5Zzf2yWlCkpM5r9Fy/DAM0SSzs+uzBhbc\r\nDmKsb43r5BihxaNkcdS91DIfTjX7EJNZm/au7V9Zonw5SqrWLTjE7sSTplbs\r\nxuquGnAwpkphGmg3Ni0rU5iHEdnEqfhdX6rZyoCG08bDrNfPGdrt+MKjU5aJ\r\ncIeRAOQF4GQxDWGYxGmpGJgV6nkaIRR6eBUFbyS0RjboS28iNLSh4vovUAai\r\nr1QO/91xrXYMZC+Eo9ni7DLjsH8mIRmAQ3FJm7EUt9aiHVFHR2qZLhC1PBRK\r\n5N35lTQ0/XDaupzl2GKaj/Xbabl5kZ3ET6lXQgoNSh0JiyPmU5WiNQlz8qf9\r\noXbNEYyBrsI23TZ6w5BhRMgORLFdnso6qRPc6TpFVC4IhTOmRIq34vxA+h/6\r\n3UZzhTsOUJXt8G6u72/+jzqQ91uQnEaP1mhfvfweveLt78OafPpa7SKteSZ9\r\nLWuLkocHWYNYldDJ2tFpAUT9yDUsx/ok46IU2FpIZSYvEG+tQxAra6PgmyU9\r\nzGjvRFBeSbQXMipQF2YDXiWFqFF0Fyo5Fnggso3sevabP99JjHof0MmVzO5R\r\nW1ev9hrgQfZcstu4cWx6mnkYhGJtotiWvA0=\r\n=fHMr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.18_1682196070203_0.14581568451429772", "host": "s3://npm-registry-packages"}}, "0.17.19": {"name": "@esbuild/win32-arm64", "version": "0.17.19", "license": "MIT", "_id": "@esbuild/win32-arm64@0.17.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "9aa9dc074399288bdcdd283443e9aeb6b9552b6f", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.17.19.tgz", "fileCount": 3, "integrity": "sha512-yYx+8jwowUstVdorcMdNlzklLYhPxjniHWFKgRqH7IFlUEa0Umu3KuYplf1HUZZ422e3NU9F4LGb+4O0Kdcaag==", "signatures": [{"sig": "MEUCIBy7vdVhwimnSIBbt3ie+GmDu2XWlSH7QuSnrGvL4FnlAiEAregpujVYr8HNS9UE0cKUlvbkXbw6bnpahKMURna41fk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8546790}, "engines": {"node": ">=12"}, "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.17.19_1683936375167_0.07236600653188341", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@esbuild/win32-arm64", "version": "0.18.0", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "fa32bfa8e716d3fd7d56ee8db831dc73f9fa53f5", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.0.tgz", "fileCount": 3, "integrity": "sha512-WohArFQ3HStBu9MAsx3JUk2wfC2v8QoadnMoNfx3Y26ac54tD/wQhPzw4QOzQbSqOFqzIMLKWbxindTsko+9OA==", "signatures": [{"sig": "MEQCIGNHTMsh5NF8rOhBTySNVa3fR9V/aur/eoinrvzZwinJAiAoZzELxftmo8mvVcPtoNMEW7WH7x0tI4wisY2i/rl+4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8550885}, "engines": {"node": ">=12"}, "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.0_1686345847719_0.7763797703553874", "host": "s3://npm-registry-packages"}}, "0.18.1": {"name": "@esbuild/win32-arm64", "version": "0.18.1", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "ca26f130a8a0d2852d4abf5f24713214f804d4f4", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.1.tgz", "fileCount": 3, "integrity": "sha512-d6wt4g9GluZp7xCmgpm7gY6wy0mjcBHbKeeK9MYrlWNFJd8KBcD2uCil8kFuaH3Dt6AUz62D0wIoDETFsZ01Tg==", "signatures": [{"sig": "MEYCIQDUN1BHxLEJOFmurqUOYe3uUn5XD5DGbtWlOxRa7fHXHQIhAPr+BQ9u8hQ2Mg15sgMqtGcc/4MfQuqHYWbfldk3qEco", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8557541}, "engines": {"node": ">=12"}, "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.1_1686545493196_0.2620114614168798", "host": "s3://npm-registry-packages"}}, "0.18.2": {"name": "@esbuild/win32-arm64", "version": "0.18.2", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "3ede172caacdec56bce206bfc118ffddaf638f49", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.2.tgz", "fileCount": 3, "integrity": "sha512-H2zzjPdzSDNwUnZdZf9/xfm0CYqHFXuenCMAx+tRzIRqWUT6MmZ9/q7722KnAZ6uPpq0RLs7EjCIIfmt6CaRGg==", "signatures": [{"sig": "MEYCIQDfwPZ6EhDSPHvOlsaJg1BVUrIAErKAXKm9tqMhRlQygAIhAJK7Hapnn4RIuJ+y892Sp6+m+9HJqOA27l3fATGCPjvU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8562149}, "engines": {"node": ">=12"}, "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.2_1686624015906_0.46941602192865695", "host": "s3://npm-registry-packages"}}, "0.18.3": {"name": "@esbuild/win32-arm64", "version": "0.18.3", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "21336b42038fffc7dcacb201683701499ce9ecd7", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.3.tgz", "fileCount": 3, "integrity": "sha512-LPJC8ub+9uzyC6ygVmp00dAqet1q1DsZ/OldGIIBt+y+Ctd1OfnKNlzQgXK8nxwY1G8fAhklFSeSRRgAUJnR0w==", "signatures": [{"sig": "MEQCIHcLRkQXgajBA1dBvBqARDEEmuKwGA+/Xls6ORZdxqeLAiATl4lUfhyvzwPO445twKlKu8/DgSjnUP6nv75wMDo5jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8562149}, "engines": {"node": ">=12"}, "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.3_1686831639738_0.21624525525422422", "host": "s3://npm-registry-packages"}}, "0.18.4": {"name": "@esbuild/win32-arm64", "version": "0.18.4", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6e0518345d702012e68d9cdbbcf897367764eb9e", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.4.tgz", "fileCount": 3, "integrity": "sha512-1NxP+iOk8KSvS1L9SSxEvBAJk39U0GiGZkiiJGbuDF9G4fG7DSDw6XLxZMecAgmvQrwwx7yVKdNN3GgNh0UfKg==", "signatures": [{"sig": "MEQCIAEg7fIho/4eJasFIIG+4GeVx5LZ+/8amWSG0DiUpZLCAiAnBnN0zrY5W2DVMwf1RM5pOrJ2PhB0PNQ83Oke8bXbjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8567781}, "engines": {"node": ">=12"}, "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.4_1686929860626_0.31994299148261174", "host": "s3://npm-registry-packages"}}, "0.18.5": {"name": "@esbuild/win32-arm64", "version": "0.18.5", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "5bfaa03a4c0c677f1076289bb87584132ba5d8a6", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.5.tgz", "fileCount": 3, "integrity": "sha512-XqpS89+MGLzR8YtQQkBYsLCfAv1ySflMb+FEH99rOp6kOPv/ORO+ujEB5ICDBZZbvYqB75uFrNELo1BVEQbS3g==", "signatures": [{"sig": "MEUCIBvzzdKncmw+b3ZDBmrndVG/Trmv51EKfiBWmoDG73LqAiEA3FNb1z4N7loxXlTIMUrYsylLbML8cVZa4JLDzeqKwsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8583141}, "engines": {"node": ">=12"}, "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.5_1687222343342_0.5103038792077808", "host": "s3://npm-registry-packages"}}, "0.18.6": {"name": "@esbuild/win32-arm64", "version": "0.18.6", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "e22721c832fb083c31535dfbefb9c24d731728ba", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.6.tgz", "fileCount": 3, "integrity": "sha512-oXlXGS9zvNCGoAT/tLHAsFKrIKye1JaIIP0anCdpaI+Dc10ftaNZcqfLzEwyhdzFAYInXYH4V7kEdH4hPyo9GA==", "signatures": [{"sig": "MEUCIAbOmH17/W0uStaG+kdl/NEyqhGwPMf9+dgYQjBuLY9ZAiEAlqizm7+2l6/9FjZGwaGgoVM7jXG8hhi1y/9z/Y45erA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8590821}, "engines": {"node": ">=12"}, "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.6_1687303478320_0.14235668310261151", "host": "s3://npm-registry-packages"}}, "0.18.7": {"name": "@esbuild/win32-arm64", "version": "0.18.7", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "b5cb3c6c349ce4ece2e01fefe3e6cce464d39050", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.7.tgz", "fileCount": 3, "integrity": "sha512-p9ipv7rPKitXAAeufg5BzmyYZHZtFfrLGUX15+AxpTQqSZDAZd2wIusaOW5oONTf8RB53ujIqQw7W0QnZMuiUw==", "signatures": [{"sig": "MEQCIDc5M7oHsMVoiHafTBBjRSIAtyml4+znGXFewxaSfOB5AiA5EsGjRKdeWDoVRxUHPQ3jRF8CJbdf7VjKxVVO84xhzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8624613}, "engines": {"node": ">=12"}, "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.7_1687574770875_0.4672408992283543", "host": "s3://npm-registry-packages"}}, "0.18.8": {"name": "@esbuild/win32-arm64", "version": "0.18.8", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "58a4ebada8d4c219390533cfb9c66b59a5b48484", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.8.tgz", "fileCount": 3, "integrity": "sha512-eWoYo48Hp1yWbe2SSnmMNqnVprNgKtp0mP+aUeX/Lkw3gcsgRju5Qj7psfpA8cR0ULeWkrhmaSS4mgj4wfo97A==", "signatures": [{"sig": "MEUCIBm4lzFPibyJ1UUPvZy1QkwpW2xg7WsEQ2qq/1GjEP6WAiEAn6NpPX8mxoWvKYOTkSz3GpKcI8H2ClXwAy6xZrGzxQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8628197}, "engines": {"node": ">=12"}, "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.8_1687663135475_0.8587120346690733", "host": "s3://npm-registry-packages"}}, "0.18.9": {"name": "@esbuild/win32-arm64", "version": "0.18.9", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6a233d4d0bafffad51253022952d97f22b0ad91a", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.9.tgz", "fileCount": 3, "integrity": "sha512-84FMz3Sh1hwGk/oWy6XGIW2bGVcsqvHLjjtbwd982XoTHOvQSthhrMef0J+4ShE1ZE7VeUXHIt2Mfer+myedYw==", "signatures": [{"sig": "MEUCIEDRLMLezODFTvmH9UgrVDPbFOKuV7aKsHWvTVnujJ32AiEA+8ocq9wPVdGimfr6/GqcMPXepVw7rT7qnzGP10bnG9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8645605}, "engines": {"node": ">=12"}, "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.9_1687757256874_0.46335173555023457", "host": "s3://npm-registry-packages"}}, "0.18.10": {"name": "@esbuild/win32-arm64", "version": "0.18.10", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f12fcff13b9dbd3e80481f4cb6282708ce7794bb", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.10.tgz", "fileCount": 3, "integrity": "sha512-kRhNcMZFGMW+ZHCarAM1ypr8OZs0k688ViUCetVCef9p3enFxzWeBg9h/575Y0nsFu0ZItluCVF5gMR2pwOEpA==", "signatures": [{"sig": "MEQCIFGP/AXMdlFQVEG6GIHNOEirPEYsQ2fzZleqpKmYaRjUAiAaUt2fCKm1sWpOCxDI+pNuU3zn9BcIpmN/Z0l8rnZX0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8645606}, "engines": {"node": ">=12"}, "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.10_1687814416803_0.6147711645996876", "host": "s3://npm-registry-packages"}}, "0.18.11": {"name": "@esbuild/win32-arm64", "version": "0.18.11", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "05d364582b7862d7fbf4698ef43644f7346dcfcc", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.11.tgz", "fileCount": 3, "integrity": "sha512-Sh0dDRyk1Xi348idbal7lZyfSkjhJsdFeuC13zqdipsvMetlGiFQNdO+Yfp6f6B4FbyQm7qsk16yaZk25LChzg==", "signatures": [{"sig": "MEYCIQC7TkqOhRTjOfIL/tg68erEQl13WcPG8TXOy8FsXLvc5AIhAKggx7X7LZwT/C+D2t3CcIntz/qpM+OB03duFhR030Hl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8645606}, "engines": {"node": ">=12"}, "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.11_1688191420605_0.4561723862974014", "host": "s3://npm-registry-packages"}}, "0.18.12": {"name": "@esbuild/win32-arm64", "version": "0.18.12", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "cdb85b318a92ce7ee7736f7c29cde2f5c687957e", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.12.tgz", "fileCount": 3, "integrity": "sha512-GNHuciv0mFM7ouzsU0+AwY+7eV4Mgo5WnbhfDCQGtpvOtD1vbOiRjPYG6dhmMoFyBjj+pNqQu2X+7DKn0KQ/Gw==", "signatures": [{"sig": "MEYCIQDiJjTwTjv0pqRVGUcUnTO5kYXZiNUA0OKNoMcozeNoUAIhAMtPicALLIGQE/avLROI31slZmuHBougSJjDWVYENTgy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8647654}, "engines": {"node": ">=12"}, "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.12_1689212029196_0.15742616396176934", "host": "s3://npm-registry-packages"}}, "0.18.13": {"name": "@esbuild/win32-arm64", "version": "0.18.13", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "11dedda0e8cfb5f781411ea362b2040304be0fc3", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.13.tgz", "fileCount": 3, "integrity": "sha512-4CGYdRQT/ILd+yLLE5i4VApMPfGE0RPc/wFQhlluDQCK09+b4JDbxzzjpgQqTPrdnP7r5KUtGVGZYclYiPuHrw==", "signatures": [{"sig": "MEUCIApqNDEfZ8KcXcKVwFaV6p480PpwYri/hZ5rm9MXQs0rAiEAj7SJK16MmfwARNZnp8AhYVU5MgZwoYmMizFUA25Hktk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8647654}, "engines": {"node": ">=12"}, "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.13_1689388619352_0.9378403772607053", "host": "s3://npm-registry-packages"}}, "0.18.14": {"name": "@esbuild/win32-arm64", "version": "0.18.14", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "d8531d370e6fd0e0e40f40e016f996bbe4fd5ebf", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.14.tgz", "fileCount": 3, "integrity": "sha512-1c44RcxKEJPrVj62XdmYhxXaU/V7auELCmnD+Ri+UCt+AGxTvzxl9uauQhrFso8gj6ZV1DaORV0sT9XSHOAk8Q==", "signatures": [{"sig": "MEQCIHwqe9cR9uzOYl9PMU4cPNxNQBwZXn5AjRIHripp6xPrAiBnZrJSQNmkbaSioVmvc0iQoLvnhVSl7+z5wg6QGTdX0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8678886}, "engines": {"node": ">=12"}, "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.14_1689656402469_0.4486771822229947", "host": "s3://npm-registry-packages"}}, "0.18.15": {"name": "@esbuild/win32-arm64", "version": "0.18.15", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7f15fe5d14b9b24eb18ca211ad92e0f5df92a18b", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.15.tgz", "fileCount": 3, "integrity": "sha512-HC4/feP+pB2Vb+cMPUjAnFyERs+HJN7E6KaeBlFdBv799MhD+aPJlfi/yk36SED58J9TPwI8MAcVpJgej4ud0A==", "signatures": [{"sig": "MEYCIQDGXsVJKGRAMfhETFnCbyDW5GHPh10FGLZNJvAPDkJp2QIhAKq6YDE2puY/TtjjMvhjYf9VsV/zEbQ6FvnZVgdvCKs1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8687590}, "engines": {"node": ">=12"}, "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.15_1689857579633_0.07929319929823064", "host": "s3://npm-registry-packages"}}, "0.18.16": {"name": "@esbuild/win32-arm64", "version": "0.18.16", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "8de33682243508eef8d4de1816df2c05adad2b21", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.16.tgz", "fileCount": 3, "integrity": "sha512-+j4anzQ9hrs+iqO+/wa8UE6TVkKua1pXUb0XWFOx0FiAj6R9INJ+WE//1/Xo6FG1vB5EpH3ko+XcgwiDXTxcdw==", "signatures": [{"sig": "MEUCIEAncMivD1ETlrRf+Ax9tSkoq809spyaTt1VanIcDJYyAiEA4rEpYITYioOGL9oPEs0lteIO5qiBqf1zYlZ5Ux8C/sk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8690662}, "engines": {"node": ">=12"}, "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.16_1690087665990_0.8022059466173204", "host": "s3://npm-registry-packages"}}, "0.18.17": {"name": "@esbuild/win32-arm64", "version": "0.18.17", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "fd95ffd217995589058a4ed8ac17ee72a3d7f615", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.17.tgz", "fileCount": 3, "integrity": "sha512-bwPmTJsEQcbZk26oYpc4c/8PvTY3J5/QK8jM19DVlEsAB41M39aWovWoHtNm78sd6ip6prilxeHosPADXtEJFw==", "signatures": [{"sig": "MEQCIHerA3fCx1bTJZ5yqQztNU5aXkAMzxe33dTNrXQOiCyaAiBcWSiK28jY6WeJeF6Y0A4TevZyH752g33PAY7KsPLUNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8710630}, "engines": {"node": ">=12"}, "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.17_1690335639071_0.9047452171721451", "host": "s3://npm-registry-packages"}}, "0.18.18": {"name": "@esbuild/win32-arm64", "version": "0.18.18", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "e94d9e6d058e0ccb92d858badd4a6aa74772150e", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.18.tgz", "fileCount": 3, "integrity": "sha512-YKD6LF/XXY9REu+ZL5RAsusiG48n602qxsMVh/E8FFD9hp4OyTQaL9fpE1ovxwQXqFio+tT0ITUGjDSSSPN13w==", "signatures": [{"sig": "MEYCIQDAe7+pauD9JChqb+NQt2FExHf7Yk8RnTdr0xgXHdIi9AIhAI0B80eke12oQufMONBUOoDboe94UowEPpWsS5ZvzglQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8726502}, "engines": {"node": ">=12"}, "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.18_1691255174556_0.9155110661280086", "host": "s3://npm-registry-packages"}}, "0.18.19": {"name": "@esbuild/win32-arm64", "version": "0.18.19", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "9dca55f0dcbbdb50bf36353d1114f5f71c269275", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.19.tgz", "fileCount": 3, "integrity": "sha512-L4vb6pcoB1cEcXUHU6EPnUhUc4+/tcz4OqlXTWPcSQWxegfmcOprhmIleKKwmMNQVc4wrx/+jB7tGkjjDmiupg==", "signatures": [{"sig": "MEUCIEbijvFXlSfES2686OjqEzEbkHJcYHzeyX24KqE4ESkwAiEA+Ku+LpoL59q8YyipeDmhphO40psZM4PGPxH8+/C4hXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8754662}, "engines": {"node": ">=12"}, "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.19_1691376663540_0.6745096378516391", "host": "s3://npm-registry-packages"}}, "0.18.20": {"name": "@esbuild/win32-arm64", "version": "0.18.20", "license": "MIT", "_id": "@esbuild/win32-arm64@0.18.20", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "73bc7f5a9f8a77805f357fab97f290d0e4820ac9", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.18.20.tgz", "fileCount": 3, "integrity": "sha512-ddYFR6ItYgoaq4v4JmQQaAI5s7npztfV4Ag6NrhiaW0RrnOXqBkgwZLofVTlq1daVTQNhtI5oieTvkRPfZrePg==", "signatures": [{"sig": "MEYCIQDhhbMx3C5SB6auC4gpFbFebKm5b7/945iaudqiUca42gIhAKqCKmM33X0hlYwC87N1DsU8fPDKJjMxDC7wj/EXSqJd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8763366}, "engines": {"node": ">=12"}, "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.18.20_1691468078490_0.39565538560442515", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@esbuild/win32-arm64", "version": "0.19.0", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "67c2b410ff8862be2cd61145ad21e11be00fb914", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.0.tgz", "fileCount": 3, "integrity": "sha512-NQJ+4jmnA79saI+sE+QzcEls19uZkoEmdxo7r//PDOjIpX8pmoWtTnWg6XcbnO7o4fieyAwb5U2LvgWynF4diA==", "signatures": [{"sig": "MEUCIFDox2i6xH8QrTvzD6uMVxEC62eOm3m6/6EuQrPvoizwAiEA5v4V5l8x+pn8j8igiTj6JScRBAmkEWGt1I0WF3qz5EE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8807397}, "engines": {"node": ">=12"}, "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.0_1691509869839_0.4042374191348914", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@esbuild/win32-arm64", "version": "0.19.1", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "810ac7fcc15e4c0b606acdb39cd5cf342ba16dd7", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.1.tgz", "fileCount": 3, "integrity": "sha512-9Hb/WUXgyXlL55w3iNVyLkN9gq9x+agv3kk80foWbfpOwe7Qw4Vx6JGB+XQdsIfvvP1kShVQPIvBgVj0TxLlVw==", "signatures": [{"sig": "MEUCIQDvqHUQrOQIYw5TXacAvnbmthkCRMnr4TtJdMdwKfkTCgIgTQSKpue+OTa1eXCYMwLYp+7vw0UD4G+yj2yUPCaLJRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8817637}, "engines": {"node": ">=12"}, "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.1_1691769434216_0.7988015721304655", "host": "s3://npm-registry-packages"}}, "0.19.2": {"name": "@esbuild/win32-arm64", "version": "0.19.2", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "719ed5870855de8537aef8149694a97d03486804", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.2.tgz", "fileCount": 3, "integrity": "sha512-5NayUlSAyb5PQYFAU9x3bHdsqB88RC3aM9lKDAz4X1mo/EchMIT1Q+pSeBXNgkfNmRecLXA0O8xP+x8V+g/LKg==", "signatures": [{"sig": "MEUCIQDb9OwAy6o9TuTqNF7vqhgJ5dyTZxKEvuxOZC9KGNkT6AIgBgmSIE/L5nRO0+w6a4+2L4XyBBY+wtuS6CAEdcpUOD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8815589}, "engines": {"node": ">=12"}, "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.2_1691978287290_0.8867830960154652", "host": "s3://npm-registry-packages"}}, "0.19.3": {"name": "@esbuild/win32-arm64", "version": "0.19.3", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "1974c8c180c9add4962235662c569fcc4c8f43dd", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.3.tgz", "fileCount": 3, "integrity": "sha512-FSrAfjVVy7TifFgYgliiJOyYynhQmqgPj15pzLyJk8BUsnlWNwP/IAy6GAiB1LqtoivowRgidZsfpoYLZH586A==", "signatures": [{"sig": "MEQCIFHqlAQeJYLWoLaDsAxAjNvp4hteVO/UgH81+Jtw25JEAiAhsLKNAxVJabuZfDjmMOvVEDmNogXrWock8XApkEqrMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8824293}, "engines": {"node": ">=12"}, "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.3_1694653929616_0.19142170799370684", "host": "s3://npm-registry-packages"}}, "0.19.4": {"name": "@esbuild/win32-arm64", "version": "0.19.4", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "80be69cec92da4da7781cf7a8351b95cc5a236b0", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.4.tgz", "fileCount": 3, "integrity": "sha512-PkIl7Jq4mP6ke7QKwyg4fD4Xvn8PXisagV/+HntWoDEdmerB2LTukRZg728Yd1Fj+LuEX75t/hKXE2Ppk8Hh1w==", "signatures": [{"sig": "MEQCICd054O/G6PLmrCUayEyLi/1HUKk9oHWtQMwpaUSV/YUAiBZEp1ii8+5EDsa0h2kVGuPuPEEYGCgTdCK7fGvGHdgMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8824293}, "engines": {"node": ">=12"}, "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.4_1695865589775_0.7475566667716314", "host": "s3://npm-registry-packages"}}, "0.19.5": {"name": "@esbuild/win32-arm64", "version": "0.19.5", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "29f8184afa7a02a956ebda4ed638099f4b8ff198", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.5.tgz", "fileCount": 3, "integrity": "sha512-dWVjD9y03ilhdRQ6Xig1NWNgfLtf2o/STKTS+eZuF90fI2BhbwD6WlaiCGKptlqXlURVB5AUOxUj09LuwKGDTg==", "signatures": [{"sig": "MEUCIDPsO5RBnMKDQHLxOBE1CgC2PEi+BXyMsf+7FBVQVtwsAiEAzP5lv6RnRsQxP8bWJD9I8BAix+V5CKWabQuYpHSJHBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8830437}, "engines": {"node": ">=12"}, "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.5_1697519418994_0.07331322668654772", "host": "s3://npm-registry-packages"}}, "0.19.6": {"name": "@esbuild/win32-arm64", "version": "0.19.6", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "34e5665f239047c302c8d153406c87db22afd58a", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.6.tgz", "fileCount": 3, "integrity": "sha512-2DchFXn7vp/B6Tc2eKdTsLzE0ygqKkNUhUBCNtMx2Llk4POIVMUq5rUYjdcedFlGLeRe1uLCpVvCmE+G8XYybA==", "signatures": [{"sig": "MEUCIAoMU8RRfgLFViVshBusoXgcHgPiRIj+I20SekeOH94sAiEAkcLngqCXZ7g0S3SLEd8b5YF1hQX/m1sJ4vS2xPPsKJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8848869}, "engines": {"node": ">=12"}, "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.6_1700377875868_0.9001422145489091", "host": "s3://npm-registry-packages"}}, "0.19.7": {"name": "@esbuild/win32-arm64", "version": "0.19.7", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "e61de6c4eb204d83fd912f3ae6812cc8c7d32d25", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.7.tgz", "fileCount": 3, "integrity": "sha512-ZA<PERSON>KSYti5w5toax5FpmfcAgu3ZNJxYSRm0AW/Dao5up0YV1hDVof1NvwLomjEN+3/GMtaWDI+CIyJOMTRSTdMw==", "signatures": [{"sig": "MEYCIQCRH5IBpmX6WDUCJkS3ua4nSP/oAqwPVZrNJCwBHSCnsAIhALoc00X09K3LVE/2WcSW18w88wA3S1i40dZ3BC7ugESh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8880613}, "engines": {"node": ">=12"}, "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.7_1700528431916_0.693934077254869", "host": "s3://npm-registry-packages"}}, "0.19.8": {"name": "@esbuild/win32-arm64", "version": "0.19.8", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "ec029e62a2fca8c071842ecb1bc5c2dd20b066f1", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.8.tgz", "fileCount": 3, "integrity": "sha512-rq6WzBGjSzihI9deW3fC2Gqiak68+b7qo5/3kmB6Gvbh/NYPA0sJhrnp7wgV4bNwjqM+R2AApXGxMO7ZoGhIJg==", "signatures": [{"sig": "MEYCIQCy+kVv2aQq/X5kK357D5rL3zUA8cJXdtzMLcTdYTfL/gIhAJdg4yEO+gygZddTlynzcaaf3w095dJpFfsOXDcSVEUo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8880101}, "engines": {"node": ">=12"}, "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.8_1701040059801_0.8455039748515212", "host": "s3://npm-registry-packages"}}, "0.19.9": {"name": "@esbuild/win32-arm64", "version": "0.19.9", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "2be22131ab18af4693fd737b161d1ef34de8ca9d", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.9.tgz", "fileCount": 3, "integrity": "sha512-GQoa6OrQ8G08guMFgeXPH7yE/8Dt0IfOGWJSfSH4uafwdC7rWwrfE6P9N8AtPGIjUzdo2+7bN8Xo3qC578olhg==", "signatures": [{"sig": "MEYCIQD2CIsWiK7KIinfc3F3wzuLCp9jEve7B9Sq8mtFgUllCgIhAO5q/YQ4Up1pVZoV0o6TB54nF8D0bUfXL8ZAdPxA/q4f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8973797}, "engines": {"node": ">=12"}, "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.9_1702184950339_0.8911434511882366", "host": "s3://npm-registry-packages"}}, "0.19.10": {"name": "@esbuild/win32-arm64", "version": "0.19.10", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "7e417d1971dbc7e469b4eceb6a5d1d667b5e3dcc", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.10.tgz", "fileCount": 3, "integrity": "sha512-+Sa4oTDbpBfGpl3Hn3XiUe4f8TU2JF7aX8cOfqFYMMjXp6ma6NJDztl5FDG8Ezx0OjwGikIHw+iA54YLDNNVfw==", "signatures": [{"sig": "MEUCIQDPivfrNejTO2FIZGb7VD8BjwBUQzZl/TobWVov1vOAZQIgZdAjjj7z5e/CDmV9lrY9ATX2ntl8gec6jCeDNo4pEcI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978406}, "engines": {"node": ">=12"}, "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.10_1702945272963_0.4595104674900159", "host": "s3://npm-registry-packages"}}, "0.19.11": {"name": "@esbuild/win32-arm64", "version": "0.19.11", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "c778b45a496e90b6fc373e2a2bb072f1441fe0ee", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.11.tgz", "fileCount": 3, "integrity": "sha512-0P58Sbi0LctOMOQbpEOvOL44Ne0sqbS0XWHMvvrg6NE5jQ1xguCSSw9jQeUk2lfrXYsKDdOe6K+oZiwKPilYPQ==", "signatures": [{"sig": "MEYCIQDAnRsAd9lqcx+GL2cXXx76nxwYd2Vu5Hux8qOSRIcE6AIhAMIJsUoy8NfEPYua/BKyEDQIuivovwskE9LbHR8xmtKz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8978918}, "engines": {"node": ">=12"}, "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.11_1703881892024_0.41348880058932025", "host": "s3://npm-registry-packages"}}, "0.19.12": {"name": "@esbuild/win32-arm64", "version": "0.19.12", "license": "MIT", "_id": "@esbuild/win32-arm64@0.19.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "773bdbaa1971b36db2f6560088639ccd1e6773ae", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.19.12.tgz", "fileCount": 3, "integrity": "sha512-URgtR1dJnmGvX864pn1B2YUYNzjmXkuJOIqG2HdU62MVS4EHpU2946OZoTMnRUHklGtJdJZ33QfzdjGACXhn1A==", "signatures": [{"sig": "MEUCICyx9aBZV+IIouK+ihEmFchtyIySkza3VzUWyn3ze9FpAiEA3Muv7aQ1FTFDzKacv1+rEMMM9vGB4RxMxCRL7ysHSd4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8979430}, "engines": {"node": ">=12"}, "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.19.12_1706031596381_0.8606745897785895", "host": "s3://npm-registry-packages"}}, "0.20.0": {"name": "@esbuild/win32-arm64", "version": "0.20.0", "license": "MIT", "_id": "@esbuild/win32-arm64@0.20.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "a5c171e4a7f7e4e8be0e9947a65812c1535a7cf0", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.20.0.tgz", "fileCount": 3, "integrity": "sha512-0vYsP8aC4TvMlOQYozoksiaxjlvUcQrac+muDqj1Fxy6jh9l9CZJzj7zmh8JGfiV49cYLTorFLxg7593pGldwQ==", "signatures": [{"sig": "MEQCIBh4qUcQEABWySHMLJIwiL20OfUo3L8TVpUj0mCdXrn4AiBcICeanemlfHt97KHNM+OxQBIEpFnsVyO+L1P4Q/9kxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8980497}, "engines": {"node": ">=12"}, "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.20.0_1706374157240_0.24410055034193", "host": "s3://npm-registry-packages"}}, "0.20.1": {"name": "@esbuild/win32-arm64", "version": "0.20.1", "license": "MIT", "_id": "@esbuild/win32-arm64@0.20.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "36c4e311085806a6a0c5fc54d1ac4d7b27e94d7b", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.20.1.tgz", "fileCount": 3, "integrity": "sha512-QZ6gXue0vVQY2Oon9WyLFCdSuYbXSoxaZrPuJ4c20j6ICedfsDilNPYfHLlMH7vGfU5DQR0czHLmJvH4Nzis/A==", "signatures": [{"sig": "MEQCIANyv30vNK/SjZ3HWc2tjU9aG1A8FHrS2CGcc6Z7qj6IAiAEekL1CaD2OJwL63OP9KPMX09Mw0uNG7ijA+cbT9JAsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8996881}, "engines": {"node": ">=12"}, "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.20.1_1708324663935_0.13625474528812753", "host": "s3://npm-registry-packages"}}, "0.20.2": {"name": "@esbuild/win32-arm64", "version": "0.20.2", "license": "MIT", "_id": "@esbuild/win32-arm64@0.20.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f1ae5abf9ca052ae11c1bc806fb4c0f519bacf90", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.20.2.tgz", "fileCount": 3, "integrity": "sha512-GRibxoawM9ZCnDxnP3usoUDO9vUkpAxIIZ6GQI+IlVmr5kP3zUq+l17xELTHMWTWzjxa2guPNyrpq1GWmPvcGQ==", "signatures": [{"sig": "MEYCIQC3+z0xsee+uwB8mygkQYULBVnXL8GQAkuLUT3ipsAEIAIhAJqvHFNnT/ENzuF0mmsV7YO1oQ2ulgb71mhQaRa0SRIR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9000465}, "engines": {"node": ">=12"}, "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.20.2_1710445760424_0.40345609725115184", "host": "s3://npm-registry-packages"}}, "0.21.0": {"name": "@esbuild/win32-arm64", "version": "0.21.0", "license": "MIT", "_id": "@esbuild/win32-arm64@0.21.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "28632772edbec48927f662192ef7ea5967b26e88", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.21.0.tgz", "fileCount": 3, "integrity": "sha512-06BY4wjQQ2bPjayuvKWXr5X3V+ZGnoTOX1+doLoQBUSyCDb9JZgX7o0N3t3rRNmEiMY/DuxXwu+EE+U32B4ErA==", "signatures": [{"sig": "MEUCIQDhPGX8W5id8Pjr+lIjund9xF7aYo3H6erwphHQZzk0DgIgCPKObnvYahbHz4NC/FKfngDb/T3R28J4usLuTQVccxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9071633}, "engines": {"node": ">=12"}, "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.21.0_1715050329089_0.5204506669733198", "host": "s3://npm-registry-packages"}}, "0.21.1": {"name": "@esbuild/win32-arm64", "version": "0.21.1", "license": "MIT", "_id": "@esbuild/win32-arm64@0.21.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "df4f44f9b4fec9598c0ec2c34fec9c568c8ab85d", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.21.1.tgz", "fileCount": 3, "integrity": "sha512-jb5B4k+xkytGbGUS4T+Z89cQJ9DJ4lozGRSV+hhfmCPpfJ3880O31Q1srPCimm+V6UCbnigqD10EgDNgjvjerQ==", "signatures": [{"sig": "MEUCIQCl/ORpg1GDcbxg1DOXX7J4+YdJrQCpDQyv5sbiqS5i1QIgI99/uZNaR+7+tH0tBsrX9b57i56kkys1j9VFgcUldrE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9073169}, "engines": {"node": ">=12"}, "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.21.1_1715100879337_0.574225483387959", "host": "s3://npm-registry-packages"}}, "0.21.2": {"name": "@esbuild/win32-arm64", "version": "0.21.2", "license": "MIT", "_id": "@esbuild/win32-arm64@0.21.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "251e4cdafae688d54a43ac8544cb8c71e8fcdf15", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.21.2.tgz", "fileCount": 3, "integrity": "sha512-sNndlsBT8OeE/MZDSGpRDJlWuhjuUz/dn80nH0EP4ZzDUYvMDVa7G87DVpweBrn4xdJYyXS/y4CQNrf7R2ODXg==", "signatures": [{"sig": "MEUCIAjpJAMxEdhPvHj2v8Wjb5fQKZV4RgR1+23Xv99k7u9sAiEA3+8eVizbEvFqYSWOnSPVFba3l7jE+cJ+L7UPUzeGOgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9073681}, "engines": {"node": ">=12"}, "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.21.2_1715545963245_0.03762168066162119", "host": "s3://npm-registry-packages"}}, "0.21.3": {"name": "@esbuild/win32-arm64", "version": "0.21.3", "license": "MIT", "_id": "@esbuild/win32-arm64@0.21.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "c6c3c0b1a1dfc6327ef4db6aa4fb6efd9df531f7", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.21.3.tgz", "fileCount": 3, "integrity": "sha512-EjEomwyLSCg8Ag3LDILIqYCZAq/y3diJ04PnqGRgq8/4O3VNlXyMd54j/saShaN4h5o5mivOjAzmU6C3X4v0xw==", "signatures": [{"sig": "MEYCIQCLMjnMf4mfKRE2LO2qorjTZrHQlGcxnBecfuZMoVPPjAIhAKZrBIUlXI+SHL/E6C3iBgIQE7B8KzkYLUkQKlaKXMg1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9070097}, "engines": {"node": ">=12"}, "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.21.3_1715806335349_0.2128160636191596", "host": "s3://npm-registry-packages"}}, "0.21.4": {"name": "@esbuild/win32-arm64", "version": "0.21.4", "license": "MIT", "_id": "@esbuild/win32-arm64@0.21.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "94afb4c2ac89b0f09791606d6d93fdab322f81c8", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.21.4.tgz", "fileCount": 3, "integrity": "sha512-UUfMgMoXPoA/bvGUNfUBFLCh0gt9dxZYIx9W4rfJr7+hKe5jxxHmfOK8YSH4qsHLLN4Ck8JZ+v7Q5fIm1huErg==", "signatures": [{"sig": "MEQCIFIjcBMfSPk4T9NpeMjB6RknD60ZJnTX0xM2diHbSJ1tAiBpKmLwVXfJTbhjkBCEUZW5kE3m+nBs+2/OCLN0oJR1kQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9077265}, "engines": {"node": ">=12"}, "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.21.4_1716603038329_0.15422528991493456", "host": "s3://npm-registry-packages"}}, "0.21.5": {"name": "@esbuild/win32-arm64", "version": "0.21.5", "license": "MIT", "_id": "@esbuild/win32-arm64@0.21.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "675b7385398411240735016144ab2e99a60fc75d", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz", "fileCount": 3, "integrity": "sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==", "signatures": [{"sig": "MEUCIFjOyeRYY6kO2wj6dItZ1r/keuvyglui53VKDz99TR16AiEAhjU/zzeXC1lB2zMBIiN1+TBnEjE5yrYcTYRnQBTLOaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9083409}, "engines": {"node": ">=12"}, "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.21.5_1717967802591_0.9347568489533629", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "@esbuild/win32-arm64", "version": "0.22.0", "license": "MIT", "_id": "@esbuild/win32-arm64@0.22.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6b4224f2d049c26f37026904210a4293e34c2747", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.22.0.tgz", "fileCount": 3, "integrity": "sha512-Kml5F7tv/1Maam0pbbCrvkk9vj046dPej30kFzlhXnhuCtYYBP6FGy/cLbc5yUT1lkZznGLf2OvuvmLjscO5rw==", "signatures": [{"sig": "MEYCIQCP/uJ488CTp3kPVkAEW2y+EmAAeW9HzFM7N6cLl3EC0wIhAIDlpO2zm0hNFCQ+HkR5NnW7W2wW8a1lS76avUEsW97J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9408529}, "engines": {"node": ">=18"}, "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.22.0_1719779849602_0.9437575554402382", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "@esbuild/win32-arm64", "version": "0.23.0", "license": "MIT", "_id": "@esbuild/win32-arm64@0.23.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "b858b2432edfad62e945d5c7c9e5ddd0f528ca6d", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.23.0.tgz", "fileCount": 3, "integrity": "sha512-lY6AC8p4Cnb7xYHuIxQ6iYPe6MfO2CC43XXKo9nBXDb35krYt7KGhQnOkRGar5psxYkircpCqfbNDB4uJbS2jQ==", "signatures": [{"sig": "MEUCIF+pjdo2E9LxJl8EmbwnyH7HoDUKq4/gcAjJwUlT3tsCAiEA9xp33CvYdhQE0cXYME6dH+XrcHNKhfuOb5oRN3S2OWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9409041}, "engines": {"node": ">=18"}, "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.23.0_1719891203943_0.7112020930707779", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "@esbuild/win32-arm64", "version": "0.23.1", "license": "MIT", "_id": "@esbuild/win32-arm64@0.23.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "84906f50c212b72ec360f48461d43202f4c8b9a2", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.23.1.tgz", "fileCount": 3, "integrity": "sha512-4O+gPR5rEBe2FpKOVyiJ7wNDPA8nGzDuJ6gN4okSA1gEOYZ67N8JPk58tkWtdtPeLz7lBnY6I5L3jdsr3S+A6A==", "signatures": [{"sig": "MEUCICj16kOv8DUtInQApcgOzNHJZTvj8whONQhNi83UCsUvAiEA+ck37Q/Zv3rLfqk+2s92SBM34Pru1fuHsm3fwf0RgGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9413137}, "engines": {"node": ">=18"}, "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.23.1_1723846377015_0.3991940392542084", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "@esbuild/win32-arm64", "version": "0.24.0", "license": "MIT", "_id": "@esbuild/win32-arm64@0.24.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "6e79c8543f282c4539db684a207ae0e174a9007b", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.24.0.tgz", "fileCount": 3, "integrity": "sha512-iKc8GAslzRpBytO2/aN3d2yb2z8XTVfNV0PjGlCxKo5SgWmNXx82I/Q3aG1tFfS+A2igVCY97TJ8tnYwpUWLCA==", "signatures": [{"sig": "MEQCIClrAoehXDYEpaYkSu4Z85t+KBGsHVMrxT4CV9IDK2HUAiBZhlkM8XxbLYvnUpsDmRvBb80vysI7Iit4v/+Oh8Uq/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9574417}, "engines": {"node": ">=18"}, "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.24.0_1726970767834_0.26862442310451606", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "@esbuild/win32-arm64", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/win32-arm64@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "a2b6216c6e6915ba71afed29c354cf8b0ad11df0", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.24.1.tgz", "fileCount": 3, "integrity": "sha512-HqeXG1ttUnENzcGlPr0ouQHk8PQIoWi3thXElmafH1pVxC94sYdBVQregb2Qz7l1BmooUIOnzCGPCT4Oma0yTg==", "signatures": [{"sig": "MEYCIQDnKrWyxLgzHV0j0ZRZ4ub86LzPrFl95fKCaO9PxDVh5wIhANW9lh/Pjgx5xoUpH/02zngzCu2pciZXR2h1Jfgb8iOr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9581073}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.24.1_1734673213172_0.8039145179222358", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/win32-arm64", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/win32-arm64@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "c023afb647cabf0c3ed13f0eddfc4f1d61c66a85", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.24.2.tgz", "fileCount": 3, "integrity": "sha512-LihEQ2BBKVFLOC9ZItT9iFprsE9tqjDjnbulhHoFxYQtQfai7qfluVODIYxt1PgdoyQkz23+01rzwNwYfutxUQ==", "signatures": [{"sig": "MEYCIQC1tQYgjPW/0VW/jzMIa2lHMKA9RbQLUObq6drwizS4TAIhAOKo60uQggqwyZNQ7905d1IQz2OrBWdCA8FR2rD6vEyr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9582097}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.24.2_1734717336197_0.4175006466534157", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/win32-arm64", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/win32-arm64@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "849c62327c3229467f5b5cd681bf50588442e96c", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.0.tgz", "fileCount": 3, "integrity": "sha512-ZUAc2YK6JW89xTbXvftxdnYy3m4iHIkDtK3CLce8wg8M2L+YZhIvO1DKpxrd0Yr59AeNNkTiic9YLf6FTtXWMw==", "signatures": [{"sig": "MEYCIQC3XPAtwTVT+XArYaPjqS13kmWiTfZBe8uZydCUlUMirgIhAI50wklaneyM/uroKWUqMJOegdjUdxkOuW2ggnbF1fDd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9637393}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.25.0_1738983709783_0.6284378285063876", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/win32-arm64", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/win32-arm64@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "354358647a6ea98ea6d243bf48bdd7a434999582", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.1.tgz", "fileCount": 3, "integrity": "sha512-GE7XvrdOzrb+yVKB9KsRMq+7a2U/K5Cf/8grVFRAGJmfADr/e/ODQ134RK2/eeHqYV5eQRFxb1hY7Nr15fv1NQ==", "signatures": [{"sig": "MEYCIQCMr9Y3/tpricWsLl0TC7gM5un7TowAKrRvMBk4F2CatAIhAK7HbloOSoEMbJBjyWMOqvFUqLBtoT+G4f0JXEv4EyWT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9640977}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.25.1_1741578293457_0.13654666226255396", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/win32-arm64", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/win32-arm64@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "081424168463c7d6c7fb78f631aede0c104373cf", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.2.tgz", "fileCount": 3, "integrity": "sha512-7Loyjh+D/Nx/sOTzV8vfbB3GJuHdOQyrOryFdZvPHLf42Tk9ivBU5Aedi7iyX+x6rbn2Mh68T4qq1SDqJBQO5Q==", "signatures": [{"sig": "MEYCIQCLIWdD8YaNGcF86Pd+oXnsnM83NNFfX2Bc75U8XqzpuAIhALLR+ht5i0JQYLPRurnRQcbZrF+K+RX1CKloyu0Eq2tH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9644049}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.25.2_1743355952442_0.46159033491179025", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/win32-arm64", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/win32-arm64@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "f1c867bd1730a9b8dfc461785ec6462e349411ea", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.3.tgz", "fileCount": 3, "integrity": "sha512-YWcow8peiHpNBiIXHwaswPnAXLsLVygFwCB3A7Bh5jRkIBFWHGmNQ48AlX4xDvQNoMZlPYzjVOQDYEzWCqufMQ==", "signatures": [{"sig": "MEQCICS+mCn1VQiTaO1m4l++DaKptkG0aBI+n3QIGZAB02zRAiB+rb3axjcoVFHRyspwOtzfifLkjpC7IZjfNIIUnWXxAw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9649681}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.25.3_1745380518022_0.8682316641836967", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/win32-arm64", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/win32-arm64@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["win32"], "cpu": ["arm64"], "dist": {"shasum": "b4dbcb57b21eeaf8331e424c3999b89d8951dc88", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.4.tgz", "fileCount": 3, "integrity": "sha512-AVUP428VQTSddguz9dO9ngb+E5aScyg7nOeJDrF1HPYu555gmza3bDGMPhmVXL8svDSoqPCsCPjb265yG/kLKQ==", "signatures": [{"sig": "MEYCIQDUgrMhPV+QmXs6RoCk1h7kZ84V/WIwNY3hH12bHLWj/AIhAIdaEfyX8EDj/EUTrtb7SPlZcIt3mFJsH8ps0YJydYMp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9655313}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/win32-arm64_0.25.4_1746491415739_0.9386599171623389", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/win32-arm64", "version": "0.25.5", "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["win32"], "cpu": ["arm64"], "_id": "@esbuild/win32-arm64@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw==", "shasum": "6eadbead38e8bd12f633a5190e45eff80e24007e", "tarball": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz", "fileCount": 3, "unpackedSize": 9657361, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCakJaM5wdU5PCgyXtGYRo+sLBkx7mMEfv8XuLLA+HWnQIgaf4Ir/DZzieZK81FXU0RjjxHVilFetdV4MdiQN3dCb0="}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/win32-arm64_0.25.5_1748315534298_0.045686952785277235"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-12-05T23:58:58.215Z", "modified": "2025-05-27T03:12:14.772Z", "0.15.18": "2022-12-05T23:58:59.385Z", "0.16.0": "2022-12-07T03:54:44.916Z", "0.16.1": "2022-12-07T04:47:56.949Z", "0.16.2": "2022-12-08T06:59:32.562Z", "0.16.3": "2022-12-08T20:12:33.001Z", "0.16.4": "2022-12-10T03:50:15.306Z", "0.16.5": "2022-12-13T17:47:28.141Z", "0.16.6": "2022-12-14T05:22:58.008Z", "0.16.7": "2022-12-14T22:46:43.300Z", "0.16.8": "2022-12-16T23:38:19.699Z", "0.16.9": "2022-12-18T04:31:08.111Z", "0.16.10": "2022-12-19T23:26:19.511Z", "0.16.11": "2022-12-27T01:38:47.679Z", "0.16.12": "2022-12-28T02:04:29.575Z", "0.16.13": "2023-01-02T22:57:06.712Z", "0.16.14": "2023-01-04T20:12:38.750Z", "0.16.15": "2023-01-07T04:18:22.716Z", "0.16.16": "2023-01-08T22:43:37.264Z", "0.16.17": "2023-01-11T21:57:45.278Z", "0.17.0": "2023-01-14T04:33:24.596Z", "0.17.1": "2023-01-16T18:05:21.859Z", "0.17.2": "2023-01-17T06:39:30.225Z", "0.17.3": "2023-01-18T19:14:10.840Z", "0.17.4": "2023-01-22T06:13:24.807Z", "0.17.5": "2023-01-27T16:37:29.433Z", "0.17.6": "2023-02-06T17:00:29.407Z", "0.17.7": "2023-02-09T22:26:31.210Z", "0.17.8": "2023-02-13T06:35:30.134Z", "0.17.9": "2023-02-19T17:45:08.642Z", "0.17.10": "2023-02-20T17:54:42.065Z", "0.17.11": "2023-03-03T22:39:55.523Z", "0.17.12": "2023-03-17T06:16:08.423Z", "0.17.13": "2023-03-24T18:56:54.424Z", "0.17.14": "2023-03-26T02:47:24.074Z", "0.17.15": "2023-04-01T22:26:32.231Z", "0.17.16": "2023-04-10T04:34:47.210Z", "0.17.17": "2023-04-16T21:23:22.037Z", "0.17.18": "2023-04-22T20:41:10.444Z", "0.17.19": "2023-05-13T00:06:15.441Z", "0.18.0": "2023-06-09T21:24:08.023Z", "0.18.1": "2023-06-12T04:51:33.537Z", "0.18.2": "2023-06-13T02:40:16.160Z", "0.18.3": "2023-06-15T12:20:40.024Z", "0.18.4": "2023-06-16T15:37:40.961Z", "0.18.5": "2023-06-20T00:52:23.644Z", "0.18.6": "2023-06-20T23:24:38.572Z", "0.18.7": "2023-06-24T02:46:11.242Z", "0.18.8": "2023-06-25T03:18:55.869Z", "0.18.9": "2023-06-26T05:27:37.182Z", "0.18.10": "2023-06-26T21:20:17.077Z", "0.18.11": "2023-07-01T06:03:40.892Z", "0.18.12": "2023-07-13T01:33:49.446Z", "0.18.13": "2023-07-15T02:36:59.638Z", "0.18.14": "2023-07-18T05:00:02.813Z", "0.18.15": "2023-07-20T12:52:59.892Z", "0.18.16": "2023-07-23T04:47:46.189Z", "0.18.17": "2023-07-26T01:40:39.357Z", "0.18.18": "2023-08-05T17:06:14.833Z", "0.18.19": "2023-08-07T02:51:03.812Z", "0.18.20": "2023-08-08T04:14:38.776Z", "0.19.0": "2023-08-08T15:51:10.052Z", "0.19.1": "2023-08-11T15:57:14.493Z", "0.19.2": "2023-08-14T01:58:07.605Z", "0.19.3": "2023-09-14T01:12:09.938Z", "0.19.4": "2023-09-28T01:46:30.106Z", "0.19.5": "2023-10-17T05:10:19.411Z", "0.19.6": "2023-11-19T07:11:16.221Z", "0.19.7": "2023-11-21T01:00:32.232Z", "0.19.8": "2023-11-26T23:07:40.071Z", "0.19.9": "2023-12-10T05:09:10.649Z", "0.19.10": "2023-12-19T00:21:13.275Z", "0.19.11": "2023-12-29T20:31:32.306Z", "0.19.12": "2024-01-23T17:39:56.636Z", "0.20.0": "2024-01-27T16:49:17.488Z", "0.20.1": "2024-02-19T06:37:44.122Z", "0.20.2": "2024-03-14T19:49:20.707Z", "0.21.0": "2024-05-07T02:52:09.285Z", "0.21.1": "2024-05-07T16:54:39.698Z", "0.21.2": "2024-05-12T20:32:43.468Z", "0.21.3": "2024-05-15T20:52:15.582Z", "0.21.4": "2024-05-25T02:10:38.593Z", "0.21.5": "2024-06-09T21:16:42.857Z", "0.22.0": "2024-06-30T20:37:29.899Z", "0.23.0": "2024-07-02T03:33:24.275Z", "0.23.1": "2024-08-16T22:12:57.304Z", "0.24.0": "2024-09-22T02:06:08.163Z", "0.24.1": "2024-12-20T05:40:13.589Z", "0.24.2": "2024-12-20T17:55:36.441Z", "0.25.0": "2025-02-08T03:01:49.986Z", "0.25.1": "2025-03-10T03:44:53.691Z", "0.25.2": "2025-03-30T17:32:32.768Z", "0.25.3": "2025-04-23T03:55:18.286Z", "0.25.4": "2025-05-06T00:30:16.105Z", "0.25.5": "2025-05-27T03:12:14.593Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "The Windows ARM 64-bit binary for esbuild, a JavaScript bundler.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the Windows ARM 64-bit binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "readmeFilename": "README.md"}