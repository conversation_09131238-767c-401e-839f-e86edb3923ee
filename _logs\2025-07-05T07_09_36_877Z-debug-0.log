0 verbose cli E:\Node\node.exe E:\Nvm\nvm\v20.15.1\node_modules\npm\bin\npm-cli.js
1 info using npm@10.7.0
2 info using node@v20.15.1
3 silly config:load:file:E:\Nvm\nvm\v20.15.1\node_modules\npm\npmrc
4 silly config:load:file:D:\.0000\scarboroughfair\.npmrc
5 silly config:load:file:C:\Users\<USER>\.npmrc
6 silly config:load:file:e:\Node\etc\npmrc
7 verbose title npm exec expo start --clear
8 verbose argv "exec" "--" "expo" "start" "--clear"
9 verbose logfile logs-max:10 dir:d:\.0000\scarboroughfair\_logs\2025-07-05T07_09_36_877Z-
10 verbose logfile d:\.0000\scarboroughfair\_logs\2025-07-05T07_09_36_877Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly logfile done cleaning log files
