{"_id": "@isaacs/fs-minipass", "_rev": "1-bf6b64dba0483f4f98c3b0370f0ac8cd", "name": "@isaacs/fs-minipass", "dist-tags": {"latest": "4.0.1"}, "versions": {"4.0.0": {"name": "@isaacs/fs-minipass", "version": "4.0.0", "main": "./dist/commonjs/index.js", "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "keywords": [], "author": {"name": "<PERSON>"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^7.0.4"}, "devDependencies": {"@types/node": "^20.11.30", "mutate-fs": "^2.1.1", "prettier": "^3.2.5", "tap": "^18.7.1", "tshy": "^1.12.0", "typedoc": "^0.25.12"}, "engines": {"node": ">=18.0.0"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "_id": "@isaacs/fs-minipass@4.0.0", "gitHead": "159a72cf1225de1a5fa172267dee805cdec0bd82", "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-S00nN1Qt3z3dSP6Db45fj/mksrAq5XWNIJ/SWXGP8XPT2jrzEuYRCSEx08JpJwBcG2F1xgiOtBMGDU0AZHmxew==", "shasum": "7c7246204f295bc8bb9a7dafb520d98dd9bdeccb", "tarball": "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.0.tgz", "fileCount": 13, "unpackedSize": 100601, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+oKn50U0D4yIF7krV0C7TJbSCGEuFWmidzr7sOeEGDQIhAImiIzE0Zc00pHiv3uWyz4rsnaO0/TsBZxzN51h9MQsZ"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_4.0.0_1712355025127_0.6930585611093503"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "@isaacs/fs-minipass", "version": "4.0.1", "main": "./dist/commonjs/index.js", "scripts": {"prepare": "tshy", "pretest": "npm run prepare", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "keywords": [], "author": {"name": "<PERSON>"}, "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "description": "fs read and write streams based on minipass", "dependencies": {"minipass": "^7.0.4"}, "devDependencies": {"@types/node": "^20.11.30", "mutate-fs": "^2.1.1", "prettier": "^3.2.5", "tap": "^18.7.1", "tshy": "^1.12.0", "typedoc": "^0.25.12"}, "engines": {"node": ">=18.0.0"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "_id": "@isaacs/fs-minipass@4.0.1", "gitHead": "30706a599289d01e79f313b706c913a1600ddf32", "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "homepage": "https://github.com/npm/fs-minipass#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w==", "shasum": "2d59ae3ab4b38fb4270bfa23d30f8e2e86c7fe32", "tarball": "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz", "fileCount": 13, "unpackedSize": 100617, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGewbN4F6TVrXpZacQLq/IANQuB78P9gYZYHZaXjQ2yyAiEAj/EvkinypmxO2Cwr4yeG7UNK/aY3jWvKYT0I8z7aPQ8="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-minipass_4.0.1_1713460485458_0.8562173541066544"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-04-05T22:10:25.048Z", "4.0.0": "2024-04-05T22:10:25.295Z", "modified": "2024-04-18T17:14:45.826Z", "4.0.1": "2024-04-18T17:14:45.622Z"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "description": "fs read and write streams based on minipass", "homepage": "https://github.com/npm/fs-minipass#readme", "keywords": [], "repository": {"type": "git", "url": "git+https://github.com/npm/fs-minipass.git"}, "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/npm/fs-minipass/issues"}, "license": "ISC", "readme": "# fs-minipass\n\nFilesystem streams based on [minipass](http://npm.im/minipass).\n\n4 classes are exported:\n\n- ReadStream\n- ReadStreamSync\n- WriteStream\n- WriteStreamSync\n\nWhen using `ReadStreamSync`, all of the data is made available\nimmediately upon consuming the stream.  Nothing is buffered in memory\nwhen the stream is constructed.  If the stream is piped to a writer,\nthen it will synchronously `read()` and emit data into the writer as\nfast as the writer can consume it.  (That is, it will respect\nbackpressure.)  If you call `stream.read()` then it will read the\nentire file and return the contents.\n\nWhen using `WriteStreamSync`, every write is flushed to the file\nsynchronously.  If your writes all come in a single tick, then it'll\nwrite it all out in a single tick.  It's as synchronous as you are.\n\nThe async versions work much like their node builtin counterparts,\nwith the exception of introducing significantly less Stream machinery\noverhead.\n\n## USAGE\n\nIt's just streams, you pipe them or read() them or write() to them.\n\n```js\nimport { ReadStream, WriteStream } from 'fs-minipass'\n// or: const { ReadStream, WriteStream } = require('fs-minipass')\nconst readStream = new ReadStream('file.txt')\nconst writeStream = new WriteStream('output.txt')\nwriteStream.write('some file header or whatever\\n')\nreadStream.pipe(writeStream)\n```\n\n## ReadStream(path, options)\n\nPath string is required, but somewhat irrelevant if an open file\ndescriptor is passed in as an option.\n\nOptions:\n\n- `fd` Pass in a numeric file descriptor, if the file is already open.\n- `readSize` The size of reads to do, defaults to 16MB\n- `size` The size of the file, if known.  Prevents zero-byte read()\n  call at the end.\n- `autoClose` Set to `false` to prevent the file descriptor from being\n  closed when the file is done being read.\n\n## WriteStream(path, options)\n\nPath string is required, but somewhat irrelevant if an open file\ndescriptor is passed in as an option.\n\nOptions:\n\n- `fd` Pass in a numeric file descriptor, if the file is already open.\n- `mode` The mode to create the file with. Defaults to `0o666`.\n- `start` The position in the file to start reading.  If not\n  specified, then the file will start writing at position zero, and be\n  truncated by default.\n- `autoClose` Set to `false` to prevent the file descriptor from being\n  closed when the stream is ended.\n- `flags` Flags to use when opening the file.  Irrelevant if `fd` is\n  passed in, since file won't be opened in that case.  Defaults to\n  `'a'` if a `pos` is specified, or `'w'` otherwise.\n", "readmeFilename": "README.md"}