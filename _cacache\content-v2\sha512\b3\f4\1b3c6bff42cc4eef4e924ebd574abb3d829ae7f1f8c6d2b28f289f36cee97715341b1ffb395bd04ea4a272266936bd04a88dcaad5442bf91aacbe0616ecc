{"_id": "minipass", "_rev": "140-5351224f1119b88c54e05ce408c7d018", "name": "minipass", "description": "minimal implementation of a PassThrough stream", "dist-tags": {"latest": "7.1.2", "legacy-v4": "4.2.8"}, "versions": {"1.0.0": {"name": "minipass", "version": "1.0.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "6e91377e7c076f768c2cfd556f365090717d249b", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.0.0", "_shasum": "0ca84f7235109ffe3a8e459cb14d7d6bc3119a67", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "0ca84f7235109ffe3a8e459cb14d7d6bc3119a67", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.0.0.tgz", "integrity": "sha512-15/j8qP71EAWMHeZ58xKaBCZwGRTnr/qEs8ZabDTx0KHDLuPn0TAYVuKF9NGiQWfypykZIfMnhW/TszUu99SXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDI9MHTUXDOWB3uRAIitwCnITwLAwnv/CjnbAiAcq+WkwIhAJ2N6NjZu7gfG+RQV6msxeH1aXlpDodscIUzlldnlU7m"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-1.0.0.tgz_1489450315564_0.20009294734336436"}, "directories": {}}, "1.0.1": {"name": "minipass", "version": "1.0.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "2ee0d11bc4ed89dda4892cffe92718190e4aa4ef", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.0.1", "_shasum": "ca57afde318b5ba9e059a7e64844baa53cdb4034", "_from": ".", "_npmVersion": "4.4.2", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ca57afde318b5ba9e059a7e64844baa53cdb4034", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.0.1.tgz", "integrity": "sha512-+UewrOmdZYgtPN6SyipT8WZiwl39tPaMPqPdrKtc6tvvjL3YQV/XrP6n6w1u03JEnj5TXAReZWTMnmdZUeo/Nw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDU+nIDuXt2SiGEUI4VdxGo8yjF4h3UnOBNM1Q9/aKe5QIgAd1Fx93PqcIw1Q0WuMCSnM7yXmlfe/2OY8ExMkFOWC4="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.0.1.tgz_1490142376611_0.3365593715570867"}, "directories": {}}, "1.0.2": {"name": "minipass", "version": "1.0.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "bbd2607d80a77b3b2e880f065552ada47089f6f2", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.0.2", "_shasum": "c2169e3f3ac2119f6e128ad860c679bf9854c0ec", "_from": ".", "_npmVersion": "4.4.2", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "c2169e3f3ac2119f6e128ad860c679bf9854c0ec", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.0.2.tgz", "integrity": "sha512-sZK3hco/615FTe2WBEoUUEs+jKIWVU3qYR9FPOPFY3ODiLrrBgZNy7pFA0waXPH5K4IYR07ZCesBywg6D6xdfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5Y/c/p2Q1ePrfzk7S0lrsYhpa5WWNyruaC9bfCNJ3XwIgVnOGVkmeP4Sjc9JIINtJ6l/Ddwg2JfbwxGNKxVwUmWc="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.0.2.tgz_1490157604381_0.3243618570268154"}, "directories": {}}, "1.1.0": {"name": "minipass", "version": "1.1.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "5ff9f9e0394a3f997072e347204b0c2deea48d58", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.0", "_shasum": "a0bac558ae88e5f2652b2dea6b80e77aecdbe5be", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "a0bac558ae88e5f2652b2dea6b80e77aecdbe5be", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.0.tgz", "integrity": "sha512-qsTMszSyFH8/TtMCPWC+++Izcynuz+c7QN3JAIdv7GINQkpRB8Wseod70VwglwDi1lmF5srlU/HaddCNhXg7OQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAj8tasD6bqH9q6XP0nDtM7BRSOxx8qmvO5ltaGEsb3bAiEAxSrkw5FyapZVtEskNTS+Py7oRmxNFRYafxrFp/ilQ+E="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.0.tgz_1490681589971_0.4406989624258131"}, "directories": {}}, "1.1.1": {"name": "minipass", "version": "1.1.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "27bbc0c5d090f3deff5836aeb43b20e115e874fd", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.1", "_shasum": "e5c6a6a64562e6038330d2d46694f660e7ebeed1", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "e5c6a6a64562e6038330d2d46694f660e7ebeed1", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.1.tgz", "integrity": "sha512-rgITvLMgK/g/XqwQVjuSSTkmxppX5NgcYLGcgU4oUyar5ZRwezLI78+a4WDEl+IYIrQ7er3L0Cp5wp+mbi27XA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnZ+T0nM99vjobjpV0PppwvYK2t7T9i0ifp9plxmGz9AIhAMvjr1hvzNK9WME7Q3CUz2nomTLIJPIy9tQVDmy2Vd4b"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-1.1.1.tgz_1490684781086_0.2492500205989927"}, "directories": {}}, "1.1.2": {"name": "minipass", "version": "1.1.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "a226cbecaec3f354e7d3d3c68918d8d4ad552690", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.2", "_shasum": "83b490887b77c95f3049a1083403e16d3c0be6fa", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "83b490887b77c95f3049a1083403e16d3c0be6fa", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.2.tgz", "integrity": "sha512-puyFE3xPVp4RHAb0qeMu/LxrTXf8RY21MaV+wruRK04ZMeXMr1Cppizw6QSPzzHN5W9V1JuvMa710Mu/jTF0og==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDfuu5kzkoQ6g8P4c7vFF7sxQQ1+UT+gbx7fKGwBNZOGAiEAwup2Gb01lLPpe5qXUn2VTEq0B+zjBFO2Jf9Co7yDXU4="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.2.tgz_1490688898041_0.4253221561666578"}, "directories": {}}, "1.1.3": {"name": "minipass", "version": "1.1.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "852e03965a06911ff7f650b9d9d502e1a649c785", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.3", "_shasum": "ed16a0d8585a2e67fe0762352e8cf9110c125427", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "ed16a0d8585a2e67fe0762352e8cf9110c125427", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.3.tgz", "integrity": "sha512-U4tryzwDkt5Pwmbv5qXELpASJkmdW6p9FqZy+bH1BNRvkdf2Y6uA4balj4yv8lEKFqwjxjqnvCZTxun5SLZpwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE5iLCaxSEmsvaCwlbFnodQ4/pitf7cM3A5YGQZyQPcVAiEAg/ImDLw4yhuRtzhFKdLxKzITRwfavJ1tHz0kvSnU9rQ="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-1.1.3.tgz_1490749088000_0.1479788450524211"}, "directories": {}}, "1.1.4": {"name": "minipass", "version": "1.1.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "b9cb84b174cd7295eada68d071a590d4f0c47642", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.4", "_shasum": "c8801d72c80981c0e29e59d10e18cd15cadc927d", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "c8801d72c80981c0e29e59d10e18cd15cadc927d", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.4.tgz", "integrity": "sha512-QgpWnmIwn+ifdbFD9G9nUm52aZMsxDd/o5dw9ns6NRSgsOalZhBOa4lMfQizIjHeNmubdsm3+KgUeaLHaFfVPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLYbDjvCGbeKH+CNMXWRYeDs9kUhIr8oic1zhgxlk5tAIgGpH+loBfpotcSOH+3gOO1SIXycX6xD9efL5lcuIC7PM="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.4.tgz_1490750723304_0.5657442165538669"}, "directories": {}}, "1.1.5": {"name": "minipass", "version": "1.1.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "0232f2585e43297c6b5c15b36453ab6d43183be3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.5", "_shasum": "b7a2293b9cc7a123f4878581b8e139693a39891b", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "b7a2293b9cc7a123f4878581b8e139693a39891b", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.5.tgz", "integrity": "sha512-cAf0ktsJ0t48JgfzmKDNpMqA5coPcOELWVM8u/4BS8MNc+lNrsfiE6Bfd3mgV74V98EcyWKef34Zcd6MAJIQdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDkKvTuHn6KVkmHTrhn1lIUMo4BMguH9EyfMZAZ+u14VAiEApAU5VM6ZDuKxyMtBWXYYdcRHT+Y8ptHERsIW6np6JXM="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.5.tgz_1490768335439_0.06365093612112105"}, "directories": {}}, "1.1.6": {"name": "minipass", "version": "1.1.6", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "8e1550a1fc92eea4cf4266782daf5c66682e22a9", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.6", "_shasum": "0dde63c08a781ad3f72ee2ba68f1cc23012b8944", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "0dde63c08a781ad3f72ee2ba68f1cc23012b8944", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.6.tgz", "integrity": "sha512-d43+cD5WXq/h7NbyaOYZlYeBpdwL6yWIoisVh8Bibyxajiy9dg5ernS9oFjfZDsxRWSOWCEZhjvfJEfuazfiWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCvxg2DY9xvHN4yPxi1WbBQkYU6MLZ6iUpMge0TJaeJtAIhAPs7hkq6U1w7Lx/xsJtzbVAwPfopI8Hv2IxcpVu9/lFL"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.6.tgz_1490770011445_0.6162307255435735"}, "directories": {}}, "1.1.7": {"name": "minipass", "version": "1.1.7", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "88b390364333585399e82bcac5878a56b42b9e90", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.7", "_shasum": "8e800c24c61e49e1424d8c367855509df550f453", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "8e800c24c61e49e1424d8c367855509df550f453", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.7.tgz", "integrity": "sha512-M8LOXSP2yDpf36Vt5jGL3tn8JFmH4w4Zxhc+4RwUL6QPWAEYh8x1f+EX9Pla/ioM7Vlcv6Rc/zHuXKPtxO13Ug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmSbD522zhtg+Igkl4tyMn7XGlxS1iwdClPSI0iWETrQIhAPkzfwWvgOews3Zi23ehjq18oeyvnsEWCiskbTWWuoDy"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.7.tgz_1491250866481_0.022621459094807506"}, "directories": {}}, "1.1.8": {"name": "minipass", "version": "1.1.8", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "cfc08d331585f233a0a513a15f9eb0804cdc814c", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.8", "_shasum": "70f2d1ec93c9f11613b6150529e9e1571e562b5c", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "70f2d1ec93c9f11613b6150529e9e1571e562b5c", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.8.tgz", "integrity": "sha512-289//bRHb97eux2cDy7I5v0P+G+mx77V8JHbxeDmzXeiNqTkdex35yU5E3gNqXoQfjaRFxNJ49pu80mWUdlfxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUSwfV5MFTWKFel8ahn9f8wlW2D6WeQq3wrqphZZ22vAIgFgizZSNE0TMqatAVIalk2bKGh8X9z0TD6XgdNNF3HHk="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.8.tgz_1491846777505_0.5472147725522518"}, "directories": {}}, "1.1.9": {"name": "minipass", "version": "1.1.9", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "374dee67e86ca47b79573ed70a642ab20ae50853", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.9", "_shasum": "5fc44fce0b7f1a1f0c27418ec2fa4127f31bb1dc", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "5fc44fce0b7f1a1f0c27418ec2fa4127f31bb1dc", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.9.tgz", "integrity": "sha512-VAK8wAzHddkF+F/reqmFaoms7S5lueIm1oJyimFdKHjgvhnxv6s10u1Oa5yiMC3e4o8ER/34TtmhOG/Ey/6k3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDLJy30OIcwVkDXRD2xv50dS3g29JbWc9TDFD94dNt2gIgW4g47JmfZdDHJ5pKQZ5I7JSM2tB2j3bZpXB9RHKwpgg="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.9.tgz_1492830145534_0.7150215269066393"}, "directories": {}}, "1.1.10": {"name": "minipass", "version": "1.1.10", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "939c3e089af81de898e715151b11ac4cdb3f2887", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.10", "_shasum": "cb0f3810d3380435597763069693b1c72f219eb2", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "cb0f3810d3380435597763069693b1c72f219eb2", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.10.tgz", "integrity": "sha512-3nLEdSnN220giZ2CrXxq5arjdfoNBEnUYs5GH6/tavWLPhxYgPRQZIPx/ivlPwPA5yemQ0BjQFsJWsbTWdg/VA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+xQ+7+5SXu6wh/ei4nhSM+lv+u/unUuTGt94m+9yoPQIgJwjl8ku363TICz/rnVy1LE9GPic4YPVqog+Ov/0eyNs="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-1.1.10.tgz_1493340043913_0.4495376900304109"}, "directories": {}}, "1.1.11": {"name": "minipass", "version": "1.1.11", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "ce4800e752946d0dd11b4779123da0d9f5bb6930", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.1.11", "_shasum": "0780ea6f54a04253e680eace2602302fc123af53", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "0780ea6f54a04253e680eace2602302fc123af53", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.1.11.tgz", "integrity": "sha512-KZ93E4uya83zC53wbYjLP3YB9gIt+WW5pfpROmYJsnnPm9QeNzZces2cwRoQbkHc/IYz4pi7UTz5Z+Xj85Vf0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHBte+ocALDxztfWlN+3I+vnqqi0sDj9uwRfVMunxEzBAiEAkYBagKBJ5fv7I8Bv2ibp1uIo6vaBgeUIUX/FgbnNHtI="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.1.11.tgz_1493519761127_0.9377385785337538"}, "directories": {}}, "1.2.0": {"name": "minipass", "version": "1.2.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "e133c4120ae4ab18acc2cd468bd5668f499054cd", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@1.2.0", "_shasum": "1f4af9c2803921d3035de8aa19443aa5860fea7c", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "1f4af9c2803921d3035de8aa19443aa5860fea7c", "tarball": "https://registry.npmjs.org/minipass/-/minipass-1.2.0.tgz", "integrity": "sha512-XA57AfZj07foIQ6hKuI6KC/+gL5DxbEKm9r1Pn2mJP2cxXQdOOuiM++etKT8M9dO6AyuGJcnMYIlnb+gwOJRGQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDaM4p48+V4U2MKkBKA3t0DOfsCjinYDhsG495C4zkY2wIhAOR05e2z/J8zfFtC8rHpELTNWNN771RmGMqY56Rzfkdo"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-1.2.0.tgz_1493525670102_0.469406632008031"}, "directories": {}}, "2.0.0": {"name": "minipass", "version": "2.0.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "81ddd48d09008cea36adaa7b2d307cb181901140", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.0.0", "_shasum": "e095385c8e8a89be8b2577c7b09ffa91e091fa02", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "e095385c8e8a89be8b2577c7b09ffa91e091fa02", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.0.0.tgz", "integrity": "sha512-bD6zy8L6TIJ39RMrNb1Fs7BADKc0Vo5bQLKytqIQXmuJrxtD81hxt/739uNERaBEKK5Ik7qjP6FVytIK01QU7w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG3r3GiZRrjNZYLiS9kUX3IVtW8pPvdrJxmez3JMeUG2AiATN8J7tg485t6mfcdLm0YqHdap9dkWGIF/ZPdQqVhqjg=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/minipass-2.0.0.tgz_1493884792228_0.924768059514463"}, "directories": {}}, "2.0.1": {"name": "minipass", "version": "2.0.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "d029efb88726070e7c18c61259021e46efb1b8b1", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.0.1", "_npmVersion": "5.0.0-beta.33", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uXQyMf0GrY/RDfekKG1CzpY99cj/6X7VLxBXPt12+84KaPAeWWo3lEwI+DCJInrhSpO6IA4HCbCXJptsh6gM0Q==", "shasum": "9ce4b0ce5b065eb2e5e2e20687e7e1d43579ef78", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICv/VDnedfCgP/qKQ9LFz79m0/IIcbFkJSFKwkV9/1tAAiAucIBrblsa7v2T5hbTwmzvPt2ocwcZRIr9kyYPwHvnZQ=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-2.0.1.tgz_1493930811196_0.312193572986871"}, "directories": {}}, "2.0.2": {"name": "minipass", "version": "2.0.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "b18627e376d16f9188cd4049efb8febacf8b2fa2", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.0.2", "_npmVersion": "5.0.0-beta.44", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DtFmHGmqDzlw/iUypeGvwFSbP3n7R6S0wcJ1GiQkKxM1aQigmCUaQLOT2fGQGNwZCKdqxTtC2NN5FzEWFiz+KA==", "shasum": "fae5c78124051f56fd2007df0012e0dac7a752ce", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAE+u2iq44qmLuE2uN+0j48hiU9b9KJrg/TO9n7uDtMwIhAKXdzVedXsGyvvUV8CKAoJN/TQQx+F7m7kjZdsjg4U3u"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/minipass-2.0.2.tgz_1494436039082_0.553761099698022"}, "directories": {}}, "2.1.0": {"name": "minipass", "version": "2.1.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "7c67ec39540408cc404bac8d96e8bdf572cc641f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.1.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DmPo3Ry1ilNYyQshqcGo45jrMZ4Yq8lBd7eW5o8tqju8E6rryj6qAf8ijh5oq6V3iTJDlA00wfsrEarx/wp18Q==", "shasum": "a25f21dd72a2b8e715ea5c784aa0dbd703759ef7", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2Ts5D2/RAuyjkwDTXe47WB/HaPi5F1ZaeDU4KYJAQaQIhAPSfLGLVPOdLuuohjwf4A18KxvWbqgMRW6/X//LWq+oX"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass-2.1.0.tgz_1497457050581_0.37674622470512986"}, "directories": {}}, "2.1.1": {"name": "minipass", "version": "2.1.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "10", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "cc4d17e603adf90567fc0e0da2cf0b9cfb43d72b", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.1.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xZjdNWL+9Z5Ut0Ay+S/2JJranFcuJJMmXIRKbFEpzETZITghn5w3Gf524kwfrpB7Jm8QplXwKJnkDn/pdF3/7Q==", "shasum": "c80c80a3491c180d4071d8f219bd1b4b0284999d", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCDAKXvJArQ+0VqU8gYNUpizEAN5k/9EDzEJv23ujdTLgIhAL9sl4jUh1nkIfovAcNvE/YunnvAuPQjc3mVd9TuA6ux"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass-2.1.1.tgz_1497457610522_0.42575242393650115"}, "directories": {}}, "2.2.0": {"name": "minipass", "version": "2.2.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^10.7.0", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "277abefa0edbac568083d05b2d7130bc768ed552", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.2.0", "_npmVersion": "5.1.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2Cy9UnruqC1KHTuOyu00TmCgt8YzEQLN58gshpt6JaL8Vq3ir1ArIZ1rU8V1oJzrHpPmoKjlm7eH61R57dc+9Q==", "shasum": "c0db6c9d8ec7609e5a98b40a01bb229b803c961d", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEnB49DRXVcNt67IP79h4GhCe637+B4UADPwruPF20VQIhAMv/Cll7DH2YeU29BWsKbiaWtZO4+k3t80s8vz0AtNrz"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass-2.2.0.tgz_1499577467556_0.7973325287457556"}, "directories": {}}, "2.2.1": {"name": "minipass", "version": "2.2.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^10.7.0", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "7b7d9ad23f01dd0a6347f66a8441cb2dbc0abcec", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.2.1", "_npmVersion": "5.1.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-u1aUllxPJUI07cOqzR7reGmQxmCqlH88uIIsf6XZFEWgw7gXKpJdR+5R9Y3KEDmWYkdIz9wXZs3C0jOPxejk/Q==", "shasum": "5ada97538b1027b4cf7213432428578cb564011f", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.2.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGDs1vxKIhQPmi5m/j0iJWeL/mnSTxSxajK2Xt8CALqQAiEApOKnbsa+crQewqzuPDRpV3HQRaaxVvj5fMzsYUWMs98="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass-2.2.1.tgz_1499663387972_0.7067792634479702"}, "directories": {}}, "2.2.2": {"name": "minipass", "version": "2.2.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^10.7.0", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "ba027488f63fc7de95ccf1ef770901693ae14c2f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.2.2", "_npmVersion": "5.7.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PhtCbGQmUep5DSJW19ixmEpomjGv1xW4fpG0w4PbbrGWy1YJ5Duau8VxZxeuiHdlbpukJvHCzrVHJu900kcwLA==", "shasum": "26feb237462d235ed8b6542abf36fe019490063a", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.2.2.tgz", "fileCount": 19, "unpackedSize": 84963, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7wKNyhFGgRGKhkHdXRIiXBwy5ksdD3XDMhIWvhLynzgIhALeuoXuRpieUKzfMCqjLpiXj2t/CtFKRbOO5VjaU1xmE"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.2.2_1521563006537_0.0684973195384373"}, "_hasShrinkwrap": false}, "2.2.3": {"name": "minipass", "version": "2.2.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^10.7.0", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "8af41a48a2ca01dc857003d650dd74765e950e03", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.2.3", "_npmVersion": "5.7.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kpUYlSURoUeOQ823yjhFyNiEGUYfa3J8ufxOYkvp8ilVFZrukGPHWrDn5xOtfueJS56+Z1msB8D2qVcaaJhlkQ==", "shasum": "cf0ec79c88cd3880da991a9c376a77fe1140ed63", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.2.3.tgz", "fileCount": 3, "unpackedSize": 9661, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNZOdWDpwy2oZ8Ze2OgBZ1omEAxjHisrOdT59WLUfKuQIgTLqPq2KujQMRkJN8gP3AwPe0bmgvATcjKXpDLqXUDyI="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.2.3_1521563195774_0.8260329475137804"}, "_hasShrinkwrap": false}, "2.2.4": {"name": "minipass", "version": "2.2.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.1", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^10.7.0", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "73cf8431e3678b27083e353b8ef0a6bfab84650c", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.2.4", "_npmVersion": "5.7.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hzXIWWet/BzWhYs2b+u7dRHlruXhwdgvlTMDKC6Cb1U7ps6Ac6yQlR39xsbjWJE377YTCtKwIXIpJ5oP+j5y8g==", "shasum": "03c824d84551ec38a8d1bb5bc350a5a30a354a40", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.2.4.tgz", "fileCount": 3, "unpackedSize": 9926, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF9FzT/Re+HBYjlQi0+unmTO0XW9sQHlCqh1pKLZSuOdAiEArcnlxM5wmjJQYJ/HwIreZSBXYCwVNkoirqgiZ2+11Wo="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.2.4_1521564268128_0.1298490999080657"}, "_hasShrinkwrap": false}, "2.3.0": {"name": "minipass", "version": "2.3.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.1", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^11.1.4", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "bcb7f538847de6d30af0c8e83933af7cade2cc58", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jWC2Eg+Np4bxah7llu1IrUNSJQxtLz/J+pOjTM0nFpJXGAaV18XBWhUn031Q1tAA/TJtA1jgwnOe9S2PQa4Lbg==", "shasum": "2e11b1c46df7fe7f1afbe9a490280add21ffe384", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.3.0.tgz", "fileCount": 3, "unpackedSize": 13431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa70DdCRA9TVsSAnZWagAAaNIQAILDYgMFjJkLYlrLWN8v\nb+A/u7O/FPtiDOzrd9UICfDtvRReOaUguOPcfZC2KUxdEYQfBYSR23M/MmqS\niak0p2U43zVQRGu8cfGtrW5nnmaztdFec+KWTml/IlQGawuT8XB3u4cNL4b1\nxTlnhCFntC1/4B2LJ1fs55uWoMVOPKBQfrlFpHoLVvjvKo5HXAnWQtWppXxx\njMGqjOqUiLbjPT7SBoAzosL//QuASnhKnkxwgAptSsLsxYeIilt9/MfWIdtl\nebG6eTXfHoIwayEjBOUIu9mGISKzmDukx5k8VUfWMj3tNtF1MjWdLDEtrmlj\nMwsMkzgaJtT8sD6TH3kjSKCtMuVslypQhKU0UbML0RuLTTM3cxADxjQqrL5D\nP6ustJwFKsgxNFGXHv56sCbIQUw64mdRfbDrg0frYMTfSxdYKAoa+ljmYtzd\nMseBuRIxIth33fUQBOI7Dpf5If0tkd1XeLZZUjMX+D+sFNftYDMM/dTD7KN5\nYmA+b/CWkrcg080u0isgLMmkn9Xb3B2mczeeJMvrfsY9b9XrzBR2BUS58Bvn\niA8niGpCzm9rATjvD2zIIS7J9LTXo8G8Sy+flNVRyq3sO4ranKMd7bpkFl3w\nwlHW5s8gRhH5FucYu0/ki31MaZYWwKfMZDGSlrdQYR+z7uUgOOGDGrgjgLMX\nmrw3\r\n=LoZV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHbVYZs0Lq5SzT3gy074qyQwWqOgF1vJ578C/qKYYc2VAiAOUJMzoODquAVTmxaaFhHPLeuuzg8UkSV5y90MYYIcPQ=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.0_1525629147927_0.7791628235820471"}, "_hasShrinkwrap": false}, "2.3.1": {"name": "minipass", "version": "2.3.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.1", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^11.1.4", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "df22eac41ed1d11e9f7bd2903f88f021cc34f27f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.1", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-liT0Gjaz7OHXg2qsfefVFfryBE9uAsqVFWQ6wVf4KNMzI2edsrCDjdGDpTxRaykbxhSKHu/SDtRRcMEcCcTQ2g==", "shasum": "4e872b959131a672837ab3cb554962bc84b1537d", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.3.1.tgz", "fileCount": 3, "unpackedSize": 13433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/2D8CRA9TVsSAnZWagAAaHQQAIzI4uxYuLRXH0b4n+pn\nSuE4R5Xk9Xj074SBUwvndQZ3B6csp5/GQflXuJOMfXubBbSQvhn+asWfYrvi\nJhgKxd7XcgnOBFVjBn84oF4vl9XIrEh787fV5kaTqtcumUjD8ewn57OnUkrl\nKXV5uN9LQPK5TvxT5+2XWhGNyb/3QcG5rEGKVOkBY1hh/Ct04Kc5OB+mYCia\nQ6iRBXnHA8+lLXzhclD6FsIrFOw6Zuy+3ZzuQf681oQRjqaZhcc5Q1epZ12C\niivNQi6sidEMPSl5s+yScrWzfN2LQeuPnZQ5jhFkklAIhFzz6X32+oiJ711B\nnd1HYsgRE7Idg9gPe2Jbt0FbqkJoR8+C2SoOwA0bEa5CHYZoMBhEZYDsqbOO\nHuCcqaJZsAzc9CWbERc+ec0l/XIL5kes+xG+gzBjB3ARDoFvR71B5YV/LkoV\n/KS1+Mj/NdDmoEWyDa/HOiCMIWCqWKxSzgJqT1Obbpf0hBjoptOMafftXUdN\n7lgXJlxYCmCtQHrgI1IHiGS/tfQ7L/0JiFwY0+4HVAuNExIauR0EuNXjjDSl\nrvwCyGDBzRtZKK20UBQtR07pqasGFvtSU6q4LnEZ4ZB6GFyXXdXzavqqjWRG\nG6QlSPA6ttUMsjCzA0TzIonl6gYP6ohHBZtju5PFMnR26oY5sauOcpru6ECJ\nPYd2\r\n=Y5KV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBK24vDi/hUPLT9EAwynPJ5MHaULTXbYzKzz3Jxr6hWUAiAW8bx7iKZwBYqmYwTSc5Ve12JpIm2LYdSGSjAlcTKS2A=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.1_1526685947485_0.8626022137673797"}, "_hasShrinkwrap": false}, "2.3.2": {"name": "minipass", "version": "2.3.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.1", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^11.1.4", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "77971fac31a26cada594071e82cadfdfdd4949f3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.2", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4AwVB38b0GIVYTfI3+Y9A1yynJkUHA3SGH+nx4YSzjwKXIOeJ9GVdvN88Rl7bivL0DaFkYng6hAC7vbb6+eZ5w==", "shasum": "2bb00e836f1f8d44573f96b08c4f440b330072e4", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.3.2.tgz", "fileCount": 3, "unpackedSize": 13478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbA5GyCRA9TVsSAnZWagAA2e4P/3K5qdTIstyfsF7lBqXn\naCQVtpIDodRTqlk1rN6eS6oqQMmUMZKHd6kUWG76XvVlr/X19E35hLpJWLl8\nXNw9ZCBbECj3Omy/ZoU8fyMAsEevCQzyITeju7O4O82lMsgHH2XlXa+vrpWb\nzNs7669zZK/+Sr9Mh9NQQItkUbBR/ofAEZu1yoNIX0LjEOIwrBgpKZ5fDask\nfP75bKo48o9tevAhCLAZ3fDUXNcXbsApGxZlyviaCBy3Ic0TbowuYYHDjIKo\nhAVcCUsWrrAE3otZfLDvBtJK1ZDq1EYjegli5CixN4PcbZWPuPCzfV58j7bE\n4zbZBBCihp1PxF69HaCrGOTQbtJzTgKWb89yJzJ9u7Ad9l+Jaeshyy8X7MyQ\nTXOjFzNkpyR/CKSLFEd+XKrG776mQ+cx3AkCr3Pl+ll6BnMyzRf9A4ZCUoba\n+96pp/2N/i74v5D0/iHSsdQ3vEAOf7tFDmKxTPUOXVOcV6OyiPAzPFlfNjtH\npsMsSv7mW3C8Mb9J07f5JRxEucBG5q9uPbIe5WgzGiZ3R/188jgbM+76c1sa\nYigDPnb3NuQkdPQopPm9PJm0S6FnAo1IrAJwv2I/WSBsuF5/kRp+rBmGeoHn\nipdyYqlOOoqi5YtsO1SSL/lcK5YQ+gY0u9WlnqFdeHty0AIwMcgJxTg9L3WZ\n0ApF\r\n=EU11\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdd8tU23FGJnY6iGsOFvY1zy2GdiWIJdTF3RC5pdezQgIhAO1U0045RMwWhA64Uqv+2Q5jozpVDOYMtssUL2IOK4RP"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.2_1526960561378_0.7850614237575084"}, "_hasShrinkwrap": false}, "2.3.3": {"name": "minipass", "version": "2.3.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^12.0.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "eb6835c4199e205fae9ae48c5340ff4b3edca60e", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.3", "_npmVersion": "6.0.1", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/jAn9/tEX4gnpyRATxgHEOV6xbcyxgT7iUnxo9Y3+OB0zX00TgKIv/2FZCf5brBbICcwbLqVv2ImjvWWrQMSYw==", "shasum": "a7dcc8b7b833f5d368759cce544dccb55f50f233", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.3.3.tgz", "fileCount": 3, "unpackedSize": 13779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBGiYCRA9TVsSAnZWagAA4fUQAIXgODe6XSj8mBz51x/Y\n+bpAwb72xK1aOG5CoIwCQtPe65iqpIi24Mf9jbjGNdtYwOmIzTR+g1q2LT6l\n9JYKz4ukcOr3u0fSWi35bd+vc4uaqps1r6fXt+CX6BXfW/Z0NZ/4PlkW35q3\n0Tw+Fzon7UhJgtCMw9/DoWohAYJTGrzcsxQhodJph7GXgmSUh+9lL1H/nvKP\nWdjw1B3QK2aXREnH7aX4LipMWpTF9oYZYPxFrel2r32OV+/yqOShKMDgE0X+\nN/7MFagmqnHVUk1ifksU14Omt4JjTiGhVvd3/L77BI1jzNe886Kn4nHr9yNg\n8vYr8fAhg8H0gS04WI8mQNR/0Wtg0wDwhx8SVfwOU+74tV67MX3zC5usqKzy\nZW8isJcJPM61NqCQJbz/RQOVNFLkp84YHEraNH+5JDaLAMXgoTMFdGHW0AUU\n4HkMerUxF2Q1f2DFsdftcpWvr5LAw2rB/T2yA09xFGlfe9vqsONR+twcZmr8\nmhuFFOxkrQyN+PE/dRVl+1CeXrHW/bvVnt73H+a3p3RcWSv90MmBSztCw0P0\nMYz50+U7HxyCFjVufjNpV0t3h+6Q83eoEr+/lL299yPScuXsTvVhnIeV0a8M\nJK9MVHI4DIyZqAvP78/hTr88Dh8Ow+uA26JBNRP90/yJ9ZyiUwqt/sWUmv4C\nplh1\r\n=9vM2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBFYTgWl/MEd6MpH+nDZPB8pcK84DjJZ6/sYeq4Cc3g/AiBNLB1apVrde2F8UNbJyk5nrR7dqJrutIowKZZYxhIyfw=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.3_1527015575005_0.7372253058813618"}, "_hasShrinkwrap": false}, "2.3.4": {"name": "minipass", "version": "2.3.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^12.0.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "files": ["index.js"], "gitHead": "6b2713ffe4871849d232caca4f05d390cfcb93dc", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.4", "_npmVersion": "6.3.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-mlouk1OHlaUE8Odt1drMtG1bAJA4ZA6B/ehysgV0LUIrDHdKgo1KorZq3pK0b/7Z7LJIQ12MNM6aC+Tn6lUZ5w==", "shasum": "4768d7605ed6194d6d576169b9e12ef71e9d9957", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.3.4.tgz", "fileCount": 4, "unpackedSize": 14408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbbxyCRA9TVsSAnZWagAAE88P/RqUiqntM6RQ9sy7QRxD\n/MgbL+0MYqxRlyqPnPdf6A8hHkWm2aG27ZwZHDQLq1kbDF2G3DD5lSjXB8gk\ndY0AJZKJd3OIWECT6XmGAVLqRvXKGZFpKLSZQiPGcBmcRN2KC9BuvccjEKHB\nDrG3NzBvIZ/prEdfEY++TRw4Ohid1enteJ4aLLetnnxIDEyhjNE08UPEMQ66\nUWQRdkxa5G3y4kJhz3I+7ThdttnHUglKowRY4LAqXZkuyqEIQqHy0aW1xOTm\n+qzzfN+5PSMUT7GCTsfkbXR69+TWO67c5qkuBn/UMo6Xl4F3pBlc3hqFKRry\nK2cc3esitMxxcvjkuqLOTcZQn6ybuE3bNsna9wb8XTH3A6jR5n/+xitkI2a5\n3b3zgVx9DbylG6u9Yr9uS4Pdx+09CyKJ5WDlhRkXLB4i57wCgUoWgHaQwc+z\nkZUbDH9qYFJ2B0fbDp3wKtwI1sQ0dVbdnecHdgMxRb+vMFeoaWB6VKNZhbKi\nx3GZ6tTkJsm5zZvv1pMf4cqOpraX0iDEILHzmPsSIozB1A7NjOZgQvIewERW\ninroC0xna1LZJiewElTPP1AX7JRPz3sMsC0yVDa2Q6SXmKLkml32zTxRXmVw\npKGaAbspjq5zMkxOmBEXV8ohVSx5w+ezgvccv9COIdEVWRHuxfx236nNtpuY\nWUjb\r\n=f2tM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFZYYPMCGU3aUv6eeftVNthvtnNAXpnQ/GPDCNlpZMRtAiB0ExPbYWC//BfqJzRvDLJvu+jxjywMb9U+DiCkLgWg6g=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.4_1533918321682_0.4793462682470073"}, "_hasShrinkwrap": false}, "2.3.5": {"name": "minipass", "version": "2.3.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^12.0.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "d362cb255c2b2ad24b0b6bf8cef35c522ba29019", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.3.5", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Gi1W4k059gyRbyVUZQ4mEqLm0YIUiGYfvxhF6SIlk3ui1WVxMTGfGdQ2SInh3PDrRTVvPKgULkpJtT4RH10+VA==", "shasum": "cacebe492022497f656b0f0f51e2682a9ed2d848", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.3.5.tgz", "fileCount": 4, "unpackedSize": 14386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbz5aqCRA9TVsSAnZWagAA/B0P/Rej1sO8g6TSQIjZjCvF\nYsjpCFY2OTCUyOv4S4ZgsedbTBYJF93gbJfVm1hox5Ot0/8G+lBUhD+PfRob\nz6jqVE7u6s7XWnEbIoKXbXDn7ZR4TpcdZDOVSUU9wYODeFiqDyYVhdG5dYbs\nT5VKJ8qeFKlYvE1J4vi78uw4cITREHQruGHxFrWK2YRtSBmhicTxMq9fmrBY\ncKMKVPOA/9tbmskbONUHaLJPxGfn29hda1b7WVouXgOAZIsRI10kRa/AsrTU\nf/7cjF+WLWOSopo2KJmotXzkzSYt88WQmWJdW+WrizCxr7jU4+gLhVRU8tWw\nLwV9v14gNDSDz1KZkdswMIQn2ur+g7pIwU74ssWmHEbaonhNkgFzCVGb1Bko\nzSRCTbwB2jdYre4GMlNtZ2aykZxxOGyPx3E3dBbx5RrfCAf/9PWpXwOOofal\nLaazfYdmvPZzA3pT3/IfbS6aVi0zeJ+9U0NbylcvBUQrZkeNQl15DSO+ecRy\nu8WB30itu09Vsc3pWtjJVdqrtjCd+fJyWYUltTFtBUQwr2Oci6oUDweA7qfe\nDcjVk/bFhCmBq/zfY6PQMQGfnrUcXvyQWQAigSvcPElXQEov2DHkuedB3iPy\npA1mwxQv3T59lvImDtLElLM012+J8QGg9IeUZkG8EPQ6T+8jR68lvoNa/yAZ\nkIYX\r\n=mxCF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDcgt16mK06WqAUbnwfKTzVpgfcfsZb/LmywcbEjZTHnAiA9OlR4TbyrR5cxMky6rZJi1p5EqTLeWQJh5npdGSHZpw=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.3.5_1540331177990_0.793403052371799"}, "_hasShrinkwrap": false}, "2.4.0": {"name": "minipass", "version": "2.4.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^12.0.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "565ebb363ae2b6720fe4d0795aed96ba90f059d7", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.4.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.1", "dist": {"integrity": "sha512-6PmOuSP4NnZXzs2z6rbwzLJu/c5gdzYg1mRI/WIYdx45iiX7T+a4esOzavD6V/KmBzAaopFSTZPZcUx73bqKWA==", "shasum": "38f0af94f42fb6f34d3d7d82a90e2c99cd3ff485", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.4.0.tgz", "fileCount": 4, "unpackedSize": 14402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdYBXyCRA9TVsSAnZWagAAWEQP/RVRncXHbKSzz/Oyzfty\nfSpwiCOArbgwN7v30TB399bPvzDP3HatWLHbZfXx/2eFwynzaMUWV7o5SSB9\neF1Hm5u3oVrdreqVhBRysvEwvPe6jmCOsVZpECwwjM0B8iQXShTtbp490Pg7\n3akqkwyz/uPyWNS8xWr/3tQa44//MwJ0l9ZtnTFqu41bKLZouqf8hZQ/72vO\nG8Fy8KVf08Gufz7KEvptJt+0ptzHvRke32igPI8mq83tST9OTccDI5NISG0z\nBapLSmQQkC93hmyVyiLKkT/PeaZZFMMVA0DsCxAHYdyBoNJSBfFsQwBluivm\nvpMFpOPeaXQcWk6FzpQGD2/TgRl57eBauF9aooJXUJbVj3ZmgChhWura8IUS\nGUIxzf6KpIC6zymx30LFJZ0HZmxYD0WHGd0XKN+I9Qn/EXSZw19/PQvtR5Xi\nYWMJpm28XzK5Q+7t4xIlaf6hfS++vASDZtRw6I+7JLhmAfcwSosDBQN+02qy\nSibS4fv/ltJsQ4x6FJueAkwjQnCaAmnEkGFSVY6tHIK/nvb51YdF770SWhzZ\nW47FkqqbYoSqpQ5Z/y4WZqpuiipqfy2tOZpaSi7yN/plYNqHxXx27hSUNn4n\nFPaIetXoz/Dx8Zd5rS6rgCnTJYeFS4QSKnrY9blrGeDiqX+QinD0waSlDICb\nVmqV\r\n=uDZ1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSaA3iO9VSPp+sTnUJo+rur+h4idHJwnjCqX4s2cz2wwIhAMZcqsOVsqcQaE/jcr6+Dci3zriR+DfaS63kytEOJXQK"}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.4.0_1566578162152_0.5234555613255545"}, "_hasShrinkwrap": false}, "2.5.0": {"name": "minipass", "version": "2.5.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "fab29874f70b04730c864ac52136a7a01bd1801c", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.5.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.2", "dist": {"integrity": "sha512-9FwMVYhn6ERvMR8XFdOavRz4QK/VJV8elU1x50vYexf9lslDcWe/f4HBRxCPd185ekRSjU6CfYyJCECa/CQy7Q==", "shasum": "dddb1d001976978158a05badfcbef4a771612857", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.5.0.tgz", "fileCount": 4, "unpackedSize": 14886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZwwkCRA9TVsSAnZWagAA4AkP/1vfm5IOEJahFgsaqxMv\nW5nhZ1lP2XEGhnSblCxP2dnriKvDV0GiQ6zpfy2tHfz/4zq1sbCLs0tpUcuz\nkPuA2cAv9L8ghkuWdYjbqyaDGg/KPCE7rWAOTj8Wf/DTPo6HywjlkPbbpbJQ\n6XpEdLBqfT3Z16A22Da3gRDNwwURcagKri9D0KarCvSIgRZvT2FjUjIxGvOM\nda4XgGupEtyYFBHoxz+rN0h+I8kWiPExQfTDP13IrLkhJ8XZwwK/o/6zZ7OO\n++cjQilYk9sesm9c/OijDD/7Bk1UMNTjZrhDrwm8IT0YS3oXjm2J9RB9vZWp\nFGVRj2NgBMHskudNiEiPFgS9FAH9/9qvu+ZTcNCSF1Y26SILoYtjNBsEMkPm\nvnV3VhY89A8O+7x38QL3wNVZWxJ+59YDefgSvqu/gjstIO7M2hYLANddto/Q\n5yVnXLB6FUx/3gT+YJ2lj+HY4QPytI8QBe9FPHQxISP/89yK/dwWeoZBJs1w\nWBHUBz7xnxQ+CJ1XlBECtpScsXZDq2ZJ9/i7LLSkon4h8o6A2UvygnYYBiTq\nW2S5u3f8N50P6JgG5Xx/5u1LkYw7nmwXTvPR43Y1ry9ViikFGSkzhsciTbSe\nzTspsEP0yf39Z3Q+5Dwza+jL1dSuaIoYHzDl3mkvYfkmoYYCuzWJvmIzy7MP\n16KY\r\n=BV0J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSSBV8TNKZz5AYq79w0/0WqqALqk/Ap01zz/OlxdIilQIgf2aYBf2cMWLGGeL575Myzfzmjb80bDr8lWd6C4WUqZ8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.5.0_1567034403527_0.5492616475869285"}, "_hasShrinkwrap": false}, "2.5.1": {"name": "minipass", "version": "2.5.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "43f76e2a9dfaccd1b6cc1f93c4130a849df29523", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.5.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-dmpSnLJtNQioZFI5HfQ55Ad0DzzsMAb+HfokwRTNXwEQjepbTkl5mtIlSVxGIkOkxlpX7wIn5ET/oAd9fZ/Y/Q==", "shasum": "cf435a9bf9408796ca3a3525a8b851464279c9b8", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.5.1.tgz", "fileCount": 4, "unpackedSize": 15040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddsVJCRA9TVsSAnZWagAAYcoP/27gJquOtLMaWeJv7+uR\nPW7CfdApTCRDdUuOd/AJvQ70QlWEZlxOrp/AeJ3CcTcnTZCLqhl7fS4FNm39\n/q/mf3QWbTFBaIDBwFIcX3vec2WGfnT5TwpHpqdYAsWSj5L3Narf2mUwPjQQ\nq632XysPCpG26Y/6Sasx/6LOQmy+mvmEwqiNHIdPDkm/yfvzuiWGH3iEkBec\n3snZ/yWzZoXe2qpSqtt1BpR4aOEvu6IYC1IlTLtR+8rqkk0vwZ+jumnec2rw\n6zQqK+3trwbI/PLHmwjlPxYx7JPvysFnVYfhpYmASeorDrTaCUDS4WREcvAZ\nyVLTKHkqSP6/j0yXTMXf8woFMzx/nocEHjuQe04y3qiPKXytLAPnjukPn9VG\ng6RyNR71GfyIxNhzI531tMifylBf5c0cunOXFD1sIBZ4k5IiQo/2IFvw81tO\n6JyBfxbAgd9mbYKNKRvi8rZA7HI7h4nF9dimrVCynwFiCFyqBNp8DJ+XQVer\nM0CrmL58R/Xrh0NMVu6TURzHqpyKiH5kJQdn6h4FyK/V2fv5MgwqT1LUWijH\nWz5dSjOoiw4LgpZmuqZLgNN9/PUw1xN0kgryvf/Xc0uH98TgWbYGtnvBnycC\nPUiSdbxLgD5VTDtNhfp55VCgqKsfzwLpYFyKZZ3F8n63PjiGGpCwbm3HMg6T\n16Km\r\n=HrAl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDxh9XRpzCJAC29lrHYVRFnFyZCaCcxsc/N+GfYD9FIpAiBgDTFiVHUSvglSWtjf5nAj0t7rUgHNAYJrnmr0UKaHJA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.5.1_1568064840586_0.8526816449859596"}, "_hasShrinkwrap": false}, "2.6.0": {"name": "minipass", "version": "2.6.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "f55f91cdda83c20552e431be8fb5ed1daed23ea4", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.6.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-OuNZ0OHrrI+jswzmgivYBZ+fAAGHZA4293d5q0z631/I9QSw3yumKB92njxHIHiB1eAdGRsE+3CcOPkoEyV5FQ==", "shasum": "80a68c8a43257b7f744ce09733f6a9c6eef9f731", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.6.0.tgz", "fileCount": 4, "unpackedSize": 17587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdfyfeCRA9TVsSAnZWagAAbEUP/1Cz0gDRJL4EEzhfQhdi\nq4eH3s5/Qumg41PBZhakzdYQIkPcjo9pb5Lhz9VWQ5cYwtFExoXM61JqmM0b\nEMJCduS9k595Png2dk+57UGKJWNh5JSSvxmOSyHN9Sjp0RM3IU/Lpvb/CNFc\nzK+WNtDjguwe2JGsFp/SfnifnZansAdEmQ0bKakqAUm8QOZutIvIRA2ugRgV\n2NGOaW5/gAUmOLYK5OOLWvSCEcFD2O5pThwpDW8MVRyHVrehmoxngDICisc5\n0CJymhva18N6q2Ii2vsKRaKO8iuh6mPRcbdTRJq0o+aDljkEwpwjoegevxcH\n9iMczIstGV9OcqPjlIx84Tf4n896YhHAHeBpZv5CBPqRoRpyL3T+6uDOSuJj\n8upuMnsECqmxrJ8VBjmpODZ5VjUqmLJoTJWKXh+PagYWADHxCi5OglJyCVGh\n6to0V6rC3ZH0/DbPC30t91TJjwNBcYyoN/HPUw2Uqb9GFvteMfCue/dRZv3F\nKZZd5Wi2CqfHp1yx6PifUMA3SVCfdyIphZpu8OaRorvzjItFDZBSLd+BjXWm\nkO9aa+ECBGnYz7o7NiDWPIxNseTtMUCbUgumLqdv6rzzgC1OJQ9ChiVNcE7A\nkSlzLf5kDaeqZxnzPV1nIsKDJtpnbxk3ubQ5QYptfUiw/vtXbY5dhV80RKJZ\n8OBw\r\n=uOHG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG6vENePYXlF0d7cxvkLuR/c7u2YT2PXjn9T1mY1cbBgAiEAuj4j0rPVyOVbp5LkC1K0GaVuAp7dCHjjH6VUqIkOwZM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.6.0_1568614365824_0.06329190526617268"}, "_hasShrinkwrap": false}, "2.6.1": {"name": "minipass", "version": "2.6.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "78150126c62caef9e2f2d59e33ce6101adbaf95b", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.6.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-B+5oiJnCKYgeJxBiy5FJi/s/0x56Oe0WdaAGlxNLHhhfdvkfgbKKEkLIiikYoxZLA3OKzVrXvN25fuhKH7FZxg==", "shasum": "5f44a94f0d0cd1a347fd65274f32122f68b76acd", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.6.1.tgz", "fileCount": 4, "unpackedSize": 18335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdf/bVCRA9TVsSAnZWagAAowYP/1MsHdvtl+xHBQLotOEG\nQJazXgkKe/VpczN0JFcUpnhV9lIk5i+2Xi2WFs2lbOOWpQsg3o5+VyTYe5rN\nqkY/5JaU1Ju+QXgP+tuuWNNBmUaQkC02am7xRewtYgw+/We1AtngH6XSjwAw\nQ0s8Ny89X62dvlT1jV1OX3PR0GQ6ijWV53mVsmBw8Ea8QE3DhgLX9WkYkcBJ\ndtMlAuo/39g2oNDZlB/aat5pt4ItN23lwTEmYj3tTv4HvgR6noU6uOm5wXmH\nqLJXVkpieWUdBmnCUzzqqjBBWZo74/U93D7Bxz7TCKGcaSl2O4OZoo4Eptp1\n4AaNPcHRUb68psAOjF3Jr/UXKzMhN9EV9A3dTYau41JdQelzEOtnzfB8Jmpz\nEar0xuZX+rBWC88YppPs9Km1VcFkvORrwmS0Dg1dz1L+/ytVuZWdnmw5Sw7+\nspZ1uWnm46abYHJPApY5eb4VlYtTVtpV1KAdm6N7TV/GRK2Hunb/zoeFJAJ6\n5Jx/zkgFV0SgL2BqoHlH93zFAE5kVcQmmu9d3uUj4EDpfULzLrcXOah6FF/u\nRACaWOIHS9EDoT8oQ49Bu8Rz3+ZIR0hRDcyfAVCb/jafvNWJIxeh9bf1CMUM\nz+ue425pbEz0Nga1n5GWaJ6l1yKZXP9wle18Fh7INS7LGn6gwGBR/2hIqXtL\nUD/3\r\n=4eji\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEmPA0tZrSpJf0MR3KGfgfc3F0TONtG61+dW61NQobejAiEAj/kxEZ4xTB1P2vTGKf+5cHt0OrymRPA5IPaAqHfC140="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.6.1_1568667348804_0.059379787938716344"}, "_hasShrinkwrap": false}, "2.6.2": {"name": "minipass", "version": "2.6.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "61a76c19b9cd2159c0784585a77d50645bdfe2d4", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.6.2", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-38Jwdc8AttUDaQAIRX8Iaw3QoCDWjAwKMGeGDF9JUi9QCPMjH5qAQg/hdO8o1nC7Nmh1/CqzMg5FQPEKuKwznQ==", "shasum": "c3075a22680b3b1479bae5915904cb1eba50f5c0", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.6.2.tgz", "fileCount": 4, "unpackedSize": 18068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgAV1CRA9TVsSAnZWagAAUrsP/jVPEmWIE+FYy+laq5JE\nH/zuel/hzbt7H9l6qSe8ihSMit//W1a9WS0K2eCaOPZdb8J5/IZfiu5AOo3F\nX63QEn20v6DMzJ2y0zpMkQK/uB8b8vx/7K5EtzPYRD5lzY5Hq0CsuaiHEKkG\nJe8xD0UDChwUdiLOpXtXoTFDPcWAMIk97ZfUqYiUFjHYy9j2oVEZEZSdJ8Xn\nJEOHyzW3MMAcnEHSTKTH8uuKoBhHzVHLs0aHDX+93uPmIlLJMZwR7CQ4unJ8\nPrBSD9kO9vPt7ScH07iACYfnkUBf4IlX57ZxdAeZFFH4XKIHH2IjxvkBadUy\nnCcFLb6DM6fbqQHZt66BgEd/EbzvLqI1JoCXBKdz8QycYIE13MLwPqk77jw4\nVfGCcMY28i7YqrITK4IoouPkgAMbj5TnrVdBLgwOoCIL2so3Yywl7l6R1lKa\nJCuS9nDGc+dH7L7gvr5MRPgzSN9ZJGgVuTiLUIDNqTuq9V7fJwKup9Zawe9A\nX8bjpsNV7p0bpvopkf0mrzbzKv2tWr3M45VwqvjZi9qR8ksLjcKVYuLLhGDe\nJYadS/UQpB2E1ShbMb4G/0xP0U5BllHi00+35WDO5aaqsQOOm8aFnu4t9hM+\ndX2/x3LZuCM6BJ828Pj++CGgS1BIf4qa+EwwNoBDLsU6Bl8JbsnsPcDbahU9\nTKcn\r\n=74M2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMAkuK4uUdknnrJPKBmUHOyh+IlrsrGBIJ+Q0JJRHj5gIgZghvZMjudVOa5GPDe7eEL0CvB5Y6av4o4BNZ2wpFlks="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.6.2_1568671093262_0.8670196477368928"}, "_hasShrinkwrap": false}, "2.6.3": {"name": "minipass", "version": "2.6.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "0e5cd3265fd37d33ffd2474868d20fd529e40519", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.6.3", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-TQJDWx0rizi7qvXGydyOx+it271jnk/zxV7/mCkTecpMrSksvZ6zTXxWgJS2gSzVmYG1tBufz5r5NaBVkJEERQ==", "shasum": "3ac4ae0fc8835946fdf6d2480659e98e17e88424", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.6.3.tgz", "fileCount": 4, "unpackedSize": 18231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgO+JCRA9TVsSAnZWagAAjYYP/3orn43SUwaUd/1CkxcF\n6KXNxEu+InyRqKV2QS6CHbCjSBsMEu2B1ES/IHnLLhB0fUDs4ooDE1DCus/k\nmLRFZabb3IdaCbV5VLLegdTjjpsh2bVBSy3RFCYgR1ovHOYvPwWDjPkHuhph\nFXdxD+KttEjGbWeCu+sqCxQZQ88fMFYD/YXcdFiJsf8sFkydNs0V6mspdaAP\ne5gPiqaFY8iuhx6CLIeBCwBdxTn+AVX0HfUU3dhDQFU31nQ1NTdMCnQ8c5rW\n9Fy6jlv//HeVmeXTuCrudgwbZe2crtzmUMR431n1Tn9bh1vHCQEgcw+aKFeS\nAbeZBtIZNLQtJSWaLL+EmxFSfYd7K16/KKDS01sNTcyYcTUALHOXitBVc9Fz\n/2oIO4sBuyvo4PYClAskCnMLRne+0oIQ/EAyK6fLcD7KaiYTNYJkX9P/ufFd\nGDOWcckmWXk5GaLUMvX9D8Eg05c12f2clxlC5mlBXljnbsGXX1PpnAVn5N9Q\njwst6mYSeu4XhRXqAa8sGzyi1iZQjaWauHnSW/43xtPIyBQWrmEXpuRoPl0k\nQXfugBT87v6r1JwOlBie0bnNNqu/GYip3Q5a3XKSCo2PU/bNkdBozMNQxfs6\nxIS9PB2t676/w/nEEPyaXO9Umbr414WRNl96uNkwjeml3SLZvrfEeeWXdi3F\n8+Fs\r\n=zRJo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEkP5xVf45ZaADOdiPbLB/ljca6Hj9MqgF4PvoylvgbIAiBjHOfHXMb/bQhq68rlSMn++z6xjHFLFCxt+zWqsKo4IQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.6.3_1568731016405_0.9743197614232673"}, "_hasShrinkwrap": false}, "2.6.4": {"name": "minipass", "version": "2.6.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "e3b7320555e1d7a258bda1987e205ed72c623305", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.6.4", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-D/+wBy2YykFsCcWvaIslCKKus5tqGQZ8MhEzNx4mujLNgHhXWaaUOZkok6/kztAlTt0QkYLEyIShrybNmzoeTA==", "shasum": "c15b8e86d1ecee001652564a2c240c0b6e58e817", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.6.4.tgz", "fileCount": 4, "unpackedSize": 18529, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgQfACRA9TVsSAnZWagAA2hMQAJ6lsVHJ/aM3AoPByJht\n5m2vm3LW8A4WLG3y9f8AioYZl8/FzLhr+bc+gz73wPgAvnm8/grIvJieAbuq\nPybI0V9TsadFl2193jTT32AhlgRJeHcrXRDinjcBoRawhPo9PZqptsOAMOPI\nDtxHtzZV71ZWf8zUlpfQZK97tVl9B5MLViLMncbaR2bp+SaJpPqxUiDzB8LJ\nkfRyvPth8f7MTHtj3Ca39k64ij/YCFPVHt4OKOpi5sqDQBbaU7hNf6nsqdyG\nkUMy77t0vLwZElqXf0a2sZyBrHzjzOpPMWdwTaHuP3AwlIEgr+tXm5cYkG3s\n2c8O+Zz8w+2IEGAMNRxA/h0ApBLIoDrCdIUtVmEkTQ1i3+K8v9aTjnBbAsk0\nKV7O95Tacfqgi7OSDYZeUfTAOdW0yAkqociQVn91xK0o9p3VRi5jmOHzyoaH\n9Apze44ao1tRJjr2GNFvuCPCs4rfm7JWQPcO0SJIPyDGSWL5TEkW3xQwgJCT\nl/KdxAFEEFyElN0LfvsPHnmnYhwFlvHdjSgytvH1a/1TlLvRXkopsIBO0Zzl\n6UHULqYQe9BN5vv3Pa/Zi8H2EgZJjBf41P2Es1MpJixzlsbpE5uzYh85gCN4\nIMEsNoS+5OuY2e6SIpV1K0Uvj2OFiOjCN4PpLFtPUk0u2KnFuuJAzRtzccpx\ndW23\r\n=anRl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDkimBWJsAJbphQ0Nivsbw9CCB63FVLNG90l8Q1m2fXQIhAOHCMHfkvY6dePoI7mqWxpd4b0T46IkARFJDIt6Qs6zc"}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.6.4_1568737215637_0.8029580992745486"}, "_hasShrinkwrap": false}, "2.6.5": {"name": "minipass", "version": "2.6.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "eb03b7ad760791ebb727a393a1056aab490e45ba", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.6.5", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-ewSKOPFH9blOLXx0YSE+mbrNMBFPS+11a2b03QZ+P4LVrUHW/GAlqeYC7DBknDyMWkHzrzTpDhUvy7MUxqyrPA==", "shasum": "1c245f9f2897f70fd4a219066261ce6c29f80b18", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.6.5.tgz", "fileCount": 4, "unpackedSize": 18549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgVu3CRA9TVsSAnZWagAAx+kQAIT7HrzvUSmyaXbmmAG/\niXXhq2Gkr7S610o5IDXmaLzuYStYdkyXHdXjXIXXinTVQNPjPX1bzAwseyA3\nPmILh99WAww0Blcd1FcLukJTcQESsHacrHg09gukT/yJs+qXus4Xlj9bP3PX\nqs+fsCUimV3U5JTru/ctBbFZvdWaetdWq6Y9wqagdVLK0sUbexAlngZgLPSS\niJsninsOQ7Hy25WlctB0IqYHQekH6kwQoX14rGEJxq17W3u3VzLk5g6MMRS6\nP9riLanrZCP9UJJJlIDlp+SMZyDbkOx/kz826pLNRjoY4SE7ncVuBJvQvAdn\nOb2zCzZzzZe083x1PNo4tPVQKcuC1zQF3avQ9iqdIiHH6cDfGsZCpAtNWaKQ\n/QXP4UucO2zh4rggvJEkip9x57cltl0ZKMtR/BwLYN4HkDJgKprzNCuto6ZL\nfBb/PARozNbXFqGv/29IjeX5mP6dWd2Smu6zedG0zeiKMmrVtz8d/la7T+Ry\ntvijafVfk3BE/s2fQPUvt+6F9u1NKy/Cr6a1k20cbmrdjtThh5mY42+UWaJl\nReqcpI74sqLoiGTB6GdUYwgS/zS9DrNnVV3yimrLo1Dr0I7ctqr/OIp4TTdz\naq88HhY2b6FhazYbyAeEckbDx/qguYEmAYd0G9p9ZjTntjNSbSwCeK+Tz8nG\nEtwW\r\n=ZJWy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAngsQlkfS+khaN0EZelQCdyJem3IQ/lTcN1zJPsS69PAiEA+z5cb6LfD+sgWerH5PSEUsdRHYbP7h8dN9qb1XwVGOY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.6.5_1568758710901_0.00493985783646278"}, "_hasShrinkwrap": false}, "2.7.0": {"name": "minipass", "version": "2.7.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.1", "through2": "^2.0.3"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "6e1486f21bc805f98221d1ab07969b39a498a79d", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.7.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-+CbZuJ4uEiuTL9s5Z/ULkuRg1O9AvVqVvceaBrhbYHIy1R3dPO7FMmG0nZLD0//ZzZq0MUOjwdBQvk+w1JHUqQ==", "shasum": "c01093a82287c8331f08f1075499fef124888796", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.7.0.tgz", "fileCount": 4, "unpackedSize": 26975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhxcYCRA9TVsSAnZWagAA170QAJw6YFRHcd22lpJc4qVO\ni0rX9aMkx4LrMHaB3bmKEn83ZAA8ll1VX3aTwpw++VDH4CQDgkz1FsrCQ7VN\nuOU1RJeUQh4SUesh6slZtaqrPgCN6g69nu5i3dhrCXz2yvCqWoDWs91sPPe7\ncinefXyhWDdkryn49Zu2beFigROyvPC5jHBtaqDUJ3tMvBg16C/cuLkl2bV4\n2rkW90EH3rbihQGh3k66TBnXi61IU1OXTskntMslp5MXRAkt11dWf6IcoviJ\nm0FZpNMYD96moaJihbzNcVo8rcIfpDuD9PnU4EIt09lvljriHlcfRlb2hY7a\nkN2rm6bdbzX8u10emFO8AhhRR2CaDAAfS/O40vKFmb9/ntkjf9NBibfn+EfN\npU9p8Cny6IzbSMrxfVyN3aSU83+DAbnyyEni1P88BQwrRg1J4B9B/ox72y7/\n4nnVwrbIvq0/GUfi2Vx8ea5AGaDM57hAGKfUF3j1gvyyzeHXpl761dudqupU\nIgkDpjMFFOTndeFz1TbTUy5GXmo8MdsIwN6u/dOW3GnilR7BDPfDXD10RY1N\nXaUnrMfpd60oCUA6FJ9MXyAcHP0P2iwXl6wath8t7XTQFs9fOItNH5/fAJXW\n3MMIUATJ9hQtnqtxyFDVOhgbevdhmgpQzMk6LNpUp0avfSX2lodUJkwk+T9V\ngad/\r\n=aUZo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1YW9bhAJuCYynsT6V5SngxD6stI5ogU/ElswzVGwq1gIhAK4Gw1XRfBitNIpTsPGn9RBaGGqGFmNz50SlymwI59Gq"}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.7.0_1569134359593_0.6757777043008619"}, "_hasShrinkwrap": false}, "2.8.0": {"name": "minipass", "version": "2.8.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "8cfe8502b45c48bdd3df7ced13a726f0e69f345a", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-zsFPWDGtVTM6szptnZFwpFwXIj2cQW+BhrGmR87In8/Of5dnYnIlsb6Pujb9BpQMCSURRopBg3o2HFHFlfXOPA==", "shasum": "63eb51b46697cbaeb75158ae00cf4f95fb3d88e5", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.8.0.tgz", "fileCount": 4, "unpackedSize": 29861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiAoyCRA9TVsSAnZWagAAeE8P/0wJIWqJ2E++iNcKqtfc\nS7hTakyLiNbRRBcV5u5xMplCt/KwKlZUh8MnuqZUDxNMbD2xrA8M1Wl+MbO2\nCeK2SrbB1iDjoH9SlkrV0SmLHAi1VbDqvUADRV2N6FjBUuEaZsd9ByPU4Ns4\nyMLrKgj0+iH7AlTLiI0LJBORx8DnTeUjruFcNIlvse+NP6FvG9ZYqt11FtA8\n7S6wc6HG0Nni4yu9na82DcTXyxzqvYg3P9PtSFYITs4Qw/GQeXh6156Hb8e+\nrOeTAXACotGteJogzFiIg41lJjq77XURNGGpNUuJURJQh4UuH1nYZuh9bjGc\nlutFw+hwSUl/bMaBZOSh9IlYOctADyJm6VJV+/lX+x7EVy0o8zfKo9ot7Zun\n2PEBpWaM+QABROphqvI3z56fyyj+dHG7TLIWDFn2YGdk0MMfMojIz4de4xpR\nW6SpV/5r669rRL9uwWjfD1RKR0fGrFOBqYjevsxGx/FoxsmEGAgrr3AhdR+7\nOscCLoZfpmn00KkE7aIhZOFb9wqcG4XfGtpXQH1wiVY3t/7g6U+j1Q4PSBtf\nNa6LI2hXp5rTP2WX2RlT4zQ100C2MJ+s5b/usPezkwYDaMZIuCDzys30KWT/\njsWSNgVtXSaBs52KqUi35sYXaH4YkFypMUNi4SaSuAolYESL5XyDtSoDGehI\nvIkk\r\n=Tp3x\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFkuLHQhLtH14wGqtlVWK6hOPhbPYsBGPzkLZHUUCpzWAiB1okPRlWKRrSmhmWNCdwQZoF9jjH3zovGCyM07kA4YdQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.0_1569196592923_0.383813049498406"}, "_hasShrinkwrap": false}, "2.8.1": {"name": "minipass", "version": "2.8.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "f193f5cf35b3b04d434c0b1cea954928842b3770", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-QCG523ParRcE2+9A6wYh9UI3uy2FFLw4DQaVYQrY5HPfszc5M6VDD+j0QCwHm19LI2imes4RB+NBD8cOJccyCg==", "shasum": "a73bdc84cad62e8e6c8d56eba1302a5fe04c5910", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.8.1.tgz", "fileCount": 4, "unpackedSize": 29889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiAwcCRA9TVsSAnZWagAABrsP/ib4lfy9p6cpb9w1uXu/\nbMyS4CW9/C6fTGM7f9bmjEeToEhP6Op1gQ/HaoqACrblwin3HsMudYv3eWDO\ntdpgK1965schRp6sEZkfdvsp8vkAmbRYIHhhMVRc/bU26s9KSfTK65GeOA+m\nutaskLTDSwEcOpJEJTyUjrTJRjlrD7/iz2EqjfN+ljEERWN22pNSGTvTQNSY\nQH/DKTUKCXOuaa2SKFqNyVl9faD3dmKG9sqytBD+xmuV1aTpJQPQ39gr+3DT\nZxtYEgLsapKwO2hJmjGM1trHGyrIlsfV95kOArokJVopT0VMGgqhC62T/AjI\np1cE1xHPuAV8tWRQRwGjXtSBYdzRzMJgV5BO0dZPlY1NfQ4LvbDt6OC8Aa24\nW9GBitr6bnCQr8Rjj9M71ga6PdopJUdMVd1AcJ9Aoq3l4QBjRPbQf3E1j2si\n+rF//H8hDpYcCFjpP54w6AeyLh+l8CRpDLOVHUAzaCzMbnvW3mWa+MB5mhOx\ndvooyvQrjSkjlLnstrJHVzxnS7Yb7KQgjQa7Z21EPwH3kRvhK46XHEPHkWsZ\niz868AJ2H0tX9WWLjdLLrH8q58Uqv7zYIw7WgcgAiW57NAuw0Xne1CxDGZUc\nDT+tS2HRaGG7tVMv84BdusfiCaHXsm+/CW4bs/vC1wGBLnkIq9Ei02AL4hdk\nJYmG\r\n=8Ou4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDW7CY0dOViJOt8ZY5wfr0VKJG9Rbkw3o2vKPEfX6sJ/QIhALyNyJqRWS1oghgrKkkp5XLe+XKFVSC88v9/Ah0xBp92"}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.1_1569197083493_0.4502391094175884"}, "_hasShrinkwrap": false}, "2.8.2": {"name": "minipass", "version": "2.8.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "320eb57f38b3e03965e761efa878ef11ae6afea0", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.2", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-b+Eph8Vj+LLlW739/5jV9aTOQ7lnnyeZ8NbZnK+RqqH702jj6//10UnE0ufPUaBKdJpUZi0CnOUyRIQH1YQAwQ==", "shasum": "17ce3a7ecac3bdb8cafc9caf311e9176e6c840db", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.8.2.tgz", "fileCount": 4, "unpackedSize": 30566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiPluCRA9TVsSAnZWagAAPG8P/0QGs5HGWNr7YHw83vG1\niYm1Cmy1PgWXQtSDo6BLALjUFjMJdK/F2u7p4bbekEpN+IFAf7lIMME1Kyyu\nP77NVSB4gNBuPJOe5cqeqCq/lWvWbL+g9iXmsDNdBW/g5IO49bDA9jFNtOtJ\nHFPuxs9QyJBGbVWT9jXSQkYak0SFtc/lfd5XRJkzpVdvimRI3KWeGTC1/y0R\niJqola0ucYoof/g2VNbrMSYhVLVitVcYez5KjAa/9z8iTKG0oN879TfzoigX\nuyltIWd5mxZ/BJKBiUZilSRwzBFxvtpXqnqClSQRd8AQZE/1ats5i1cufqOx\n8UIyFG7DSFzsFtJdAXgrTQLA/SzExFR7AjOYEpaaEIJ3U6fxlawSN4tOODt2\nbzGGpui7DcNw5pejzLkmic431JUdXruslwObunzITXiufRAm5T05l9cR5K04\ni3MZsh7t2H/YzuSp8IkB6ZhrnV+MPA+6XxOHo6G9O1a09zJejlQ/ozBm40Y9\nGtJ/B4LqdHXP8e3nJXLK0m7lXHqXjfvcMJDki17l7uNRFKWjkBaG9zcymAHS\n4CUDTfaDtuZ3uiNMQ+j/Hdh8pncpLZfcMGqA+HQvjkaJtcrDDfxqYGWnVHpy\nc5FiVBv/SVeAGi+f40pdS2I/ztJjwfP8KyuVDTpJm28Dvyo5WI5XcVYFdFyY\nGNYl\r\n=Gof9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAUE0HWX/Pt4wf4CCsAOSSHIYnoh+1HH8xdh85YdQcD2AiEA3xWKnQD4IsUO8FMwCHM107pPnBuZbMW0YBjm05UrnpQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.2_1569257837761_0.27577167683323145"}, "_hasShrinkwrap": false}, "2.8.3": {"name": "minipass", "version": "2.8.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "549cb9a7f8bad5346e5855ceb6d5e702a6e6049f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.3", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-NcbXxDThFeNW47XHPCCsb6uQdAg7CHy4KNCPKzE0KN2VC0t4E09beCsI5x+3UnB6+AFMnC9jso92SB3KGRpkJQ==", "shasum": "aa87cf674150a1b3c8e278c5f5937d1d3d02b955", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.8.3.tgz", "fileCount": 4, "unpackedSize": 30515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiRN/CRA9TVsSAnZWagAAI/IP/1AXWRkkqyuUa/S0tnr6\nTYgE6jb1E1dRw4tqIH/LvD9omPi2YM7YVEjv4BB9RWd+XlKFj111oRs1CBXF\nICfbseyUUswFcODOQxEf5ITPMlCvJeVc3IIdCImYM+yiI1qP9KgP2nxoTZ5p\n8qKwvxp2/bWFvkH1pf9klXcIKUaErlk5gx4fSRYXQFyhYFsAdzHZoX0o72PZ\nzrnXWVfU30uRx+K/re/otulMjLC0k//AKk1yfkj5qYs8a6tQSoH4Oleehbtz\nefv4jfK5RPrNCBnh6yc/V+0FNPbtk6BhhFHYxHGY811vc0zhLpsZw31SdLPh\n3xYq+jxnoxo4+ZO0P/Vjo9LmkNyDSrGwLo6B7sgjbLMr9XSrmsb7dQ6SFUms\nwbW5rHOXu7tQ5ddhKGtqtGbfn1iBFrM4q3X0NnIl+btJ69/Q7k4v5txTBg2r\nVExXkv+zxF5E41alMvVWFA9PNOPM4Tywkl09mi8GoU4xiyJWBvmawamcM0FL\nJmQp927Rs1sNeTj9noC6TTkuPXkIhHNWXdsxkXjkdH2iegHAGzD7xdMaPaR9\nOVXjXleWg6y9b8b4pCCr8Ti/zA+Qx8xNlGuyYCaO7VdIMoNiE0NCoD6ff3we\nHZMspGy0uy9T9izrBgA88mtSIOKTgqr+oMx673TffEzJCDKHTepMNAEB3dl8\nMLkS\r\n=ejCK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDm7L2FDbsDi5nTTGa5aXtxxoX4M4fS+8kUXB0A58IyIwIgMhh5bjM2O+Vz/UtBekhAHUN3fdZGXK2jSciQ/m5I9MI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.3_1569264510349_0.9347850990046873"}, "_hasShrinkwrap": false}, "2.8.4": {"name": "minipass", "version": "2.8.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "b49a11897700937c425ec1439ec1af2c055b16a8", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.4", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-i97pKD0f0eZLlhcTTSa6b6QlxCD7cvh8t/5MyR/pqchD5GPAeDaUrXQCoYA+W/VmmCgWvS/ADbfW3FUc+iT51Q==", "shasum": "490fe62adeb620c4a3373f62ee1c31cf2ef36385", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.8.4.tgz", "fileCount": 4, "unpackedSize": 30831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiWtlCRA9TVsSAnZWagAAHbEP/2rcncZgqjg+tvJKvPoA\nVHNRQqp604D1S0v22FWl45Xt73t2VJDGeL4Z5MvLWzXpcCMEnpJ3752D2Ixz\ntzW1KK5zxW169JgF0av2V1R2cV7m9P+xC3Er3cs+VfaChcIqDgh9g5NLnQGC\nbYqEjcp+LDmYXTaHSPsz/sdrp8PWjCRooCuEMKeWJgbDFVKoeXhby29jG/Mo\nt9nkSFGQgqpqonXtm2uF9B6DwM6jm5tbYTWLU4Eo3Dnjkwy0GqATDYgOF9bx\nACDSZ1kCxAaS8zQPdHQjEdLuUo1oUmh4VYuMJcPw7YtrlbHU04YkTl5Ag8Pf\nIp9Ha2cqybw1MRf5geXtpZhaV0hMJPGY2RTLJYpSqYfRhx4nDoNpqH4gXIUl\ncAhAhYdTFR06dpGxbfjO5/oyLmThCv1XN6PZbbJcZgG8GBAlXPl2CdOHeLL+\ny2JM731fe15sb9aTV84WUGdYTLM8izB8PRa4sOP4Ln18GB1Nfxtt8iw+vjYz\nzjZA9odxU7Bb2uGsEcfNS6lLsIJm7j28nttz8QgsF5uQB38F61qeVnCfviLh\niGWNuoaqmiZmJYnmoApmnni955ClvZKecSbvezq08ruX1zASa/+4z1eO2QRn\n/aUo3/YzA3tMJy7Pe/ToEm8bfJemZFQkjKlYBROen2rZ91tjwMqosQTrxJ8Y\nIIGQ\r\n=8bem\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGaWwOs0Lc00L1fdprdZ/QZnrRwCf7V2LKT2XZyRr84tAiEAr+9PLbUi03qNW3LKQ/rjbyazyPa43AwI8p1BD+LuDjg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.4_1569287012744_0.2723172970443315"}, "_hasShrinkwrap": false, "deprecated": "This version of Minipass has a serious regression which was fixed in v2.8.5.  Please update ASAP."}, "2.8.5": {"name": "minipass", "version": "2.8.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "5718d456b6bc535febe3b3f52168fa8b2acada04", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.5", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-D5+szmZBoOAfbLjOXY4Ve5bWylyTdrUOmbJPgYmgTF5ovCnCFFTY+I16Ccm/UjSNNAekXtIEDvoCDioFzRz18Q==", "shasum": "072f3c67b1f05fe4703f58a3c38e186c03a17692", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.8.5.tgz", "fileCount": 4, "unpackedSize": 30154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdicwVCRA9TVsSAnZWagAALWgP+QAi5dP6eNV7dGJnjJ6S\neR9gBrukwml/lDWj3ERBVX753YuGIdy4Sdcbp8tioGgi3JPkncbZjpcuqbfR\n5r4BFxwO3Zg9OH69dqwgAJRxpmuOZ2O/2ozolZURlV2wTppdGihR8HxGyQVX\nASrHFR8E2Bu/ps8DQtonMcHfFXSDNVlXxcrqqlsN7ZI2w3MciY0Z+RkM98pD\nyp1hTPRhMCNSqUG60c8CfYwWcwXTR1lMPYfZX9D/U2IrW5GVdc+Yn0j6xNrd\n5223NX3WCGYYk1WgSMHqTFXY52zc7Fe5gWNcQ0OmMDQnLGnIrzlw3lujnLHD\nFytm2oYb0c09zMhLKuhi8hFDkGExf5O71spVHy9GqMqi7BWHpfUSli5GN6Aq\nFBehe+L+kwPi0gNFnyxtSaspg1/Cul7UFhV8sz3XlYwAUSKSQDhBvA/92Re5\njKMpXBCHMMIIFQ+/bvUC4iJvos555eknZW4SV2rzGF0SbJevBYiRqCekjbld\nHbv6qUPlXdtEWctGnOOg1ZDmgynTZIzKtqbIsCuBZxBXPmDWQAPJOXILywFt\nIt6OaWowlsJi9kQKJJXUJaLAOKHZU3nJ6K4FijBcsRWyOh7rYdjrTovChN1O\nclUvqk4ZMZuWVoefSPUzzP8Tq8zpX1xo0hAimWn3b7N2GaYgV3e5NQulm3ql\n+PqH\r\n=OiHl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDxizai0g7j+/uwyVjtxSD6v/4TWRvwwuJHP7cAKomPsAiB73BtKt2VLolaqT6qi3T72gbnp3adoNzucRhYDM3zgXw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.5_1569311764545_0.23634394433582018"}, "_hasShrinkwrap": false}, "2.8.6": {"name": "minipass", "version": "2.8.6", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.4", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "8243e5efd6a058d28479a66e40ab3aba0207cb76", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.8.6", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-lFG7d6g3+/UaFDCOtqPiKAC9zngWWsQZl1g5q6gaONqrjq61SX2xFqXMleQiFVyDpYwa018E9hmlAFY22PCb+A==", "shasum": "620d889ace26356391d010ecb9458749df9b6db5", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.8.6.tgz", "fileCount": 4, "unpackedSize": 35786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdikLECRA9TVsSAnZWagAACxAQAJRQPNCb0+NEU8AA+tax\nNZHOdkljQWK/QJneXsbGa70k3BS2koiEJ/y20ioDKEcWALzL3Vy2xXTW6Ixh\nIOBe/nKT9ssmxlk9JgzB7J3xopnhvRol+zqSXkl9z2jl+BnD8C6r6kBnPZzl\nWEzWvRXKzJIJimAyfyDvLWXZGkDe/1e+XWf+pLEdF1rtTWfbdTQfXjnVlOPv\nTnnU2Cn64pHRYUHK5dFkp/JBm9K7PUcqlPNrAcNrT7A7VF7HfmWtMoTdMnyx\nq29rfpXBHuvTEOug8drHqG7BYgK6NFd8hy7TPih0LDoSIlMF8mRfXjwhky5r\nbVQRFExqXp7nxlG5OdtrmaYAio166+duEQRyPVU+T46L3idKSOYcoKnSGYp/\nQg1I7aTE42Fhu5PmtFpGgPE6R6oJ+RFS2ZuFrQVL75EC++riLJ5dYN0EUCMG\nP1yGyL4pR8cq1wIl0M0h0F1MFiDZtV+yzW6t0nzpireb2ndRyTk/mWkeVOaN\nZ3trn3wZ8s1NzDgNAFmn0O6FLCE4yAU9cqBuepBLa2xv62fmCOY0guK5vyhh\nmTOsJhnq4LNnZrSix/XrAkSMp7pJZXrraNc2CZeCo+26wVpTiwHxhd5bsRVD\nMPl2K+P9HXYuooR/GoFOVt2JvmLItzHY5aUluNNZczBo9qjM769IjBvJmMLO\nY0jj\r\n=ARwv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHEO8PDzndBqyWL8/E0x4yKL12ufi2AYhrwry6uNA1h1AiAByIxvsiokA6N5a3hFi15KzJgDTkrnGgwXJxXapEiXDQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.8.6_1569342147428_0.013183884532681267"}, "_hasShrinkwrap": false}, "2.9.0": {"name": "minipass", "version": "2.9.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "gitHead": "86068755a48b4d58d21376c0c30c1ecc44fe4a8e", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@2.9.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-wxfUjg9WebH+CUDX/CdbRlh5SmfZiy/hpkxaRI16Y9W56Pa75sWgd/rvFilSgrauD9NyFymP/+JFV3KwzIsJeg==", "shasum": "e713762e7d3e32fed803115cf93e04bca9fcc9a6", "tarball": "https://registry.npmjs.org/minipass/-/minipass-2.9.0.tgz", "fileCount": 4, "unpackedSize": 36504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiqoGCRA9TVsSAnZWagAARbUP/19CYwv8HnEmGWKHtdKo\nmvJ5sCP6Tg0UiUUKUz7mYqGhDxobrqPJHQsE+8qmBuatbXu5is9MQDzAgqdu\n4UUivC+DKFr4cNI3oHBPq1ac9mrhHTPQt5NCeEii8M/IiGs83m79gJ1RiTNC\nbij0frdGrGaTtNJWchhiBRxVz/cJpUhb2TzXFEFtWrdqZmhpl1SIMitIYzTb\n4hw2CIQoGS05od/d+8GpPA2JafWVfolvGNeITqAbXKHZj76bsFJJ/i8oHIA3\nmz0Xp6+3nF/RmugTTj1tIREgaDa3lzdmdfuziDbsyZ0E/carfBXoI0vs+xVd\nV2i3jKn9qiYNpV+i9c2DHLN8RJQHNeOluXGHObWC5vdwjMWLOESDw1BVPT7k\nCgWNwzVLrXPFomsL97gIRaFaVv8LIs8eamS/hKEU8X5HZfcAuoAUNVkgaDoF\nrJdVb2RvxUl6A5obso1hy1GY4oJletQLFYfyqOK/Rx1pogPUe+6L2ju32ATl\n1r4qEOj7x1tFCwUSs5udwKLYbtclFd87zqghV4A9aoy1B5xZcPe1iGb9LuZQ\nA5seuKf0kEjpfl/1UWHpDLte2MfpqZWlMXF24QZXjyGc6y3w6HnYrInptN+q\nGaKCzz9j0u9YvJUVRNUwKSGpX7EmH2WHFPXYUYBJa7bAwTWyORwLM0O8E0am\nnvwn\r\n=Yzpm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmqAKtAFy7s50rjkD5MpWOniRPigV8IbRWubxYUfG2eQIgZ5ISuEP9oySZjPae6Zh7E5lCrhQnaeWBpgXtvJIHSO4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_2.9.0_1569368581649_0.9807254959544474"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "minipass", "version": "3.0.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1oObKSrVwLX_7Ut4Z6g3fZW-AX1j1-k6w-cDsrkaSbHM/edit#gid=0)\nfor objects, strings, and buffers.\n\nSupports pipe()ing (including multi-pipe() and backpressure\ntransmission), buffering data until either a `data` event handler or\n`pipe()` is added (so you don't lose the first chunk), and most other\ncases where PassThrough is a good idea.\n\nThere is a `read()` method, but it's much more efficient to consume\ndata from this stream via `'data'` events or by calling `pipe()` into\nsome other stream.  Calling `read()` requires the buffer to be\nflattened in some cases, which requires copying memory.\n\nThere is also no `unpipe()` method.  Once you start piping, there is\nno stopping it!\n\nIf you set `objectMode: true` in the options, then whatever is written\nwill be emitted.  Otherwise, it'll do a minimal amount of Buffer\ncopying to ensure proper Streams semantics when `read(n)` is called.\n\n`objectMode` can also be set by doing `stream.objectMode = true`, or by\nwriting any non-string/non-buffer data.  `objectMode` cannot be set to\nfalse once it is set.\n\nThis is not a `through` or `through2` stream.  It doesn't transform\nthe data, it just passes it right through.  If you want to transform\nthe data, extend the class, and override the `write()` method.  Once\nyou're done transforming the data however you want, call\n`super.write()` with the transform output.\n\nFor some examples of streams that extend Minipass in various ways, check\nout:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap)\n- [treport](http://npm.im/tap)\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different from (and in\nsome ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with noode-core streams and\nintend to use Minipass streams in your programs.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.  Thus, data\nis emitted as soon as it is available, always.  It is buffered until read,\nbut no longer.  Another way to look at it is that Minipass streams are\nexactly as synchronous as the logic that writes into them.\n\nThis can be surprising if your code relies on `PassThrough.write()` always\nproviding data on the next tick rather than the current one, or being able\nto call `resume()` and not have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no way for\nMinipass to achieve the speeds it does, or support the synchronous use\ncases that it does.  Simply put, waiting takes time.\n\nThis non-deferring approach makes Minipass streams much easier to reason\nabout, especially in the context of Promises and other flow-control\nmechanisms.\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer, returning `true`\non all writes until the limit is hit, even if the data has nowhere to go.\nThen, they will not attempt to draw more data in until the buffer size dips\nbelow a minimum value.\n\nMinipass streams are much simpler.  The `write()` method will return `true`\nif the data has somewhere to go (which is to say, given the timing\nguarantees, that the data is already there by the time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and the data\nsits in a buffer, to be drained out immediately as soon as anyone consumes\nit.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written all the way\nthrough the pipeline, and `write()` always returns true/false based on\nwhether the data was fully flushed, backpressure is communicated\nimmediately to the upstream caller.  This minimizes buffering.\n\nConsider this case:\n\n```js\nconst {PassThrough} = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage, and\nmultiple event deferrals happened, for an unblocked pipeline where it was\nperfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead someone reading\nthe code to think an advisory maximum of 1KiB is being set for the\npipeline.  However, the actual advisory buffering level is the _sum_ of\n`highWaterMark` values, since each one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data written,\nor _ever_ buffer data that can be flushed all the way through.  Neither\nnode-core streams nor Minipass ever fail to buffer written data, but\nnode-core streams do a lot of unnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less stuff and\nwaits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing any data\ninto it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't want to\npotentially happen immediately (for example, closing file descriptors,\nmoving on to the next entry in an archive parse stream, etc.) then be sure\nto call `stream.pause()` on creation, and then `stream.resume()` once you\nare ready to respond to the `end` event.\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not yet have had\na chance to add a listener.  In order to avoid this hazard, Minipass\nstreams safely re-emit the `'end'` event if a new listener is added after\n`'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream has already\nemitted `end`, then it will call the handler right away.  (You can think of\nthis somewhat like attaching a new `.then(fn)` to a previously-resolved\nPromise.)\n\nTo prevent calling handlers multiple times who would not expect multiple\nends to occur, all listeners are removed from the `'end'` event whenever it\nis emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data through the\npipeline when a new pipe destination is added, this can have surprising\neffects, especially when a stream comes in from some other function and may\nor may not have data in its buffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nThe solution is to create a dedicated tee-stream junction that pipes to\nboth locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nstream.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners.  The first one\nadded will _immediately_ receive all of the data, leaving nothing for the\nsecond:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\n## USAGE\n\nIt's a stream!  Use it like a stream and it'll most likely do what you want.\n\n```js\nconst Minipass = require('minipass')\nconst mp = new Minipass(options) // optional: { encoding, objectMode }\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n* `encoding` How would you like the data coming _out_ of the stream to be\n  encoded?  Accepts any values that can be passed to `Buffer.toString()`.\n* `objectMode` Emit data exactly as it comes in.  This will be flipped on\n  by default if you write() something other than a string or Buffer at any\n  point.  Setting `objectMode: true` will prevent setting any encoding\n  value.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and `Writable`\nstreams.\n\n### Methods\n\n* `write(chunk, [encoding], [callback])` - Put data in.  (Note that, in the\n  base Minipass class, the same data will come out.)  Returns `false` if\n  the stream will buffer the next write, or true if it's still in\n  \"flowing\" mode.\n* `end([chunk, [encoding]], [callback])` - Signal that you have no more\n  data to write.  This will queue an `end` event to be fired when all the\n  data has been consumed.\n* `setEncoding(encoding)` - Set the encoding for data coming of the\n  stream.  This can only be done once.\n* `pause()` - No more data for a while, please.  This also prevents `end`\n  from being emitted for empty streams until the stream is resumed.\n* `resume()` - Resume the stream.  If there's data in the buffer, it is\n  all discarded.  Any buffered events are immediately emitted.\n* `pipe(dest)` - Send all output to the stream provided.  There is no way\n  to unpipe.  When data is emitted, it is immediately written to any and\n  all pipe destinations.\n* `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are EventEmitters.\n  Some events are given special treatment, however.  (See below under\n  \"events\".)\n* `promise()` - Returns a Promise that resolves when the stream emits\n  `end`, or rejects if the stream emits `error`.\n* `collect()` - Return a Promise that resolves on `end` with an array\n  containing each chunk of data that was emitted, or rejects if the\n  stream emits `error`.  Note that this consumes the stream data.\n* `concat()` - Same as `collect()`, but concatenates the data into a\n  single Buffer object.  Will reject the returned promise if the stream is\n  in objectMode, or if it goes into objectMode by the end of the data.\n* `read(n)` - Consume `n` bytes of data out of the buffer.  If `n` is not\n  provided, then consume all of it.  If `n` bytes are not available, then\n  it returns null.  **Note** consuming streams in this way is less\n  efficient, and can lead to unnecessary Buffer copying.\n* `destroy([er])` - Destroy the stream.  If an error is provided, then an\n  `'error'` event is emitted.  If the stream has a `close()` method, and\n  has not emitted a `'close'` event yet, then `stream.close()` will be\n  called.  Any Promises returned by `.promise()`, `.collect()` or\n  `.concat()` will be rejected.  After being destroyed, writing to the\n  stream will emit an error.  No more data will be emitted if the stream is\n  destroyed, even if it was previously buffered.\n\n### Properties\n\n* `bufferLength` Read-only.  Total number of bytes buffered, or in the case\n  of objectMode, the total number of objects.\n* `encoding` The encoding that has been set.  (Setting this is equivalent\n  to calling `setEncoding(enc)` and has the same prohibition against\n  setting multiple times.)\n* `flowing` Read-only.  Boolean indicating whether a chunk written to the\n  stream will be immediately emitted.\n* `emittedEnd` Read-only.  Boolean indicating whether the end-ish events\n  (ie, `end`, `prefinish`, `finish`) have been emitted.  Note that\n  listening on any end-ish event will immediateyl re-emit it if it has\n  already been emitted.\n* `writable` Whether the stream is writable.  Default `true`.  Set to\n  `false` when `end()`\n* `readable` Whether the stream is readable.  Default `true`.\n* `buffer` A [yallist](http://npm.im/yallist) linked list of chunks written\n  to the stream that have not yet been emitted.  (It's probably a bad idea\n  to mess with this.)\n* `pipes` A [yallist](http://npm.im/yallist) linked list of streams that\n  this stream is piping into.  (It's probably a bad idea to mess with\n  this.)\n* `destroyed` A getter that indicates whether the stream was destroyed.\n* `paused` True if the stream has been explicitly paused, otherwise false.\n* `objectMode` Indicates whether the stream is in `objectMode`.  Once set\n  to `true`, it cannot be set to `false`.\n\n### Events\n\n* `data` Emitted when there's data to read.  Argument is the data to read.\n  This is never emitted while not flowing.  If a listener is attached, that\n  will resume the stream.\n* `end` Emitted when there's no more data to read.  This will be emitted\n  immediately for empty streams when `end()` is called.  If a listener is\n  attached, and `end` was already emitted, then it will be emitted again.\n  All listeners are removed when `end` is emitted.\n* `prefinish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'end'`.\n* `finish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'prefinish'`.\n* `close` An indication that an underlying resource has been released.\n  Minipass does not emit this event, but will defer it until after `end`\n  has been emitted, since it throws off some stream libraries otherwise.\n* `drain` Emitted when the internal buffer empties, and it is again\n  suitable to `write()` into the stream.\n* `readable` Emitted when data is buffered and ready to be read by a\n  consumer.\n* `resume` Emitted when stream changes state from buffering to flowing\n  mode.  (Ie, when `resume` is called, `pipe` is called, or a `data` event\n  listener is added.)\n\n### Static Methods\n\n* `Minipass.isStream(stream)` Returns `true` if the argument is a stream,\n  and false otherwise.  To be considered a stream, the object must be\n  either an instance of Minipass, or an EventEmitter that has either a\n  `pipe()` method, or both `write()` and `end()` methods.  (Pretty much any\n  stream in node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass streams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(() => {\n  // stream is finished\n}, er => {\n  // stream emitted an error\n})\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one chunk for\nyou, but if you're going to do it yourself anyway, it's convenient this\nway:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in\nplatforms that support it.\n\nSynchronous iteration will end when the currently available data is\nconsumed, even if the `end` event has not been reached.  In string and\nbuffer mode, the data is concatenated, so unless multiple writes are\noccurring in the same tick as the `read()`, sync iteration loops will\ngenerally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written, with\nno flattening, create the stream with the `{ objectMode: true }`\noption.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i --> 0)\n    mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume () {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write (chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end (chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(new (class extends Minipass {\n    emit (ev, ...data) {\n      // let's also log events, because debugging some weird thing\n      console.log('EMIT', ev)\n      return super.emit(ev, ...data)\n    }\n    write (chunk, encoding, callback) {\n      console.log('WRITE', chunk, encoding)\n      return super.write(chunk, encoding, callback)\n    }\n    end (chunk, encoding, callback) {\n      console.log('END', chunk, encoding)\n      return super.end(chunk, encoding, callback)\n    }\n  }))\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit (ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write (obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end (obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor (options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write (chunk, encoding, cb) {\n    if (typeof chunk === 'string' &&\n        typeof encoding === 'string' &&\n        encoding !== 'utf8') {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk))\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      let parsed\n      try {\n        super.write(parsed)\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb)\n      cb()\n  }\n}\n```\n", "readmeFilename": "README.md", "gitHead": "76ac42b7ffa1bd3cc2f669d828ec5aedf74ad6f5", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.0.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"integrity": "sha512-FKNU4XrAPDX0+ynwns7njVu4RolyG1mUKSlT6n6GwGXLtYSYh2Znc0S83Rl6zEr1zgFfXvAzIBabnmItm+n19g==", "shasum": "adb830268348df8b32217ceda3fc48684faff232", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.0.0.tgz", "fileCount": 4, "unpackedSize": 36316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkmKGCRA9TVsSAnZWagAATh8P/01E8RbfOc2iGt3iijMJ\nHZ1mi9XpYAWciHhi+hwfQGjyJtOt2KcPvuw2qP43L4YBGE0ggv8NW1rNayAM\nWPUs7cmXYplDTX3X9/z5l5WeTn5/0hXQnQpNzYkktb8HvC8agnNvEF+hhqxK\n3vYLILaRyOsdoOPfKWNwaggGxOPYG3HV4TRipAnI9VzSA0Cj5ITg9ffWyYDI\nejrguPBoYWM/LaZiaUzUot0lmpwYlrAfvljHvK5SWjYNLKd6PGT9bngiBDC2\nFQPlahPI3NEUnrTSAU4xjaoK6rbLJnu7GLPHwrdLJdehlIVTkm++vs0YbVF2\naTYg7Ku3L3bWp0O7S2bd2TNqkAWy2ThbQG9eOyYPvVv5VYTp+S3c5jR7Du9i\nWlD2Jk8ohTrLgA2v9l97LNz0fEviw3JyQAE1ILIZGVUWDkCtKo6wK3mcYA6v\nM6SZg2zkSbdRFyRe50XR3ZlmBHuwMxcEJHcbY89hhOv56lJfH/j68/EMZAXs\nBnzdk5Q/zfMNdp2vLRS2BxukhlvXk3zduOwFbrH4RC/nYphirkk9sOBmZp6r\nUbF9e2nOS+nqau9BoBP3Xv/5OxAwdBQDutGRw5Q4eE8kI2VQEkl4Yvd9kHWJ\nWcUmWyto8KI1GH40NdgcsD3htaN9nURlYoLbD5X83OU+s3fuEOFLCnewNjec\nFsOD\r\n=ycn/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAygEBkn4cyVci+x70MRCPXupTCiY80J+Dr6vUP1j2mFAiEAzlfthhkOVqq0B9wLXaf5u34f9gg6ft3VnNkwDOO/+Sw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.0.0_1569874565676_0.7731705094606287"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "minipass", "version": "3.0.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "gitHead": "603b8116ea11af26b43f2f2d0888edad77696b4f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.0.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"integrity": "sha512-2y5okJ4uBsjoD2vAbLKL9EUQPPkC0YMIp+2mZOXG3nBba++pdfJWRxx2Ewirc0pwAJYu4XtWg2EkVo1nRXuO/w==", "shasum": "b4fec73bd61e8a40f0b374ddd04260ade2c8ec20", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.0.1.tgz", "fileCount": 4, "unpackedSize": 36376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlNGyCRA9TVsSAnZWagAAeXYP/3P8+w1GqE3HfaChttnt\nVZV2EjJJhcWs5lWB1kxmbVkwC1tqjmEOdfPgo9Uu1qzFbh0q3tFxGtotoW/Y\nNUclm0+9FCra2sX/+Wp/hTyOgERx9tAyBMwW2M5X/paM0LpWdbNW2sgQK8wR\nf0iiq573E73ns0lIsWdHn48Sn/ORdKuV7nWSe8wbw3FlpZUgcqZkpT53QYtd\n0Qc7vxZ6XhYiBDoP04dmg5yZmCMVbrQKlWwkXhmejeQ86uz9c+IpbMXMiHNM\nKbbOTV1xlslLGdiE91HTr4bdj7wakO5i+bCVa1iD0W4/KJ+QTdh7pmQ5Z/n8\n4QkdDV3tByhGUt2xMHG39btJs0p13G4J7ZuOYEua1ywAgvf0dy1tKFSSLVmK\nylcb96LujFNkDxoT7/+Q9VwFjrO+FKaKAgWyCQD1rpCGrfoIQZhtF4wWpw0/\np+J1Err9q/Lp2zGNbKDdSeTedDVWLZRzGzzyRk7vB9c0frHdzoDHH+FGjwPa\nPfgVeXDqIaM6/BY7OlmTYPyZWuiIqOkqE0x4/qrEITYsjsepOU6n0/+u+QWI\n/B6gY7quR5XDxRS/UUXnwZHlMneSfQYaYbpdrUMPRzcEMHimVH3ruJfEQdtk\nt7DeuQZ0+q1VpUw6snya73Flx8Pv5yaoJk3pIeHvBXR2MnyCgXfOZvyY1K6S\n9evl\r\n=tUZh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWuYajGEr/U2cWv923gilSCaLEP6NamMkYXnwINxv3LQIgXWLoNQh9vhEYb7LHEuRKyMxqYWQNWxEey0Nm5/CT1WE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.0.1_1570034097465_0.9922015990068342"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "minipass", "version": "3.1.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1oObKSrVwLX_7Ut4Z6g3fZW-AX1j1-k6w-cDsrkaSbHM/edit#gid=0)\nfor objects, strings, and buffers.\n\nSupports pipe()ing (including multi-pipe() and backpressure transmission),\nbuffering data until either a `data` event handler or `pipe()` is added (so\nyou don't lose the first chunk), and most other cases where PassThrough is\na good idea.\n\nThere is a `read()` method, but it's much more efficient to consume data\nfrom this stream via `'data'` events or by calling `pipe()` into some other\nstream.  Calling `read()` requires the buffer to be flattened in some\ncases, which requires copying memory.\n\nThere is also no `unpipe()` method.  Once you start piping, there is no\nstopping it!\n\nIf you set `objectMode: true` in the options, then whatever is written will\nbe emitted.  Otherwise, it'll do a minimal amount of Buffer copying to\nensure proper Streams semantics when `read(n)` is called.\n\n`objectMode` can also be set by doing `stream.objectMode = true`, or by\nwriting any non-string/non-buffer data.  `objectMode` cannot be set to\nfalse once it is set.\n\nThis is not a `through` or `through2` stream.  It doesn't transform the\ndata, it just passes it right through.  If you want to transform the data,\nextend the class, and override the `write()` method.  Once you're done\ntransforming the data however you want, call `super.write()` with the\ntransform output.\n\nFor some examples of streams that extend Minipass in various ways, check\nout:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap)\n- [treport](http://npm.im/tap)\n- [minipass-fetch](http://npm.im/minipass-fetch)\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different from (and in\nsome ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with noode-core streams and\nintend to use Minipass streams in your programs.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.  Thus, data\nis emitted as soon as it is available, always.  It is buffered until read,\nbut no longer.  Another way to look at it is that Minipass streams are\nexactly as synchronous as the logic that writes into them.\n\nThis can be surprising if your code relies on `PassThrough.write()` always\nproviding data on the next tick rather than the current one, or being able\nto call `resume()` and not have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no way for\nMinipass to achieve the speeds it does, or support the synchronous use\ncases that it does.  Simply put, waiting takes time.\n\nThis non-deferring approach makes Minipass streams much easier to reason\nabout, especially in the context of Promises and other flow-control\nmechanisms.\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer, returning `true`\non all writes until the limit is hit, even if the data has nowhere to go.\nThen, they will not attempt to draw more data in until the buffer size dips\nbelow a minimum value.\n\nMinipass streams are much simpler.  The `write()` method will return `true`\nif the data has somewhere to go (which is to say, given the timing\nguarantees, that the data is already there by the time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and the data\nsits in a buffer, to be drained out immediately as soon as anyone consumes\nit.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written all the way\nthrough the pipeline, and `write()` always returns true/false based on\nwhether the data was fully flushed, backpressure is communicated\nimmediately to the upstream caller.  This minimizes buffering.\n\nConsider this case:\n\n```js\nconst {PassThrough} = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage, and\nmultiple event deferrals happened, for an unblocked pipeline where it was\nperfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead someone reading\nthe code to think an advisory maximum of 1KiB is being set for the\npipeline.  However, the actual advisory buffering level is the _sum_ of\n`highWaterMark` values, since each one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data written,\nor _ever_ buffer data that can be flushed all the way through.  Neither\nnode-core streams nor Minipass ever fail to buffer written data, but\nnode-core streams do a lot of unnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less stuff and\nwaits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing any data\ninto it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't want to\npotentially happen immediately (for example, closing file descriptors,\nmoving on to the next entry in an archive parse stream, etc.) then be sure\nto call `stream.pause()` on creation, and then `stream.resume()` once you\nare ready to respond to the `end` event.\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not yet have had\na chance to add a listener.  In order to avoid this hazard, Minipass\nstreams safely re-emit the `'end'` event if a new listener is added after\n`'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream has already\nemitted `end`, then it will call the handler right away.  (You can think of\nthis somewhat like attaching a new `.then(fn)` to a previously-resolved\nPromise.)\n\nTo prevent calling handlers multiple times who would not expect multiple\nends to occur, all listeners are removed from the `'end'` event whenever it\nis emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data through the\npipeline when a new pipe destination is added, this can have surprising\neffects, especially when a stream comes in from some other function and may\nor may not have data in its buffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nThe solution is to create a dedicated tee-stream junction that pipes to\nboth locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nstream.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners.  The first one\nadded will _immediately_ receive all of the data, leaving nothing for the\nsecond:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\n## USAGE\n\nIt's a stream!  Use it like a stream and it'll most likely do what you\nwant.\n\n```js\nconst Minipass = require('minipass')\nconst mp = new Minipass(options) // optional: { encoding, objectMode }\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n* `encoding` How would you like the data coming _out_ of the stream to be\n  encoded?  Accepts any values that can be passed to `Buffer.toString()`.\n* `objectMode` Emit data exactly as it comes in.  This will be flipped on\n  by default if you write() something other than a string or Buffer at any\n  point.  Setting `objectMode: true` will prevent setting any encoding\n  value.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and `Writable`\nstreams.\n\n### Methods\n\n* `write(chunk, [encoding], [callback])` - Put data in.  (Note that, in the\n  base Minipass class, the same data will come out.)  Returns `false` if\n  the stream will buffer the next write, or true if it's still in \"flowing\"\n  mode.\n* `end([chunk, [encoding]], [callback])` - Signal that you have no more\n  data to write.  This will queue an `end` event to be fired when all the\n  data has been consumed.\n* `setEncoding(encoding)` - Set the encoding for data coming of the stream.\n  This can only be done once.\n* `pause()` - No more data for a while, please.  This also prevents `end`\n  from being emitted for empty streams until the stream is resumed.\n* `resume()` - Resume the stream.  If there's data in the buffer, it is all\n  discarded.  Any buffered events are immediately emitted.\n* `pipe(dest)` - Send all output to the stream provided.  There is no way\n  to unpipe.  When data is emitted, it is immediately written to any and\n  all pipe destinations.\n* `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are EventEmitters.  Some\n  events are given special treatment, however.  (See below under \"events\".)\n* `promise()` - Returns a Promise that resolves when the stream emits\n  `end`, or rejects if the stream emits `error`.\n* `collect()` - Return a Promise that resolves on `end` with an array\n  containing each chunk of data that was emitted, or rejects if the stream\n  emits `error`.  Note that this consumes the stream data.\n* `concat()` - Same as `collect()`, but concatenates the data into a single\n  Buffer object.  Will reject the returned promise if the stream is in\n  objectMode, or if it goes into objectMode by the end of the data.\n* `read(n)` - Consume `n` bytes of data out of the buffer.  If `n` is not\n  provided, then consume all of it.  If `n` bytes are not available, then\n  it returns null.  **Note** consuming streams in this way is less\n  efficient, and can lead to unnecessary Buffer copying.\n* `destroy([er])` - Destroy the stream.  If an error is provided, then an\n  `'error'` event is emitted.  If the stream has a `close()` method, and\n  has not emitted a `'close'` event yet, then `stream.close()` will be\n  called.  Any Promises returned by `.promise()`, `.collect()` or\n  `.concat()` will be rejected.  After being destroyed, writing to the\n  stream will emit an error.  No more data will be emitted if the stream is\n  destroyed, even if it was previously buffered.\n\n### Properties\n\n* `bufferLength` Read-only.  Total number of bytes buffered, or in the case\n  of objectMode, the total number of objects.\n* `encoding` The encoding that has been set.  (Setting this is equivalent\n  to calling `setEncoding(enc)` and has the same prohibition against\n  setting multiple times.)\n* `flowing` Read-only.  Boolean indicating whether a chunk written to the\n  stream will be immediately emitted.\n* `emittedEnd` Read-only.  Boolean indicating whether the end-ish events\n  (ie, `end`, `prefinish`, `finish`) have been emitted.  Note that\n  listening on any end-ish event will immediateyl re-emit it if it has\n  already been emitted.\n* `writable` Whether the stream is writable.  Default `true`.  Set to\n  `false` when `end()`\n* `readable` Whether the stream is readable.  Default `true`.\n* `buffer` A [yallist](http://npm.im/yallist) linked list of chunks written\n  to the stream that have not yet been emitted.  (It's probably a bad idea\n  to mess with this.)\n* `pipes` A [yallist](http://npm.im/yallist) linked list of streams that\n  this stream is piping into.  (It's probably a bad idea to mess with\n  this.)\n* `destroyed` A getter that indicates whether the stream was destroyed.\n* `paused` True if the stream has been explicitly paused, otherwise false.\n* `objectMode` Indicates whether the stream is in `objectMode`.  Once set\n  to `true`, it cannot be set to `false`.\n\n### Events\n\n* `data` Emitted when there's data to read.  Argument is the data to read.\n  This is never emitted while not flowing.  If a listener is attached, that\n  will resume the stream.\n* `end` Emitted when there's no more data to read.  This will be emitted\n  immediately for empty streams when `end()` is called.  If a listener is\n  attached, and `end` was already emitted, then it will be emitted again.\n  All listeners are removed when `end` is emitted.\n* `prefinish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'end'`.\n* `finish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'prefinish'`.\n* `close` An indication that an underlying resource has been released.\n  Minipass does not emit this event, but will defer it until after `end`\n  has been emitted, since it throws off some stream libraries otherwise.\n* `drain` Emitted when the internal buffer empties, and it is again\n  suitable to `write()` into the stream.\n* `readable` Emitted when data is buffered and ready to be read by a\n  consumer.\n* `resume` Emitted when stream changes state from buffering to flowing\n  mode.  (Ie, when `resume` is called, `pipe` is called, or a `data` event\n  listener is added.)\n\n### Static Methods\n\n* `Minipass.isStream(stream)` Returns `true` if the argument is a stream,\n  and false otherwise.  To be considered a stream, the object must be\n  either an instance of Minipass, or an EventEmitter that has either a\n  `pipe()` method, or both `write()` and `end()` methods.  (Pretty much any\n  stream in node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass streams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(() => {\n  // stream is finished\n}, er => {\n  // stream emitted an error\n})\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one chunk for\nyou, but if you're going to do it yourself anyway, it's convenient this\nway:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in platforms\nthat support it.\n\nSynchronous iteration will end when the currently available data is\nconsumed, even if the `end` event has not been reached.  In string and\nbuffer mode, the data is concatenated, so unless multiple writes are\noccurring in the same tick as the `read()`, sync iteration loops will\ngenerally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written, with no\nflattening, create the stream with the `{ objectMode: true }` option.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i --> 0)\n    mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume () {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write (chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end (chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(new (class extends Minipass {\n    emit (ev, ...data) {\n      // let's also log events, because debugging some weird thing\n      console.log('EMIT', ev)\n      return super.emit(ev, ...data)\n    }\n    write (chunk, encoding, callback) {\n      console.log('WRITE', chunk, encoding)\n      return super.write(chunk, encoding, callback)\n    }\n    end (chunk, encoding, callback) {\n      console.log('END', chunk, encoding)\n      return super.end(chunk, encoding, callback)\n    }\n  }))\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit (ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write (obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end (obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor (options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write (chunk, encoding, cb) {\n    if (typeof chunk === 'string' &&\n        typeof encoding === 'string' &&\n        encoding !== 'utf8') {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk))\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      let parsed\n      try {\n        super.write(parsed)\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb)\n      cb()\n  }\n}\n```\n", "readmeFilename": "README.md", "gitHead": "4a5f1c26a1881fb3370d9c28ea9bdefc6a3eb402", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.0", "_nodeVersion": "12.12.0", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-wVCobyF3/vj8KTVCp7+XKmorSiBCKvIKJDsB3VjC1m/pUCxhvInRUpnqLcaETKlqZig0KNP6EYjqBZxC42GUBg==", "shasum": "c08b140a0d5e8b6c6034056f7a168a3f5441df3b", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.1.0.tgz", "fileCount": 4, "unpackedSize": 36495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdq+hJCRA9TVsSAnZWagAA7pgP/1R2buJ4pEFFiXgvlN4o\ntEOfwEOX64oLtqfB+AfcBCmXw0RDV1TIvJPVDdEA9qDmu+r39cI7BQh0SP6R\nYyncpsYmuKcvNArPS24HQfK312BjFZNuiQcnl0ES8of0GkUr3CYaGDSYm1NM\nRzFnWOybO4pH6tvAeJtZsKDDz4IDRKTWPkixUShosLtArBlH2fzn0lqZN4O8\ntP42nEkxOPRPiKJ2TxQbhD1i0m/aa5GiruJUYmmt67h/1pcdxLEMMpKoOjTS\nM1B59Crn1d4pRAU/l/PJw5Z+3g0ETLj6Juq/sdy16n+ebS5ojkkfbJ3N8Ovp\nI91baqvSFXi+NVzKt19zjvIldyfkTV68sHvqt2JjXG9+0sEtJFdheYJ/ecy9\ny5PbApSJqaooP0nbbz/mGmj1m+hTqtlvvcZgJ5pZ7VjCK2Zz7R+FaaEjH9Rg\nMg/YsM2XIbIwAAUwaHPYB1FWw2oDpxGm+T22i9IkUvNZAMfdjnl7AELJ475r\nh7x3NJhjR0N2xV6kHRTnwOth92XxdrTm+FfpHorBYo/VgwkSu7jSFMfGsZYG\n2TR5/sxDldQsIXRXyY71BLiYIQ3/mgPX4GwTR1ThGHzn9WXLlHOMjhZPBfxj\npnsuaEqRkVdkvirF/KBDXadudwM9ZOBOBdDl0/etSP1OAoSG+AofFImabaXn\ndNva\r\n=9tmT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKFokfDGcDVPqAEi5j87NiTw9HSdTQj/IR+RGa2vIuvwIgEdDW7GgKL8NukoNRawZdLMFDckWnQsgzPwxHJv8Hnw0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.0_1571547208516_0.163407317672416"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "minipass", "version": "3.1.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "gitHead": "0e866597838dadaf8dcfd7872ab8951f3c8ba3c0", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.1", "_nodeVersion": "12.12.0", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-UFqVihv6PQgwj8/yTGvl9kPz7xIAY+R5z6XYjRInD3Gk3qx6QGSD6zEcpeG4Dy/lQnv1J6zv8ejV90hyYIKf3w==", "shasum": "7607ce778472a185ad6d89082aa2070f79cedcd5", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.1.1.tgz", "fileCount": 4, "unpackedSize": 36604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdshkBCRA9TVsSAnZWagAAjPAP/3Wz9O7m0W8llOhGtJJQ\neZxGilj8lpC1M83kZs6dF34zyPFHdJLz58wZfqIBlLXR95vPr2s1qqvxjN4J\neaDQfM27/0COQ3n9DndeqHeo+hVbVcXDphSLrW9cDvTCRmWKVvFHQQHWLBgx\nNpj6Ij5lrjssInmf/w2MVVfGiZa0orUIFUQq9P0CeyEkarOgPQQYr4eanCZJ\n9MwJocJtsnNMQ9EAaDVQxC2yfFtTNL0z5VEH1lgKEG6sNYpRo7pjpYm3nyKB\nFVpd6Lbg32jgE6OM9BUzhI05JDjxotf6tRKi1cfAjGVqrVD0W4We1BwDpLh/\n28RiIStpNmBiYLUWJ9hJlqWW4hycfTZp/qwTiGo6fm+SzJNUWpqoY2uFstGm\naHftvRd9AzgTHoEZ/2Mq05xlzL0EHvKinCHs6mgJW6FNSmwMJq3poranlODn\n2QXHWfwGcSaJzcl6oQckSKs2Lvmi/Ly/O+yHu38bAt3au2fPclyncmyLX+Pw\nM5EoBjNyFOLTOKEwg7Y53nuNdl41HRoRZiY1wFy9O5Ds0eO3I13DB8D4P+2N\nG13R90Eksm8q7RS/oI9aoItMXib7ER7RJjUHav1t66eVjQqr+OOuNyVsLOuj\nGa5+0gNNIjbfYSfopq0+DKzah05shD5rC2IbQA3hc/n1OP4AtbIu/IQh8t0f\nX1bm\r\n=clq7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJ6OJRX78pNGH4SKUjJadz02yUy6k15jOeX9uKmxzl3wIhANovR5VFguKCXyKZ8uNm9WHSy9QV0VE01Gw6tcszqIex"}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "billatnpm"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "mike<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.1_1571952897280_0.4106563063011017"}, "_hasShrinkwrap": false}, "3.1.2": {"name": "minipass", "version": "3.1.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1oObKSrVwLX_7Ut4Z6g3fZW-AX1j1-k6w-cDsrkaSbHM/edit#gid=0)\nfor objects, strings, and buffers.\n\nSupports pipe()ing (including multi-pipe() and backpressure transmission),\nbuffering data until either a `data` event handler or `pipe()` is added (so\nyou don't lose the first chunk), and most other cases where PassThrough is\na good idea.\n\nThere is a `read()` method, but it's much more efficient to consume data\nfrom this stream via `'data'` events or by calling `pipe()` into some other\nstream.  Calling `read()` requires the buffer to be flattened in some\ncases, which requires copying memory.\n\nThere is also no `unpipe()` method.  Once you start piping, there is no\nstopping it!\n\nIf you set `objectMode: true` in the options, then whatever is written will\nbe emitted.  Otherwise, it'll do a minimal amount of Buffer copying to\nensure proper Streams semantics when `read(n)` is called.\n\n`objectMode` can also be set by doing `stream.objectMode = true`, or by\nwriting any non-string/non-buffer data.  `objectMode` cannot be set to\nfalse once it is set.\n\nThis is not a `through` or `through2` stream.  It doesn't transform the\ndata, it just passes it right through.  If you want to transform the data,\nextend the class, and override the `write()` method.  Once you're done\ntransforming the data however you want, call `super.write()` with the\ntransform output.\n\nFor some examples of streams that extend Minipass in various ways, check\nout:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap)\n- [treport](http://npm.im/tap)\n- [minipass-fetch](http://npm.im/minipass-fetch)\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different from (and in\nsome ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with noode-core streams and\nintend to use Minipass streams in your programs.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.  Thus, data\nis emitted as soon as it is available, always.  It is buffered until read,\nbut no longer.  Another way to look at it is that Minipass streams are\nexactly as synchronous as the logic that writes into them.\n\nThis can be surprising if your code relies on `PassThrough.write()` always\nproviding data on the next tick rather than the current one, or being able\nto call `resume()` and not have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no way for\nMinipass to achieve the speeds it does, or support the synchronous use\ncases that it does.  Simply put, waiting takes time.\n\nThis non-deferring approach makes Minipass streams much easier to reason\nabout, especially in the context of Promises and other flow-control\nmechanisms.\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer, returning `true`\non all writes until the limit is hit, even if the data has nowhere to go.\nThen, they will not attempt to draw more data in until the buffer size dips\nbelow a minimum value.\n\nMinipass streams are much simpler.  The `write()` method will return `true`\nif the data has somewhere to go (which is to say, given the timing\nguarantees, that the data is already there by the time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and the data\nsits in a buffer, to be drained out immediately as soon as anyone consumes\nit.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written all the way\nthrough the pipeline, and `write()` always returns true/false based on\nwhether the data was fully flushed, backpressure is communicated\nimmediately to the upstream caller.  This minimizes buffering.\n\nConsider this case:\n\n```js\nconst {PassThrough} = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage, and\nmultiple event deferrals happened, for an unblocked pipeline where it was\nperfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead someone reading\nthe code to think an advisory maximum of 1KiB is being set for the\npipeline.  However, the actual advisory buffering level is the _sum_ of\n`highWaterMark` values, since each one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data written,\nor _ever_ buffer data that can be flushed all the way through.  Neither\nnode-core streams nor Minipass ever fail to buffer written data, but\nnode-core streams do a lot of unnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less stuff and\nwaits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing any data\ninto it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't want to\npotentially happen immediately (for example, closing file descriptors,\nmoving on to the next entry in an archive parse stream, etc.) then be sure\nto call `stream.pause()` on creation, and then `stream.resume()` once you\nare ready to respond to the `end` event.\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not yet have had\na chance to add a listener.  In order to avoid this hazard, Minipass\nstreams safely re-emit the `'end'` event if a new listener is added after\n`'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream has already\nemitted `end`, then it will call the handler right away.  (You can think of\nthis somewhat like attaching a new `.then(fn)` to a previously-resolved\nPromise.)\n\nTo prevent calling handlers multiple times who would not expect multiple\nends to occur, all listeners are removed from the `'end'` event whenever it\nis emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data through the\npipeline when a new pipe destination is added, this can have surprising\neffects, especially when a stream comes in from some other function and may\nor may not have data in its buffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nThe solution is to create a dedicated tee-stream junction that pipes to\nboth locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nstream.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners.  The first one\nadded will _immediately_ receive all of the data, leaving nothing for the\nsecond:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\n## USAGE\n\nIt's a stream!  Use it like a stream and it'll most likely do what you\nwant.\n\n```js\nconst Minipass = require('minipass')\nconst mp = new Minipass(options) // optional: { encoding, objectMode }\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n* `encoding` How would you like the data coming _out_ of the stream to be\n  encoded?  Accepts any values that can be passed to `Buffer.toString()`.\n* `objectMode` Emit data exactly as it comes in.  This will be flipped on\n  by default if you write() something other than a string or Buffer at any\n  point.  Setting `objectMode: true` will prevent setting any encoding\n  value.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and `Writable`\nstreams.\n\n### Methods\n\n* `write(chunk, [encoding], [callback])` - Put data in.  (Note that, in the\n  base Minipass class, the same data will come out.)  Returns `false` if\n  the stream will buffer the next write, or true if it's still in \"flowing\"\n  mode.\n* `end([chunk, [encoding]], [callback])` - Signal that you have no more\n  data to write.  This will queue an `end` event to be fired when all the\n  data has been consumed.\n* `setEncoding(encoding)` - Set the encoding for data coming of the stream.\n  This can only be done once.\n* `pause()` - No more data for a while, please.  This also prevents `end`\n  from being emitted for empty streams until the stream is resumed.\n* `resume()` - Resume the stream.  If there's data in the buffer, it is all\n  discarded.  Any buffered events are immediately emitted.\n* `pipe(dest)` - Send all output to the stream provided.  There is no way\n  to unpipe.  When data is emitted, it is immediately written to any and\n  all pipe destinations.\n* `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are EventEmitters.  Some\n  events are given special treatment, however.  (See below under \"events\".)\n* `promise()` - Returns a Promise that resolves when the stream emits\n  `end`, or rejects if the stream emits `error`.\n* `collect()` - Return a Promise that resolves on `end` with an array\n  containing each chunk of data that was emitted, or rejects if the stream\n  emits `error`.  Note that this consumes the stream data.\n* `concat()` - Same as `collect()`, but concatenates the data into a single\n  Buffer object.  Will reject the returned promise if the stream is in\n  objectMode, or if it goes into objectMode by the end of the data.\n* `read(n)` - Consume `n` bytes of data out of the buffer.  If `n` is not\n  provided, then consume all of it.  If `n` bytes are not available, then\n  it returns null.  **Note** consuming streams in this way is less\n  efficient, and can lead to unnecessary Buffer copying.\n* `destroy([er])` - Destroy the stream.  If an error is provided, then an\n  `'error'` event is emitted.  If the stream has a `close()` method, and\n  has not emitted a `'close'` event yet, then `stream.close()` will be\n  called.  Any Promises returned by `.promise()`, `.collect()` or\n  `.concat()` will be rejected.  After being destroyed, writing to the\n  stream will emit an error.  No more data will be emitted if the stream is\n  destroyed, even if it was previously buffered.\n\n### Properties\n\n* `bufferLength` Read-only.  Total number of bytes buffered, or in the case\n  of objectMode, the total number of objects.\n* `encoding` The encoding that has been set.  (Setting this is equivalent\n  to calling `setEncoding(enc)` and has the same prohibition against\n  setting multiple times.)\n* `flowing` Read-only.  Boolean indicating whether a chunk written to the\n  stream will be immediately emitted.\n* `emittedEnd` Read-only.  Boolean indicating whether the end-ish events\n  (ie, `end`, `prefinish`, `finish`) have been emitted.  Note that\n  listening on any end-ish event will immediateyl re-emit it if it has\n  already been emitted.\n* `writable` Whether the stream is writable.  Default `true`.  Set to\n  `false` when `end()`\n* `readable` Whether the stream is readable.  Default `true`.\n* `buffer` A [yallist](http://npm.im/yallist) linked list of chunks written\n  to the stream that have not yet been emitted.  (It's probably a bad idea\n  to mess with this.)\n* `pipes` A [yallist](http://npm.im/yallist) linked list of streams that\n  this stream is piping into.  (It's probably a bad idea to mess with\n  this.)\n* `destroyed` A getter that indicates whether the stream was destroyed.\n* `paused` True if the stream has been explicitly paused, otherwise false.\n* `objectMode` Indicates whether the stream is in `objectMode`.  Once set\n  to `true`, it cannot be set to `false`.\n\n### Events\n\n* `data` Emitted when there's data to read.  Argument is the data to read.\n  This is never emitted while not flowing.  If a listener is attached, that\n  will resume the stream.\n* `end` Emitted when there's no more data to read.  This will be emitted\n  immediately for empty streams when `end()` is called.  If a listener is\n  attached, and `end` was already emitted, then it will be emitted again.\n  All listeners are removed when `end` is emitted.\n* `prefinish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'end'`.\n* `finish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'prefinish'`.\n* `close` An indication that an underlying resource has been released.\n  Minipass does not emit this event, but will defer it until after `end`\n  has been emitted, since it throws off some stream libraries otherwise.\n* `drain` Emitted when the internal buffer empties, and it is again\n  suitable to `write()` into the stream.\n* `readable` Emitted when data is buffered and ready to be read by a\n  consumer.\n* `resume` Emitted when stream changes state from buffering to flowing\n  mode.  (Ie, when `resume` is called, `pipe` is called, or a `data` event\n  listener is added.)\n\n### Static Methods\n\n* `Minipass.isStream(stream)` Returns `true` if the argument is a stream,\n  and false otherwise.  To be considered a stream, the object must be\n  either an instance of Minipass, or an EventEmitter that has either a\n  `pipe()` method, or both `write()` and `end()` methods.  (Pretty much any\n  stream in node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass streams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(() => {\n  // stream is finished\n}, er => {\n  // stream emitted an error\n})\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one chunk for\nyou, but if you're going to do it yourself anyway, it's convenient this\nway:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in platforms\nthat support it.\n\nSynchronous iteration will end when the currently available data is\nconsumed, even if the `end` event has not been reached.  In string and\nbuffer mode, the data is concatenated, so unless multiple writes are\noccurring in the same tick as the `read()`, sync iteration loops will\ngenerally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written, with no\nflattening, create the stream with the `{ objectMode: true }` option.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i --> 0)\n    mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume () {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write (chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end (chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(new (class extends Minipass {\n    emit (ev, ...data) {\n      // let's also log events, because debugging some weird thing\n      console.log('EMIT', ev)\n      return super.emit(ev, ...data)\n    }\n    write (chunk, encoding, callback) {\n      console.log('WRITE', chunk, encoding)\n      return super.write(chunk, encoding, callback)\n    }\n    end (chunk, encoding, callback) {\n      console.log('END', chunk, encoding)\n      return super.end(chunk, encoding, callback)\n    }\n  }))\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit (ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write (obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end (obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor (options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write (chunk, encoding, cb) {\n    if (typeof chunk === 'string' &&\n        typeof encoding === 'string' &&\n        encoding !== 'utf8') {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk))\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      let parsed\n      try {\n        super.write(parsed)\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb)\n      cb()\n  }\n}\n```\n", "readmeFilename": "README.md", "gitHead": "41ec3d09cb8034cbfc471b802269dc4a684aa0c3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.2", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-1UelkoRxUOd3d3VOKu2YIgwqhnLaBRpPyqiCpLFOesz5gqEMS8ryTnrzbge1J6C4LBKecr9eKb1FD6INXvWssw==", "shasum": "74bce7f1b9624236dc8d374a49910181f9eb3600", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.1.2.tgz", "fileCount": 4, "unpackedSize": 36600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetxmhCRA9TVsSAnZWagAAnZgP/1XIYXPB0vIOoTHrFxjs\nbO5baaGi0cAShCimJMNMlvgQbqOmnvJEFp1o9Xd1RagiQJc0abE8eS9aNzxH\nk+p2A+SG86qxKBcmLNWfFoSPgs9Uezs/WE8aGUP736CAOOaoKcmLwJ8D1CxL\nCfSFcMNSomv48r3zd5ZXMMJGtzfdyrxSHqjkD69YOA5pBu8D5ViJpudX8P9e\nHrmR73O1w8jnU21JrplPRZhCl8REiuL+2nT5JKvOkxlk3FEbItf/cp2BkYYR\nIA9CIWF3Z6NW7QhYXVTyDVENuQw1eBCFM+4uhbKluhCs7gcaUhcNCpkHR18s\niTJ29EvW8nla3G4JU1UzlVWLkrWDmPv8nJcdEBh5tF2U5FBbzD4iO0pPWYTV\nqsXJTh4ppqOWnCJPHzR8WiSldDJ/ZbhRRRwsDAmeUf0nC07U13J8nnq/fS+U\n+Q5ZoXg00sVS0tVoMcmrxF1fQJRI2WLcNI2EYsqYg5g5xUBMCC6JAf/S60hy\niC6eNQD9CvrE1tdr617A/QB1k82EgbIF7lCfeV2ivX8h7sZXL+m6uiSh3rXd\n1vOHo91vnEFp5y+h4ecrw3XuLdsvgg3ppx4HOQh6dHu4O/LIKlDbG/sVFBiA\nMCTOvqxfQ+sWtTjgx03sEmUNMIS5Cu0ieCl1HemF6wiXTvyva5uBDsa9CJFr\nhhhs\r\n=n9cr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1122tPKMKcUKdwHXxTFR5bMaqVGogG6tg+g1y7fuzqAIgKLVfYOaMv2e8zlxa/tUnEAnjjTcdgqpsYUyx74dypug="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.2_1589057952720_0.49020973461025363"}, "_hasShrinkwrap": false}, "3.1.3": {"name": "minipass", "version": "3.1.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "gitHead": "a67764b3ea5c8c2679167390897da2b46d0b8022", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.3", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-Mgd2GdMVzY+x3IJ+oHnVM+KG3lA5c8tnabyJKmHSaG2kAGpudxuOf8ToDkhumF7UzME7DecbQE9uOZhNm7PuJg==", "shasum": "7d42ff1f39635482e15f9cdb53184deebd5815fd", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.1.3.tgz", "fileCount": 4, "unpackedSize": 37205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeu0a1CRA9TVsSAnZWagAAWDMP/iL2c6c5cvPatpHN6Swy\n006xNCX098OjCf5A3MPKkArIyiNBkOtHDQBl4TV88u8cyWD7Cr1YB/q4WIA8\nD/WhOvUEFAiSu8FDb+/OG1wejKQX5FeVIzMLzMxOKzEFc80sxRV4vHLEmnuN\nSd4toARvnvEbhr9ChwGCYWv25t1/dx+y1pybTywXp21Wk8wtP8n3vQ2DROJ/\nbPQHILAd9IBEzInCTcmOfvsNVQz9ysOq7QF4FwOMaofl2pdbQsPdafBMleGs\nQoljq7tW1Cl/ZdBCMHCnEbQWypVe9BhvxytRz1XsY8KzenZMkIj6nldfolBl\nMyIQ+dZGuXIadWGAfPEg1fGc3PAstDsGE2p6AfeIgcL+3pFpf3rBtUS96YmV\nZ4wR4X4nKg+LO/0ptCiq7a9eGwhhu81SK4BazjnkpkO6lH9VCHjeGt458y+W\nRS5dvprQ7jpgqyuQrK+oJe1JGAEdOnAz/G2CGzp7rVQjdrCkj9+g9xJmGZ8b\nNWX/D0P+xdbjl8qXZ+sPTUHYziVWAhpXKYB5eHATgncCXiUbuVhcySJMPu+J\n3894RgQ4RAV9FK1dhjiU74aaiu07Y+AXPo5Uos9ACt88Aq//vy4FKphyay35\nlZ/qQql4iZf9ZMdcb/4fv/zVOhM8dvmNFNbVoO/8sXmQezua770xXolWr2xI\njDd0\r\n=ajxW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9lrsA7n0Y2frD2nTYdCxWXeg78MyLbYEumboSgAYFyAIgdJcRx6J/KMEZLkUCyHSJbU3KKM32XJWPMv/i+mxToD4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "adam_baldwin"}, {"email": "<EMAIL>", "name": "clau<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "isaacs"}, {"email": "<EMAIL>", "name": "ruyadorno"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.3_1589331636888_0.7787979174732615"}, "_hasShrinkwrap": false}, "3.1.4": {"name": "minipass", "version": "3.1.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^15.0.9", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1oObKSrVwLX_7Ut4Z6g3fZW-AX1j1-k6w-cDsrkaSbHM/edit#gid=0)\nfor objects, strings, and buffers.\n\nSupports `pipe()`ing (including multi-`pipe()` and backpressure transmission),\nbuffering data until either a `data` event handler or `pipe()` is added (so\nyou don't lose the first chunk), and most other cases where PassThrough is\na good idea.\n\nThere is a `read()` method, but it's much more efficient to consume data\nfrom this stream via `'data'` events or by calling `pipe()` into some other\nstream.  Calling `read()` requires the buffer to be flattened in some\ncases, which requires copying memory.\n\nThere is also no `unpipe()` method.  Once you start piping, there is no\nstopping it!\n\nIf you set `objectMode: true` in the options, then whatever is written will\nbe emitted.  Otherwise, it'll do a minimal amount of Buffer copying to\nensure proper Streams semantics when `read(n)` is called.\n\n`objectMode` can also be set by doing `stream.objectMode = true`, or by\nwriting any non-string/non-buffer data.  `objectMode` cannot be set to\nfalse once it is set.\n\nThis is not a `through` or `through2` stream.  It doesn't transform the\ndata, it just passes it right through.  If you want to transform the data,\nextend the class, and override the `write()` method.  Once you're done\ntransforming the data however you want, call `super.write()` with the\ntransform output.\n\nFor some examples of streams that extend Minipass in various ways, check\nout:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap-parser)\n- [treport](http://npm.im/treport)\n- [minipass-fetch](http://npm.im/minipass-fetch)\n- [pacote](http://npm.im/pacote)\n- [make-fetch-happen](http://npm.im/make-fetch-happen)\n- [cacache](http://npm.im/cacache)\n- [ssri](http://npm.im/ssri)\n- [npm-registry-fetch](http://npm.im/npm-registry-fetch)\n- [minipass-json-stream](http://npm.im/minipass-json-stream)\n- [minipass-sized](http://npm.im/minipass-sized)\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different from (and in\nsome ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with node-core streams and\nintend to use Minipass streams in your programs.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.  Thus, data\nis emitted as soon as it is available, always.  It is buffered until read,\nbut no longer.  Another way to look at it is that Minipass streams are\nexactly as synchronous as the logic that writes into them.\n\nThis can be surprising if your code relies on `PassThrough.write()` always\nproviding data on the next tick rather than the current one, or being able\nto call `resume()` and not have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no way for\nMinipass to achieve the speeds it does, or support the synchronous use\ncases that it does.  Simply put, waiting takes time.\n\nThis non-deferring approach makes Minipass streams much easier to reason\nabout, especially in the context of Promises and other flow-control\nmechanisms.\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer, returning `true`\non all writes until the limit is hit, even if the data has nowhere to go.\nThen, they will not attempt to draw more data in until the buffer size dips\nbelow a minimum value.\n\nMinipass streams are much simpler.  The `write()` method will return `true`\nif the data has somewhere to go (which is to say, given the timing\nguarantees, that the data is already there by the time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and the data\nsits in a buffer, to be drained out immediately as soon as anyone consumes\nit.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written all the way\nthrough the pipeline, and `write()` always returns true/false based on\nwhether the data was fully flushed, backpressure is communicated\nimmediately to the upstream caller.  This minimizes buffering.\n\nConsider this case:\n\n```js\nconst {PassThrough} = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage, and\nmultiple event deferrals happened, for an unblocked pipeline where it was\nperfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead someone reading\nthe code to think an advisory maximum of 1KiB is being set for the\npipeline.  However, the actual advisory buffering level is the _sum_ of\n`highWaterMark` values, since each one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data written,\nor _ever_ buffer data that can be flushed all the way through.  Neither\nnode-core streams nor Minipass ever fail to buffer written data, but\nnode-core streams do a lot of unnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less stuff and\nwaits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing any data\ninto it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't want to\npotentially happen immediately (for example, closing file descriptors,\nmoving on to the next entry in an archive parse stream, etc.) then be sure\nto call `stream.pause()` on creation, and then `stream.resume()` once you\nare ready to respond to the `end` event.\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not yet have had\na chance to add a listener.  In order to avoid this hazard, Minipass\nstreams safely re-emit the `'end'` event if a new listener is added after\n`'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream has already\nemitted `end`, then it will call the handler right away.  (You can think of\nthis somewhat like attaching a new `.then(fn)` to a previously-resolved\nPromise.)\n\nTo prevent calling handlers multiple times who would not expect multiple\nends to occur, all listeners are removed from the `'end'` event whenever it\nis emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data through the\npipeline when a new pipe destination is added, this can have surprising\neffects, especially when a stream comes in from some other function and may\nor may not have data in its buffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nThe solution is to create a dedicated tee-stream junction that pipes to\nboth locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nsrc.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners.  The first one\nadded will _immediately_ receive all of the data, leaving nothing for the\nsecond:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\n## USAGE\n\nIt's a stream!  Use it like a stream and it'll most likely do what you\nwant.\n\n```js\nconst Minipass = require('minipass')\nconst mp = new Minipass(options) // optional: { encoding, objectMode }\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n* `encoding` How would you like the data coming _out_ of the stream to be\n  encoded?  Accepts any values that can be passed to `Buffer.toString()`.\n* `objectMode` Emit data exactly as it comes in.  This will be flipped on\n  by default if you write() something other than a string or Buffer at any\n  point.  Setting `objectMode: true` will prevent setting any encoding\n  value.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and `Writable`\nstreams.\n\n### Methods\n\n* `write(chunk, [encoding], [callback])` - Put data in.  (Note that, in the\n  base Minipass class, the same data will come out.)  Returns `false` if\n  the stream will buffer the next write, or true if it's still in \"flowing\"\n  mode.\n* `end([chunk, [encoding]], [callback])` - Signal that you have no more\n  data to write.  This will queue an `end` event to be fired when all the\n  data has been consumed.\n* `setEncoding(encoding)` - Set the encoding for data coming of the stream.\n  This can only be done once.\n* `pause()` - No more data for a while, please.  This also prevents `end`\n  from being emitted for empty streams until the stream is resumed.\n* `resume()` - Resume the stream.  If there's data in the buffer, it is all\n  discarded.  Any buffered events are immediately emitted.\n* `pipe(dest)` - Send all output to the stream provided.  There is no way\n  to unpipe.  When data is emitted, it is immediately written to any and\n  all pipe destinations.\n* `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are EventEmitters.  Some\n  events are given special treatment, however.  (See below under \"events\".)\n* `promise()` - Returns a Promise that resolves when the stream emits\n  `end`, or rejects if the stream emits `error`.\n* `collect()` - Return a Promise that resolves on `end` with an array\n  containing each chunk of data that was emitted, or rejects if the stream\n  emits `error`.  Note that this consumes the stream data.\n* `concat()` - Same as `collect()`, but concatenates the data into a single\n  Buffer object.  Will reject the returned promise if the stream is in\n  objectMode, or if it goes into objectMode by the end of the data.\n* `read(n)` - Consume `n` bytes of data out of the buffer.  If `n` is not\n  provided, then consume all of it.  If `n` bytes are not available, then\n  it returns null.  **Note** consuming streams in this way is less\n  efficient, and can lead to unnecessary Buffer copying.\n* `destroy([er])` - Destroy the stream.  If an error is provided, then an\n  `'error'` event is emitted.  If the stream has a `close()` method, and\n  has not emitted a `'close'` event yet, then `stream.close()` will be\n  called.  Any Promises returned by `.promise()`, `.collect()` or\n  `.concat()` will be rejected.  After being destroyed, writing to the\n  stream will emit an error.  No more data will be emitted if the stream is\n  destroyed, even if it was previously buffered.\n\n### Properties\n\n* `bufferLength` Read-only.  Total number of bytes buffered, or in the case\n  of objectMode, the total number of objects.\n* `encoding` The encoding that has been set.  (Setting this is equivalent\n  to calling `setEncoding(enc)` and has the same prohibition against\n  setting multiple times.)\n* `flowing` Read-only.  Boolean indicating whether a chunk written to the\n  stream will be immediately emitted.\n* `emittedEnd` Read-only.  Boolean indicating whether the end-ish events\n  (ie, `end`, `prefinish`, `finish`) have been emitted.  Note that\n  listening on any end-ish event will immediateyl re-emit it if it has\n  already been emitted.\n* `writable` Whether the stream is writable.  Default `true`.  Set to\n  `false` when `end()`\n* `readable` Whether the stream is readable.  Default `true`.\n* `buffer` A [yallist](http://npm.im/yallist) linked list of chunks written\n  to the stream that have not yet been emitted.  (It's probably a bad idea\n  to mess with this.)\n* `pipes` A [yallist](http://npm.im/yallist) linked list of streams that\n  this stream is piping into.  (It's probably a bad idea to mess with\n  this.)\n* `destroyed` A getter that indicates whether the stream was destroyed.\n* `paused` True if the stream has been explicitly paused, otherwise false.\n* `objectMode` Indicates whether the stream is in `objectMode`.  Once set\n  to `true`, it cannot be set to `false`.\n\n### Events\n\n* `data` Emitted when there's data to read.  Argument is the data to read.\n  This is never emitted while not flowing.  If a listener is attached, that\n  will resume the stream.\n* `end` Emitted when there's no more data to read.  This will be emitted\n  immediately for empty streams when `end()` is called.  If a listener is\n  attached, and `end` was already emitted, then it will be emitted again.\n  All listeners are removed when `end` is emitted.\n* `prefinish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'end'`.\n* `finish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'prefinish'`.\n* `close` An indication that an underlying resource has been released.\n  Minipass does not emit this event, but will defer it until after `end`\n  has been emitted, since it throws off some stream libraries otherwise.\n* `drain` Emitted when the internal buffer empties, and it is again\n  suitable to `write()` into the stream.\n* `readable` Emitted when data is buffered and ready to be read by a\n  consumer.\n* `resume` Emitted when stream changes state from buffering to flowing\n  mode.  (Ie, when `resume` is called, `pipe` is called, or a `data` event\n  listener is added.)\n\n### Static Methods\n\n* `Minipass.isStream(stream)` Returns `true` if the argument is a stream,\n  and false otherwise.  To be considered a stream, the object must be\n  either an instance of Minipass, or an EventEmitter that has either a\n  `pipe()` method, or both `write()` and `end()` methods.  (Pretty much any\n  stream in node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass streams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(() => {\n  // stream is finished\n}, er => {\n  // stream emitted an error\n})\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one chunk for\nyou, but if you're going to do it yourself anyway, it's convenient this\nway:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in platforms\nthat support it.\n\nSynchronous iteration will end when the currently available data is\nconsumed, even if the `end` event has not been reached.  In string and\nbuffer mode, the data is concatenated, so unless multiple writes are\noccurring in the same tick as the `read()`, sync iteration loops will\ngenerally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written, with no\nflattening, create the stream with the `{ objectMode: true }` option.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i-- > 0)\n    mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume () {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write (chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end (chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(new (class extends Minipass {\n    emit (ev, ...data) {\n      // let's also log events, because debugging some weird thing\n      console.log('EMIT', ev)\n      return super.emit(ev, ...data)\n    }\n    write (chunk, encoding, callback) {\n      console.log('WRITE', chunk, encoding)\n      return super.write(chunk, encoding, callback)\n    }\n    end (chunk, encoding, callback) {\n      console.log('END', chunk, encoding)\n      return super.end(chunk, encoding, callback)\n    }\n  }))\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit (ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write (obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end (obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor (options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write (chunk, encoding, cb) {\n    if (typeof chunk === 'string' &&\n        typeof encoding === 'string' &&\n        encoding !== 'utf8') {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk))\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      try {\n        // JSON.parse can throw, emit an error on that\n        super.write(JSON.parse(jsonData[i]))\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb)\n      cb()\n  }\n}\n```\n", "readmeFilename": "README.md", "gitHead": "bb90f266f391b77b07c0a241384cbe0f705eb38e", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.4", "_nodeVersion": "16.5.0", "_npmVersion": "7.23.0", "dist": {"integrity": "sha512-0EWfzWLOeFe013Jz0k5iaza6l7/hjSozD+sHgKY9LClEKZY+0jbIwd6CnkOcEFDxlZVwcG5oPUWDbzXzJ0k8nA==", "shasum": "bd5a9b097a326977e84a6ff1a824f322c516ab84", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.1.4.tgz", "fileCount": 4, "unpackedSize": 37368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQLQNCRA9TVsSAnZWagAAQ5gP/0qBxpryI5XVAZXnlfzJ\n9VIJaXw4paJBHrPIp49N5bIPk92o0/oD+JwkFxLowOu69uY3leRxN96w7PIY\necCmsnM2M1g50gpI/WCw5cwXUqZCYZ/ronksmT80n8vDZIRdRzbN+PwcQKb4\nyzwwWmGBSMmdG+WAvC/cjQAyTqhm44szwaUt1ECsJvlTWnNsWSqtz/11tbGF\n9k9O31tOKZkj2TpAPQES1V0qKAWpz3YR11BA04SC6BxU6uDBk8sTTbdJTfz1\nJSh4eV0ujaZzJMw6zDVaKatIi6UVZANjChKRK4egl9MytkrD1OvNRmP3xFqA\nXV/9OxLIm+dXZCV04HCoCZ60BDtWRympm8cNn9PFGA9XIi5Blc6CD+1e1HcD\nlSCcNV272+lD4FPaktiPFzkJWzV5ecD8WbQGYji/q6tFJE2H/l5titu3MfSf\nQHDyQDTeW8j19Sy4dAw7Cs9FA8elSUU3SP0R5B8aPKNyKKWHY861mBK5DziW\nMW+hmqM5LoD/XLwtJex5Fz2pfjwIZuu9wkeFfx6i8+5axRL3WmOQwGWBXRe0\nJ9yYEaurW/WzGo4K95bEDXJTSkkh08E1e93TRyxBpV7WsjWQxu1Ii6JX9lm3\nLZSjlPjzXUEEW0oOd273huoWcftVMiO0aG3wfadzF7bttOHPSnHgIQ6DY2d4\nu/qg\r\n=ou8V\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMs53Pd6DgIFg2lIch105OUlE9COYHf5NViDCIX+xB/gIgGOHjKaWrQvWaAbXIv+Wbklm/lRJjwXXzukzI2hiaPfM="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.4_1631630349451_0.8024518838544734"}, "_hasShrinkwrap": false}, "3.1.5": {"name": "minipass", "version": "3.1.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^15.0.9", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "gitHead": "9bfcf550f7f71667294c0f3a75458347020754ff", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.5", "_nodeVersion": "16.5.0", "_npmVersion": "7.23.0", "dist": {"integrity": "sha512-+8NzxD82XQoNKNrl1d/FSi+X8wAEWR+sbYAfIvub4Nz0d22plFG72CEVVaufV8PNf4qSslFTD8VMOxNVhHCjTw==", "shasum": "71f6251b0a33a49c01b3cf97ff77eda030dff732", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.1.5.tgz", "fileCount": 4, "unpackedSize": 37611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQPnHCRA9TVsSAnZWagAA7F0QAIBnLEXovWf6XW686YoH\nkuG3fK7Ysw29Qehkp4uLRvQNdgnNQzDFWJslTi/Bz5xTfYqwuzsUGVnjBzpY\nylCDzTkeLBUo1nMYaREXTKKDFGgi+EaOBlyGZVoJt2OzMzih/NBGceVcwcSp\nYfLlrf2CSHv863XGdATyuxeGa2xJAj4dsJGS3JeDn2HYJjsg9NRyrH+pL2BU\n8x8+Xen4GvWl8ZaYxKEQhjCLTk+jRG0sj3XXJ83Pf9abGS+ig/m4+mq36KqX\n6XPpftpJAW5kEbtc8EvXrMEPvW2aauoLwt3qehHxA7EST0aCehaujPtioNOb\nGw7tAZvxukhWnJWQX7R3e5/HW4YZBejHqKg08ZM99jhU0H0qjgYkhmHbh68P\n7gdDuVW9BJJTPec/+ZroU79P4QDlbCi0fQb0Y7GH4Wq3GOWATqLU8TApzwcd\nMQz/JNCoKkqrr/sODAYrpzIMEBtwKBuksKCpRl8w/Lar9LKvu/9MthC0TRzu\nth1jZsWU+Tw46tCa7jUhpgEM9eZtmk5lEqhyzntufCQ50bNmA5kvnBGJUfhR\nhfWM7GA5LuvpoPN5GiocY6Ie1xBFYOY56eevdIhs7d3eyZUbNC3Mgb2C0Q4e\n0XAHx3d2xuEnaVTlNj1N++AUoLf813DeAureILSdeKO3KS4q6QQ3tyN89yxW\ntlmJ\r\n=bEfz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCI7RD+JTOhdq9qRicqSUHR2sGucLGV/zpIiZkWeuGuuQIgOEnvbLjnwNzF/qnftcfVyw69RB9eKscB+r3zlXSjDmU="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.5_1631648199111_0.4963903964208507"}, "_hasShrinkwrap": false}, "3.1.6": {"name": "minipass", "version": "3.1.6", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^15.0.9", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "gitHead": "f55015f024cfaf1a27b595ddcedebd99c38dc189", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.1.6", "_nodeVersion": "16.5.0", "_npmVersion": "8.1.3", "dist": {"integrity": "sha512-rty5kpw9/z8SX9dmxblFA6edItUmwJgMeYDZRrwlIVN27i8gysGbznJwUggw2V/FVqFSDdWy040ZPS811DYAqQ==", "shasum": "3b8150aa688a711a1521af5e8779c1d3bb4f45ee", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.1.6.tgz", "fileCount": 4, "unpackedSize": 37791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrmhZCRA9TVsSAnZWagAA6B8P/AyQZ7FDRbeYF8pdHbha\nn7//BY0OyQ+yq0vgh6kEoMqnUIfSrANz5kwZehLKjARw2vcUG+e/2s5P4ATN\nLd5SWpG/L71IfPe0n/guuSKJW1IwH7nYj0FjMBkWSOnUnw3SE8rOhg8K1Smt\n4TdO9Lpp7QLkkBmrU35MVXg55YN4n57BeV5wmK5kGkeybYV1bzs2xR5OinYa\nvxi4ySKFFFhoX38SwtN8XTms/Wuw1D4y3ejBlSGoqhlHHHbp+iJDKx0xDe2b\n6IwAu1yzj0kiJLJ8bVM/Bz84y+JzKVN+GIatDXdA3kYs6BBGuVkqNDh/ckRG\nYHO3jFg0Eo7AnJrzAdCUn+pcj0ISHKuoDwuTsMnD05JMVFXmD5UWUwkYPxqf\nfWqn2q48+8XkHMCguWf2F1d5Ri+lr5ck/o43KRyeXRIj5ClTpGuGOpEYO8cw\nOHLVDxEdefVlUVBcE88ztw6kt9pNZ2Tmfk0/qyfW2+CQlpTaxv+nKgaj5CoC\nOk0wEvWb4uQn3r7t3IrTCd7AD7guwRAe6Xnn2ccSkmhPjaa59x9yRnrxsGer\nxolHdV48bxcdKZ4DTGZ7sipRM/or7gNY/8ELrfdkI1vqnGMDTa6KlFnn/rfJ\ncpEl9NPUPsEdDfiXXYRukE9s5G/pCC13WaKabhaTKDJxmc2r5f47qTmHuX1v\n0DXq\r\n=HYuS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVWsofytktuaLkTif/sI++K0tdythssKE16uF+7PCeFgIhAPrItyN8AwMvuRJhAi/PYiRGHKPMtE3fqQU5DVGKBTJ5"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.1.6_1638819929280_0.34760831556769944"}, "_hasShrinkwrap": false}, "3.2.0": {"name": "minipass", "version": "3.2.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^16.2.0", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1oObKSrVwLX_7Ut4Z6g3fZW-AX1j1-k6w-cDsrkaSbHM/edit#gid=0)\nfor objects, strings, and buffers.\n\nSupports `pipe()`ing (including multi-`pipe()` and backpressure transmission),\nbuffering data until either a `data` event handler or `pipe()` is added (so\nyou don't lose the first chunk), and most other cases where PassThrough is\na good idea.\n\nThere is a `read()` method, but it's much more efficient to consume data\nfrom this stream via `'data'` events or by calling `pipe()` into some other\nstream.  Calling `read()` requires the buffer to be flattened in some\ncases, which requires copying memory.\n\nThere is also no `unpipe()` method.  Once you start piping, there is no\nstopping it!\n\nIf you set `objectMode: true` in the options, then whatever is written will\nbe emitted.  Otherwise, it'll do a minimal amount of Buffer copying to\nensure proper Streams semantics when `read(n)` is called.\n\n`objectMode` can also be set by doing `stream.objectMode = true`, or by\nwriting any non-string/non-buffer data.  `objectMode` cannot be set to\nfalse once it is set.\n\nThis is not a `through` or `through2` stream.  It doesn't transform the\ndata, it just passes it right through.  If you want to transform the data,\nextend the class, and override the `write()` method.  Once you're done\ntransforming the data however you want, call `super.write()` with the\ntransform output.\n\nFor some examples of streams that extend Minipass in various ways, check\nout:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap-parser)\n- [treport](http://npm.im/treport)\n- [minipass-fetch](http://npm.im/minipass-fetch)\n- [pacote](http://npm.im/pacote)\n- [make-fetch-happen](http://npm.im/make-fetch-happen)\n- [cacache](http://npm.im/cacache)\n- [ssri](http://npm.im/ssri)\n- [npm-registry-fetch](http://npm.im/npm-registry-fetch)\n- [minipass-json-stream](http://npm.im/minipass-json-stream)\n- [minipass-sized](http://npm.im/minipass-sized)\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different from (and in\nsome ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with node-core streams and\nintend to use Minipass streams in your programs.\n\nYou can avoid most of these differences entirely (for a very\nsmall performance penalty) by setting `{async: true}` in the\nconstructor options.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.  Thus, data\nis emitted as soon as it is available, always.  It is buffered until read,\nbut no longer.  Another way to look at it is that Minipass streams are\nexactly as synchronous as the logic that writes into them.\n\nThis can be surprising if your code relies on `PassThrough.write()` always\nproviding data on the next tick rather than the current one, or being able\nto call `resume()` and not have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no way for\nMinipass to achieve the speeds it does, or support the synchronous use\ncases that it does.  Simply put, waiting takes time.\n\nThis non-deferring approach makes Minipass streams much easier to reason\nabout, especially in the context of Promises and other flow-control\nmechanisms.\n\nExample:\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ async: true })\nstream.on('data', () => console.log('data event'))\nconsole.log('before write')\nstream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// data event\n// after write\n```\n\n### Exception: Async Opt-In\n\nIf you wish to have a Minipass stream with behavior that more\nclosely mimics Node.js core streams, you can set the stream in\nasync mode either by setting `async: true` in the constructor\noptions, or by setting `stream.async = true` later on.\n\n```js\nconst Minipass = require('minipass')\nconst asyncStream = new Minipass({ async: true })\nasyncStream.on('data', () => console.log('data event'))\nconsole.log('before write')\nasyncStream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// after write\n// data event <-- this is deferred until the next tick\n```\n\nSwitching _out_ of async mode is unsafe, as it could cause data\ncorruption, and so is not enabled.  Example:\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nsetStreamSyncAgainSomehow(stream) // <-- this doesn't actually exist!\nstream.write('world')\nconsole.log('after writes')\n// hypothetical output would be:\n// before writes\n// world\n// after writes\n// hello\n// NOT GOOD!\n```\n\nTo avoid this problem, once set into async mode, any attempt to\nmake the stream sync again will be ignored.\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nstream.async = false // <-- no-op, stream already async\nstream.write('world')\nconsole.log('after writes')\n// actual output:\n// before writes\n// after writes\n// hello\n// world\n```\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer, returning `true`\non all writes until the limit is hit, even if the data has nowhere to go.\nThen, they will not attempt to draw more data in until the buffer size dips\nbelow a minimum value.\n\nMinipass streams are much simpler.  The `write()` method will return `true`\nif the data has somewhere to go (which is to say, given the timing\nguarantees, that the data is already there by the time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and the data\nsits in a buffer, to be drained out immediately as soon as anyone consumes\nit.\n\nSince nothing is ever buffered unnecessarily, there is much less\ncopying data, and less bookkeeping about buffer capacity levels.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written all the way\nthrough the pipeline, and `write()` always returns true/false based on\nwhether the data was fully flushed, backpressure is communicated\nimmediately to the upstream caller.  This minimizes buffering.\n\nConsider this case:\n\n```js\nconst {PassThrough} = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage, and\nmultiple event deferrals happened, for an unblocked pipeline where it was\nperfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead someone reading\nthe code to think an advisory maximum of 1KiB is being set for the\npipeline.  However, the actual advisory buffering level is the _sum_ of\n`highWaterMark` values, since each one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data written,\nor _ever_ buffer data that can be flushed all the way through.  Neither\nnode-core streams nor Minipass ever fail to buffer written data, but\nnode-core streams do a lot of unnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less stuff and\nwaits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing any data\ninto it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't want to\npotentially happen immediately (for example, closing file descriptors,\nmoving on to the next entry in an archive parse stream, etc.) then be sure\nto call `stream.pause()` on creation, and then `stream.resume()` once you\nare ready to respond to the `end` event.\n\nHowever, this is _usually_ not a problem because:\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not yet have had\na chance to add a listener.  In order to avoid this hazard, Minipass\nstreams safely re-emit the `'end'` event if a new listener is added after\n`'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream has already\nemitted `end`, then it will call the handler right away.  (You can think of\nthis somewhat like attaching a new `.then(fn)` to a previously-resolved\nPromise.)\n\nTo prevent calling handlers multiple times who would not expect multiple\nends to occur, all listeners are removed from the `'end'` event whenever it\nis emitted.\n\n### Emit `error` When Asked\n\nThe most recent error object passed to the `'error'` event is\nstored on the stream.  If a new `'error'` event handler is added,\nand an error was previously emitted, then the event handler will\nbe called immediately (or on `process.nextTick` in the case of\nasync streams).\n\nThis makes it much more difficult to end up trying to interact\nwith a broken stream, if the error handler is added after an\nerror was previously emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data through the\npipeline when a new pipe destination is added, this can have surprising\neffects, especially when a stream comes in from some other function and may\nor may not have data in its buffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nOne solution is to create a dedicated tee-stream junction that pipes to\nboth locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nsrc.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners.  The first one\nadded will _immediately_ receive all of the data, leaving nothing for the\nsecond:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\nAll of the hazards in this section are avoided by setting `{\nasync: true }` in the Minipass constructor, or by setting\n`stream.async = true` afterwards.  Note that this does add some\noverhead, so should only be done in cases where you are willing\nto lose a bit of performance in order to avoid having to refactor\nprogram logic.\n\n## USAGE\n\nIt's a stream!  Use it like a stream and it'll most likely do what you\nwant.\n\n```js\nconst Minipass = require('minipass')\nconst mp = new Minipass(options) // optional: { encoding, objectMode }\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n* `encoding` How would you like the data coming _out_ of the stream to be\n  encoded?  Accepts any values that can be passed to `Buffer.toString()`.\n* `objectMode` Emit data exactly as it comes in.  This will be flipped on\n  by default if you write() something other than a string or Buffer at any\n  point.  Setting `objectMode: true` will prevent setting any encoding\n  value.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and `Writable`\nstreams.\n\n### Methods\n\n* `write(chunk, [encoding], [callback])` - Put data in.  (Note that, in the\n  base Minipass class, the same data will come out.)  Returns `false` if\n  the stream will buffer the next write, or true if it's still in \"flowing\"\n  mode.\n* `end([chunk, [encoding]], [callback])` - Signal that you have no more\n  data to write.  This will queue an `end` event to be fired when all the\n  data has been consumed.\n* `setEncoding(encoding)` - Set the encoding for data coming of the stream.\n  This can only be done once.\n* `pause()` - No more data for a while, please.  This also prevents `end`\n  from being emitted for empty streams until the stream is resumed.\n* `resume()` - Resume the stream.  If there's data in the buffer, it is all\n  discarded.  Any buffered events are immediately emitted.\n* `pipe(dest)` - Send all output to the stream provided.  There is no way\n  to unpipe.  When data is emitted, it is immediately written to any and\n  all pipe destinations.\n* `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are EventEmitters.  Some\n  events are given special treatment, however.  (See below under \"events\".)\n* `promise()` - Returns a Promise that resolves when the stream emits\n  `end`, or rejects if the stream emits `error`.\n* `collect()` - Return a Promise that resolves on `end` with an array\n  containing each chunk of data that was emitted, or rejects if the stream\n  emits `error`.  Note that this consumes the stream data.\n* `concat()` - Same as `collect()`, but concatenates the data into a single\n  Buffer object.  Will reject the returned promise if the stream is in\n  objectMode, or if it goes into objectMode by the end of the data.\n* `read(n)` - Consume `n` bytes of data out of the buffer.  If `n` is not\n  provided, then consume all of it.  If `n` bytes are not available, then\n  it returns null.  **Note** consuming streams in this way is less\n  efficient, and can lead to unnecessary Buffer copying.\n* `destroy([er])` - Destroy the stream.  If an error is provided, then an\n  `'error'` event is emitted.  If the stream has a `close()` method, and\n  has not emitted a `'close'` event yet, then `stream.close()` will be\n  called.  Any Promises returned by `.promise()`, `.collect()` or\n  `.concat()` will be rejected.  After being destroyed, writing to the\n  stream will emit an error.  No more data will be emitted if the stream is\n  destroyed, even if it was previously buffered.\n\n### Properties\n\n* `bufferLength` Read-only.  Total number of bytes buffered, or in the case\n  of objectMode, the total number of objects.\n* `encoding` The encoding that has been set.  (Setting this is equivalent\n  to calling `setEncoding(enc)` and has the same prohibition against\n  setting multiple times.)\n* `flowing` Read-only.  Boolean indicating whether a chunk written to the\n  stream will be immediately emitted.\n* `emittedEnd` Read-only.  Boolean indicating whether the end-ish events\n  (ie, `end`, `prefinish`, `finish`) have been emitted.  Note that\n  listening on any end-ish event will immediateyl re-emit it if it has\n  already been emitted.\n* `writable` Whether the stream is writable.  Default `true`.  Set to\n  `false` when `end()`\n* `readable` Whether the stream is readable.  Default `true`.\n* `buffer` A [yallist](http://npm.im/yallist) linked list of chunks written\n  to the stream that have not yet been emitted.  (It's probably a bad idea\n  to mess with this.)\n* `pipes` A [yallist](http://npm.im/yallist) linked list of streams that\n  this stream is piping into.  (It's probably a bad idea to mess with\n  this.)\n* `destroyed` A getter that indicates whether the stream was destroyed.\n* `paused` True if the stream has been explicitly paused, otherwise false.\n* `objectMode` Indicates whether the stream is in `objectMode`.  Once set\n  to `true`, it cannot be set to `false`.\n\n### Events\n\n* `data` Emitted when there's data to read.  Argument is the data to read.\n  This is never emitted while not flowing.  If a listener is attached, that\n  will resume the stream.\n* `end` Emitted when there's no more data to read.  This will be emitted\n  immediately for empty streams when `end()` is called.  If a listener is\n  attached, and `end` was already emitted, then it will be emitted again.\n  All listeners are removed when `end` is emitted.\n* `prefinish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'end'`.\n* `finish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'prefinish'`.\n* `close` An indication that an underlying resource has been released.\n  Minipass does not emit this event, but will defer it until after `end`\n  has been emitted, since it throws off some stream libraries otherwise.\n* `drain` Emitted when the internal buffer empties, and it is again\n  suitable to `write()` into the stream.\n* `readable` Emitted when data is buffered and ready to be read by a\n  consumer.\n* `resume` Emitted when stream changes state from buffering to flowing\n  mode.  (Ie, when `resume` is called, `pipe` is called, or a `data` event\n  listener is added.)\n\n### Static Methods\n\n* `Minipass.isStream(stream)` Returns `true` if the argument is a stream,\n  and false otherwise.  To be considered a stream, the object must be\n  either an instance of Minipass, or an EventEmitter that has either a\n  `pipe()` method, or both `write()` and `end()` methods.  (Pretty much any\n  stream in node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass streams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(() => {\n  // stream is finished\n}, er => {\n  // stream emitted an error\n})\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one chunk for\nyou, but if you're going to do it yourself anyway, it's convenient this\nway:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in platforms\nthat support it.\n\nSynchronous iteration will end when the currently available data is\nconsumed, even if the `end` event has not been reached.  In string and\nbuffer mode, the data is concatenated, so unless multiple writes are\noccurring in the same tick as the `read()`, sync iteration loops will\ngenerally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written, with no\nflattening, create the stream with the `{ objectMode: true }` option.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i-- > 0)\n    mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume () {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write (chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end (chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(new (class extends Minipass {\n    emit (ev, ...data) {\n      // let's also log events, because debugging some weird thing\n      console.log('EMIT', ev)\n      return super.emit(ev, ...data)\n    }\n    write (chunk, encoding, callback) {\n      console.log('WRITE', chunk, encoding)\n      return super.write(chunk, encoding, callback)\n    }\n    end (chunk, encoding, callback) {\n      console.log('END', chunk, encoding)\n      return super.end(chunk, encoding, callback)\n    }\n  }))\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit (ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write (obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end (obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor (options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write (chunk, encoding, cb) {\n    if (typeof chunk === 'string' &&\n        typeof encoding === 'string' &&\n        encoding !== 'utf8') {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk))\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      try {\n        // JSON.parse can throw, emit an error on that\n        super.write(JSON.parse(jsonData[i]))\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb)\n      cb()\n  }\n}\n```\n", "readmeFilename": "README.md", "gitHead": "b5962821660cfa51f570fa5c2aeb8373d98b2270", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.2.0", "_nodeVersion": "18.2.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-rosVvUUjMkTW1UoqXVHzNw937MAKv1ewomUBIqYk0IXPYk+LpVCOV1+kBpzAiQrKGjG3Ta81ZNzk/EcL28zABw==", "shasum": "cee61ecabea6634b5706139b0195794103e70af9", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.2.0.tgz", "fileCount": 4, "unpackedSize": 41954, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIN7FPYfROiOXZL37Gg4ijX/iQSFKctot/ZIj5SpLRPQIhANC6f2gQLh1sOh0qZ935JhWO1eHOfCtnEGFnbdOJerUo"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioNr0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXSg/+I/JQcflKVqRbDY3M/HRsmE0MhnMTDC01kcdSRLe2I8g1PoXS\r\nrclPpOyl4X3hxMawy9Sh7HhdiGoY/zEbq8SwnklfpBN3FpuVjNWKcqqTJ7N2\r\nk4S9xT09rFfa9ey8w2dfJ/TPp01+Ou5bKHNE5qgGwXhoqC3JRhncPkQ6IpfY\r\nunNQE1JyWBXqb0kgc/c1C3X7LIJFCtGI5L1XtNq3BsmlLeRvKSDJ3WYcGvrN\r\nx8oLVhdDA464x4sodRLq5KFN02sOlljKyE8NDJv7ZLc1G1guT0sdigfuqmNR\r\nR9G8LAyvrLPImjK+GIg68tVmC522ZES4tLT4o7WMpLgXWuAvSaTmI9f3wus8\r\nuXJmvyiVgrG20p8oEbRO0IQeKaMiJKtHwNj1JxBAToA9SmqPwPZgGkv0ETqT\r\nSkGscrAgrLPXOH/EM/p/p45WcS5h4q9szTD5MlPTlTGEc0dK1KEDu1+TYIND\r\nI+ay2WdHVm1Tc47oh+2D+AzFnGZxwhm7qfZ/lgbsRxvPcbb1jI1Xf/XvqdOA\r\nLbmAbZZwgxy2jU5TaWi0DMQRptCBC7ofj1nB7tyrqZmHlGztE1vxU0eFsoYn\r\nm4ity+ozp8nN7Fy+GrgaWivxyRpUeagkkuOGQB1ghJOjCLH3hk2JdpsSYh1L\r\nzZoB7YSWr6FkSulUdXDwmJ+1kwANURSMyWQ=\r\n=6AK4\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.2.0_1654708980407_0.8034251288290672"}, "_hasShrinkwrap": false}, "3.2.1": {"name": "minipass", "version": "3.2.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^16.2.0", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1oObKSrVwLX_7Ut4Z6g3fZW-AX1j1-k6w-cDsrkaSbHM/edit#gid=0)\nfor objects, strings, and buffers.\n\nSupports `pipe()`ing (including multi-`pipe()` and backpressure transmission),\nbuffering data until either a `data` event handler or `pipe()` is added (so\nyou don't lose the first chunk), and most other cases where PassThrough is\na good idea.\n\nThere is a `read()` method, but it's much more efficient to consume data\nfrom this stream via `'data'` events or by calling `pipe()` into some other\nstream.  Calling `read()` requires the buffer to be flattened in some\ncases, which requires copying memory.\n\nThere is also no `unpipe()` method.  Once you start piping, there is no\nstopping it!\n\nIf you set `objectMode: true` in the options, then whatever is written will\nbe emitted.  Otherwise, it'll do a minimal amount of Buffer copying to\nensure proper Streams semantics when `read(n)` is called.\n\n`objectMode` can also be set by doing `stream.objectMode = true`, or by\nwriting any non-string/non-buffer data.  `objectMode` cannot be set to\nfalse once it is set.\n\nThis is not a `through` or `through2` stream.  It doesn't transform the\ndata, it just passes it right through.  If you want to transform the data,\nextend the class, and override the `write()` method.  Once you're done\ntransforming the data however you want, call `super.write()` with the\ntransform output.\n\nFor some examples of streams that extend Minipass in various ways, check\nout:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap-parser)\n- [treport](http://npm.im/treport)\n- [minipass-fetch](http://npm.im/minipass-fetch)\n- [pacote](http://npm.im/pacote)\n- [make-fetch-happen](http://npm.im/make-fetch-happen)\n- [cacache](http://npm.im/cacache)\n- [ssri](http://npm.im/ssri)\n- [npm-registry-fetch](http://npm.im/npm-registry-fetch)\n- [minipass-json-stream](http://npm.im/minipass-json-stream)\n- [minipass-sized](http://npm.im/minipass-sized)\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different from (and in\nsome ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with node-core streams and\nintend to use Minipass streams in your programs.\n\nYou can avoid most of these differences entirely (for a very\nsmall performance penalty) by setting `{async: true}` in the\nconstructor options.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.  Thus, data\nis emitted as soon as it is available, always.  It is buffered until read,\nbut no longer.  Another way to look at it is that Minipass streams are\nexactly as synchronous as the logic that writes into them.\n\nThis can be surprising if your code relies on `PassThrough.write()` always\nproviding data on the next tick rather than the current one, or being able\nto call `resume()` and not have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no way for\nMinipass to achieve the speeds it does, or support the synchronous use\ncases that it does.  Simply put, waiting takes time.\n\nThis non-deferring approach makes Minipass streams much easier to reason\nabout, especially in the context of Promises and other flow-control\nmechanisms.\n\nExample:\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ async: true })\nstream.on('data', () => console.log('data event'))\nconsole.log('before write')\nstream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// data event\n// after write\n```\n\n### Exception: Async Opt-In\n\nIf you wish to have a Minipass stream with behavior that more\nclosely mimics Node.js core streams, you can set the stream in\nasync mode either by setting `async: true` in the constructor\noptions, or by setting `stream.async = true` later on.\n\n```js\nconst Minipass = require('minipass')\nconst asyncStream = new Minipass({ async: true })\nasyncStream.on('data', () => console.log('data event'))\nconsole.log('before write')\nasyncStream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// after write\n// data event <-- this is deferred until the next tick\n```\n\nSwitching _out_ of async mode is unsafe, as it could cause data\ncorruption, and so is not enabled.  Example:\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nsetStreamSyncAgainSomehow(stream) // <-- this doesn't actually exist!\nstream.write('world')\nconsole.log('after writes')\n// hypothetical output would be:\n// before writes\n// world\n// after writes\n// hello\n// NOT GOOD!\n```\n\nTo avoid this problem, once set into async mode, any attempt to\nmake the stream sync again will be ignored.\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nstream.async = false // <-- no-op, stream already async\nstream.write('world')\nconsole.log('after writes')\n// actual output:\n// before writes\n// after writes\n// hello\n// world\n```\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer, returning `true`\non all writes until the limit is hit, even if the data has nowhere to go.\nThen, they will not attempt to draw more data in until the buffer size dips\nbelow a minimum value.\n\nMinipass streams are much simpler.  The `write()` method will return `true`\nif the data has somewhere to go (which is to say, given the timing\nguarantees, that the data is already there by the time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and the data\nsits in a buffer, to be drained out immediately as soon as anyone consumes\nit.\n\nSince nothing is ever buffered unnecessarily, there is much less\ncopying data, and less bookkeeping about buffer capacity levels.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written all the way\nthrough the pipeline, and `write()` always returns true/false based on\nwhether the data was fully flushed, backpressure is communicated\nimmediately to the upstream caller.  This minimizes buffering.\n\nConsider this case:\n\n```js\nconst {PassThrough} = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage, and\nmultiple event deferrals happened, for an unblocked pipeline where it was\nperfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead someone reading\nthe code to think an advisory maximum of 1KiB is being set for the\npipeline.  However, the actual advisory buffering level is the _sum_ of\n`highWaterMark` values, since each one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data written,\nor _ever_ buffer data that can be flushed all the way through.  Neither\nnode-core streams nor Minipass ever fail to buffer written data, but\nnode-core streams do a lot of unnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less stuff and\nwaits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing any data\ninto it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't want to\npotentially happen immediately (for example, closing file descriptors,\nmoving on to the next entry in an archive parse stream, etc.) then be sure\nto call `stream.pause()` on creation, and then `stream.resume()` once you\nare ready to respond to the `end` event.\n\nHowever, this is _usually_ not a problem because:\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not yet have had\na chance to add a listener.  In order to avoid this hazard, Minipass\nstreams safely re-emit the `'end'` event if a new listener is added after\n`'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream has already\nemitted `end`, then it will call the handler right away.  (You can think of\nthis somewhat like attaching a new `.then(fn)` to a previously-resolved\nPromise.)\n\nTo prevent calling handlers multiple times who would not expect multiple\nends to occur, all listeners are removed from the `'end'` event whenever it\nis emitted.\n\n### Emit `error` When Asked\n\nThe most recent error object passed to the `'error'` event is\nstored on the stream.  If a new `'error'` event handler is added,\nand an error was previously emitted, then the event handler will\nbe called immediately (or on `process.nextTick` in the case of\nasync streams).\n\nThis makes it much more difficult to end up trying to interact\nwith a broken stream, if the error handler is added after an\nerror was previously emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data through the\npipeline when a new pipe destination is added, this can have surprising\neffects, especially when a stream comes in from some other function and may\nor may not have data in its buffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nOne solution is to create a dedicated tee-stream junction that pipes to\nboth locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nsrc.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners.  The first one\nadded will _immediately_ receive all of the data, leaving nothing for the\nsecond:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\nAll of the hazards in this section are avoided by setting `{\nasync: true }` in the Minipass constructor, or by setting\n`stream.async = true` afterwards.  Note that this does add some\noverhead, so should only be done in cases where you are willing\nto lose a bit of performance in order to avoid having to refactor\nprogram logic.\n\n## USAGE\n\nIt's a stream!  Use it like a stream and it'll most likely do what you\nwant.\n\n```js\nconst Minipass = require('minipass')\nconst mp = new Minipass(options) // optional: { encoding, objectMode }\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n* `encoding` How would you like the data coming _out_ of the stream to be\n  encoded?  Accepts any values that can be passed to `Buffer.toString()`.\n* `objectMode` Emit data exactly as it comes in.  This will be flipped on\n  by default if you write() something other than a string or Buffer at any\n  point.  Setting `objectMode: true` will prevent setting any encoding\n  value.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and `Writable`\nstreams.\n\n### Methods\n\n* `write(chunk, [encoding], [callback])` - Put data in.  (Note that, in the\n  base Minipass class, the same data will come out.)  Returns `false` if\n  the stream will buffer the next write, or true if it's still in \"flowing\"\n  mode.\n* `end([chunk, [encoding]], [callback])` - Signal that you have no more\n  data to write.  This will queue an `end` event to be fired when all the\n  data has been consumed.\n* `setEncoding(encoding)` - Set the encoding for data coming of the stream.\n  This can only be done once.\n* `pause()` - No more data for a while, please.  This also prevents `end`\n  from being emitted for empty streams until the stream is resumed.\n* `resume()` - Resume the stream.  If there's data in the buffer, it is all\n  discarded.  Any buffered events are immediately emitted.\n* `pipe(dest)` - Send all output to the stream provided.  There is no way\n  to unpipe.  When data is emitted, it is immediately written to any and\n  all pipe destinations.\n* `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are EventEmitters.  Some\n  events are given special treatment, however.  (See below under \"events\".)\n* `promise()` - Returns a Promise that resolves when the stream emits\n  `end`, or rejects if the stream emits `error`.\n* `collect()` - Return a Promise that resolves on `end` with an array\n  containing each chunk of data that was emitted, or rejects if the stream\n  emits `error`.  Note that this consumes the stream data.\n* `concat()` - Same as `collect()`, but concatenates the data into a single\n  Buffer object.  Will reject the returned promise if the stream is in\n  objectMode, or if it goes into objectMode by the end of the data.\n* `read(n)` - Consume `n` bytes of data out of the buffer.  If `n` is not\n  provided, then consume all of it.  If `n` bytes are not available, then\n  it returns null.  **Note** consuming streams in this way is less\n  efficient, and can lead to unnecessary Buffer copying.\n* `destroy([er])` - Destroy the stream.  If an error is provided, then an\n  `'error'` event is emitted.  If the stream has a `close()` method, and\n  has not emitted a `'close'` event yet, then `stream.close()` will be\n  called.  Any Promises returned by `.promise()`, `.collect()` or\n  `.concat()` will be rejected.  After being destroyed, writing to the\n  stream will emit an error.  No more data will be emitted if the stream is\n  destroyed, even if it was previously buffered.\n\n### Properties\n\n* `bufferLength` Read-only.  Total number of bytes buffered, or in the case\n  of objectMode, the total number of objects.\n* `encoding` The encoding that has been set.  (Setting this is equivalent\n  to calling `setEncoding(enc)` and has the same prohibition against\n  setting multiple times.)\n* `flowing` Read-only.  Boolean indicating whether a chunk written to the\n  stream will be immediately emitted.\n* `emittedEnd` Read-only.  Boolean indicating whether the end-ish events\n  (ie, `end`, `prefinish`, `finish`) have been emitted.  Note that\n  listening on any end-ish event will immediateyl re-emit it if it has\n  already been emitted.\n* `writable` Whether the stream is writable.  Default `true`.  Set to\n  `false` when `end()`\n* `readable` Whether the stream is readable.  Default `true`.\n* `buffer` A [yallist](http://npm.im/yallist) linked list of chunks written\n  to the stream that have not yet been emitted.  (It's probably a bad idea\n  to mess with this.)\n* `pipes` A [yallist](http://npm.im/yallist) linked list of streams that\n  this stream is piping into.  (It's probably a bad idea to mess with\n  this.)\n* `destroyed` A getter that indicates whether the stream was destroyed.\n* `paused` True if the stream has been explicitly paused, otherwise false.\n* `objectMode` Indicates whether the stream is in `objectMode`.  Once set\n  to `true`, it cannot be set to `false`.\n\n### Events\n\n* `data` Emitted when there's data to read.  Argument is the data to read.\n  This is never emitted while not flowing.  If a listener is attached, that\n  will resume the stream.\n* `end` Emitted when there's no more data to read.  This will be emitted\n  immediately for empty streams when `end()` is called.  If a listener is\n  attached, and `end` was already emitted, then it will be emitted again.\n  All listeners are removed when `end` is emitted.\n* `prefinish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'end'`.\n* `finish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'prefinish'`.\n* `close` An indication that an underlying resource has been released.\n  Minipass does not emit this event, but will defer it until after `end`\n  has been emitted, since it throws off some stream libraries otherwise.\n* `drain` Emitted when the internal buffer empties, and it is again\n  suitable to `write()` into the stream.\n* `readable` Emitted when data is buffered and ready to be read by a\n  consumer.\n* `resume` Emitted when stream changes state from buffering to flowing\n  mode.  (Ie, when `resume` is called, `pipe` is called, or a `data` event\n  listener is added.)\n\n### Static Methods\n\n* `Minipass.isStream(stream)` Returns `true` if the argument is a stream,\n  and false otherwise.  To be considered a stream, the object must be\n  either an instance of Minipass, or an EventEmitter that has either a\n  `pipe()` method, or both `write()` and `end()` methods.  (Pretty much any\n  stream in node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass streams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(() => {\n  // stream is finished\n}, er => {\n  // stream emitted an error\n})\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one chunk for\nyou, but if you're going to do it yourself anyway, it's convenient this\nway:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in platforms\nthat support it.\n\nSynchronous iteration will end when the currently available data is\nconsumed, even if the `end` event has not been reached.  In string and\nbuffer mode, the data is concatenated, so unless multiple writes are\noccurring in the same tick as the `read()`, sync iteration loops will\ngenerally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written, with no\nflattening, create the stream with the `{ objectMode: true }` option.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i-- > 0)\n    mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume () {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write (chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end (chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(new (class extends Minipass {\n    emit (ev, ...data) {\n      // let's also log events, because debugging some weird thing\n      console.log('EMIT', ev)\n      return super.emit(ev, ...data)\n    }\n    write (chunk, encoding, callback) {\n      console.log('WRITE', chunk, encoding)\n      return super.write(chunk, encoding, callback)\n    }\n    end (chunk, encoding, callback) {\n      console.log('END', chunk, encoding)\n      return super.end(chunk, encoding, callback)\n    }\n  }))\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit (ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write (obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end (obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor (options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write (chunk, encoding, cb) {\n    if (typeof chunk === 'string' &&\n        typeof encoding === 'string' &&\n        encoding !== 'utf8') {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk))\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      try {\n        // JSON.parse can throw, emit an error on that\n        super.write(JSON.parse(jsonData[i]))\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb)\n      cb()\n  }\n}\n```\n", "readmeFilename": "README.md", "gitHead": "547db2981c1c301c9552f3158ccad13c1a106cfc", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.2.1", "_nodeVersion": "18.2.0", "_npmVersion": "8.9.0", "dist": {"integrity": "sha512-v5cqJP4WxUVXYXhOOdPiOZEDoF7omSpLivw2GMCL1v/j+xh886bPXKh6SzyA6sa45e4NRQ46IRBEkAazvb6I6A==", "shasum": "12ac0ab289be638db0ad8887b28413b773355c13", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.2.1.tgz", "fileCount": 4, "unpackedSize": 42013, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwL9Wd5Nga9fJ1MuLHwazQSiIpFAiB1OmKiG2ojWbGrAIhAON3wsp54NrJpTDBIKHD82DfxXRGvfs9v/C/2eHYET/B"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio5CWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYzg/+JnHdrJEaOlLAWBd4U/+MNb25emW45tM2psHG4SxxfW1V8m9f\r\njOMQOdfU53n0x55rF7CvFK5Xuqt/F5zA+V3DDgOa3y5uVsw8OdpaGUhFvJtW\r\nw8sIrEmZvpHi4JhC5pWAfsNSJLSEuAl7fWbNlv3IadLqRIvPTqRJqreLgvXU\r\ntpVeitWn00WPIHInqE2vqQI/bdZ3GGXorHCWWGBRWU+7jklXTHYyOCJmPbqZ\r\nuoLQnpABp1LbvEmFtNH66psoZLZHD2Hjxj0qgEHt+94dj+sLWlKylt7b689q\r\nDOCBXfHp2PDRvCgRyY9Gq3n2HcPns/VSdygdVnWek6ROqgGwHDMpKk/1gXUF\r\ndZuQIJXgW8GUsDayr0nxfixTmM0BduJ3YGJ0mVIxu3tHbTPumQ6S/y32WDcf\r\nFK7lCIAhsxiOMiX/1Flkgl279Bhua64Pm7586MqRXuG4uZ39vuVgoYWlfsYf\r\nM3S1sfUprwXa8puLU5YBCDTTWKinRKgxskQ3K0cQPSnz2iZ3MAdPXc9V8Mig\r\nU1B3yTuIHxbeIsfIUHp3zAotJKaea+TxgB7eKx1xJh7HvfjH/n5sjV3CoefR\r\nsGCEyhB/PFw26lSCxYX7urIQAQL4vU48aX8Wi0cczyM+P9gzetrDtk0OVGpb\r\nhyiS0nBnulWmrQRe6jxD83Ik/pRzySq71KE=\r\n=+fQZ\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.2.1_1654886549880_0.8924112603311831"}, "_hasShrinkwrap": false}, "3.3.0": {"name": "minipass", "version": "3.3.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "types": "./index.d.ts", "gitHead": "80662a0ddf9e3795ec0e4d773aa2c9f34bc0dbd1", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.0", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-rIjEM0fvMW1i+txLUOo+ZwoW+cB1dJrhy62iM9ptwhYSaZ7yoHtkO3m+Wpq9kYO/pPKOK6MuziXawNC7OyJcjA==", "shasum": "9bb11578125d483c45c8debd14f2a1be8ea82bab", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.3.0.tgz", "fileCount": 4, "unpackedSize": 43834, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIErJ8wrEDbauQxWgisK7yOMI7w3mivel2mbUevqr/FC5AiBeWQhwU91QwetdvuEJE09lZKFnRKBUy7I3xfOHIdKcNw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJir9hiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmof5A//fEYxjEECGBP0+R3fAMm5PlZptMEs/l1Wd1cVdI0nxPycf1OP\r\nEucE1f6Gus2Z1Oqoa39QqjqVqcf6eA8Mf2ncrJUWrKsWFIaB0tGonp5T7vzz\r\ner4nDXnCl4/g36lwH9zCjUC+83hw50Tt+CEpMujINJ/g+HRMJPDFErfMv1Eu\r\nWNc3Bx5dHGNinmowdpQbxLt9CFnfjvyzhzsaKqQPNdhfA6HOB6vnnNJsktFB\r\nPWBs9LXgWawecux20QCHiBQfMD1QO/XKVjuZh6C7+ycbf0PKSm/RGfMwoUvG\r\npwT36QQZGG3mBQ3sxj7V/n7Q1pXppg5Ne/crqQsDcV4ANB66weIltIdFQouM\r\n6D8STYUyLqX+PMEILTCKSiZXRZSm7caoPr4Q3LrI5lqnFQLQiw+w+H548ZUv\r\nVLdjNBNED5IPyJsWoGoK+kMOPe9OQaYWNl3j5zJGbfh622q/05QiT1bVcBam\r\nd6mgvuMNl8bMNyRBP/DCI1jUo3ywM+JayDRpRwYm8kU8EGmXF/GXxYXM8k88\r\nTtzSgu0TUn3Dvqr0pD43oN34VWuwQORJR4M7vmOP9SJ/t6uI6dL1GL+CEeUF\r\nM7NUECaMM16goKlA45a+Q7xQNOVjwAzjRtIkMwe5mZu4TgKQM0JZdEmnGRuu\r\nXbjJ8Q2EUVKRbR84ox2hol/EJelc0y5SuDI=\r\n=sKBO\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.0_1655691362014_0.542472729959655"}, "_hasShrinkwrap": false}, "3.3.1": {"name": "minipass", "version": "3.3.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "types": "./index.d.ts", "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1oObKSrVwLX_7Ut4Z6g3fZW-AX1j1-k6w-cDsrkaSbHM/edit#gid=0)\nfor objects, strings, and buffers.\n\nSupports `pipe()`ing (including multi-`pipe()` and backpressure transmission),\nbuffering data until either a `data` event handler or `pipe()` is added (so\nyou don't lose the first chunk), and most other cases where PassThrough is\na good idea.\n\nThere is a `read()` method, but it's much more efficient to consume data\nfrom this stream via `'data'` events or by calling `pipe()` into some other\nstream.  Calling `read()` requires the buffer to be flattened in some\ncases, which requires copying memory.\n\nIf you set `objectMode: true` in the options, then whatever is written will\nbe emitted.  Otherwise, it'll do a minimal amount of Buffer copying to\nensure proper Streams semantics when `read(n)` is called.\n\n`objectMode` can also be set by doing `stream.objectMode = true`, or by\nwriting any non-string/non-buffer data.  `objectMode` cannot be set to\nfalse once it is set.\n\nThis is not a `through` or `through2` stream.  It doesn't transform the\ndata, it just passes it right through.  If you want to transform the data,\nextend the class, and override the `write()` method.  Once you're done\ntransforming the data however you want, call `super.write()` with the\ntransform output.\n\nFor some examples of streams that extend Minipass in various ways, check\nout:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap-parser)\n- [treport](http://npm.im/treport)\n- [minipass-fetch](http://npm.im/minipass-fetch)\n- [pacote](http://npm.im/pacote)\n- [make-fetch-happen](http://npm.im/make-fetch-happen)\n- [cacache](http://npm.im/cacache)\n- [ssri](http://npm.im/ssri)\n- [npm-registry-fetch](http://npm.im/npm-registry-fetch)\n- [minipass-json-stream](http://npm.im/minipass-json-stream)\n- [minipass-sized](http://npm.im/minipass-sized)\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different from (and in\nsome ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with node-core streams and\nintend to use Minipass streams in your programs.\n\nYou can avoid most of these differences entirely (for a very\nsmall performance penalty) by setting `{async: true}` in the\nconstructor options.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.  Thus, data\nis emitted as soon as it is available, always.  It is buffered until read,\nbut no longer.  Another way to look at it is that Minipass streams are\nexactly as synchronous as the logic that writes into them.\n\nThis can be surprising if your code relies on `PassThrough.write()` always\nproviding data on the next tick rather than the current one, or being able\nto call `resume()` and not have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no way for\nMinipass to achieve the speeds it does, or support the synchronous use\ncases that it does.  Simply put, waiting takes time.\n\nThis non-deferring approach makes Minipass streams much easier to reason\nabout, especially in the context of Promises and other flow-control\nmechanisms.\n\nExample:\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ async: true })\nstream.on('data', () => console.log('data event'))\nconsole.log('before write')\nstream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// data event\n// after write\n```\n\n### Exception: Async Opt-In\n\nIf you wish to have a Minipass stream with behavior that more\nclosely mimics Node.js core streams, you can set the stream in\nasync mode either by setting `async: true` in the constructor\noptions, or by setting `stream.async = true` later on.\n\n```js\nconst Minipass = require('minipass')\nconst asyncStream = new Minipass({ async: true })\nasyncStream.on('data', () => console.log('data event'))\nconsole.log('before write')\nasyncStream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// after write\n// data event <-- this is deferred until the next tick\n```\n\nSwitching _out_ of async mode is unsafe, as it could cause data\ncorruption, and so is not enabled.  Example:\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nsetStreamSyncAgainSomehow(stream) // <-- this doesn't actually exist!\nstream.write('world')\nconsole.log('after writes')\n// hypothetical output would be:\n// before writes\n// world\n// after writes\n// hello\n// NOT GOOD!\n```\n\nTo avoid this problem, once set into async mode, any attempt to\nmake the stream sync again will be ignored.\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nstream.async = false // <-- no-op, stream already async\nstream.write('world')\nconsole.log('after writes')\n// actual output:\n// before writes\n// after writes\n// hello\n// world\n```\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer, returning `true`\non all writes until the limit is hit, even if the data has nowhere to go.\nThen, they will not attempt to draw more data in until the buffer size dips\nbelow a minimum value.\n\nMinipass streams are much simpler.  The `write()` method will return `true`\nif the data has somewhere to go (which is to say, given the timing\nguarantees, that the data is already there by the time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and the data\nsits in a buffer, to be drained out immediately as soon as anyone consumes\nit.\n\nSince nothing is ever buffered unnecessarily, there is much less\ncopying data, and less bookkeeping about buffer capacity levels.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written all the way\nthrough the pipeline, and `write()` always returns true/false based on\nwhether the data was fully flushed, backpressure is communicated\nimmediately to the upstream caller.  This minimizes buffering.\n\nConsider this case:\n\n```js\nconst {PassThrough} = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage, and\nmultiple event deferrals happened, for an unblocked pipeline where it was\nperfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead someone reading\nthe code to think an advisory maximum of 1KiB is being set for the\npipeline.  However, the actual advisory buffering level is the _sum_ of\n`highWaterMark` values, since each one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data written,\nor _ever_ buffer data that can be flushed all the way through.  Neither\nnode-core streams nor Minipass ever fail to buffer written data, but\nnode-core streams do a lot of unnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less stuff and\nwaits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing any data\ninto it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't want to\npotentially happen immediately (for example, closing file descriptors,\nmoving on to the next entry in an archive parse stream, etc.) then be sure\nto call `stream.pause()` on creation, and then `stream.resume()` once you\nare ready to respond to the `end` event.\n\nHowever, this is _usually_ not a problem because:\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not yet have had\na chance to add a listener.  In order to avoid this hazard, Minipass\nstreams safely re-emit the `'end'` event if a new listener is added after\n`'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream has already\nemitted `end`, then it will call the handler right away.  (You can think of\nthis somewhat like attaching a new `.then(fn)` to a previously-resolved\nPromise.)\n\nTo prevent calling handlers multiple times who would not expect multiple\nends to occur, all listeners are removed from the `'end'` event whenever it\nis emitted.\n\n### Emit `error` When Asked\n\nThe most recent error object passed to the `'error'` event is\nstored on the stream.  If a new `'error'` event handler is added,\nand an error was previously emitted, then the event handler will\nbe called immediately (or on `process.nextTick` in the case of\nasync streams).\n\nThis makes it much more difficult to end up trying to interact\nwith a broken stream, if the error handler is added after an\nerror was previously emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data through the\npipeline when a new pipe destination is added, this can have surprising\neffects, especially when a stream comes in from some other function and may\nor may not have data in its buffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nOne solution is to create a dedicated tee-stream junction that pipes to\nboth locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nsrc.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners.  The first one\nadded will _immediately_ receive all of the data, leaving nothing for the\nsecond:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\nAll of the hazards in this section are avoided by setting `{\nasync: true }` in the Minipass constructor, or by setting\n`stream.async = true` afterwards.  Note that this does add some\noverhead, so should only be done in cases where you are willing\nto lose a bit of performance in order to avoid having to refactor\nprogram logic.\n\n## USAGE\n\nIt's a stream!  Use it like a stream and it'll most likely do what you\nwant.\n\n```js\nconst Minipass = require('minipass')\nconst mp = new Minipass(options) // optional: { encoding, objectMode }\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n* `encoding` How would you like the data coming _out_ of the stream to be\n  encoded?  Accepts any values that can be passed to `Buffer.toString()`.\n* `objectMode` Emit data exactly as it comes in.  This will be flipped on\n  by default if you write() something other than a string or Buffer at any\n  point.  Setting `objectMode: true` will prevent setting any encoding\n  value.\n* `async` Defaults to `false`.  Set to `true` to defer data\n  emission until next tick.  This reduces performance slightly,\n  but makes Minipass streams use timing behavior closer to Node\n  core streams.  See [Timing](#timing) for more details.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and `Writable`\nstreams.\n\n### Methods\n\n* `write(chunk, [encoding], [callback])` - Put data in.  (Note that, in the\n  base Minipass class, the same data will come out.)  Returns `false` if\n  the stream will buffer the next write, or true if it's still in \"flowing\"\n  mode.\n* `end([chunk, [encoding]], [callback])` - Signal that you have no more\n  data to write.  This will queue an `end` event to be fired when all the\n  data has been consumed.\n* `setEncoding(encoding)` - Set the encoding for data coming of the stream.\n  This can only be done once.\n* `pause()` - No more data for a while, please.  This also prevents `end`\n  from being emitted for empty streams until the stream is resumed.\n* `resume()` - Resume the stream.  If there's data in the buffer, it is all\n  discarded.  Any buffered events are immediately emitted.\n* `pipe(dest)` - Send all output to the stream provided.  When\n  data is emitted, it is immediately written to any and all pipe\n  destinations.  (Or written on next tick in `async` mode.)\n* `unpipe(dest)` - Stop piping to the destination stream.  This\n  is immediate, meaning that any asynchronously queued data will\n  _not_ make it to the destination when running in `async` mode.\n    * `options.end` - Boolean, end the destination stream when\n      the source stream ends.  Default `true`.\n    * `options.proxyErrors` - Boolean, proxy `error` events from\n      the source stream to the destination stream.  Note that\n      errors are _not_ proxied after the pipeline terminates,\n      either due to the source emitting `'end'` or manually\n      unpiping with `src.unpipe(dest)`.  Default `false`.\n* `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are EventEmitters.  Some\n  events are given special treatment, however.  (See below under \"events\".)\n* `promise()` - Returns a Promise that resolves when the stream emits\n  `end`, or rejects if the stream emits `error`.\n* `collect()` - Return a Promise that resolves on `end` with an array\n  containing each chunk of data that was emitted, or rejects if the stream\n  emits `error`.  Note that this consumes the stream data.\n* `concat()` - Same as `collect()`, but concatenates the data into a single\n  Buffer object.  Will reject the returned promise if the stream is in\n  objectMode, or if it goes into objectMode by the end of the data.\n* `read(n)` - Consume `n` bytes of data out of the buffer.  If `n` is not\n  provided, then consume all of it.  If `n` bytes are not available, then\n  it returns null.  **Note** consuming streams in this way is less\n  efficient, and can lead to unnecessary Buffer copying.\n* `destroy([er])` - Destroy the stream.  If an error is provided, then an\n  `'error'` event is emitted.  If the stream has a `close()` method, and\n  has not emitted a `'close'` event yet, then `stream.close()` will be\n  called.  Any Promises returned by `.promise()`, `.collect()` or\n  `.concat()` will be rejected.  After being destroyed, writing to the\n  stream will emit an error.  No more data will be emitted if the stream is\n  destroyed, even if it was previously buffered.\n\n### Properties\n\n* `bufferLength` Read-only.  Total number of bytes buffered, or in the case\n  of objectMode, the total number of objects.\n* `encoding` The encoding that has been set.  (Setting this is equivalent\n  to calling `setEncoding(enc)` and has the same prohibition against\n  setting multiple times.)\n* `flowing` Read-only.  Boolean indicating whether a chunk written to the\n  stream will be immediately emitted.\n* `emittedEnd` Read-only.  Boolean indicating whether the end-ish events\n  (ie, `end`, `prefinish`, `finish`) have been emitted.  Note that\n  listening on any end-ish event will immediateyl re-emit it if it has\n  already been emitted.\n* `writable` Whether the stream is writable.  Default `true`.  Set to\n  `false` when `end()`\n* `readable` Whether the stream is readable.  Default `true`.\n* `buffer` A [yallist](http://npm.im/yallist) linked list of chunks written\n  to the stream that have not yet been emitted.  (It's probably a bad idea\n  to mess with this.)\n* `pipes` A [yallist](http://npm.im/yallist) linked list of streams that\n  this stream is piping into.  (It's probably a bad idea to mess with\n  this.)\n* `destroyed` A getter that indicates whether the stream was destroyed.\n* `paused` True if the stream has been explicitly paused, otherwise false.\n* `objectMode` Indicates whether the stream is in `objectMode`.  Once set\n  to `true`, it cannot be set to `false`.\n\n### Events\n\n* `data` Emitted when there's data to read.  Argument is the data to read.\n  This is never emitted while not flowing.  If a listener is attached, that\n  will resume the stream.\n* `end` Emitted when there's no more data to read.  This will be emitted\n  immediately for empty streams when `end()` is called.  If a listener is\n  attached, and `end` was already emitted, then it will be emitted again.\n  All listeners are removed when `end` is emitted.\n* `prefinish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'end'`.\n* `finish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'prefinish'`.\n* `close` An indication that an underlying resource has been released.\n  Minipass does not emit this event, but will defer it until after `end`\n  has been emitted, since it throws off some stream libraries otherwise.\n* `drain` Emitted when the internal buffer empties, and it is again\n  suitable to `write()` into the stream.\n* `readable` Emitted when data is buffered and ready to be read by a\n  consumer.\n* `resume` Emitted when stream changes state from buffering to flowing\n  mode.  (Ie, when `resume` is called, `pipe` is called, or a `data` event\n  listener is added.)\n\n### Static Methods\n\n* `Minipass.isStream(stream)` Returns `true` if the argument is a stream,\n  and false otherwise.  To be considered a stream, the object must be\n  either an instance of Minipass, or an EventEmitter that has either a\n  `pipe()` method, or both `write()` and `end()` methods.  (Pretty much any\n  stream in node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass streams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(() => {\n  // stream is finished\n}, er => {\n  // stream emitted an error\n})\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one chunk for\nyou, but if you're going to do it yourself anyway, it's convenient this\nway:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in platforms\nthat support it.\n\nSynchronous iteration will end when the currently available data is\nconsumed, even if the `end` event has not been reached.  In string and\nbuffer mode, the data is concatenated, so unless multiple writes are\noccurring in the same tick as the `read()`, sync iteration loops will\ngenerally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written, with no\nflattening, create the stream with the `{ objectMode: true }` option.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i-- > 0)\n    mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume () {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write (chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end (chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(new (class extends Minipass {\n    emit (ev, ...data) {\n      // let's also log events, because debugging some weird thing\n      console.log('EMIT', ev)\n      return super.emit(ev, ...data)\n    }\n    write (chunk, encoding, callback) {\n      console.log('WRITE', chunk, encoding)\n      return super.write(chunk, encoding, callback)\n    }\n    end (chunk, encoding, callback) {\n      console.log('END', chunk, encoding)\n      return super.end(chunk, encoding, callback)\n    }\n  }))\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit (ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write (obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end (obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor (options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write (chunk, encoding, cb) {\n    if (typeof chunk === 'string' &&\n        typeof encoding === 'string' &&\n        encoding !== 'utf8') {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk))\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      try {\n        // JSON.parse can throw, emit an error on that\n        super.write(JSON.parse(jsonData[i]))\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb)\n      cb()\n  }\n}\n```\n", "readmeFilename": "README.md", "gitHead": "d65917f5e5f592f0a9454b057f69c6abcff974ec", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.1", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-BwHdcCb8ouar1yk+z3Nxu2SQvDcE3yrrrzDkOzavvbdlPFY+DC5wngUMZkkg+QtNIupbdUO4hgB24ySi8WJ1gg==", "shasum": "8959f676c7ed669334a2db4d8dd980c2c6d8e55c", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.3.1.tgz", "fileCount": 5, "unpackedSize": 47890, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICzrXaTHR/Wn7M2U/OKdaRnRwLxpQZKRUy5gTx+h7FR2AiEA0I470Kd98O4nSPoAgLix4W7p5n4N+rlGefkHekQM2FQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJir+DaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8whAAheh6EnaaB3jdj6W09QaakNiHsMCf3Lw8FOczkCXMpR30IP6X\r\nYcgwsYvj3ewi7LJbyDlqVChrE7aPpSfRHSg7zX+HaVJzsADYtppka/bHuAUn\r\nc2B6u6PVgwTI6OHQdeSTfXcZ/quP+XRRNlBKWz8FHrLpgsX4Etp2AX2iT7xm\r\nJoLAak5LvW6fgyYeTy82COxAvaL+LjEFICZw4rUxWXNSgBChnVPS3TmboaI2\r\nEoD/46+l3aKalA14kSJ6MTDoUxG8NcCAIHmE51V/Wm1Rh77CX0TqbOr2HFRz\r\nV865+m+YeLjjecn+YSvbrnHTrWTNTD49d8DLhfJuvZdakEcjd1v7BTPAk8Rt\r\n22ZacFaopqOx34DO4h3DK7/4T/vigM0ybBJtspFsAqKPSH+i15U0DoFAeaZV\r\nmgFkoixu/IQWOK3DfTi4UkOgqC5J0qSMBwstwWlsVhB4Cu1xcckv3FMP5h76\r\nc9aehYai3GUcjSjSZLJHJ2J4nld1Ve6OpYylfAHb5ZrDqbb19U0tzFo6kReG\r\nnTXXl7WkpBKu5wYzJWTVPPaUiWPsyPYD0vPrpA+JiBoVzTX1sz6/m25/xLyE\r\nM0i27LSWQSW6MMLMCQFAm5ju5Y/B9cLmX/4GrNhdxRfCVX5VPucZV+X8TS64\r\n5em4lweqifLopV7/V+wZVhxsKiLe7rXBYwQ=\r\n=JvGN\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.1_1655693530623_0.9527395009725081"}, "_hasShrinkwrap": false}, "3.3.2": {"name": "minipass", "version": "3.3.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "types": "./index.d.ts", "gitHead": "3802694369561391f1908f93c56420e5a8b1cca5", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.2", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-Z2BWOv2d19zOhU6Roua5LXHzdPkLJsq0REESIf+kJy27EIgCRxRvYcf6Ww0OD8HlYATBzkeXL/0CCt7hrmUe2w==", "shasum": "7be1929b2963b08f889b8426098f9af92e08f279", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.3.2.tgz", "fileCount": 5, "unpackedSize": 47903, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC0Yuz59pk/x7RpVdhCtiXi8F7hA9A72bjI8XU4XvZg8AiBck4mGABOomFFy5BtmobG1DzU+oNL82/YD1tCM1mRU9A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJir+IWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooOA/8CHKqMjL3iHYpp93xSqp4l3g7FdUaQMbRqLhRBKIlxw5RWjEt\r\nSRS/m46PNdEv5V4ajclIXJGv7/RmXzLvLM2WRvlGEHwldMFX40q/ZONruoUy\r\nt+R6O6QCbxFCjC+wTxPTajFdLPq5ORslHpCKM4TddVx/YGGsRfwTwO5BbLwH\r\ni9l1+RSvyYvE0IczWS/ni125dIbu3PRXZtyNeINqK091XoWXYmK0T5makWYV\r\nR3MdDl9U04NUhenSdPNHO7Fof6arTUJ7xKEmdXYre6pYiDJfdJBUqqTh5l9s\r\n967HIEyU7a5Bh6b0hOelOMJc3MQvVX3wAPMeuq0LO+OuejIWcwhEkZDkqLsK\r\nCVeFQzh6ZbivoOh6cI1pC4mOteYio9GPR7t96QYETsUxH0qX7mULfgIljqIu\r\nGwz8D06RXjqiklMLJoZ5v/oPHRG4AZOS97qpb6Pv17rMGtBnTE129bllbm+M\r\n8Dql8oJkmsifRvSLDCD9n0xQVUJ4CsVOkL5wMRF+PHV2YBp/5SyRTtXlETli\r\nEwP1nP6Sy0RZn6N2GcFdcIiNzywsBja0TwYrblCdYsWktkwvOy9K0C9DLdqz\r\n3G9jTX/hGAiFmExEho1deock8/RQAmQoxQtwZtoHjrH8izM+gVX8JFEfeG0W\r\nNyXneV1Ta+BxgP9XlQWRMj9bzaxIjMVjmag=\r\n=jPo0\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.2_1655693845957_0.25637305163514035"}, "_hasShrinkwrap": false}, "3.3.3": {"name": "minipass", "version": "3.3.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "types": "./index.d.ts", "gitHead": "af6d2aeaa9254f547675f82cbde18aebf0126960", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.3", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-N0BOsdFAlNRfmwMhjAsLVWOk7Ljmeb39iqFlsV1At+jqRhSUP9yeof8FyJu4imaJiSUp8vQebWD/guZwGQC8iA==", "shasum": "fd1f0e6c06449c10dadda72618b59c00f3d6378d", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.3.3.tgz", "fileCount": 5, "unpackedSize": 47855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDDnWccgb0rXc1WUKSijySM+RbciGs9osa0I1+D4Re0uAiB+uyxoBvm3dZqczxAy6H0ggOZS+HIUwvmIy2CekThijQ=="}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJir+vKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQGA/+M2HBrPmabDpxxbQRwN+faLEBOT2YgtvogcS15bQIvg2v/isl\r\nzFGeVqs8NiHD4VJHCxAdZyrmQaLmdwNp0tcX09UVEDz9V/66RgpvdYaKSriV\r\nbfnHzMYKxaRDQln+KPS1mgLWNy2B3MfC71h5hSx2JmOSSdeyxR9iXPcyXU3K\r\nn0jniIsRDQszOKSesPl70gUqEg9KRKkLdPJAZZy1NFDPVYDJJGeUgakm+3NY\r\nPuBM29ZTevNy1nDRfpqqrID2mB2KlFGpZw+24fOl/fosss/5wuMrQdxbSPEK\r\n9nEVGIU/nd8IRo+BETj1/aCTLAq2LLMuvN+kF0yS/ztFmrFgvx5MX8I9nONr\r\ntqBppMzDk3348YiBCzWg3e/7M41hLG0yVDd7lP8jbiGsFwxhGnkxH5fYItbq\r\nmiOArnOfLLmFjIqgymmlO6t9W0fm+GVcqtuB3REU7h+S1gPYGU4cX7MxongU\r\nsRlfu36yjp6VWRPn8EosXt7eh1NdkbQw5lF2gNATqH4r/FwPJPm6kvSZnkpZ\r\nxYm3G7M1ev3rxINUWf9rhiwQ1KRSMbxF7BTMQU/DTUvect8pR2GZYxWZ7Bsu\r\nnv0c4isV4Yw7Gmas9SndFOAni2KSDAerOH5VgFIZkh3JY6DryDtXmVPNVZeJ\r\n/Ed36yZV6uAJjO8H+w4inVh0DK+N4dxNBCs=\r\n=3hUS\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.3_1655696330262_0.271118571703173"}, "_hasShrinkwrap": false}, "3.3.4": {"name": "minipass", "version": "3.3.4", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "types": "./index.d.ts", "gitHead": "66a65348ec33823db3f8dc90e5a60348eb2da600", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.4", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-I9WPbWHCGu8W+6k1ZiGpPu0GkoKBeorkfKNuAFBNS1HNFJvke82sxvI5bzcCNpWPorkOO5QQ+zomzzwRxejXiw==", "shasum": "ca99f95dd77c43c7a76bf51e6d200025eee0ffae", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.3.4.tgz", "fileCount": 5, "unpackedSize": 47841, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzu8dyxvuvoBZRJx0TYoui0pa24MCiVPTC0YZRxgORxgIgPhSmUVHj8eGzHddEJkX8CQPyekQeCJ+aU2NLk/2P/Do="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiu0vYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzNg/+KkwAdyjrPW06Rxy4FMxyxup2vUBa8ZvlltxErvbH7OhuysHj\r\nrI+M5BF1oN5N8j8IFi2vORLxR7RyX6ZRN60ou7RtOHTJqUAlIPXfIrXcBVV+\r\ngdAunwjogPLhGfUP6zmcKKuqmqceE5r+dJaJnQrYDq3G81bDjKuyxuMGswvm\r\nZa2Y6AjvJqVsrRhPCRsVexQdFYQGx2gdRhX4VouteU9ZZusg4nDb97G7lBG/\r\n7+ojAhH3uraOqiwH2+QsBto0QXhhXDsNoKVk7Mgtd9m3znwScf6K03g9yiyn\r\nPsld7GhYXLfjIIz+KP9wS2HhvzRtYJX33HazEjcDYh1mOzSz2YqpXktarWky\r\nEfRz5maSx9qKtzg0FaBQCteb/6r1fTTFtjk00lT3GzUw2Bd7b1hNfgtI5gCl\r\nP3DbYo2Ss/8vwUzWv2/RZyWvD+qTylH+KKKwyggphP5JuFAK9i6X0amhQHME\r\nMRUvWvoV6ApOQMjJAl1MPD9mvEzt66xcINHD3OjqMB3JnQF8NuYsiP7DaYKv\r\n9EwI5H5LQ7/FXOuN+0l/FXk3Ey/AeqdRTxV05f2TmZJEEudPLQGFg1wQBeSq\r\ng0HudVSKT/mTpE88e/fj7NJtQl4G1dInERH/S1vt4tNmHIkvKYeu20T/xlpt\r\nWP49BuClFU4jnlb1lEDSQvDm4u0OHuLJLB8=\r\n=P8AF\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.4_1656441815769_0.8491380440336691"}, "_hasShrinkwrap": false}, "3.3.5": {"name": "minipass", "version": "3.3.5", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1oObKSrVwLX_7Ut4Z6g3fZW-AX1j1-k6w-cDsrkaSbHM/edit#gid=0)\nfor objects, strings, and buffers.\n\nSupports `pipe()`ing (including multi-`pipe()` and backpressure transmission),\nbuffering data until either a `data` event handler or `pipe()` is added (so\nyou don't lose the first chunk), and most other cases where PassThrough is\na good idea.\n\nThere is a `read()` method, but it's much more efficient to consume data\nfrom this stream via `'data'` events or by calling `pipe()` into some other\nstream.  Calling `read()` requires the buffer to be flattened in some\ncases, which requires copying memory.\n\nIf you set `objectMode: true` in the options, then whatever is written will\nbe emitted.  Otherwise, it'll do a minimal amount of Buffer copying to\nensure proper Streams semantics when `read(n)` is called.\n\n`objectMode` can also be set by doing `stream.objectMode = true`, or by\nwriting any non-string/non-buffer data.  `objectMode` cannot be set to\nfalse once it is set.\n\nThis is not a `through` or `through2` stream.  It doesn't transform the\ndata, it just passes it right through.  If you want to transform the data,\nextend the class, and override the `write()` method.  Once you're done\ntransforming the data however you want, call `super.write()` with the\ntransform output.\n\nFor some examples of streams that extend Minipass in various ways, check\nout:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap-parser)\n- [treport](http://npm.im/treport)\n- [minipass-fetch](http://npm.im/minipass-fetch)\n- [pacote](http://npm.im/pacote)\n- [make-fetch-happen](http://npm.im/make-fetch-happen)\n- [cacache](http://npm.im/cacache)\n- [ssri](http://npm.im/ssri)\n- [npm-registry-fetch](http://npm.im/npm-registry-fetch)\n- [minipass-json-stream](http://npm.im/minipass-json-stream)\n- [minipass-sized](http://npm.im/minipass-sized)\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different from (and in\nsome ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with node-core streams and\nintend to use Minipass streams in your programs.\n\nYou can avoid most of these differences entirely (for a very\nsmall performance penalty) by setting `{async: true}` in the\nconstructor options.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.  Thus, data\nis emitted as soon as it is available, always.  It is buffered until read,\nbut no longer.  Another way to look at it is that Minipass streams are\nexactly as synchronous as the logic that writes into them.\n\nThis can be surprising if your code relies on `PassThrough.write()` always\nproviding data on the next tick rather than the current one, or being able\nto call `resume()` and not have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no way for\nMinipass to achieve the speeds it does, or support the synchronous use\ncases that it does.  Simply put, waiting takes time.\n\nThis non-deferring approach makes Minipass streams much easier to reason\nabout, especially in the context of Promises and other flow-control\nmechanisms.\n\nExample:\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ async: true })\nstream.on('data', () => console.log('data event'))\nconsole.log('before write')\nstream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// data event\n// after write\n```\n\n### Exception: Async Opt-In\n\nIf you wish to have a Minipass stream with behavior that more\nclosely mimics Node.js core streams, you can set the stream in\nasync mode either by setting `async: true` in the constructor\noptions, or by setting `stream.async = true` later on.\n\n```js\nconst Minipass = require('minipass')\nconst asyncStream = new Minipass({ async: true })\nasyncStream.on('data', () => console.log('data event'))\nconsole.log('before write')\nasyncStream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// after write\n// data event <-- this is deferred until the next tick\n```\n\nSwitching _out_ of async mode is unsafe, as it could cause data\ncorruption, and so is not enabled.  Example:\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nsetStreamSyncAgainSomehow(stream) // <-- this doesn't actually exist!\nstream.write('world')\nconsole.log('after writes')\n// hypothetical output would be:\n// before writes\n// world\n// after writes\n// hello\n// NOT GOOD!\n```\n\nTo avoid this problem, once set into async mode, any attempt to\nmake the stream sync again will be ignored.\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nstream.async = false // <-- no-op, stream already async\nstream.write('world')\nconsole.log('after writes')\n// actual output:\n// before writes\n// after writes\n// hello\n// world\n```\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer, returning `true`\non all writes until the limit is hit, even if the data has nowhere to go.\nThen, they will not attempt to draw more data in until the buffer size dips\nbelow a minimum value.\n\nMinipass streams are much simpler.  The `write()` method will return `true`\nif the data has somewhere to go (which is to say, given the timing\nguarantees, that the data is already there by the time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and the data\nsits in a buffer, to be drained out immediately as soon as anyone consumes\nit.\n\nSince nothing is ever buffered unnecessarily, there is much less\ncopying data, and less bookkeeping about buffer capacity levels.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written all the way\nthrough the pipeline, and `write()` always returns true/false based on\nwhether the data was fully flushed, backpressure is communicated\nimmediately to the upstream caller.  This minimizes buffering.\n\nConsider this case:\n\n```js\nconst {PassThrough} = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage, and\nmultiple event deferrals happened, for an unblocked pipeline where it was\nperfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead someone reading\nthe code to think an advisory maximum of 1KiB is being set for the\npipeline.  However, the actual advisory buffering level is the _sum_ of\n`highWaterMark` values, since each one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data written,\nor _ever_ buffer data that can be flushed all the way through.  Neither\nnode-core streams nor Minipass ever fail to buffer written data, but\nnode-core streams do a lot of unnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less stuff and\nwaits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing any data\ninto it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't want to\npotentially happen immediately (for example, closing file descriptors,\nmoving on to the next entry in an archive parse stream, etc.) then be sure\nto call `stream.pause()` on creation, and then `stream.resume()` once you\nare ready to respond to the `end` event.\n\nHowever, this is _usually_ not a problem because:\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not yet have had\na chance to add a listener.  In order to avoid this hazard, Minipass\nstreams safely re-emit the `'end'` event if a new listener is added after\n`'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream has already\nemitted `end`, then it will call the handler right away.  (You can think of\nthis somewhat like attaching a new `.then(fn)` to a previously-resolved\nPromise.)\n\nTo prevent calling handlers multiple times who would not expect multiple\nends to occur, all listeners are removed from the `'end'` event whenever it\nis emitted.\n\n### Emit `error` When Asked\n\nThe most recent error object passed to the `'error'` event is\nstored on the stream.  If a new `'error'` event handler is added,\nand an error was previously emitted, then the event handler will\nbe called immediately (or on `process.nextTick` in the case of\nasync streams).\n\nThis makes it much more difficult to end up trying to interact\nwith a broken stream, if the error handler is added after an\nerror was previously emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data through the\npipeline when a new pipe destination is added, this can have surprising\neffects, especially when a stream comes in from some other function and may\nor may not have data in its buffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nOne solution is to create a dedicated tee-stream junction that pipes to\nboth locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nsrc.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners.  The first one\nadded will _immediately_ receive all of the data, leaving nothing for the\nsecond:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\nAll of the hazards in this section are avoided by setting `{\nasync: true }` in the Minipass constructor, or by setting\n`stream.async = true` afterwards.  Note that this does add some\noverhead, so should only be done in cases where you are willing\nto lose a bit of performance in order to avoid having to refactor\nprogram logic.\n\n## USAGE\n\nIt's a stream!  Use it like a stream and it'll most likely do what you\nwant.\n\n```js\nconst Minipass = require('minipass')\nconst mp = new Minipass(options) // optional: { encoding, objectMode }\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n* `encoding` How would you like the data coming _out_ of the stream to be\n  encoded?  Accepts any values that can be passed to `Buffer.toString()`.\n* `objectMode` Emit data exactly as it comes in.  This will be flipped on\n  by default if you write() something other than a string or Buffer at any\n  point.  Setting `objectMode: true` will prevent setting any encoding\n  value.\n* `async` Defaults to `false`.  Set to `true` to defer data\n  emission until next tick.  This reduces performance slightly,\n  but makes Minipass streams use timing behavior closer to Node\n  core streams.  See [Timing](#timing) for more details.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and `Writable`\nstreams.\n\n### Methods\n\n* `write(chunk, [encoding], [callback])` - Put data in.  (Note that, in the\n  base Minipass class, the same data will come out.)  Returns `false` if\n  the stream will buffer the next write, or true if it's still in \"flowing\"\n  mode.\n* `end([chunk, [encoding]], [callback])` - Signal that you have no more\n  data to write.  This will queue an `end` event to be fired when all the\n  data has been consumed.\n* `setEncoding(encoding)` - Set the encoding for data coming of the stream.\n  This can only be done once.\n* `pause()` - No more data for a while, please.  This also prevents `end`\n  from being emitted for empty streams until the stream is resumed.\n* `resume()` - Resume the stream.  If there's data in the buffer, it is all\n  discarded.  Any buffered events are immediately emitted.\n* `pipe(dest)` - Send all output to the stream provided.  When\n  data is emitted, it is immediately written to any and all pipe\n  destinations.  (Or written on next tick in `async` mode.)\n* `unpipe(dest)` - Stop piping to the destination stream.  This\n  is immediate, meaning that any asynchronously queued data will\n  _not_ make it to the destination when running in `async` mode.\n    * `options.end` - Boolean, end the destination stream when\n      the source stream ends.  Default `true`.\n    * `options.proxyErrors` - Boolean, proxy `error` events from\n      the source stream to the destination stream.  Note that\n      errors are _not_ proxied after the pipeline terminates,\n      either due to the source emitting `'end'` or manually\n      unpiping with `src.unpipe(dest)`.  Default `false`.\n* `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are EventEmitters.  Some\n  events are given special treatment, however.  (See below under \"events\".)\n* `promise()` - Returns a Promise that resolves when the stream emits\n  `end`, or rejects if the stream emits `error`.\n* `collect()` - Return a Promise that resolves on `end` with an array\n  containing each chunk of data that was emitted, or rejects if the stream\n  emits `error`.  Note that this consumes the stream data.\n* `concat()` - Same as `collect()`, but concatenates the data into a single\n  Buffer object.  Will reject the returned promise if the stream is in\n  objectMode, or if it goes into objectMode by the end of the data.\n* `read(n)` - Consume `n` bytes of data out of the buffer.  If `n` is not\n  provided, then consume all of it.  If `n` bytes are not available, then\n  it returns null.  **Note** consuming streams in this way is less\n  efficient, and can lead to unnecessary Buffer copying.\n* `destroy([er])` - Destroy the stream.  If an error is provided, then an\n  `'error'` event is emitted.  If the stream has a `close()` method, and\n  has not emitted a `'close'` event yet, then `stream.close()` will be\n  called.  Any Promises returned by `.promise()`, `.collect()` or\n  `.concat()` will be rejected.  After being destroyed, writing to the\n  stream will emit an error.  No more data will be emitted if the stream is\n  destroyed, even if it was previously buffered.\n\n### Properties\n\n* `bufferLength` Read-only.  Total number of bytes buffered, or in the case\n  of objectMode, the total number of objects.\n* `encoding` The encoding that has been set.  (Setting this is equivalent\n  to calling `setEncoding(enc)` and has the same prohibition against\n  setting multiple times.)\n* `flowing` Read-only.  Boolean indicating whether a chunk written to the\n  stream will be immediately emitted.\n* `emittedEnd` Read-only.  Boolean indicating whether the end-ish events\n  (ie, `end`, `prefinish`, `finish`) have been emitted.  Note that\n  listening on any end-ish event will immediateyl re-emit it if it has\n  already been emitted.\n* `writable` Whether the stream is writable.  Default `true`.  Set to\n  `false` when `end()`\n* `readable` Whether the stream is readable.  Default `true`.\n* `buffer` A [yallist](http://npm.im/yallist) linked list of chunks written\n  to the stream that have not yet been emitted.  (It's probably a bad idea\n  to mess with this.)\n* `pipes` A [yallist](http://npm.im/yallist) linked list of streams that\n  this stream is piping into.  (It's probably a bad idea to mess with\n  this.)\n* `destroyed` A getter that indicates whether the stream was destroyed.\n* `paused` True if the stream has been explicitly paused, otherwise false.\n* `objectMode` Indicates whether the stream is in `objectMode`.  Once set\n  to `true`, it cannot be set to `false`.\n\n### Events\n\n* `data` Emitted when there's data to read.  Argument is the data to read.\n  This is never emitted while not flowing.  If a listener is attached, that\n  will resume the stream.\n* `end` Emitted when there's no more data to read.  This will be emitted\n  immediately for empty streams when `end()` is called.  If a listener is\n  attached, and `end` was already emitted, then it will be emitted again.\n  All listeners are removed when `end` is emitted.\n* `prefinish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'end'`.\n* `finish` An end-ish event that follows the same logic as `end` and is\n  emitted in the same conditions where `end` is emitted.  Emitted after\n  `'prefinish'`.\n* `close` An indication that an underlying resource has been released.\n  Minipass does not emit this event, but will defer it until after `end`\n  has been emitted, since it throws off some stream libraries otherwise.\n* `drain` Emitted when the internal buffer empties, and it is again\n  suitable to `write()` into the stream.\n* `readable` Emitted when data is buffered and ready to be read by a\n  consumer.\n* `resume` Emitted when stream changes state from buffering to flowing\n  mode.  (Ie, when `resume` is called, `pipe` is called, or a `data` event\n  listener is added.)\n\n### Static Methods\n\n* `Minipass.isStream(stream)` Returns `true` if the argument is a stream,\n  and false otherwise.  To be considered a stream, the object must be\n  either an instance of Minipass, or an EventEmitter that has either a\n  `pipe()` method, or both `write()` and `end()` methods.  (Pretty much any\n  stream in node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass streams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(() => {\n  // stream is finished\n}, er => {\n  // stream emitted an error\n})\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one chunk for\nyou, but if you're going to do it yourself anyway, it's convenient this\nway:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in platforms\nthat support it.\n\nSynchronous iteration will end when the currently available data is\nconsumed, even if the `end` event has not been reached.  In string and\nbuffer mode, the data is concatenated, so unless multiple writes are\noccurring in the same tick as the `read()`, sync iteration loops will\ngenerally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written, with no\nflattening, create the stream with the `{ objectMode: true }` option.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i-- > 0)\n    mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume () {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write (chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end (chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(new (class extends Minipass {\n    emit (ev, ...data) {\n      // let's also log events, because debugging some weird thing\n      console.log('EMIT', ev)\n      return super.emit(ev, ...data)\n    }\n    write (chunk, encoding, callback) {\n      console.log('WRITE', chunk, encoding)\n      return super.write(chunk, encoding, callback)\n    }\n    end (chunk, encoding, callback) {\n      console.log('END', chunk, encoding)\n      return super.end(chunk, encoding, callback)\n    }\n  }))\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit (ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write (obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end (obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor (options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write (chunk, encoding, cb) {\n    if (typeof chunk === 'string' &&\n        typeof encoding === 'string' &&\n        encoding !== 'utf8') {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk))\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      try {\n        // JSON.parse can throw, emit an error on that\n        super.write(JSON.parse(jsonData[i]))\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb)\n      cb()\n  }\n}\n```\n", "readmeFilename": "README.md", "gitHead": "e5b768d2b89c5a5be776362e913e35a6707c6df7", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.5", "_nodeVersion": "18.4.0", "_npmVersion": "8.13.2", "dist": {"integrity": "sha512-rQ/p+KfKBkeNwo04U15i+hOwoVBVmekmm/HcfTkTN2t9pbQKCMm4eN5gFeqgrrSp/kH/7BYYhTIHOxGqzbBPaA==", "shasum": "6da7e53a48db8a856eeb9153d85b230a2119e819", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.3.5.tgz", "fileCount": 5, "unpackedSize": 48119, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/RkRbZq9sY4+Nkv3kzsjmiaF2sDHe/zB0801c2MNepQIhAK6wIUFsrwKnS2H6Gy6X7LfbGK/KXEbkrb7sw1wV2DlW"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi3cZjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0bw//T4iPy194D7TcF+SrVH1fDKGKluOt7ZJpUgumIfU6Rh8p1aeM\r\npYN1m9q42Z4C6/id4MTg5Ehx2bw9VgZHoOHMknbaVt+l9kreQSdXr+xGC1tg\r\nnDh0Ots4T8moMLSyLe8ejBtaSGwmSjTZ48Gg3cWrCdH3x2yWzsinsRYx6cUC\r\ncx3sA2dWkevRvO0jr4bpGwCzDL33NReyJAuTcKypJKtkcvyhkrY2bZ5sJIFO\r\nVbr3ORHRk6W0w0msm2th8mpbP1vr7+34QTYJAmHahyRxs/kmz6QiJU8lKR2M\r\nGOjjMFixOmbCvechYkAy/thZvdX4kmnSEZRkEUSodmC0CWjxxgyr6WGWQkel\r\nfekv8X418+ZvrwxmzVF2kO+6Y03EOVBwNE+W9W7IwxwS4DeFCOVZlZDZYgd3\r\nB1MiMnujS34sod1a+RbwQ5ohYt8WVVj2y9ZEVH5FsFMeb0l4Wgq/RbFd+ebV\r\nWuPkrUg8Bfuv+/1/53/kzyRUWxBlhtNP579kOSn+NM0tgF3RN7pkzkai3fsc\r\neIJtxL3Zsbya9sAdwIAwwmRNsgt6ciCAH5ue9ypypw4pPRhsrE3DhFOFU0Fy\r\n/+OWUiB7IZ/iQJRCyI1sUR8i2rmzo3QdMRMav7xJ9Ya7RoqEUO2PlK1OPmUq\r\npES2Tt115bh3jEZSXzabU2MB4RJCXK2q9tI=\r\n=jmBv\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.5_1658701411484_0.19872163702440693"}, "_hasShrinkwrap": false}, "3.3.6": {"name": "minipass", "version": "3.3.6", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "52ab642fa447419dca139ce29fad780dd61a27af", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@3.3.6", "_nodeVersion": "18.12.0", "_npmVersion": "9.1.1", "dist": {"integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "shasum": "7bba384db3a1520d18c9c0e5251c3444e95dd94a", "tarball": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "fileCount": 5, "unpackedSize": 48090, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICmszgh/M4TJhGylTbFqjQUzPzClnto3r6OliGcTp3y+AiAiNeWHb8PcbXkrt2SUIzL/A8WTAjcux3k70LkTdjdkIQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjgHTIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoj8w//fGj3vpiV9bJwZssFywHgmSLaZkdfFJ0hyuXS8M5+8mSYkQEs\r\nWx3vT+Z1f6Q4/UB0ZaFFlSOlE29hTjfTBoRp7yTH42ImYgnqJh9jlBnQuiB9\r\nbcGpEEvLm+xzNjqSryFPJ0AaZApeufHjFMW5aDgiFsTFH37BB4r/WB5WYUsi\r\nzVMPQWvdMMY8zKENQFxRYmOEgARHq7InF8F/YXkxIOwafGSReo90k8DFiIo9\r\ncK2gMR/TieF0NW51Ji1WjgJJlz2PeSfNveufKVgaMm7psECm0gpVWuu159io\r\nX8xQZhX8pd8grOx9UTL70Eas6010MPeoNKERFxDvVdQ+pE3At34SXluKd6+b\r\nKGT+xvrFruyQkajHRrUOY3Xto8+D48T2pj2BYyA+djYRkodwXVkKrJQMVog+\r\n8STrMhYF3SmVpLJT8etQ5dHDmkDbM4xQg5wxrAuzTiw63yOfZK3/YDVfcRmA\r\nRrqTjc5u29vpZxiSHCajI6ODDACNQa7m2TdE0j1LvgCHb0CD7heqSrXVdS2M\r\nUp8oH27tnYQEq11C711ULFPs+47ArnHDgNRjseOe1VhLWtNGBGuXIa7TJZj9\r\n5mHNO+13DDXtfQRiTrz9jRQgUUime8LAum3Z39v/BytWBSfVS93F7vVFwmef\r\nu+hUditRmZHNTHHRs93XAtdlz3GrdsGnFlw=\r\n=S0p7\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_3.3.6_1669362888281_0.589549775531871"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "minipass", "version": "4.0.0", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "94124ea6c999e9f7ff76551950ff1ed79431151f", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.0.0", "_nodeVersion": "18.12.0", "_npmVersion": "9.1.1", "dist": {"integrity": "sha512-g2Uuh2jEKoht+zvO6vJqXmYpflPqzRBT+Th2h01DKh5z7wbY/AZ2gCQ78cP70YoHPyFdY30YBV5WxgLOEwOykw==", "shasum": "7cebb0f9fa7d56f0c5b17853cbe28838a8dbbd3b", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.0.0.tgz", "fileCount": 5, "unpackedSize": 48333, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPDIJ4ateOm0BMJL2Wn4F/jna49gMZpJhOPoupYQbMGAiBza1iyVp25ZRg+Uzm86RTlcA4kjBBDY5fU6ymQLa6ayg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjgqyaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6hg//XhmGUtjrY3sZIwsT2Gzfg2dDXs9NPfch+PKnn6sxo8jKEtwu\r\nnMC5v5Cmm6EkGJCHfrjO7L/AXA/VeF3rw//SBbginMtSJiXGheeYTOlX+dic\r\nvmbUC88bAfg2SMjzXKIsslwXMzlWQY72lNwpiaUA7dXDor8QB6hOGhwyJv9F\r\ntktuy1Rb9S5pPis96G/cYbDWanrM3D6I8Kd4zpRWk18Ja8X6z0Bu/sBrNBgb\r\ntR23AGZDMvtAnsw6oN5/Yd/0gGzB8m8XX+yoPthcRWfFJlQPHWsFurL6QVoE\r\nJQ+imfIfmvQ6CUtRjVgXa2tT1VgDtPzS/GzuYpxJs/1YJPmx0fcvkqU12VEl\r\njyi045os4td9RuZLcClLPIVM8AiEwsBEZ5Decxtai3sc9zjURa7x+wz7IDxC\r\n78GRs9umpoJ26dKlyb54sS/xDTqdDmJJFQh3JsQOdVBKv0pnkEyTP59gC757\r\nUC6eG0cHi/tD8PCeN45rw5ZzAPeettyMCYQIyxgnlNGaGjRKoZigcHWhNOfo\r\no80xSGll6Cte+D776rCrk1tTnlo4OPWkvTGI0QIZVZgYiHz9F6RGn53Xp1Gb\r\nw6wR+hZpZ6D7d885ynCqtPkVa3TMbm0yWMm3+UFWHiZtDf+sF/KsiQbEipJt\r\nLPmFNGnT7n/PO0jXahoZDChTAFVKCIAJB/8=\r\n=Snz0\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.0.0_1669508250476_0.8615478666925138"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "minipass", "version": "4.0.1", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "7c89949841a2a7ee24909f5775a1fcdd5a7a4e22", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.0.1", "_nodeVersion": "19.4.0", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-V9esFpNbK0arbN3fm2sxDKqMYgIp7XtVdE4Esj+PE4Qaaxdg1wIw48ITQIOn1sc8xXSmUviVL3cyjMqPlrVkiA==", "shasum": "2b9408c6e81bb8b338d600fb3685e375a370a057", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.0.1.tgz", "fileCount": 5, "unpackedSize": 48335, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAZDcR+xEG3rSE4EcV3rr3Uge2bsL57LwypcV31nFUhcAiEAzEa98YRV1TDbnz4SoG/wfLt9TqoPKHEcEDARJHh5SbA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1+HlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiLw//WMpEs9CNL2DOb1A+/8ZjlHPVhseqIj/oCIADRmLfNeJgiX8i\r\nB7nlQRoEbivCeCNgfKivjJUn0UF8BoiapXCbwmhLsjH2WXpYGUmKqSwKVdwN\r\ncUHTqUmCirRhPJvjHTAroC4zgDYCvq/iXE9EB86jqqyZAyp0j7Ivuqi/w8Yw\r\nEkIfQdwuRVjkQtT/VzSS6xjFG1pf10uaVyk4dgxnSwm3LfuTxxe+vIGJhEj5\r\nso/ypytE+o8TxrN46AwaFIaQZ7Obg6o9bo6zgCMd3RGnDNR2pS8TLlkr0fhH\r\nyHyNPOxdi12snlhS7yiPa+rg8zfoKOAdNqmbLHKM3UNd1rQHoWOOj/P63AKD\r\n3MdBXQ5PU0EnB5cxhFA3uxSR68dTikk4wRuqKYdu9XHVZF/ayTDJMSAW81/u\r\ne+FfkkZ20/v9lGYg612Dw21uwyevyZ2wV9OAwdObiNgKAOWOkgjRkdnMAa+n\r\nU+oPFfcpjN3FchXwhklhJnbrqM7fSFTzTFyrvwvfY1a21NbHQ8vq006FYpzZ\r\nx2UtyA6hMyAiof10LB1FaImI/aneSMHviEGCCSyJnJeFrd40zezg4dUqBEG6\r\n7QZJMKKNfk/126mm7AitqnZw8GK89tPVTlLHEHCQeviRLShslxdAnCxQ9c+Q\r\n8H/8+N5cSfBShc5qlsS8ie5pfWklgBIJR70=\r\n=Yo6j\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.0.1_1675092453781_0.36220475255847173"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "minipass", "version": "4.0.2", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "c3ecced436ad9c884e45220454fc17df0db38daa", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.0.2", "_nodeVersion": "18.14.0", "_npmVersion": "9.3.1", "dist": {"integrity": "sha512-4Hbzei7ZyBp+1aw0874YWpKOubZd/jc53/XU+gkYry1QV+VvrbO8icLM5CUtm4F0hyXn85DXYKEMIS26gitD3A==", "shasum": "26fc3364d5ea6cb971c6e5259eac67a0887510d1", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.0.2.tgz", "fileCount": 5, "unpackedSize": 48947, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBZhwn6V5NxPoFs5Mhjk6dBbE7b0GAdDLScEioXXns7kAiAdn3DcGINaKKGacvq8ZLx4LowS9dcrOU7sTDYVDT2p+Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3+wlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp29hAAgbb5Gic3Ed10yyL5vdRlLoIYW7Bv5YEm62rk5t6qeI4/TUHK\r\n6arbnNTipx6AmnJmGtca4oFxHVlOtNJBlPLwEjLU5JLjYEYgyM4kQT3q7get\r\nfl5waybCilOyZ/HPHkpGfTDqgjLiU5F6eIEvBvES7CR/2k+kjos1/qeoII0w\r\ndDEQ8EXv3CJaoFvIIf95t2P1xiPw/+tmeOEimQrdHBGvIYHyMKd8Q6GFT79n\r\nTTzrmLV0MidebxFRj8HY+Hs5tIw8oycOI7LsGv9XXG0sNYTJsuiO1g6jyeW9\r\n5/ol9Rc8n+p0dkppoLVZiuF3j8cMnKp9JwL5hBLeBvHRFfsX6v18OHRh/88q\r\nwRY4zy7sFE37PtVrM9mMSp/JQU6MNkE5F8ec9vKLpWh+GjYe3rz3QTMhXDsL\r\nxfxA3sAwrud5QOv0Pgdp/4Z46zcNHB+zwzkjMAX8zS67wSxOPCAgBFSUk5b8\r\nu2FCxstlGU1n9eoTBNzcQxjrcBvfm3gdcAqipLQ8c/QiHouUMziVaYOTSRsK\r\nWZAc6sdiHrmDA+F2Tjx75tm9M5YvQ1xcX27Zwot4t/HbzywERWnN+JU3G5MN\r\nKxTCWhPTRo9nbn6ME+OZb1rdEgvmQ8MAnLRlBaIPtRNtIoAQ9Sranv8Gcp8B\r\nxflpiCad5lkXyHeWKx3IDfLDwLJWkBuB4qs=\r\n=UuTi\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.0.2_1675619365690_0.8194046899768483"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "minipass", "version": "4.0.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "types": "index.d.ts", "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "d9099429d9d1ee28e753e608d497a7f5d2300490", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.0.3", "_nodeVersion": "18.14.0", "_npmVersion": "9.3.1", "dist": {"integrity": "sha512-OW2r4sQ0sI+z5ckEt5c1Tri4xTgZwYDxpE54eqWlQloQRoWtXjqt9udJ5Z4dSv7wK+nfFI7FRXyCpBSft+gpFw==", "shasum": "00bfbaf1e16e35e804f4aa31a7c1f6b8d9f0ee72", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.0.3.tgz", "fileCount": 5, "unpackedSize": 48953, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICuPLljEchGhhVxX6VvWTJTvKbW5KXUlkA8WNO2bWAKmAiBG0cQiMfTNcIIu8LbBK5C2p9jRfHvRhDi+CJyB9uJPhg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4sl1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokEQ/6A/sOsPN6S5RHsKB3mHglMo7YsoDg8ftBjLK+T8+kP8ttIJGV\r\n53t2ffJfCv9vcG6ZXAUxVZtFMGe6w0CcAnD88KtHHRh0zW/Y5wxtzPHH8hWn\r\nR567H+lZ/U3ZWRpx+2ugFUzFKZOs/87i8fVwpVRk3UI/W6PmjwRsCKdp1a62\r\neeCEF9/uSb1oQkO2wXywIiBxIH86iNNAhdiBhwjZ5eMXAWCT1i6RK2E7Nc9x\r\nTvP2tFmsdcSXqnReV25hJK6bVDNRCfSZoMiZv62Rh5Hpi79Nz4CZjv+bccPH\r\nbmfLeZIS/mrIGMScerFcn0To3DvmCw3nJON4V6bKPAXYXw5u6neYvm5AfFyl\r\nikRA4/Iv8rVj15b4NJAcizTvc8mu+HkCLRhp4jVFqIXtWbJohLrt8nryVNyf\r\nTZh0I/HZpV6EbA3bVYGe9LSnQy9+mgvVPcvq8SAqOg3Q115VDcKIif9S810w\r\nQrS5f8jT3140MhGybfAcdeaKmB9vjdYd1HjYEaNWE+t2szVUMcO0AY+vMM/R\r\nNOz1LADZmeEAGgHtRUIlDNeAdC9K77lBz1ipSHL8t/OutDuyKLTAs3ZFLRPv\r\n7+sgcefub1DBE3PiZUza9oKos3nr222ZfQgnmwaeUFcbk4D9jEW3XPAnxEjH\r\nh6DOKURcbXfvZR49XzdKQsoyBFT5ID4gJJ8=\r\n=3JHs\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.0.3_1675807092995_0.583231517344913"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "minipass", "version": "4.1.0", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.mjs", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "1eb74cf5efce01c555afe55a2c21afc09ea75a1a", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.1.0", "_nodeVersion": "18.14.0", "_npmVersion": "9.4.2", "dist": {"integrity": "sha512-WHxk07h4cIpCokP6qy2YPIJLk/5ELgqW7c0uxzhZIDXteqRd2YevFi0+ZjTPQ2Y8Z6w1FUW3HA9QzJ1UdaC8rA==", "shasum": "572e5b64ffee9ff8abe7a48d01906160c1ce9e08", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.1.0.tgz", "fileCount": 6, "unpackedSize": 67225, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH/KGTwKe8FG9WWwmLwbIkrdMgol9h1Li0zRqSwadTmaAiBt8aaCQbh/9v1PX7Qf205kj7oWZyVgBxe5nlG7oFQx/g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9Zf4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZjQ/+NO3xOwRpMX3seYQ1GbgezXXPDi1drzI5yny8/UZUwAQKiidH\r\nyQdvpDMPo5PfFL14igIACa/coDHPaKEWvOhCIJcwkHU6lOJxB5EH4qe75qhN\r\n5C1V0q6C00LnhL+z4trYa+ag8JenxYFkDqENTt8HQB0Frpvh24JabA8j6zC/\r\ne1kj9dSEQ/LUB0VyKa/8iWjzND0/32qrvwwPfAuk/1/sPI7RgXM2JCEnKj+M\r\nuJZ7z9r+aRfGC49yg8e/FojPtbYldsaAPLH9n6OTh9hPOqpWxxeYb2iO/mlD\r\nCLw5BgNaXRgwIhL9Q0n0Ct0CwBjBJjtdFzs4XfjOfBJfz62R27QNHAftm5ER\r\n9KFvyLiPGUr7k/k3eX8VI0kUT4/ypWVRSc8E71OmnDWZ6Acrub4Tl/uX3+o5\r\nGfifq0cdfasV+akAyABKpHwpFoQ3fy+FAP0JmYLfMqMNSPlXLmn9vu6zsYLG\r\nwZwMu/sd7k7VlZgF5jkjoe0AzFyCbgx8NQdE5LLVFoORbfCtX5evvIA22SlH\r\nb/DnYqVuJ7jiRuzC81YyNSMpYTgSVQ3iREYp7OpCtVCuaqdC1VyDdRSerboJ\r\nkh9BH5gYzHrpYep51pcAP+WezheauS9B7jZwaKrJZQuENu2VhX2OFTT/xjor\r\nH/cSNS4pJT3tIk+lw7jsxsJy33qSh3zsSXo=\r\n=5VqH\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.1.0_1677039608570_0.43776795906501187"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "minipass", "version": "4.2.0", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.mjs", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "0b77950c7b87a41e58aee0983429d23f51e77e08", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.0", "_nodeVersion": "18.14.0", "_npmVersion": "9.4.2", "dist": {"integrity": "sha512-ExlilAIS7zJ2EWUMaVXi14H+FnZ18kr17kFkGemMqBx6jW0m8P6XfqwYVPEG53ENlgsED+alVP9ZxC3JzkK23Q==", "shasum": "4bf124d8c87c14e99846f9a27c3219d956998c0e", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.2.0.tgz", "fileCount": 6, "unpackedSize": 70644, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+ith3Wb0Fla7RTCtzg4mHVqY3fv91EsuixRqAJP3ZtgIgTqIA3hEJF/kVyS90PQMTFUNKzOSiejTiBxUF2OzGAME="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9apFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8dw/+PotqTr1yCIPjJXclHAKNmOX7wjS+3XQWE18L+e6HA91jcClz\r\nWXCunj0/uHIft32pjVNfZk0/0vEg0bBKW44Oecj8zLKLSs7c4lQk+FgOZR3n\r\nt3qD/cbMzsr8PbEansYx0lRVJd+kzOdyX3k4Ii5PnC8QhRNDHYa9NcbMvt4y\r\n6ENSE6G10EOiNrVnTgb4x44OWPodAsX1gyxJUbfzpC7uSXnsI81Heqielvta\r\nIimjmSONMR3TUgTePvLExTflvSkceMfS1Zw13PyA83oP5U06GxU3lwjb7MLj\r\nBh7uwh3fbWYa0d+/vv8aIyF9g4hCy7kw8DYTbggCSqdmHmzT9F7EjgDWZyh+\r\n2yAxJUWxSDzKZjIdRDeql6z7cRAc+IKyCNx91PTnrPZB0ccXH3rYtASFsiDi\r\nuUgqvfvlgHJrKtzhnL6IfR45oPV7DXrs5p8fLlz3Y1vZMQbwdCQX2CU70cIK\r\ngunyu66l5BJvBJdsv7HuQybIBS25NYPaDyTzp0HeXakX72UpNonbzr4LBlSw\r\nXker6fYIbXzPijk5JignsLTEVFK6a+OuiJvmGv2lhXEcvYpTq6u0VBCiNJpN\r\nfAxnLBHTcLe/fys4BsxMeh+TyblSSSsFxZwxzayWyJ3AevSKhODRipJhqKJL\r\nRVrtIxeYq/fqdDfGmEqnuatxHSrRit4B5cI=\r\n=RNqi\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.0_1677044293765_0.8526737717005475"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "minipass", "version": "4.2.1", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "ef1f48933b9260b2664a942b1bc7ca9d8b640991", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.1", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-KS4CHIsDfOZetnT+u6fwxyFADXLamtkPxkGScmmtTW//MlRrImV+LtbmbJpLQ86Hw7km/utbfEfndhGBrfwvlA==", "shasum": "084031141113657662d40f66f9c2329036892128", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.2.1.tgz", "fileCount": 6, "unpackedSize": 70577, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFeaYcULYtuv7cQ9joXv6myvvjTDFETiJRdMuuXNaDPCAiB2wr4mluIM1BJXWUlVz9M8dJaot3n2X9MrDXMnetIlVw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+EG6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCVw/6A+6sbk+no1ptWXJpysHf9qTbVuactq4RWE4vIy+2eTZA8c4Z\r\nv99ZPI0F6d7gDEzZf7LQB0GomddxusWERGNwwU82DjLunk6WhTOFuHADQ4gB\r\n1PCh3b/+M2tcqwkCE1Vyyq3QzF0rsMeIsOTkxI1LVqBD6ZVl5XdIlJguRwMg\r\nvn6EtffS/QQfla+UJP1829QhTw/UtAbqI6eeng33r1e0sEbtuUiVg64tGhKs\r\njfysQIg1QeCVCMgjagPgaZrE7aOTPr9P2+nkoFr8ILRaXU+hQeMDZchZVxH7\r\nL5wq4ZmRZqmupcN0CMCYHhhoaYxGmv1k5kGAibkIQXVvDU9U6K/B1MEzF2Q/\r\nKyBWIQHLwae6aednzAwjeyojOWWD8rJV3yqdPgGo7ZXrQhSbtRTwGVykM4ow\r\n+qcpUtSnzaH+j9jjxzEOTMTL5SIPDir32JM6K1Y93MJqUAzoVnpBRJKPx+Rn\r\nGddXKjnvOPIESrv385cYhwqmAlZRyLF0p2z3Q63YCkUzSNe3gMmeXgAjim2e\r\nrl4s6IH9TTYHCgLbN/kLo1J0BDYpKlbOxKBJ9Ul6ORJrlEOFmpRlCG3qOABL\r\nObCRvMQ/qGcLfi+veGiBLZRuQMCxTKrBBTbZDAPsydKVxL5DBEmu3AsrOhmg\r\no3ADCGOnlMlp/ydERkytmvjamT/o9xC3epc=\r\n=mHh5\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.1_1677214138419_0.3678700408590496"}, "_hasShrinkwrap": false}, "4.2.2": {"name": "minipass", "version": "4.2.2", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "16837bea69197dcfb3b3534d0747d062e17ac473", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.2", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-CK/S6dX/gmBq0YF4FGPlvsv5O0WH6YVwEc28xJiTNjkstRGPYA4S7lfrGTqE61YydECWC68pYSUt9aP8yC70PQ==", "shasum": "4f35a099272b23d0cfe26c0dcea2a7b772aeb809", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.2.2.tgz", "fileCount": 6, "unpackedSize": 68732, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDUO0VdLd8d7DGGWDh4+jDOex28jlMnPpRizgQIWFuFBAiBVlM+7HbltBTE+gJHHaqj6XX5mxvqOUw5JMg0PuNfXUw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+wfQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmospRAAmDESxQiok1WWGiZxXkQT6lniXQGvgFXntqTsVD3FE6P3EJtR\r\nc5E1faThPP4TjcPt/Turw1vVGtyKD0zvY+48I1LDVLqWkB/YA6VPvPNoiK8Z\r\n04gTR43uowXMhH1BsYr7iF1hXMqKNzZfby74shVEzSMoXCeBLj2DinY86pNL\r\n6JiZWWSTzu4Vz/5UM5EqLqydJqNSAUtGo+hfKub7IAAFRbmB+0n2ZxllpUNc\r\nMHUdbY/d+cSTeMSihGaFSg32JAREi6RlfSvcimuMdmr/2TFOLKl13WR1AefQ\r\nx7LY6qmxdaThqLLKsyxopoLL6s2i8jjZ5UDjXpMdLn5QBY6Si8rdfpeeqBOZ\r\ntZ2EhqC/0aR0QDEW5/sYWUvBbY0w3TBWUiKtCG0tVPgI+yjhhfb6cpGeLgc5\r\n2HvZcONI3w0CZSIdfCWoLJJYMrOwhQk/xkueta4hZOOsvy6rVQDle4zmKtlG\r\n7FzK3MnWXkTP7kqbBVa8RZoe2F2Q9CZ2wR8owQS9X1EyDAszmk8uEwrjphVo\r\nk5waArRofMaEkMQrrxG4tE57twsQrtPv9TIozKE0QKqrShhUG2Lz0KFJXVOC\r\nf2BDF3v6Ei2O3uxR0I4/EfWfpAqlVuX8za8w7UP8CVGG+4PdgzrZGPaYROvw\r\nGbE3Rj5KBqtZ2UeGdEPAVv1kZ30T9UXcuq0=\r\n=nuI7\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.2_1677395920672_0.6444427884982831"}, "_hasShrinkwrap": false}, "4.2.3": {"name": "minipass", "version": "4.2.3", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "3d63d733bdcb1eed77ed7947977d85643287af60", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.3", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-iYSQ8k1lVu79HJdl033DCtz73XFBUO1cg45QS8nalhkvV3KE+G3LYj/2NBZjjILpqhshq8rfJfshJJnjdeJl8A==", "shasum": "5ee9b1ad67dfc916ce7cec86e5260fc61da64376", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.2.3.tgz", "fileCount": 6, "unpackedSize": 68804, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBEUjFBAqopAIfx87mj57/qwjDfGRStD+vBVFES65xOHAiEA5LRdmtmL0heguC/qiFS7FISNj7oK4Z7Yts/4jSoUgLI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+w4qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrG7g//QxfYGSXkuh8drWcrBeRPUJ263oe8NVtVkUhEQVgLbKt79Bfa\r\nxev5FPMskX6HgAcZ1hiHLz/TZtuNm15zDK6u3tyGjCOVTJbZM+usgO8YOh5t\r\nxoJZyFSsdwvrLnCkRtvdrcixoZkZ4OKRXOoZxhzZbw1SPkRdbf7wHGh/92cv\r\nqvmNrylD4bQQK45XpK8KVjCGNeuILXdl0elaWUG5RzTf07qfLc6jUpxQe8+n\r\naKHMcjemY+7Fk4aKFdBI2VzlMGGQ/MY+cc+pIFQ0atrOZloam1zgHuUDxl8y\r\nrdaHFiKpLVOCYut5eSDDrVHRpqIwfTx5a/7lALZzQQp+fxdEJzN0vaaQDd6G\r\nSsO6+xvrZZzixzq3XTtrIyXOuJOLzY1hvhf+fnGpxdJMAR4Ce11qfqgOdxD5\r\nEoLlJMdaP278dowNjq3UaZ4EnmQOzzag9nL14ooYgUBK9xqh4llpA2SNeP+H\r\nrK9xqLNun17s2FwsIzIydKhwkqUo5lBUligVCDvUqrOEY1I6IqYVCrOdUObG\r\ng4UsYHYlciIrOwZwAZ1JGtjSrEG7t3AjXn9YHyTBtlRmEF9CKRsQFrcZ8Qfg\r\nQEroxoJaxzIT1LiSuDDaIRZ0p2eGnWAI5l4zBUgqNRfZNkMTbS5N2l7wcqRs\r\n+naqXvKZo0LS7pOqT1oaT6fbxSNp2TLCi74=\r\n=RcLq\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.3_1677397546259_0.8851282963016653"}, "_hasShrinkwrap": false}, "4.2.4": {"name": "minipass", "version": "4.2.4", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "8a5e3921179c0ca58683678858f9496d30bddbcc", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.4", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-lwycX3cBMTvcejsHITUgYj6Gy6A7Nh4Q6h9NP4sTHY1ccJlC7yKzDmiShEHsJ16Jf1nKGDEaiHxiltsJEvk0nQ==", "shasum": "7d0d97434b6a19f59c5c3221698b48bbf3b2cd06", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.2.4.tgz", "fileCount": 6, "unpackedSize": 68932, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAtSJW37qXfjz8QSrskkBSajAq7oVZErjBvgBnzcpCg6AiEAx9ZwWEE7uzzqn13qc1XvP4mABHeEDpbWB2Vzol0w6Xc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+w9kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5TBAAgfKwb7vEVSu4YQETeXlBI6Z2kwgNybfPYVYDZLxlJSB1y2hx\r\nZMmePBgm5aCFc/aYm4jaVV+8Oej74vWawR7JY9UhwdNp1K8bwp2C3xSvtAGw\r\nQHHiA8qp82u7mflg0c4CAw3p+oJq7S8WH8hacCEvURTEuWnPGqFV7EBfvLwE\r\nFIcwm/vEJUEymbQ6CJZ1OWbiO2V4wmMFyDFBrOrJWhhJnZLY/kVweaOoja9y\r\nWpZrJnHwe2K26HXuhj39ZSEe6ZwLDxxAnNPT3rIAj5G4cPi8WA26hoA2mzLU\r\nZcFObomkG5DlMEcLx4UBczl6Seyc/r3KLjII9oNvd1BwVlSGfWSll+MxCCy9\r\njNqqnuB83uvfe+SGY7Gb6kNJkiLHJVTmUeoU8pUV84Qk06GLVCHbDWLOdgUD\r\n8bfx5rqL0/Y7xl8/Qy4UXMR2eUfHI39ZpzD4Ga8Of7LQmpK9xu1V3GTQn6Po\r\ny/9HTDNKAkksKHKlXwoammGKmvi31iWOcy2ZnZ6DxyW431gSsTdSen8snpZ8\r\nZetDgiofd02PuhG2cOsa0n29+dI058yzEYCwUTJC+mBUXQaPd1uaPx35HYyg\r\nCYTWkUROX3InkWjDNMNKUESC8R3no58DEAiHgfhA8xsRfjQ9MUcFxpn+eJjp\r\nhUD0Fh+SbxTlptbBg48HmoH+DtbHe5LRqbU=\r\n=m/1i\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.4_1677397860387_0.5042330666279724"}, "_hasShrinkwrap": false}, "4.2.5": {"name": "minipass", "version": "4.2.5", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "e3b98a071233fd4f4054ab7437f1d9a3bab71ce3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.5", "_nodeVersion": "18.14.0", "_npmVersion": "9.5.1", "dist": {"integrity": "sha512-+yQl7SX3bIT83Lhb4BVorMAHVuqsskxRdlmO9kTpyukp8vsm2Sn/fUOV9xlnG8/a5JsypJzap21lz/y3FBMJ8Q==", "shasum": "9e0e5256f1e3513f8c34691dd68549e85b2c8ceb", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.2.5.tgz", "fileCount": 6, "unpackedSize": 69380, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHwHdkGTpCWWnPtaHirVQkfvMHVwUMg/F4HpBAIf2ot/AiEAr7CHZjPHwHx3xh7yfmUBDFk9Pbvpol9zQhUxF8ChYv4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDNYiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUVQ/+PRTy7bfR4mObrOvtgacZLKRo8jCZot085aJrva9inVOip35C\r\n1263ejxV2/ljcNA2MnQecqCvwRbwXu7CfWPNYFDFxu/SJg1GOYjtf5pAz1qy\r\nc+/P7YKrNloDevbfZ3JjXheEeGb+cMigtbiPGvFpQx3irzKZVgPiRowrkIrW\r\n7A/jeFtnsVCfQRKJqx4fA1yuN55MeI8896VPZnoXngXQAH5nrQjhGbY7EDj8\r\nqOrcYECZ2y3ANb4fnqv3e7/U+g5T0yewQwWwC49rzBXQGYLiJYZXk/eXJ8wo\r\n0rPl0YhyyNyCifPVnXkWUMyInk/OXJMd5jGPuAVf7HVwCXN71LfK1+AjjX+3\r\nVgBeBZbc5SqL8M9ug7sMUFhhwU9kgXqJ12+djZQvEc3ph36K6Cu5WyX5IHAw\r\nWHnflo7/hx8EaIj+OxxCm7v48QbDNE/PISyqOyfwf4c+hkwgUvl3K2/nHOJ/\r\n88Xk2jV+1+T0MVcoVfhIieeGf+5BokvXeElBm/kIT2wVX3aJHs7338d7NhfI\r\nlNy12gtxMi0Qub7ndWqQoxd7dRWseXLwOe4U080uaKr9/WC7zMT3tZaWvSOa\r\nSL3LDmNe/v3BcXamg3iAaws4NVofo4YFegQZgNqMKS9J4FtDk2X0P/nP1AdZ\r\nmrKFrSJ2j3UdsMboR2uvQaE1v+3BtQe8dF0=\r\n=5Lps\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.5_1678562849994_0.47969510867769394"}, "_hasShrinkwrap": false}, "4.2.6": {"name": "minipass", "version": "4.2.6", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "4f37bc74563af7f5c2ff131648112e5ed9e1d72d", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.6", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.3", "dist": {"integrity": "sha512-99el+wjSnfeQqHTP/mKgFh15BXIk347QvNZ//yBGDbkYtyS4ZeOoIuAf16v+R4oCmuaYavIopwW0KEQEuUMn2w==", "shasum": "43c56f3890214d24b5d63f70e8ee97b2fb632df3", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.2.6.tgz", "fileCount": 6, "unpackedSize": 69463, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDS+nXq7/96s4C4hYXXasyrAtt6oTKrWcMc/x7bEM6TdQIgcl0gHxUaBHgBp3f5OZkW+Mh7+TCGjulAYMiUD0/Nyjk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMydbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLZhAAoLDXZ2dMjUXVHeVQC1xHHBd+kkkGmTboAiF58ixSxySCvM2J\r\nY5iCZKA3FYNk4nS+3dMh5BqeddkBBPsOfy1EGvu/rnxGGVES7eEK3wvdMsae\r\nptgEdLS1+9Y/cgGWXtZ6y2uR5xPr8/hH/SxO6QcXp+FYdIn7kHhToNeDO7JS\r\nVsHJagbrMtPjFW0LF/E17hKSEoafS89zPlB+ZHkudHhiyn3I32rfSHJetkOs\r\nZXFObmCAYDNAhkS98rbNnzQtLcRXqlm7pQevEQw85cjOwClvMNKUrd3k+aKU\r\nfw97VBEg6UDVpXspNPuNKfJjB3W2NYgM7ypX71TH9NH1BzBclLCijAxy4J2z\r\nFNkh23+3a4psvCHuTQ7kMdC/2WvNmQpiySoRusZ77Gp5TICtI80M6gSd1POp\r\nk4NyjC1VO4AizacovzhDT1rxc4t0VyxuHrNEIR7tGnkOfd7bl2vukJFqXXcv\r\nQEbFXLSmEtq9Mg+zPK0gFETE/lZe0b11i2XroZfBH7/hjNrgp15RQPumJosA\r\ng+8WspRk287rKfmZeGBVBnWPn1VzZ0+m2biTWXSd34B8J03hVAUTuKW9N07D\r\nb0/5vZgjtS3sxrUxm67BtbWtswORcc7r6NS+Wh9ue0fghsqkhmtKITrebc4s\r\nSXeg4NuqswUtbxmY0ujiH0BJi3IkyAhRs9U=\r\n=JsQ+\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.6_1681074010848_0.5148388380615421"}, "_hasShrinkwrap": false}, "4.2.7": {"name": "minipass", "version": "4.2.7", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.mts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "815a2efb09546d48f98c0817d0656aa7b6e83e99", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.7", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.3", "dist": {"integrity": "sha512-ScVIgqHcXRMyfflqHmEW0bm8z8rb5McHyOY3ewX9JBgZaR77G7nxq9L/mtV96/QbAAwtbCAHVVLzD1kkyfFQEw==", "shasum": "14c6fc0dcab54d9c4dd64b2b7032fef04efec218", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.2.7.tgz", "fileCount": 7, "unpackedSize": 69783, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC12j7PnxfPVGl/5busvBG2ZyudGoodfyAtbl4Pw7XznAIgLbcLXa2G/4htnCJRV8F37Pa2FAykycqfP92Xcnx5+c8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMy0DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYVA/+IFj1xozLOCch1Y8HIZoKp5fHNXkz+sIVp4ZEjAQFPDo0hRka\r\nME9RghrlecqcGKTgum3ysxuj2dFVnCDMs8lXyRwkqrzeWTxIWAlqQubtzYfV\r\nNklLpucy7gBllNjf0YNAWJVFqF5arxb/VKccgzdqTE9WzER7ugjSDQH56TaM\r\nZMQRoh0r/EOdzvSPIT6hnvQLfbjGKHgWGM6Ly3Twnax/CkcyjGWFGnSCnSJA\r\nPWJ4aw4QqAn5yDFZDid6KGVS/NPPVxAfGjR1ddpWg5n3+DbtPbjn//l2FBna\r\naWnqerkrUNjwQmcwX5l751+saYAqJ70H95TMFAjO3GLoK8ghkvUM0dKL02j1\r\nnmwMwT1roih8TQ+NEc1S8jRB3Plc0PCYonUgFmwx73IacBebgT6t90ZmMURy\r\nTct1jZQXQ+GSqhg8TDZrnZ9D4Ym/m6xR6DU2PdkhAQuDJ1zDdnu0TDIAGcqy\r\nY3qu58Py7mWfTEpiO37xQ2+BCbAJBW6uACbliuqFlbMMBxjajjzrRR4BYwdn\r\nn2RBPVLep/IRD/RHuX8En15aU1eGJzuFvu7ds7tEkDt8dRdBhgq41PytFzul\r\nvcOiLwO8iYZ7WxO6vWlQ/BI5JDvL8v4SkmXcafVHdnYF9DoT5n4ANLBWtJys\r\nQVB0zLxCMdwtInNvfrtuLGrV23JSXrfiTJ4=\r\n=PqeM\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.7_1681075459725_0.7272249058668407"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "minipass", "version": "5.0.0", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "3066600b811753bd9c85831a8ecd5c6ca248f2aa", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@5.0.0", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.3", "dist": {"integrity": "sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==", "shasum": "3e9788ffb90b694a5d0ec94479a45b5d8738133d", "tarball": "https://registry.npmjs.org/minipass/-/minipass-5.0.0.tgz", "fileCount": 6, "unpackedSize": 69475, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGG9d/n9j/SiJwfKLK58G4W+KXXHLm/aIadRa/mJxsnFAiAMhpcZ9419dsyFB+8n8uTkyWOkalIM/OsqBG4PrBM2lA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMzQCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbsQ//brt9nqdgncqvwVSLdtzC7rp53x+Q7gIYj9/fPCRFwJvFG98V\r\nRwhWWlSbFcbHyZDb8qCTK63KLICw4d6ha3SjKV3ZNX/+Xd7Qr4HbCp+ELyRs\r\n1+8EyQQdtcJcMAYnUBhG/wV3+h9gLgc8AY47iFColB5GJahS38Ua7lcQ5vU3\r\ncGmt1oiL5pnCAe8r/d4OT4k/0LIidvw7NDmXEOM6mknIFNyag4HPnDpczm0y\r\nbcBVHDGq7WMvysCjsgJOjxNb/CApOHx33z5qqbdmAKQLDVADjfQ/9gBPWOZ7\r\n5hSeSA30oyzZZR/vAlWKkK32hqIAuNq+/w+73s6flQB90Mccqia7F7Ahq43S\r\nioWBJ1frTjhlrPX0jmItGDDJLRRKS7kNm1TObWj5B4WWdJGPlWaNJi07VJkM\r\nJ5ubvfbPGH3zQZt9i5dK60UPBbB/Pm5ZtSUE5TugTuxM+hXTSxzJHgtrNzwU\r\nFFkxVaEBeFgfuUXiSnwiDI4bpCRmB/RByyMcIKT1LhO7V+3KKl2s1nd3+5V4\r\njoa6r1d33dG7eUacD+ijDrLzQzCj/2rwm9cgccsG2XdWjbudkaZBfgySQ+ka\r\niGsmYH1MfCpGzrKr+DgtQn0crIMh/0UdTmbh3NFdm24C2PeiskcfggYaj/Xn\r\nR2ROnLecGp3aTTn6W5PCRkeZW2GgCQDcR+Y=\r\n=ZOod\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_5.0.0_1681077250203_0.5777324393037977"}, "_hasShrinkwrap": false}, "4.2.8": {"name": "minipass", "version": "4.2.8", "publishConfig": {"tag": "legacy-v4"}, "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=8"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1K_HR5oh3r80b8WVMWCPPjfuWXUgfkmhlX7FGI6JJ8tY/edit?usp=sharing)\nfor objects, strings, and buffers.\n\nSupports `pipe()`ing (including multi-`pipe()` and backpressure\ntransmission), buffering data until either a `data` event handler\nor `pipe()` is added (so you don't lose the first chunk), and\nmost other cases where PassThrough is a good idea.\n\nThere is a `read()` method, but it's much more efficient to\nconsume data from this stream via `'data'` events or by calling\n`pipe()` into some other stream. Calling `read()` requires the\nbuffer to be flattened in some cases, which requires copying\nmemory.\n\nIf you set `objectMode: true` in the options, then whatever is\nwritten will be emitted. Otherwise, it'll do a minimal amount of\nBuffer copying to ensure proper Streams semantics when `read(n)`\nis called.\n\n`objectMode` can also be set by doing `stream.objectMode = true`,\nor by writing any non-string/non-buffer data. `objectMode` cannot\nbe set to false once it is set.\n\nThis is not a `through` or `through2` stream. It doesn't\ntransform the data, it just passes it right through. If you want\nto transform the data, extend the class, and override the\n`write()` method. Once you're done transforming the data however\nyou want, call `super.write()` with the transform output.\n\nFor some examples of streams that extend Minipass in various\nways, check out:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap-parser)\n- [treport](http://npm.im/treport)\n- [minipass-fetch](http://npm.im/minipass-fetch)\n- [pacote](http://npm.im/pacote)\n- [make-fetch-happen](http://npm.im/make-fetch-happen)\n- [cacache](http://npm.im/cacache)\n- [ssri](http://npm.im/ssri)\n- [npm-registry-fetch](http://npm.im/npm-registry-fetch)\n- [minipass-json-stream](http://npm.im/minipass-json-stream)\n- [minipass-sized](http://npm.im/minipass-sized)\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different\nfrom (and in some ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with node-core\nstreams and intend to use Minipass streams in your programs.\n\nYou can avoid most of these differences entirely (for a very\nsmall performance penalty) by setting `{async: true}` in the\nconstructor options.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.\nThus, data is emitted as soon as it is available, always. It is\nbuffered until read, but no longer. Another way to look at it is\nthat Minipass streams are exactly as synchronous as the logic\nthat writes into them.\n\nThis can be surprising if your code relies on\n`PassThrough.write()` always providing data on the next tick\nrather than the current one, or being able to call `resume()` and\nnot have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no\nway for Minipass to achieve the speeds it does, or support the\nsynchronous use cases that it does. Simply put, waiting takes\ntime.\n\nThis non-deferring approach makes Minipass streams much easier to\nreason about, especially in the context of Promises and other\nflow-control mechanisms.\n\nExample:\n\n```js\n// hybrid module, either works\nimport Minipass from 'minipass'\n// or:\nconst Minipass = require('minipass')\n\nconst stream = new Minipass()\nstream.on('data', () => console.log('data event'))\nconsole.log('before write')\nstream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// data event\n// after write\n```\n\n### Exception: Async Opt-In\n\nIf you wish to have a Minipass stream with behavior that more\nclosely mimics Node.js core streams, you can set the stream in\nasync mode either by setting `async: true` in the constructor\noptions, or by setting `stream.async = true` later on.\n\n```js\n// hybrid module, either works\nimport Minipass from 'minipass'\n// or:\nconst Minipass = require('minipass')\n\nconst asyncStream = new Minipass({ async: true })\nasyncStream.on('data', () => console.log('data event'))\nconsole.log('before write')\nasyncStream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// after write\n// data event <-- this is deferred until the next tick\n```\n\nSwitching _out_ of async mode is unsafe, as it could cause data\ncorruption, and so is not enabled. Example:\n\n```js\nimport Minipass from 'minipass'\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nsetStreamSyncAgainSomehow(stream) // <-- this doesn't actually exist!\nstream.write('world')\nconsole.log('after writes')\n// hypothetical output would be:\n// before writes\n// world\n// after writes\n// hello\n// NOT GOOD!\n```\n\nTo avoid this problem, once set into async mode, any attempt to\nmake the stream sync again will be ignored.\n\n```js\nconst Minipass = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nstream.async = false // <-- no-op, stream already async\nstream.write('world')\nconsole.log('after writes')\n// actual output:\n// before writes\n// after writes\n// hello\n// world\n```\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer,\nreturning `true` on all writes until the limit is hit, even if\nthe data has nowhere to go. Then, they will not attempt to draw\nmore data in until the buffer size dips below a minimum value.\n\nMinipass streams are much simpler. The `write()` method will\nreturn `true` if the data has somewhere to go (which is to say,\ngiven the timing guarantees, that the data is already there by\nthe time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and\nthe data sits in a buffer, to be drained out immediately as soon\nas anyone consumes it.\n\nSince nothing is ever buffered unnecessarily, there is much less\ncopying data, and less bookkeeping about buffer capacity levels.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written\nall the way through the pipeline, and `write()` always returns\ntrue/false based on whether the data was fully flushed,\nbackpressure is communicated immediately to the upstream caller.\nThis minimizes buffering.\n\nConsider this case:\n\n```js\nconst { PassThrough } = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage,\nand multiple event deferrals happened, for an unblocked pipeline\nwhere it was perfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead\nsomeone reading the code to think an advisory maximum of 1KiB is\nbeing set for the pipeline. However, the actual advisory\nbuffering level is the _sum_ of `highWaterMark` values, since\neach one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data\nwritten, or _ever_ buffer data that can be flushed all the way\nthrough. Neither node-core streams nor Minipass ever fail to\nbuffer written data, but node-core streams do a lot of\nunnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less\nstuff and waits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing\nany data into it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't\nwant to potentially happen immediately (for example, closing file\ndescriptors, moving on to the next entry in an archive parse\nstream, etc.) then be sure to call `stream.pause()` on creation,\nand then `stream.resume()` once you are ready to respond to the\n`end` event.\n\nHowever, this is _usually_ not a problem because:\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not\nyet have had a chance to add a listener. In order to avoid this\nhazard, Minipass streams safely re-emit the `'end'` event if a\nnew listener is added after `'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream\nhas already emitted `end`, then it will call the handler right\naway. (You can think of this somewhat like attaching a new\n`.then(fn)` to a previously-resolved Promise.)\n\nTo prevent calling handlers multiple times who would not expect\nmultiple ends to occur, all listeners are removed from the\n`'end'` event whenever it is emitted.\n\n### Emit `error` When Asked\n\nThe most recent error object passed to the `'error'` event is\nstored on the stream. If a new `'error'` event handler is added,\nand an error was previously emitted, then the event handler will\nbe called immediately (or on `process.nextTick` in the case of\nasync streams).\n\nThis makes it much more difficult to end up trying to interact\nwith a broken stream, if the error handler is added after an\nerror was previously emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data\nthrough the pipeline when a new pipe destination is added, this\ncan have surprising effects, especially when a stream comes in\nfrom some other function and may or may not have data in its\nbuffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nOne solution is to create a dedicated tee-stream junction that\npipes to both locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nsrc.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners. The\nfirst one added will _immediately_ receive all of the data,\nleaving nothing for the second:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\nAll of the hazards in this section are avoided by setting `{\nasync: true }` in the Minipass constructor, or by setting\n`stream.async = true` afterwards. Note that this does add some\noverhead, so should only be done in cases where you are willing\nto lose a bit of performance in order to avoid having to refactor\nprogram logic.\n\n## USAGE\n\nIt's a stream! Use it like a stream and it'll most likely do what\nyou want.\n\n```js\nimport Minipass from 'minipass'\nconst mp = new Minipass(options) // optional: { encoding, objectMode }\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n- `encoding` How would you like the data coming _out_ of the\n  stream to be encoded? Accepts any values that can be passed to\n  `Buffer.toString()`.\n- `objectMode` Emit data exactly as it comes in. This will be\n  flipped on by default if you write() something other than a\n  string or Buffer at any point. Setting `objectMode: true` will\n  prevent setting any encoding value.\n- `async` Defaults to `false`. Set to `true` to defer data\n  emission until next tick. This reduces performance slightly,\n  but makes Minipass streams use timing behavior closer to Node\n  core streams. See [Timing](#timing) for more details.\n- `signal` An `AbortSignal` that will cause the stream to unhook\n  itself from everything and become as inert as possible. Note\n  that providing a `signal` parameter will make `'error'` events\n  no longer throw if they are unhandled, but they will still be\n  emitted to handlers if any are attached.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and\n`Writable` streams.\n\n### Methods\n\n- `write(chunk, [encoding], [callback])` - Put data in. (Note\n  that, in the base Minipass class, the same data will come out.)\n  Returns `false` if the stream will buffer the next write, or\n  true if it's still in \"flowing\" mode.\n- `end([chunk, [encoding]], [callback])` - Signal that you have\n  no more data to write. This will queue an `end` event to be\n  fired when all the data has been consumed.\n- `setEncoding(encoding)` - Set the encoding for data coming of\n  the stream. This can only be done once.\n- `pause()` - No more data for a while, please. This also\n  prevents `end` from being emitted for empty streams until the\n  stream is resumed.\n- `resume()` - Resume the stream. If there's data in the buffer,\n  it is all discarded. Any buffered events are immediately\n  emitted.\n- `pipe(dest)` - Send all output to the stream provided. When\n  data is emitted, it is immediately written to any and all pipe\n  destinations. (Or written on next tick in `async` mode.)\n- `unpipe(dest)` - Stop piping to the destination stream. This is\n  immediate, meaning that any asynchronously queued data will\n  _not_ make it to the destination when running in `async` mode.\n  - `options.end` - Boolean, end the destination stream when the\n    source stream ends. Default `true`.\n  - `options.proxyErrors` - Boolean, proxy `error` events from\n    the source stream to the destination stream. Note that errors\n    are _not_ proxied after the pipeline terminates, either due\n    to the source emitting `'end'` or manually unpiping with\n    `src.unpipe(dest)`. Default `false`.\n- `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are\n  EventEmitters. Some events are given special treatment,\n  however. (See below under \"events\".)\n- `promise()` - Returns a Promise that resolves when the stream\n  emits `end`, or rejects if the stream emits `error`.\n- `collect()` - Return a Promise that resolves on `end` with an\n  array containing each chunk of data that was emitted, or\n  rejects if the stream emits `error`. Note that this consumes\n  the stream data.\n- `concat()` - Same as `collect()`, but concatenates the data\n  into a single Buffer object. Will reject the returned promise\n  if the stream is in objectMode, or if it goes into objectMode\n  by the end of the data.\n- `read(n)` - Consume `n` bytes of data out of the buffer. If `n`\n  is not provided, then consume all of it. If `n` bytes are not\n  available, then it returns null. **Note** consuming streams in\n  this way is less efficient, and can lead to unnecessary Buffer\n  copying.\n- `destroy([er])` - Destroy the stream. If an error is provided,\n  then an `'error'` event is emitted. If the stream has a\n  `close()` method, and has not emitted a `'close'` event yet,\n  then `stream.close()` will be called. Any Promises returned by\n  `.promise()`, `.collect()` or `.concat()` will be rejected.\n  After being destroyed, writing to the stream will emit an\n  error. No more data will be emitted if the stream is destroyed,\n  even if it was previously buffered.\n\n### Properties\n\n- `bufferLength` Read-only. Total number of bytes buffered, or in\n  the case of objectMode, the total number of objects.\n- `encoding` The encoding that has been set. (Setting this is\n  equivalent to calling `setEncoding(enc)` and has the same\n  prohibition against setting multiple times.)\n- `flowing` Read-only. Boolean indicating whether a chunk written\n  to the stream will be immediately emitted.\n- `emittedEnd` Read-only. Boolean indicating whether the end-ish\n  events (ie, `end`, `prefinish`, `finish`) have been emitted.\n  Note that listening on any end-ish event will immediateyl\n  re-emit it if it has already been emitted.\n- `writable` Whether the stream is writable. Default `true`. Set\n  to `false` when `end()`\n- `readable` Whether the stream is readable. Default `true`.\n- `pipes` An array of Pipe objects referencing streams that this\n  stream is piping into.\n- `destroyed` A getter that indicates whether the stream was\n  destroyed.\n- `paused` True if the stream has been explicitly paused,\n  otherwise false.\n- `objectMode` Indicates whether the stream is in `objectMode`.\n  Once set to `true`, it cannot be set to `false`.\n- `aborted` Readonly property set when the `AbortSignal`\n  dispatches an `abort` event.\n\n### Events\n\n- `data` Emitted when there's data to read. Argument is the data\n  to read. This is never emitted while not flowing. If a listener\n  is attached, that will resume the stream.\n- `end` Emitted when there's no more data to read. This will be\n  emitted immediately for empty streams when `end()` is called.\n  If a listener is attached, and `end` was already emitted, then\n  it will be emitted again. All listeners are removed when `end`\n  is emitted.\n- `prefinish` An end-ish event that follows the same logic as\n  `end` and is emitted in the same conditions where `end` is\n  emitted. Emitted after `'end'`.\n- `finish` An end-ish event that follows the same logic as `end`\n  and is emitted in the same conditions where `end` is emitted.\n  Emitted after `'prefinish'`.\n- `close` An indication that an underlying resource has been\n  released. Minipass does not emit this event, but will defer it\n  until after `end` has been emitted, since it throws off some\n  stream libraries otherwise.\n- `drain` Emitted when the internal buffer empties, and it is\n  again suitable to `write()` into the stream.\n- `readable` Emitted when data is buffered and ready to be read\n  by a consumer.\n- `resume` Emitted when stream changes state from buffering to\n  flowing mode. (Ie, when `resume` is called, `pipe` is called,\n  or a `data` event listener is added.)\n\n### Static Methods\n\n- `Minipass.isStream(stream)` Returns `true` if the argument is a\n  stream, and false otherwise. To be considered a stream, the\n  object must be either an instance of Minipass, or an\n  EventEmitter that has either a `pipe()` method, or both\n  `write()` and `end()` methods. (Pretty much any stream in\n  node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass\nstreams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(\n  () => {\n    // stream is finished\n  },\n  er => {\n    // stream emitted an error\n  }\n)\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one\nchunk for you, but if you're going to do it yourself anyway, it's\nconvenient this way:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in\nplatforms that support it.\n\nSynchronous iteration will end when the currently available data\nis consumed, even if the `end` event has not been reached. In\nstring and buffer mode, the data is concatenated, so unless\nmultiple writes are occurring in the same tick as the `read()`,\nsync iteration loops will generally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written,\nwith no flattening, create the stream with the `{ objectMode:\ntrue }` option.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i-- > 0) mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume() {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write(chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end(chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(\n    new (class extends Minipass {\n      emit(ev, ...data) {\n        // let's also log events, because debugging some weird thing\n        console.log('EMIT', ev)\n        return super.emit(ev, ...data)\n      }\n      write(chunk, encoding, callback) {\n        console.log('WRITE', chunk, encoding)\n        return super.write(chunk, encoding, callback)\n      }\n      end(chunk, encoding, callback) {\n        console.log('END', chunk, encoding)\n        return super.end(chunk, encoding, callback)\n      }\n    })()\n  )\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit(ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write(obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end(obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor (options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write (chunk, encoding, cb) {\n    if (typeof chunk === 'string' &&\n        typeof encoding === 'string' &&\n        encoding !== 'utf8') {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk)) {\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      try {\n        // JSON.parse can throw, emit an error on that\n        super.write(JSON.parse(jsonData[i]))\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb)\n      cb()\n  }\n}\n```\n", "readmeFilename": "README.md", "gitHead": "ceb8d68b7658039349a2bfc92ae95ae4ca822fbe", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@4.2.8", "_nodeVersion": "18.14.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-fNzuVyifolSLFL4NzpF+wEF4qrgqaaKX0haXPQEdQ7NKAN+WecoKMHV09YcuL/DHxrUsYQOK3MiuDf7Ip2OXfQ==", "shasum": "f0010f64393ecfc1d1ccb5f582bcaf45f48e1a3a", "tarball": "https://registry.npmjs.org/minipass/-/minipass-4.2.8.tgz", "fileCount": 6, "unpackedSize": 69429, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpE6oaNgiTQhK21Njy7Dq1bzo4GlD0kS/cIVykQ4zDUwIhALabUYU3enRuRmJ06G6fnsDjNqXWBj7aGjekgS7TXmoi"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNYQLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvgQ//V6X8coKNs46JL6BLV9xWSxsPego+U4HCpMt/UOq2JA/s0idq\r\ns4aXF7UpAjlgqaM+Tml08AVQUDa6anV75UOYR6OOxqOAWSjaOkQ4QINP5b/N\r\n9UuDA+jYYky2K/Ty40eSILoF/46wr1VijOjppf6f2IMOBhsOwt5RSIgC6wr4\r\naj+Swv64OVOnfk+oYuR82EagGM2ZbNkZzRppfX8B8wBpGOhjpgYgeR8iGdox\r\nDhT6BGBwLu5ZeSLZJWAARcrpS7d3Ubey0MqLBqhljojScFPIGxtHM6SweJAz\r\nHMGCIEEThPVHHS+20QWrQ4bcp0pb6x7lz9J/To9foJ9zjPEQ/5LhF75NOhU+\r\nmhDUEeU7iBPXlH5zTsDIm592Ms+5ftIqebtI0hXDdzxwFG+Hf3IzRNTvRL5C\r\nJDbCj8sleQ3Ac1LVLcFJHpDV2HWzw9W4fghSbJdRKycdZ1EMw3jYm57rdOye\r\ncnQExDG5fjyFP7QDtF/qXUFZGCDvUhdG2bSAgTtkk5qVaZHf04e9CDobyMB1\r\nzeuolaCcsrycSzMwTA+C3HZivQkYB5v0kFTmt2zNcJgU5SWsMR/NOaqpX5og\r\nPi6ZTvu04ZwqM4pHyUQeFVshy+x9EkBQrm+7VvsUMuYJMieXUJYMPhbNPqxC\r\nJa5gFdpzNyq2L2dwRT+Uodqun1RBrbRndG8=\r\n=cRYA\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_4.2.8_1681228810954_0.6015072400739621"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "minipass", "version": "6.0.0", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=16 || 14 >=14.17"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "c2aa0b2196a5d622fdf08032753dd424f53a101b", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@6.0.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-mvD5U4pUen1aWcjTxUgdoMg6PB98dcV0obc/OiPzls79++IpgNoO+MCbOHRlKfWIOvjIjmjUygjZmSStP7B0Og==", "shasum": "3b000c121dd32da5dc56156381dc322b4f2ffaa0", "tarball": "https://registry.npmjs.org/minipass/-/minipass-6.0.0.tgz", "fileCount": 6, "unpackedSize": 72054, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBlc+AX/YsyxCraefscb5Zyo3yJ45JWMhjGa81amMxsCAiBecj+rCxbucN4yPHgCBu10jzz20rpqiAV5jckNlrVe/Q=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_6.0.0_1684125669158_0.33282480463221487"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "minipass", "version": "6.0.1", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=16 || 14 >=14.17"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "6125ceeddb721eb3f69749d4e71953adb48ad37d", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@6.0.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-Tenl5QPpgozlOGBiveNYHg2f6y+VpxsXRoIHFUVJuSmTonXRAE6q9b8Mp/O46762/2AlW4ye4Nkyvx0fgWDKbw==", "shasum": "315417c259cb32a1b2fc530c0e7f55c901a60a6d", "tarball": "https://registry.npmjs.org/minipass/-/minipass-6.0.1.tgz", "fileCount": 6, "unpackedSize": 72294, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNruBUjX8fCLndo48IoRdCsdBpuk6i73yxW0hPNGROUgIhANso7bY70CzUm0WqgRSJ6F7UhPDPbUyL5N/u9LeCOk2+"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_6.0.1_1684189575979_0.959831283268048"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "minipass", "version": "6.0.2", "description": "minimal implementation of a PassThrough stream", "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^17.0.41", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^16.2.0", "through2": "^2.0.3", "ts-node": "^10.8.1", "typedoc": "^0.23.24", "typescript": "^4.7.3"}, "scripts": {"pretest": "npm run prepare", "presnap": "npm run prepare", "prepare": "node ./scripts/transpile-to-esm.js", "snap": "tap", "test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags", "typedoc": "typedoc ./index.d.ts", "format": "prettier --write . --loglevel warn"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "tap": {"check-coverage": true}, "engines": {"node": ">=16 || 14 >=14.17"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "gitHead": "15ab07809dab7a278f9c79027cf25a3a150b770a", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@6.0.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.6.5", "dist": {"integrity": "sha512-MzWSV5nYVT7mVyWCwn2o7JH13w2TBRmmSqSRCKzTw+lmft9X4z+3wjvs06Tzijo5z4W/kahUCDpRXTF+ZrmF/w==", "shasum": "542844b6c4ce95b202c0995b0a471f1229de4c81", "tarball": "https://registry.npmjs.org/minipass/-/minipass-6.0.2.tgz", "fileCount": 6, "unpackedSize": 72303, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICqqstVBSXdNnDS7yIs9sh8GRz/rsanUH1wWdrAFWQLqAiBDt4qJ26FPCCT0NIbAidBwo6o20h7lDsecxPX722wfog=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_6.0.2_1684358210139_0.12283922254492419"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "minipass", "version": "7.0.0", "description": "minimal implementation of a PassThrough stream", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./package.json": "./package.json"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/node": "^20.1.2", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.6.2", "tap": "^16.3.0", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.3", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "sync-content": "^1.0.2", "through2": "^2.0.3"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "gitHead": "d63abffc8734d679177d2382ac1841caa82349f3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@7.0.0", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-QWQmFjKDHhfJdyAievi/KRg/S5oZ41a4u/A10YsJpfgXmEtdvKeJFsaLZr+gQas7hpKoCrUUdJ97iwoySrmqHQ==", "shasum": "164051d8c2881b7a47f21d9cb6661dcb8f4121f2", "tarball": "https://registry.npmjs.org/minipass/-/minipass-7.0.0.tgz", "fileCount": 13, "unpackedSize": 284315, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFm344OpKXsjq3f41vXTkaa67iV+BE4BVB7RAIj3ns98AiEAqX9j++GUzFQy74bCR5dZIsTmMQ8Jc1ByFwxKAMeAHsw="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.0.0_1688775283609_0.8892298371891081"}, "_hasShrinkwrap": false}, "7.0.1": {"name": "minipass", "version": "7.0.1", "description": "minimal implementation of a PassThrough stream", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./package.json": "./package.json"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/node": "^20.1.2", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.6.2", "tap": "^16.3.0", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.3", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "sync-content": "^1.0.2", "through2": "^2.0.3"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "gitHead": "6baaade6726d1cac656426f89f15de631a56b3d1", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@7.0.1", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-NQ8MCKimInjVlaIqx51RKJJB7mINVkLTJbsZKmto4UAAOC/CWXES8PGaOgoBZyqoUsUA/U3DToGK7GJkkHbjJw==", "shasum": "dff63464407cd8b83d7f008c0f116fa8c9b77ebf", "tarball": "https://registry.npmjs.org/minipass/-/minipass-7.0.1.tgz", "fileCount": 13, "unpackedSize": 284357, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQClYll+UqcZLUhaw1lE28RwLHQ2FKEDJL5bEIRB5NoytgIgb3QPb1oh9psaeoRSW1zrmbWK6Vga33Wrpv8rSOeo3q0="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.0.1_1688775799416_0.7448468714465568"}, "_hasShrinkwrap": false}, "7.0.2": {"name": "minipass", "version": "7.0.2", "description": "minimal implementation of a PassThrough stream", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./package.json": "./package.json"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/node": "^20.1.2", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.6.2", "tap": "^16.3.0", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.3", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "sync-content": "^1.0.2", "through2": "^2.0.3"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "gitHead": "b220db67d918c9717911ac5a05d427d2da6074d3", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_id": "minipass@7.0.2", "_nodeVersion": "18.16.0", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-eL79dXrE1q9dBbDCLg7xfn/vl7MS4F1gvJAgjJrQli/jbQWdUttuVawphqpffoIYfRdq78LHx6GP4bU/EQ2ATA==", "shasum": "58a82b7d81c7010da5bd4b2c0c85ac4b4ec5131e", "tarball": "https://registry.npmjs.org/minipass/-/minipass-7.0.2.tgz", "fileCount": 13, "unpackedSize": 284773, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7WGcZqFyLsmrEI9b3laL7X+2hs5cFVpEZzNYLTtDPzQIhAP2KIJk5QYSNcLkw20nTWN27iZ5s680dgip2IdkVhwvh"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.0.2_1689052650121_0.789215871620615"}, "_hasShrinkwrap": false}, "7.0.3": {"name": "minipass", "version": "7.0.3", "description": "minimal implementation of a PassThrough stream", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./package.json": "./package.json"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts"}, "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/node": "^20.1.2", "@types/tap": "^15.0.8", "c8": "^7.13.0", "prettier": "^2.6.2", "tap": "^16.3.0", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.3", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "sync-content": "^1.0.2", "through2": "^2.0.3"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "_id": "minipass@7.0.3", "gitHead": "8d95dcac2d3e769bbb8e66d721ce8359a1380d42", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_nodeVersion": "18.16.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-LhbbwCfz3vsb12j/WkWQPZfKTsgqIe1Nf/ti1pKjYESGLHIVjWU96G9/ljLH4F9mWNVhlQOm0VySdAWzf05dpg==", "shasum": "05ea638da44e475037ed94d1c7efcc76a25e1974", "tarball": "https://registry.npmjs.org/minipass/-/minipass-7.0.3.tgz", "fileCount": 13, "unpackedSize": 284648, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCiYXjXuN2gRxpFynlAU0SXHTZdvKEs7mQvppOHJLx1UQIhAM43cPR9RG9QsHR55/Rii3ob1U8ICeg3BKIhA2kj6e48"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.0.3_1691868603294_0.8818660773346114"}, "_hasShrinkwrap": false}, "7.0.4": {"name": "minipass", "version": "7.0.4", "description": "minimal implementation of a PassThrough stream", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/end-of-stream": "^1.4.2", "@types/node": "^20.1.2", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "sync-content": "^1.0.2", "tap": "^18.3.0", "through2": "^2.0.3", "tshy": "^1.2.2", "typedoc": "^0.25.1", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "tap": {"include": ["test/*.ts"]}, "_id": "minipass@7.0.4", "gitHead": "c776c8778b25c479c7ea76601197db5c2dfbae8a", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_nodeVersion": "20.7.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-jYofLM5Dam9279rdkWzqHozUo4ybjdZmCsDHePy5V/PbBcVMiSZR97gmAy45aqi8CK1lG2ECd356FU86avfwUQ==", "shasum": "dbce03740f50a4786ba994c1fb908844d27b038c", "tarball": "https://registry.npmjs.org/minipass/-/minipass-7.0.4.tgz", "fileCount": 13, "unpackedSize": 284660, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIASWXUFLvLCfZ3HHD76Khv1mUxAUTuge3FJlqEjrhBqxAiBAN/+U3Bp4Nn3HF17swNziVSiS2xfH1VI/BAy0eyE99g=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.0.4_1695945513386_0.8044391401477946"}, "_hasShrinkwrap": false}, "7.1.0": {"name": "minipass", "version": "7.1.0", "description": "minimal implementation of a PassThrough stream", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/end-of-stream": "^1.4.2", "@types/node": "^20.1.2", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^18.3.0", "through2": "^2.0.3", "tshy": "^1.2.2", "typedoc": "^0.25.1", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "tap": {"typecheck": true, "include": ["test/*.ts"]}, "_id": "minipass@7.1.0", "gitHead": "1875e522c0ff22d0f5e51dbd7843423ca74b0c5c", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.1", "dist": {"integrity": "sha512-oGZRv2OT1lO2UF1zUcwdTb3wqUwI0kBGTgt/T7OdSj6M6N5m3o5uPf0AIW6lVxGGoiWUR7e2AwTE+xiwK8WQig==", "shasum": "b545f84af94e567386770159302ca113469c80b8", "tarball": "https://registry.npmjs.org/minipass/-/minipass-7.1.0.tgz", "fileCount": 13, "unpackedSize": 284683, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGciFNerqlrToRzSKykz7TuE0HbnvdRIwx4+ejCMrZIWAiEAmCkDWY8SWPzrG6oPyUIk9Xa+fToEey4fGQ3bsMIaFMk="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.1.0_1714788037968_0.17800660359126508"}, "_hasShrinkwrap": false}, "7.1.1": {"name": "minipass", "version": "7.1.1", "description": "minimal implementation of a PassThrough stream", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "tshy": {"main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/end-of-stream": "^1.4.2", "@types/node": "^20.1.2", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^18.3.0", "through2": "^2.0.3", "tshy": "^1.2.2", "typedoc": "^0.25.1", "typescript": "^5.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "tap": {"typecheck": true, "include": ["test/*.ts"]}, "_id": "minipass@7.1.1", "gitHead": "9410c3e3bb5bccb4f11c4f9080c5f4d695f72870", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-UZ7eQ+h8ywIRAW1hIEl2AqdwzJucU/Kp59+8kkZeSvafXhZjul247BvIJjEVFVeON6d7lM46XX1HXCduKAS8VA==", "shasum": "f7f85aff59aa22f110b20e27692465cf3bf89481", "tarball": "https://registry.npmjs.org/minipass/-/minipass-7.1.1.tgz", "fileCount": 13, "unpackedSize": 284808, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4QuLBeF4qIu67aOHEUzwpIc9W0PeGjmWrlfZSKQedzQIhAOYwmeFu1vsndqyaknodtTmPBk+q/iO2dgi3/+goerTi"}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.1.1_1715262566961_0.4945015346384156"}, "_hasShrinkwrap": false}, "7.1.2": {"name": "minipass", "version": "7.1.2", "description": "minimal implementation of a PassThrough stream", "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "tshy": {"selfLink": false, "main": true, "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/end-of-stream": "^1.4.2", "@types/node": "^20.1.2", "end-of-stream": "^1.4.0", "node-abort-controller": "^3.1.1", "prettier": "^2.6.2", "tap": "^19.0.0", "through2": "^2.0.3", "tshy": "^1.14.0", "typedoc": "^0.25.1"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}, "tap": {"typecheck": true, "include": ["test/*.ts"]}, "_id": "minipass@7.1.2", "gitHead": "1fc7b914533c367ac00a982051849add2169f641", "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "homepage": "https://github.com/isaacs/minipass#readme", "_nodeVersion": "20.13.1", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==", "shasum": "93a9626ce5e5e66bd4db86849e7515e92340a707", "tarball": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "fileCount": 13, "unpackedSize": 286202, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBkQJPfSAC23QGJC4cJMYAUXpqqVOq4t69yc/4rkRU3GAiEAl4BdS3v8U/I+g+Na71CrCfplx3P1Z2lvj7NBGdATUR8="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minipass_7.1.2_1716511340973_0.6336662935701123"}, "_hasShrinkwrap": false}}, "readme": "# minipass\n\nA _very_ minimal implementation of a [PassThrough\nstream](https://nodejs.org/api/stream.html#stream_class_stream_passthrough)\n\n[It's very\nfast](https://docs.google.com/spreadsheets/d/1K_HR5oh3r80b8WVMWCPPjfuWXUgfkmhlX7FGI6JJ8tY/edit?usp=sharing)\nfor objects, strings, and buffers.\n\nSupports `pipe()`ing (including multi-`pipe()` and backpressure\ntransmission), buffering data until either a `data` event handler\nor `pipe()` is added (so you don't lose the first chunk), and\nmost other cases where PassThrough is a good idea.\n\nThere is a `read()` method, but it's much more efficient to\nconsume data from this stream via `'data'` events or by calling\n`pipe()` into some other stream. Calling `read()` requires the\nbuffer to be flattened in some cases, which requires copying\nmemory.\n\nIf you set `objectMode: true` in the options, then whatever is\nwritten will be emitted. Otherwise, it'll do a minimal amount of\nBuffer copying to ensure proper Streams semantics when `read(n)`\nis called.\n\n`objectMode` can only be set at instantiation. Attempting to\nwrite something other than a String or Buffer without having set\n`objectMode` in the options will throw an error.\n\nThis is not a `through` or `through2` stream. It doesn't\ntransform the data, it just passes it right through. If you want\nto transform the data, extend the class, and override the\n`write()` method. Once you're done transforming the data however\nyou want, call `super.write()` with the transform output.\n\nFor some examples of streams that extend Minipass in various\nways, check out:\n\n- [minizlib](http://npm.im/minizlib)\n- [fs-minipass](http://npm.im/fs-minipass)\n- [tar](http://npm.im/tar)\n- [minipass-collect](http://npm.im/minipass-collect)\n- [minipass-flush](http://npm.im/minipass-flush)\n- [minipass-pipeline](http://npm.im/minipass-pipeline)\n- [tap](http://npm.im/tap)\n- [tap-parser](http://npm.im/tap-parser)\n- [treport](http://npm.im/treport)\n- [minipass-fetch](http://npm.im/minipass-fetch)\n- [pacote](http://npm.im/pacote)\n- [make-fetch-happen](http://npm.im/make-fetch-happen)\n- [cacache](http://npm.im/cacache)\n- [ssri](http://npm.im/ssri)\n- [npm-registry-fetch](http://npm.im/npm-registry-fetch)\n- [minipass-json-stream](http://npm.im/minipass-json-stream)\n- [minipass-sized](http://npm.im/minipass-sized)\n\n## Usage in TypeScript\n\nThe `Minipass` class takes three type template definitions:\n\n- `RType` the type being read, which defaults to `Buffer`. If\n  `RType` is `string`, then the constructor _must_ get an options\n  object specifying either an `encoding` or `objectMode: true`.\n  If it's anything other than `string` or `Buffer`, then it\n  _must_ get an options object specifying `objectMode: true`.\n- `WType` the type being written. If `RType` is `Buffer` or\n  `string`, then this defaults to `ContiguousData` (Buffer,\n  string, ArrayBuffer, or ArrayBufferView). Otherwise, it\n  defaults to `RType`.\n- `Events` type mapping event names to the arguments emitted\n  with that event, which extends `Minipass.Events`.\n\nTo declare types for custom events in subclasses, extend the\nthird parameter with your own event signatures. For example:\n\n```js\nimport { Minipass } from 'minipass'\n\n// a NDJSON stream that emits 'jsonError' when it can't stringify\nexport interface Events extends Minipass.Events {\n  jsonError: [e: Error]\n}\n\nexport class NDJSONStream extends Minipass<string, any, Events> {\n  constructor() {\n    super({ objectMode: true })\n  }\n\n  // data is type `any` because that's WType\n  write(data, encoding, cb) {\n    try {\n      const json = JSON.stringify(data)\n      return super.write(json + '\\n', encoding, cb)\n    } catch (er) {\n      if (!er instanceof Error) {\n        er = Object.assign(new Error('json stringify failed'), {\n          cause: er,\n        })\n      }\n      // trying to emit with something OTHER than an error will\n      // fail, because we declared the event arguments type.\n      this.emit('jsonError', er)\n    }\n  }\n}\n\nconst s = new NDJSONStream()\ns.on('jsonError', e => {\n  // here, TS knows that e is an Error\n})\n```\n\nEmitting/handling events that aren't declared in this way is\nfine, but the arguments will be typed as `unknown`.\n\n## Differences from Node.js Streams\n\nThere are several things that make Minipass streams different\nfrom (and in some ways superior to) Node.js core streams.\n\nPlease read these caveats if you are familiar with node-core\nstreams and intend to use Minipass streams in your programs.\n\nYou can avoid most of these differences entirely (for a very\nsmall performance penalty) by setting `{async: true}` in the\nconstructor options.\n\n### Timing\n\nMinipass streams are designed to support synchronous use-cases.\nThus, data is emitted as soon as it is available, always. It is\nbuffered until read, but no longer. Another way to look at it is\nthat Minipass streams are exactly as synchronous as the logic\nthat writes into them.\n\nThis can be surprising if your code relies on\n`PassThrough.write()` always providing data on the next tick\nrather than the current one, or being able to call `resume()` and\nnot have the entire buffer disappear immediately.\n\nHowever, without this synchronicity guarantee, there would be no\nway for Minipass to achieve the speeds it does, or support the\nsynchronous use cases that it does. Simply put, waiting takes\ntime.\n\nThis non-deferring approach makes Minipass streams much easier to\nreason about, especially in the context of Promises and other\nflow-control mechanisms.\n\nExample:\n\n```js\n// hybrid module, either works\nimport { Minipass } from 'minipass'\n// or:\nconst { Minipass } = require('minipass')\n\nconst stream = new Minipass()\nstream.on('data', () => console.log('data event'))\nconsole.log('before write')\nstream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// data event\n// after write\n```\n\n### Exception: Async Opt-In\n\nIf you wish to have a Minipass stream with behavior that more\nclosely mimics Node.js core streams, you can set the stream in\nasync mode either by setting `async: true` in the constructor\noptions, or by setting `stream.async = true` later on.\n\n```js\n// hybrid module, either works\nimport { Minipass } from 'minipass'\n// or:\nconst { Minipass } = require('minipass')\n\nconst asyncStream = new Minipass({ async: true })\nasyncStream.on('data', () => console.log('data event'))\nconsole.log('before write')\nasyncStream.write('hello')\nconsole.log('after write')\n// output:\n// before write\n// after write\n// data event <-- this is deferred until the next tick\n```\n\nSwitching _out_ of async mode is unsafe, as it could cause data\ncorruption, and so is not enabled. Example:\n\n```js\nimport { Minipass } from 'minipass'\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nsetStreamSyncAgainSomehow(stream) // <-- this doesn't actually exist!\nstream.write('world')\nconsole.log('after writes')\n// hypothetical output would be:\n// before writes\n// world\n// after writes\n// hello\n// NOT GOOD!\n```\n\nTo avoid this problem, once set into async mode, any attempt to\nmake the stream sync again will be ignored.\n\n```js\nconst { Minipass } = require('minipass')\nconst stream = new Minipass({ encoding: 'utf8' })\nstream.on('data', chunk => console.log(chunk))\nstream.async = true\nconsole.log('before writes')\nstream.write('hello')\nstream.async = false // <-- no-op, stream already async\nstream.write('world')\nconsole.log('after writes')\n// actual output:\n// before writes\n// after writes\n// hello\n// world\n```\n\n### No High/Low Water Marks\n\nNode.js core streams will optimistically fill up a buffer,\nreturning `true` on all writes until the limit is hit, even if\nthe data has nowhere to go. Then, they will not attempt to draw\nmore data in until the buffer size dips below a minimum value.\n\nMinipass streams are much simpler. The `write()` method will\nreturn `true` if the data has somewhere to go (which is to say,\ngiven the timing guarantees, that the data is already there by\nthe time `write()` returns).\n\nIf the data has nowhere to go, then `write()` returns false, and\nthe data sits in a buffer, to be drained out immediately as soon\nas anyone consumes it.\n\nSince nothing is ever buffered unnecessarily, there is much less\ncopying data, and less bookkeeping about buffer capacity levels.\n\n### Hazards of Buffering (or: Why Minipass Is So Fast)\n\nSince data written to a Minipass stream is immediately written\nall the way through the pipeline, and `write()` always returns\ntrue/false based on whether the data was fully flushed,\nbackpressure is communicated immediately to the upstream caller.\nThis minimizes buffering.\n\nConsider this case:\n\n```js\nconst { PassThrough } = require('stream')\nconst p1 = new PassThrough({ highWaterMark: 1024 })\nconst p2 = new PassThrough({ highWaterMark: 1024 })\nconst p3 = new PassThrough({ highWaterMark: 1024 })\nconst p4 = new PassThrough({ highWaterMark: 1024 })\n\np1.pipe(p2).pipe(p3).pipe(p4)\np4.on('data', () => console.log('made it through'))\n\n// this returns false and buffers, then writes to p2 on next tick (1)\n// p2 returns false and buffers, pausing p1, then writes to p3 on next tick (2)\n// p3 returns false and buffers, pausing p2, then writes to p4 on next tick (3)\n// p4 returns false and buffers, pausing p3, then emits 'data' and 'drain'\n// on next tick (4)\n// p3 sees p4's 'drain' event, and calls resume(), emitting 'resume' and\n// 'drain' on next tick (5)\n// p2 sees p3's 'drain', calls resume(), emits 'resume' and 'drain' on next tick (6)\n// p1 sees p2's 'drain', calls resume(), emits 'resume' and 'drain' on next\n// tick (7)\n\np1.write(Buffer.alloc(2048)) // returns false\n```\n\nAlong the way, the data was buffered and deferred at each stage,\nand multiple event deferrals happened, for an unblocked pipeline\nwhere it was perfectly safe to write all the way through!\n\nFurthermore, setting a `highWaterMark` of `1024` might lead\nsomeone reading the code to think an advisory maximum of 1KiB is\nbeing set for the pipeline. However, the actual advisory\nbuffering level is the _sum_ of `highWaterMark` values, since\neach one has its own bucket.\n\nConsider the Minipass case:\n\n```js\nconst m1 = new Minipass()\nconst m2 = new Minipass()\nconst m3 = new Minipass()\nconst m4 = new Minipass()\n\nm1.pipe(m2).pipe(m3).pipe(m4)\nm4.on('data', () => console.log('made it through'))\n\n// m1 is flowing, so it writes the data to m2 immediately\n// m2 is flowing, so it writes the data to m3 immediately\n// m3 is flowing, so it writes the data to m4 immediately\n// m4 is flowing, so it fires the 'data' event immediately, returns true\n// m4's write returned true, so m3 is still flowing, returns true\n// m3's write returned true, so m2 is still flowing, returns true\n// m2's write returned true, so m1 is still flowing, returns true\n// No event deferrals or buffering along the way!\n\nm1.write(Buffer.alloc(2048)) // returns true\n```\n\nIt is extremely unlikely that you _don't_ want to buffer any data\nwritten, or _ever_ buffer data that can be flushed all the way\nthrough. Neither node-core streams nor Minipass ever fail to\nbuffer written data, but node-core streams do a lot of\nunnecessary buffering and pausing.\n\nAs always, the faster implementation is the one that does less\nstuff and waits less time to do it.\n\n### Immediately emit `end` for empty streams (when not paused)\n\nIf a stream is not paused, and `end()` is called before writing\nany data into it, then it will emit `end` immediately.\n\nIf you have logic that occurs on the `end` event which you don't\nwant to potentially happen immediately (for example, closing file\ndescriptors, moving on to the next entry in an archive parse\nstream, etc.) then be sure to call `stream.pause()` on creation,\nand then `stream.resume()` once you are ready to respond to the\n`end` event.\n\nHowever, this is _usually_ not a problem because:\n\n### Emit `end` When Asked\n\nOne hazard of immediately emitting `'end'` is that you may not\nyet have had a chance to add a listener. In order to avoid this\nhazard, Minipass streams safely re-emit the `'end'` event if a\nnew listener is added after `'end'` has been emitted.\n\nIe, if you do `stream.on('end', someFunction)`, and the stream\nhas already emitted `end`, then it will call the handler right\naway. (You can think of this somewhat like attaching a new\n`.then(fn)` to a previously-resolved Promise.)\n\nTo prevent calling handlers multiple times who would not expect\nmultiple ends to occur, all listeners are removed from the\n`'end'` event whenever it is emitted.\n\n### Emit `error` When Asked\n\nThe most recent error object passed to the `'error'` event is\nstored on the stream. If a new `'error'` event handler is added,\nand an error was previously emitted, then the event handler will\nbe called immediately (or on `process.nextTick` in the case of\nasync streams).\n\nThis makes it much more difficult to end up trying to interact\nwith a broken stream, if the error handler is added after an\nerror was previously emitted.\n\n### Impact of \"immediate flow\" on Tee-streams\n\nA \"tee stream\" is a stream piping to multiple destinations:\n\n```js\nconst tee = new Minipass()\nt.pipe(dest1)\nt.pipe(dest2)\nt.write('foo') // goes to both destinations\n```\n\nSince Minipass streams _immediately_ process any pending data\nthrough the pipeline when a new pipe destination is added, this\ncan have surprising effects, especially when a stream comes in\nfrom some other function and may or may not have data in its\nbuffer.\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.pipe(dest1) // 'foo' chunk flows to dest1 immediately, and is gone\nsrc.pipe(dest2) // gets nothing!\n```\n\nOne solution is to create a dedicated tee-stream junction that\npipes to both locations, and then pipe to _that_ instead.\n\n```js\n// Safe example: tee to both places\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.pipe(dest1)\ntee.pipe(dest2)\nsrc.pipe(tee) // tee gets 'foo', pipes to both locations\n```\n\nThe same caveat applies to `on('data')` event listeners. The\nfirst one added will _immediately_ receive all of the data,\nleaving nothing for the second:\n\n```js\n// WARNING! WILL LOSE DATA!\nconst src = new Minipass()\nsrc.write('foo')\nsrc.on('data', handler1) // receives 'foo' right away\nsrc.on('data', handler2) // nothing to see here!\n```\n\nUsing a dedicated tee-stream can be used in this case as well:\n\n```js\n// Safe example: tee to both data handlers\nconst src = new Minipass()\nsrc.write('foo')\nconst tee = new Minipass()\ntee.on('data', handler1)\ntee.on('data', handler2)\nsrc.pipe(tee)\n```\n\nAll of the hazards in this section are avoided by setting `{\nasync: true }` in the Minipass constructor, or by setting\n`stream.async = true` afterwards. Note that this does add some\noverhead, so should only be done in cases where you are willing\nto lose a bit of performance in order to avoid having to refactor\nprogram logic.\n\n## USAGE\n\nIt's a stream! Use it like a stream and it'll most likely do what\nyou want.\n\n```js\nimport { Minipass } from 'minipass'\nconst mp = new Minipass(options) // options is optional\nmp.write('foo')\nmp.pipe(someOtherStream)\nmp.end('bar')\n```\n\n### OPTIONS\n\n- `encoding` How would you like the data coming _out_ of the\n  stream to be encoded? Accepts any values that can be passed to\n  `Buffer.toString()`.\n- `objectMode` Emit data exactly as it comes in. This will be\n  flipped on by default if you write() something other than a\n  string or Buffer at any point. Setting `objectMode: true` will\n  prevent setting any encoding value.\n- `async` Defaults to `false`. Set to `true` to defer data\n  emission until next tick. This reduces performance slightly,\n  but makes Minipass streams use timing behavior closer to Node\n  core streams. See [Timing](#timing) for more details.\n- `signal` An `AbortSignal` that will cause the stream to unhook\n  itself from everything and become as inert as possible. Note\n  that providing a `signal` parameter will make `'error'` events\n  no longer throw if they are unhandled, but they will still be\n  emitted to handlers if any are attached.\n\n### API\n\nImplements the user-facing portions of Node.js's `Readable` and\n`Writable` streams.\n\n### Methods\n\n- `write(chunk, [encoding], [callback])` - Put data in. (Note\n  that, in the base Minipass class, the same data will come out.)\n  Returns `false` if the stream will buffer the next write, or\n  true if it's still in \"flowing\" mode.\n- `end([chunk, [encoding]], [callback])` - Signal that you have\n  no more data to write. This will queue an `end` event to be\n  fired when all the data has been consumed.\n- `pause()` - No more data for a while, please. This also\n  prevents `end` from being emitted for empty streams until the\n  stream is resumed.\n- `resume()` - Resume the stream. If there's data in the buffer,\n  it is all discarded. Any buffered events are immediately\n  emitted.\n- `pipe(dest)` - Send all output to the stream provided. When\n  data is emitted, it is immediately written to any and all pipe\n  destinations. (Or written on next tick in `async` mode.)\n- `unpipe(dest)` - Stop piping to the destination stream. This is\n  immediate, meaning that any asynchronously queued data will\n  _not_ make it to the destination when running in `async` mode.\n  - `options.end` - Boolean, end the destination stream when the\n    source stream ends. Default `true`.\n  - `options.proxyErrors` - Boolean, proxy `error` events from\n    the source stream to the destination stream. Note that errors\n    are _not_ proxied after the pipeline terminates, either due\n    to the source emitting `'end'` or manually unpiping with\n    `src.unpipe(dest)`. Default `false`.\n- `on(ev, fn)`, `emit(ev, fn)` - Minipass streams are\n  EventEmitters. Some events are given special treatment,\n  however. (See below under \"events\".)\n- `promise()` - Returns a Promise that resolves when the stream\n  emits `end`, or rejects if the stream emits `error`.\n- `collect()` - Return a Promise that resolves on `end` with an\n  array containing each chunk of data that was emitted, or\n  rejects if the stream emits `error`. Note that this consumes\n  the stream data.\n- `concat()` - Same as `collect()`, but concatenates the data\n  into a single Buffer object. Will reject the returned promise\n  if the stream is in objectMode, or if it goes into objectMode\n  by the end of the data.\n- `read(n)` - Consume `n` bytes of data out of the buffer. If `n`\n  is not provided, then consume all of it. If `n` bytes are not\n  available, then it returns null. **Note** consuming streams in\n  this way is less efficient, and can lead to unnecessary Buffer\n  copying.\n- `destroy([er])` - Destroy the stream. If an error is provided,\n  then an `'error'` event is emitted. If the stream has a\n  `close()` method, and has not emitted a `'close'` event yet,\n  then `stream.close()` will be called. Any Promises returned by\n  `.promise()`, `.collect()` or `.concat()` will be rejected.\n  After being destroyed, writing to the stream will emit an\n  error. No more data will be emitted if the stream is destroyed,\n  even if it was previously buffered.\n\n### Properties\n\n- `bufferLength` Read-only. Total number of bytes buffered, or in\n  the case of objectMode, the total number of objects.\n- `encoding` Read-only. The encoding that has been set.\n- `flowing` Read-only. Boolean indicating whether a chunk written\n  to the stream will be immediately emitted.\n- `emittedEnd` Read-only. Boolean indicating whether the end-ish\n  events (ie, `end`, `prefinish`, `finish`) have been emitted.\n  Note that listening on any end-ish event will immediateyl\n  re-emit it if it has already been emitted.\n- `writable` Whether the stream is writable. Default `true`. Set\n  to `false` when `end()`\n- `readable` Whether the stream is readable. Default `true`.\n- `pipes` An array of Pipe objects referencing streams that this\n  stream is piping into.\n- `destroyed` A getter that indicates whether the stream was\n  destroyed.\n- `paused` True if the stream has been explicitly paused,\n  otherwise false.\n- `objectMode` Indicates whether the stream is in `objectMode`.\n- `aborted` Readonly property set when the `AbortSignal`\n  dispatches an `abort` event.\n\n### Events\n\n- `data` Emitted when there's data to read. Argument is the data\n  to read. This is never emitted while not flowing. If a listener\n  is attached, that will resume the stream.\n- `end` Emitted when there's no more data to read. This will be\n  emitted immediately for empty streams when `end()` is called.\n  If a listener is attached, and `end` was already emitted, then\n  it will be emitted again. All listeners are removed when `end`\n  is emitted.\n- `prefinish` An end-ish event that follows the same logic as\n  `end` and is emitted in the same conditions where `end` is\n  emitted. Emitted after `'end'`.\n- `finish` An end-ish event that follows the same logic as `end`\n  and is emitted in the same conditions where `end` is emitted.\n  Emitted after `'prefinish'`.\n- `close` An indication that an underlying resource has been\n  released. Minipass does not emit this event, but will defer it\n  until after `end` has been emitted, since it throws off some\n  stream libraries otherwise.\n- `drain` Emitted when the internal buffer empties, and it is\n  again suitable to `write()` into the stream.\n- `readable` Emitted when data is buffered and ready to be read\n  by a consumer.\n- `resume` Emitted when stream changes state from buffering to\n  flowing mode. (Ie, when `resume` is called, `pipe` is called,\n  or a `data` event listener is added.)\n\n### Static Methods\n\n- `Minipass.isStream(stream)` Returns `true` if the argument is a\n  stream, and false otherwise. To be considered a stream, the\n  object must be either an instance of Minipass, or an\n  EventEmitter that has either a `pipe()` method, or both\n  `write()` and `end()` methods. (Pretty much any stream in\n  node-land will return `true` for this.)\n\n## EXAMPLES\n\nHere are some examples of things you can do with Minipass\nstreams.\n\n### simple \"are you done yet\" promise\n\n```js\nmp.promise().then(\n  () => {\n    // stream is finished\n  },\n  er => {\n    // stream emitted an error\n  }\n)\n```\n\n### collecting\n\n```js\nmp.collect().then(all => {\n  // all is an array of all the data emitted\n  // encoding is supported in this case, so\n  // so the result will be a collection of strings if\n  // an encoding is specified, or buffers/objects if not.\n  //\n  // In an async function, you may do\n  // const data = await stream.collect()\n})\n```\n\n### collecting into a single blob\n\nThis is a bit slower because it concatenates the data into one\nchunk for you, but if you're going to do it yourself anyway, it's\nconvenient this way:\n\n```js\nmp.concat().then(onebigchunk => {\n  // onebigchunk is a string if the stream\n  // had an encoding set, or a buffer otherwise.\n})\n```\n\n### iteration\n\nYou can iterate over streams synchronously or asynchronously in\nplatforms that support it.\n\nSynchronous iteration will end when the currently available data\nis consumed, even if the `end` event has not been reached. In\nstring and buffer mode, the data is concatenated, so unless\nmultiple writes are occurring in the same tick as the `read()`,\nsync iteration loops will generally only have a single iteration.\n\nTo consume chunks in this way exactly as they have been written,\nwith no flattening, create the stream with the `{ objectMode:\ntrue }` option.\n\n```js\nconst mp = new Minipass({ objectMode: true })\nmp.write('a')\nmp.write('b')\nfor (let letter of mp) {\n  console.log(letter) // a, b\n}\nmp.write('c')\nmp.write('d')\nfor (let letter of mp) {\n  console.log(letter) // c, d\n}\nmp.write('e')\nmp.end()\nfor (let letter of mp) {\n  console.log(letter) // e\n}\nfor (let letter of mp) {\n  console.log(letter) // nothing\n}\n```\n\nAsynchronous iteration will continue until the end event is reached,\nconsuming all of the data.\n\n```js\nconst mp = new Minipass({ encoding: 'utf8' })\n\n// some source of some data\nlet i = 5\nconst inter = setInterval(() => {\n  if (i-- > 0) mp.write(Buffer.from('foo\\n', 'utf8'))\n  else {\n    mp.end()\n    clearInterval(inter)\n  }\n}, 100)\n\n// consume the data with asynchronous iteration\nasync function consume() {\n  for await (let chunk of mp) {\n    console.log(chunk)\n  }\n  return 'ok'\n}\n\nconsume().then(res => console.log(res))\n// logs `foo\\n` 5 times, and then `ok`\n```\n\n### subclass that `console.log()`s everything written into it\n\n```js\nclass Logger extends Minipass {\n  write(chunk, encoding, callback) {\n    console.log('WRITE', chunk, encoding)\n    return super.write(chunk, encoding, callback)\n  }\n  end(chunk, encoding, callback) {\n    console.log('END', chunk, encoding)\n    return super.end(chunk, encoding, callback)\n  }\n}\n\nsomeSource.pipe(new Logger()).pipe(someDest)\n```\n\n### same thing, but using an inline anonymous class\n\n```js\n// js classes are fun\nsomeSource\n  .pipe(\n    new (class extends Minipass {\n      emit(ev, ...data) {\n        // let's also log events, because debugging some weird thing\n        console.log('EMIT', ev)\n        return super.emit(ev, ...data)\n      }\n      write(chunk, encoding, callback) {\n        console.log('WRITE', chunk, encoding)\n        return super.write(chunk, encoding, callback)\n      }\n      end(chunk, encoding, callback) {\n        console.log('END', chunk, encoding)\n        return super.end(chunk, encoding, callback)\n      }\n    })()\n  )\n  .pipe(someDest)\n```\n\n### subclass that defers 'end' for some reason\n\n```js\nclass SlowEnd extends Minipass {\n  emit(ev, ...args) {\n    if (ev === 'end') {\n      console.log('going to end, hold on a sec')\n      setTimeout(() => {\n        console.log('ok, ready to end now')\n        super.emit('end', ...args)\n      }, 100)\n      return true\n    } else {\n      return super.emit(ev, ...args)\n    }\n  }\n}\n```\n\n### transform that creates newline-delimited JSON\n\n```js\nclass NDJSONEncode extends Minipass {\n  write(obj, cb) {\n    try {\n      // JSON.stringify can throw, emit an error on that\n      return super.write(JSON.stringify(obj) + '\\n', 'utf8', cb)\n    } catch (er) {\n      this.emit('error', er)\n    }\n  }\n  end(obj, cb) {\n    if (typeof obj === 'function') {\n      cb = obj\n      obj = undefined\n    }\n    if (obj !== undefined) {\n      this.write(obj)\n    }\n    return super.end(cb)\n  }\n}\n```\n\n### transform that parses newline-delimited JSON\n\n```js\nclass NDJSONDecode extends Minipass {\n  constructor(options) {\n    // always be in object mode, as far as Minipass is concerned\n    super({ objectMode: true })\n    this._jsonBuffer = ''\n  }\n  write(chunk, encoding, cb) {\n    if (\n      typeof chunk === 'string' &&\n      typeof encoding === 'string' &&\n      encoding !== 'utf8'\n    ) {\n      chunk = Buffer.from(chunk, encoding).toString()\n    } else if (Buffer.isBuffer(chunk)) {\n      chunk = chunk.toString()\n    }\n    if (typeof encoding === 'function') {\n      cb = encoding\n    }\n    const jsonData = (this._jsonBuffer + chunk).split('\\n')\n    this._jsonBuffer = jsonData.pop()\n    for (let i = 0; i < jsonData.length; i++) {\n      try {\n        // JSON.parse can throw, emit an error on that\n        super.write(JSON.parse(jsonData[i]))\n      } catch (er) {\n        this.emit('error', er)\n        continue\n      }\n    }\n    if (cb) cb()\n  }\n}\n```\n", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2024-05-24T00:42:21.343Z", "created": "2017-03-14T00:11:57.420Z", "1.0.0": "2017-03-14T00:11:57.420Z", "1.0.1": "2017-03-22T00:26:16.857Z", "1.0.2": "2017-03-22T04:40:04.601Z", "1.1.0": "2017-03-28T06:13:10.262Z", "1.1.1": "2017-03-28T07:06:23.059Z", "1.1.2": "2017-03-28T08:14:58.366Z", "1.1.3": "2017-03-29T00:58:09.871Z", "1.1.4": "2017-03-29T01:25:23.560Z", "1.1.5": "2017-03-29T06:18:55.673Z", "1.1.6": "2017-03-29T06:46:51.682Z", "1.1.7": "2017-04-03T20:21:06.787Z", "1.1.8": "2017-04-10T17:52:57.826Z", "1.1.9": "2017-04-22T03:02:25.768Z", "1.1.10": "2017-04-28T00:40:45.977Z", "1.1.11": "2017-04-30T02:36:01.406Z", "1.2.0": "2017-04-30T04:14:30.434Z", "2.0.0": "2017-05-04T07:59:52.477Z", "2.0.1": "2017-05-04T20:46:53.600Z", "2.0.2": "2017-05-10T17:07:21.058Z", "2.1.0": "2017-06-14T16:17:30.707Z", "2.1.1": "2017-06-14T16:26:50.686Z", "2.2.0": "2017-07-09T05:17:47.759Z", "2.2.1": "2017-07-10T05:09:48.146Z", "2.2.2": "2018-03-20T16:23:26.657Z", "2.2.3": "2018-03-20T16:26:35.853Z", "2.2.4": "2018-03-20T16:44:28.516Z", "2.3.0": "2018-05-06T17:52:27.992Z", "2.3.1": "2018-05-18T23:25:47.557Z", "2.3.2": "2018-05-22T03:42:41.482Z", "2.3.3": "2018-05-22T18:59:35.084Z", "2.3.4": "2018-08-10T16:25:21.783Z", "2.3.5": "2018-10-23T21:46:18.167Z", "2.4.0": "2019-08-23T16:36:02.314Z", "2.5.0": "2019-08-28T23:20:03.661Z", "2.5.1": "2019-09-09T21:34:00.679Z", "2.6.0": "2019-09-16T06:12:45.936Z", "2.6.1": "2019-09-16T20:55:48.943Z", "2.6.2": "2019-09-16T21:58:13.409Z", "2.6.3": "2019-09-17T14:36:56.519Z", "2.6.4": "2019-09-17T16:20:15.752Z", "2.6.5": "2019-09-17T22:18:31.028Z", "2.7.0": "2019-09-22T06:39:19.777Z", "2.8.0": "2019-09-22T23:56:33.134Z", "2.8.1": "2019-09-23T00:04:43.683Z", "2.8.2": "2019-09-23T16:57:17.916Z", "2.8.3": "2019-09-23T18:48:30.530Z", "2.8.4": "2019-09-24T01:03:32.894Z", "2.8.5": "2019-09-24T07:56:04.688Z", "2.8.6": "2019-09-24T16:22:27.580Z", "2.9.0": "2019-09-24T23:43:01.864Z", "3.0.0": "2019-09-30T20:16:05.884Z", "3.0.1": "2019-10-02T16:34:57.711Z", "3.1.0": "2019-10-20T04:53:28.706Z", "3.1.1": "2019-10-24T21:34:57.398Z", "3.1.2": "2020-05-09T20:59:12.842Z", "3.1.3": "2020-05-13T01:00:37.001Z", "3.1.4": "2021-09-14T14:39:09.622Z", "3.1.5": "2021-09-14T19:36:39.296Z", "3.1.6": "2021-12-06T19:45:29.457Z", "3.2.0": "2022-06-08T17:23:00.538Z", "3.2.1": "2022-06-10T18:42:30.081Z", "3.3.0": "2022-06-20T02:16:02.422Z", "3.3.1": "2022-06-20T02:52:10.767Z", "3.3.2": "2022-06-20T02:57:26.134Z", "3.3.3": "2022-06-20T03:38:50.485Z", "3.3.4": "2022-06-28T18:43:35.980Z", "3.3.5": "2022-07-24T22:23:31.633Z", "3.3.6": "2022-11-25T07:54:48.420Z", "4.0.0": "2022-11-27T00:17:30.622Z", "4.0.1": "2023-01-30T15:27:33.931Z", "4.0.2": "2023-02-05T17:49:25.951Z", "4.0.3": "2023-02-07T21:58:13.198Z", "4.1.0": "2023-02-22T04:20:08.792Z", "4.2.0": "2023-02-22T05:38:13.922Z", "4.2.1": "2023-02-24T04:48:58.574Z", "4.2.2": "2023-02-26T07:18:40.877Z", "4.2.3": "2023-02-26T07:45:46.439Z", "4.2.4": "2023-02-26T07:51:00.557Z", "4.2.5": "2023-03-11T19:27:30.190Z", "4.2.6": "2023-04-09T21:00:10.988Z", "4.2.7": "2023-04-09T21:24:19.901Z", "5.0.0": "2023-04-09T21:54:10.390Z", "4.2.8": "2023-04-11T16:00:11.170Z", "6.0.0": "2023-05-15T04:41:09.361Z", "6.0.1": "2023-05-15T22:26:16.226Z", "6.0.2": "2023-05-17T21:16:50.370Z", "7.0.0": "2023-07-08T00:14:43.854Z", "7.0.1": "2023-07-08T00:23:19.663Z", "7.0.2": "2023-07-11T05:17:30.315Z", "7.0.3": "2023-08-12T19:30:03.611Z", "7.0.4": "2023-09-28T23:58:33.597Z", "7.1.0": "2024-05-04T02:00:38.139Z", "7.1.1": "2024-05-09T13:49:27.137Z", "7.1.2": "2024-05-24T00:42:21.149Z"}, "homepage": "https://github.com/isaacs/minipass#readme", "keywords": ["passthrough", "stream"], "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/minipass/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"davidrapin": true, "isaacs": true}}