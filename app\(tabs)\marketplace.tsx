import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Store, MapPin, ShoppingBag, Receipt, Package, ChevronRight } from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { router } from 'expo-router';

const { width, height } = Dimensions.get('window');

function MarketplaceContent() {
  const { isDark } = useTheme();
  const { user, profile } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const marketplaceOptions = [
    {
      id: 'market',
      title: '市场地图',
      subtitle: '浏览开放的商店',
      icon: MapPin,
      color: colors.secondary,
      route: '/market',
    },
    {
      id: 'shop',
      title: '我的商店',
      subtitle: '管理您的商店',
      icon: Store,
      color: colors.primary,
      route: '/shop',
    },
    {
      id: 'products',
      title: '商品浏览',
      subtitle: '发现神奇商品',
      icon: Package,
      color: colors.accent,
      route: '/products',
    },
    {
      id: 'transactions',
      title: '交易记录',
      subtitle: '查看买卖记录',
      icon: Receipt,
      color: colors.warning,
      route: '/transactions',
    },
  ];

  const handleOptionPress = (route: string) => {
    router.push(route as any);
  };

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    header: {
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    merchantContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 20,
      width: '100%',
    },
    merchantFrame: {
      width: 70,
      height: 70,
      borderRadius: 35,
      borderWidth: 3,
      borderColor: colors.secondary,
      overflow: 'hidden',
      position: 'relative',
      marginRight: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.5 : 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    merchantImage: {
      width: '100%',
      height: '100%',
    },
    marketBadge: {
      position: 'absolute',
      bottom: -5,
      right: -5,
      backgroundColor: colors.secondary,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.background,
    },
    merchantInfo: {
      flex: 1,
    },
    merchantTitle: {
      fontSize: 18,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 2,
    },
    merchantSubtitle: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    titleContainer: {
      alignItems: 'center',
    },
    mainTitle: {
      fontSize: 28,
      color: colors.text,
      textAlign: 'center',
      marginBottom: 8,
      fontWeight: '700',
      textShadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.2)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    titleUnderline: {
      width: 80,
      height: 3,
      backgroundColor: colors.secondary,
      borderRadius: 2,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 30,
    },
    optionsContainer: {
      paddingHorizontal: 20,
    },
    optionCard: {
      marginBottom: 16,
      borderRadius: 16,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    optionContent: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 20,
    },
    optionIconContainer: {
      width: 60,
      height: 60,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    optionInfo: {
      flex: 1,
    },
    optionTitle: {
      fontSize: 18,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 4,
    },
    optionSubtitle: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    chevronContainer: {
      padding: 8,
    },
  });

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{ 
          uri: isDark 
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary
          ]}
          style={dynamicStyles.overlay}
        >
          <SafeAreaView style={dynamicStyles.safeArea}>
            {/* Header */}
            <Animated.View 
              style={[
                dynamicStyles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
                }
              ]}
            >
              <View style={dynamicStyles.merchantContainer}>
                <View style={dynamicStyles.merchantFrame}>
                  <Image
                    source={{ uri: profile?.avatar_url || 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                    style={dynamicStyles.merchantImage}
                    resizeMode="cover"
                  />
                  <View style={dynamicStyles.marketBadge}>
                    <ShoppingBag size={16} color={colors.background} />
                  </View>
                </View>
                <View style={dynamicStyles.merchantInfo}>
                  <Text style={dynamicStyles.merchantTitle}>斯卡布罗集市</Text>
                  <Text style={dynamicStyles.merchantSubtitle}>探索、交易、成长</Text>
                </View>
              </View>

              <View style={dynamicStyles.titleContainer}>
                <Text style={dynamicStyles.mainTitle}>集市中心</Text>
                <View style={dynamicStyles.titleUnderline} />
                <Text style={dynamicStyles.subtitle}>您的商业冒险从这里开始</Text>
              </View>
            </Animated.View>

            <ScrollView 
              style={dynamicStyles.scrollView} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={dynamicStyles.scrollContent}
            >
              {/* Marketplace Options */}
              <Animated.View 
                style={[
                  dynamicStyles.optionsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                {marketplaceOptions.map((option) => (
                  <BlurView key={option.id} intensity={30} style={dynamicStyles.optionCard}>
                    <TouchableOpacity 
                      style={dynamicStyles.optionContent}
                      onPress={() => handleOptionPress(option.route)}
                      activeOpacity={0.8}
                    >
                      <View style={[
                        dynamicStyles.optionIconContainer,
                        { backgroundColor: option.color + '20' }
                      ]}>
                        <option.icon size={32} color={option.color} />
                      </View>
                      <View style={dynamicStyles.optionInfo}>
                        <Text style={dynamicStyles.optionTitle}>{option.title}</Text>
                        <Text style={dynamicStyles.optionSubtitle}>{option.subtitle}</Text>
                      </View>
                      <View style={dynamicStyles.chevronContainer}>
                        <ChevronRight size={24} color={colors.textSecondary} />
                      </View>
                    </TouchableOpacity>
                  </BlurView>
                ))}
              </Animated.View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

export default function MarketplaceScreen() {
  return (
    <ProtectedRoute>
      <MarketplaceContent />
    </ProtectedRoute>
  );
}