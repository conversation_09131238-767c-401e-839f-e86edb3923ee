import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { 
  MapPin, 
  Compass, 
  Target,
  Eye,
  TreePine,
  Mountain,
  Castle,
  Coins,
  Star,
  Clock,
  ChevronRight,
  Zap,
  Shield,
  Sword,
  Crown,
  Gem,
  Scroll,
  Navigation
} from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

// Web-only imports
let MapContainer: any = null;
let TileLayer: any = null;
let Marker: any = null;
let Popup: any = null;
let L: any = null;

if (Platform.OS === 'web') {
  try {
    const leaflet = require('react-leaflet');
    MapContainer = leaflet.MapContainer;
    TileLayer = leaflet.TileLayer;
    Marker = leaflet.Marker;
    Popup = leaflet.Popup;
    L = require('leaflet');
    
    // Fix for default markers in Leaflet
    delete (L.Icon.Default.prototype as any)._getIconUrl;
    L.Icon.Default.mergeOptions({
      iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
      iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
    });
  } catch (error) {
    console.warn('Leaflet not available:', error);
  }
}

interface ExplorationArea {
  id: string;
  name: string;
  description: string;
  difficulty: 'easy' | 'medium' | 'hard' | 'legendary';
  rewards: string[];
  timeRequired: string;
  discovered: boolean;
  completed: boolean;
  icon: any;
  coordinates: [number, number]; // [lat, lng] for real map
  hints: string[];
  requirements?: string;
}

interface QuestHint {
  id: string;
  title: string;
  description: string;
  location: string;
  reward: string;
  urgency: 'low' | 'medium' | 'high';
  type: 'treasure' | 'monster' | 'mystery' | 'herb';
}

export default function ExplorationScreen() {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const [selectedArea, setSelectedArea] = useState<ExplorationArea | null>(null);
  const [showMap, setShowMap] = useState(false);

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Scarborough Fair area coordinates (fictional locations around Yorkshire, England)
  const explorationAreas: ExplorationArea[] = [
    {
      id: '1',
      name: '迷雾森林',
      description: '古老的森林中隐藏着珍贵的草药和神秘的生物',
      difficulty: 'easy',
      rewards: ['稀有草药', '50金币', '经验值+20'],
      timeRequired: '30分钟',
      discovered: true,
      completed: false,
      icon: TreePine,
      coordinates: [54.2781, -0.4053], // Near Scarborough
      hints: ['寻找发光的蘑菇', '注意森林深处的声音', '携带足够的药剂'],
    },
    {
      id: '2',
      name: '暗影峡谷',
      description: '危险的峡谷中藏着古老的宝藏和强大的敌人',
      difficulty: 'medium',
      rewards: ['古老武器', '150金币', '稀有宝石'],
      timeRequired: '45分钟',
      discovered: true,
      completed: false,
      icon: Mountain,
      coordinates: [54.2341, -0.3892], // Yorkshire Dales area
      hints: ['准备强力武器', '寻找隐藏的洞穴', '小心峡谷中的陷阱'],
      requirements: '需要等级5以上',
    },
    {
      id: '3',
      name: '废弃城堡',
      description: '被遗忘的城堡中可能隐藏着王室的秘密',
      difficulty: 'hard',
      rewards: ['传说装备', '300金币', '皇家徽章'],
      timeRequired: '60分钟',
      discovered: false,
      completed: false,
      icon: Castle,
      coordinates: [54.2891, -0.3721], // Near Scarborough Castle
      hints: ['寻找城堡的密室', '解开古老的谜题', '准备面对守护者'],
      requirements: '需要完成暗影峡谷',
    },
    {
      id: '4',
      name: '龙之巢穴',
      description: '传说中的龙族栖息地，蕴含无尽的财富和危险',
      difficulty: 'legendary',
      rewards: ['龙鳞护甲', '1000金币', '龙之精华'],
      timeRequired: '90分钟',
      discovered: false,
      completed: false,
      icon: Crown,
      coordinates: [54.2456, -0.4234], // Remote Yorkshire location
      hints: ['准备最强装备', '学习龙语咒文', '寻找龙之弱点'],
      requirements: '需要等级15以上',
    },
  ];

  const questHints: QuestHint[] = [
    {
      id: '1',
      title: '神秘商人的委托',
      description: '一位神秘商人需要你寻找失落的古老卷轴',
      location: '迷雾森林深处',
      reward: '200金币 + 魔法卷轴',
      urgency: 'high',
      type: 'mystery',
    },
    {
      id: '2',
      title: '草药采集任务',
      description: '村民急需治疗瘟疫的特殊草药',
      location: '暗影峡谷入口',
      reward: '100金币 + 声望+10',
      urgency: 'medium',
      type: 'herb',
    },
    {
      id: '3',
      title: '宝藏猎人的线索',
      description: '发现了通往隐藏宝藏的古老地图碎片',
      location: '废弃城堡地下室',
      reward: '500金币 + 稀有装备',
      urgency: 'low',
      type: 'treasure',
    },
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#4A7043';
      case 'medium': return '#D4A017';
      case 'hard': return '#DC3545';
      case 'legendary': return '#8B4513';
      default: return '#B0B7A4';
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '简单';
      case 'medium': return '中等';
      case 'hard': return '困难';
      case 'legendary': return '传说';
      default: return '未知';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return '#DC3545';
      case 'medium': return '#D4A017';
      case 'low': return '#4A7043';
      default: return '#B0B7A4';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'treasure': return Gem;
      case 'monster': return Sword;
      case 'mystery': return Scroll;
      case 'herb': return TreePine;
      default: return MapPin;
    }
  };

  const handleStartExploration = (area: ExplorationArea) => {
    console.log('Starting exploration:', area.name);
    // Here you would implement exploration logic
  };

  const handleAreaSelect = (area: ExplorationArea) => {
    setSelectedArea(area);
  };

  const createCustomIcon = (difficulty: string, discovered: boolean) => {
    if (Platform.OS !== 'web' || !L) return null;
    
    const color = discovered ? getDifficultyColor(difficulty) : '#B0B7A4';
    const iconHtml = `
      <div style="
        background-color: ${color};
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 3px solid #F5E8C7;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      ">
        ${discovered ? '⚔️' : '👁️'}
      </div>
    `;
    
    return L.divIcon({
      html: iconHtml,
      className: 'custom-marker',
      iconSize: [30, 30],
      iconAnchor: [15, 15],
    });
  };

  const renderMap = () => {
    if (Platform.OS !== 'web' || !MapContainer) {
      return (
        <View style={styles.mapFallback}>
          <Navigation size={40} color="#D4A017" />
          <Text style={styles.mapFallbackText}>地图功能仅在网页版可用</Text>
          <Text style={styles.mapFallbackSubtext}>请在浏览器中查看完整地图体验</Text>
        </View>
      );
    }

    return (
      <MapContainer
        center={[54.2781, -0.4053]} // Scarborough area
        zoom={12}
        style={{ height: '300px', width: '100%', borderRadius: '12px' }}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        {explorationAreas.map((area) => (
          <Marker
            key={area.id}
            position={area.coordinates}
            icon={createCustomIcon(area.difficulty, area.discovered)}
            eventHandlers={{
              click: () => handleAreaSelect(area),
            }}
          >
            <Popup>
              <div style={{ textAlign: 'center', minWidth: '150px' }}>
                <h3 style={{ margin: '0 0 8px 0', color: '#6B4E71' }}>{area.name}</h3>
                <p style={{ margin: '0 0 8px 0', fontSize: '12px', color: '#8B7355' }}>
                  {area.description}
                </p>
                <div style={{ 
                  display: 'inline-block',
                  padding: '4px 8px',
                  backgroundColor: `${getDifficultyColor(area.difficulty)}20`,
                  borderRadius: '6px',
                  fontSize: '10px',
                  color: getDifficultyColor(area.difficulty),
                  fontWeight: 'bold'
                }}>
                  {getDifficultyText(area.difficulty)}
                </div>
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    );
  };

  return (
    <View style={styles.container}>
      <ImageBackground
        source={{ uri: 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' }}
        style={styles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['rgba(245, 232, 199, 0.95)', 'rgba(245, 232, 199, 0.9)', 'rgba(245, 232, 199, 0.85)']}
          style={styles.overlay}
        >
          <SafeAreaView style={styles.safeArea}>
            {/* Header with Hunter */}
            <Animated.View 
              style={[
                styles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
                }
              ]}
            >
              <View style={styles.hunterContainer}>
                <View style={styles.hunterFrame}>
                  <Image
                    source={{ uri: 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                    style={styles.hunterImage}
                    resizeMode="cover"
                  />
                  <View style={styles.purpleCloakOverlay}>
                    <LinearGradient
                      colors={['rgba(107, 78, 113, 0.4)', 'rgba(107, 78, 113, 0.7)']}
                      style={styles.cloakGradient}
                    />
                  </View>
                  <View style={styles.bowBadge}>
                    <Target size={16} color="#F5E8C7" />
                  </View>
                  <View style={styles.mistEffect}>
                    <BlurView intensity={15} style={styles.mistBlur} />
                  </View>
                </View>
                <View style={styles.hunterInfo}>
                  <Text style={styles.hunterTitle}>精英猎手</Text>
                  <Text style={styles.hunterSubtitle}>探索未知领域</Text>
                </View>
              </View>

              <View style={styles.titleContainer}>
                <Text style={styles.mainTitle}>市场探索</Text>
                <View style={styles.titleUnderline} />
                <Text style={styles.subtitle}>发现隐藏的宝藏与秘密</Text>
              </View>
            </Animated.View>

            {/* Map Toggle */}
            <Animated.View 
              style={[
                styles.mapToggleContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}
            >
              <BlurView intensity={25} style={styles.mapToggleBar}>
                <TouchableOpacity
                  style={[
                    styles.toggleButton,
                    !showMap && styles.toggleButtonActive
                  ]}
                  onPress={() => setShowMap(false)}
                >
                  <Text style={[
                    styles.toggleText,
                    !showMap && styles.toggleTextActive
                  ]}>
                    区域视图
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.toggleButton,
                    showMap && styles.toggleButtonActive
                  ]}
                  onPress={() => setShowMap(true)}
                >
                  <Text style={[
                    styles.toggleText,
                    showMap && styles.toggleTextActive
                  ]}>
                    地图视图
                  </Text>
                </TouchableOpacity>
              </BlurView>
            </Animated.View>

            <ScrollView 
              style={styles.scrollView} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.scrollContent}
            >
              {/* OpenStreetMap or Area Grid */}
              <Animated.View 
                style={[
                  styles.mapContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <BlurView intensity={30} style={styles.mapCard}>
                  <View style={[styles.mapBorder, { borderColor: '#6B4E71' }]}>
                    <View style={styles.mapContent}>
                      <Text style={styles.mapTitle}>
                        {showMap ? '斯卡布罗探索地图' : '探索区域'}
                      </Text>
                      
                      {showMap ? (
                        <View style={styles.mapWrapper}>
                          {renderMap()}
                        </View>
                      ) : (
                        <>
                          <View style={styles.mapGrid}>
                            {explorationAreas.map((area) => (
                              <TouchableOpacity
                                key={area.id}
                                style={[
                                  styles.mapPoint,
                                  {
                                    left: `${(parseInt(area.id) - 1) * 25 + 10}%`,
                                    top: `${(parseInt(area.id) % 2) * 40 + 20}%`,
                                    backgroundColor: area.discovered 
                                      ? getDifficultyColor(area.difficulty) + '40'
                                      : 'rgba(176, 183, 164, 0.3)',
                                  }
                                ]}
                                onPress={() => handleAreaSelect(area)}
                                activeOpacity={0.7}
                              >
                                <area.icon 
                                  size={20} 
                                  color={area.discovered ? getDifficultyColor(area.difficulty) : '#B0B7A4'} 
                                />
                                {area.completed && (
                                  <View style={styles.completedBadge}>
                                    <Star size={12} color="#D4A017" fill="#D4A017" />
                                  </View>
                                )}
                                {!area.discovered && (
                                  <View style={styles.undiscoveredOverlay}>
                                    <Eye size={16} color="#8B7355" />
                                  </View>
                                )}
                              </TouchableOpacity>
                            ))}
                          </View>
                          <View style={styles.mapLegend}>
                            <View style={styles.legendItem}>
                              <View style={[styles.legendDot, { backgroundColor: '#4A7043' }]} />
                              <Text style={styles.legendText}>简单</Text>
                            </View>
                            <View style={styles.legendItem}>
                              <View style={[styles.legendDot, { backgroundColor: '#D4A017' }]} />
                              <Text style={styles.legendText}>中等</Text>
                            </View>
                            <View style={styles.legendItem}>
                              <View style={[styles.legendDot, { backgroundColor: '#DC3545' }]} />
                              <Text style={styles.legendText}>困难</Text>
                            </View>
                            <View style={styles.legendItem}>
                              <View style={[styles.legendDot, { backgroundColor: '#8B4513' }]} />
                              <Text style={styles.legendText}>传说</Text>
                            </View>
                          </View>
                        </>
                      )}
                    </View>
                  </View>
                </BlurView>
              </Animated.View>

              {/* Selected Area Details */}
              {selectedArea && (
                <Animated.View 
                  style={[
                    styles.areaDetailsContainer,
                    {
                      opacity: fadeAnim,
                      transform: [{ translateY: slideAnim }]
                    }
                  ]}
                >
                  <BlurView intensity={30} style={styles.areaDetailsCard}>
                    <View style={styles.areaHeader}>
                      <View style={styles.areaLeft}>
                        <View style={[
                          styles.areaIconContainer,
                          { backgroundColor: `${getDifficultyColor(selectedArea.difficulty)}20` }
                        ]}>
                          <selectedArea.icon 
                            size={28} 
                            color={getDifficultyColor(selectedArea.difficulty)} 
                          />
                        </View>
                        <View style={styles.areaInfo}>
                          <Text style={styles.areaName}>{selectedArea.name}</Text>
                          <View style={styles.areaMetadata}>
                            <View style={[
                              styles.difficultyBadge,
                              { backgroundColor: `${getDifficultyColor(selectedArea.difficulty)}20` }
                            ]}>
                              <Text style={[
                                styles.difficultyText,
                                { color: getDifficultyColor(selectedArea.difficulty) }
                              ]}>
                                {getDifficultyText(selectedArea.difficulty)}
                              </Text>
                            </View>
                            <View style={styles.timeContainer}>
                              <Clock size={14} color="#8B7355" />
                              <Text style={styles.timeText}>{selectedArea.timeRequired}</Text>
                            </View>
                          </View>
                        </View>
                      </View>
                    </View>

                    <Text style={styles.areaDescription}>{selectedArea.description}</Text>

                    {selectedArea.requirements && (
                      <View style={styles.requirementsContainer}>
                        <Shield size={16} color="#DC3545" />
                        <Text style={styles.requirementsText}>{selectedArea.requirements}</Text>
                      </View>
                    )}

                    {/* Task Hints */}
                    <View style={styles.hintsContainer}>
                      <Text style={styles.hintsTitle}>探索提示</Text>
                      {selectedArea.hints.map((hint, index) => (
                        <View key={index} style={styles.hintItem}>
                          <View style={[styles.hintDot, { backgroundColor: '#4A7043' }]} />
                          <Text style={[styles.hintText, { color: '#4A7043' }]}>{hint}</Text>
                        </View>
                      ))}
                    </View>

                    {/* Rewards */}
                    <View style={styles.rewardsContainer}>
                      <Text style={styles.rewardsTitle}>探索奖励</Text>
                      <View style={styles.rewardsList}>
                        {selectedArea.rewards.map((reward, index) => (
                          <View key={index} style={styles.rewardItem}>
                            <Star size={14} color="#D4A017" />
                            <Text style={styles.rewardText}>{reward}</Text>
                          </View>
                        ))}
                      </View>
                    </View>

                    {/* Start Exploration Button */}
                    <TouchableOpacity
                      style={[
                        styles.exploreButton,
                        (!selectedArea.discovered || selectedArea.completed) && styles.exploreButtonDisabled
                      ]}
                      onPress={() => handleStartExploration(selectedArea)}
                      disabled={!selectedArea.discovered || selectedArea.completed}
                    >
                      <LinearGradient
                        colors={
                          selectedArea.discovered && !selectedArea.completed
                            ? ['#D4A017', '#E6B82A']
                            : ['#B0B7A4', '#C0C7B4']
                        }
                        style={styles.exploreButtonGradient}
                      >
                        <Compass size={20} color="#6B4E71" />
                        <Text style={styles.exploreButtonText}>
                          {!selectedArea.discovered ? '未发现' : 
                           selectedArea.completed ? '已完成' : '开始探索'}
                        </Text>
                      </LinearGradient>
                    </TouchableOpacity>
                  </BlurView>
                </Animated.View>
              )}

              {/* Quest Hints */}
              <Animated.View 
                style={[
                  styles.questHintsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <Text style={styles.sectionTitle}>任务线索</Text>
                {questHints.map((hint) => {
                  const TypeIcon = getTypeIcon(hint.type);
                  return (
                    <BlurView key={hint.id} intensity={30} style={styles.questHintCard}>
                      <TouchableOpacity style={styles.questHintContent} activeOpacity={0.8}>
                        <View style={styles.questHintHeader}>
                          <View style={styles.questHintLeft}>
                            <View style={[
                              styles.questTypeContainer,
                              { backgroundColor: `${getUrgencyColor(hint.urgency)}20` }
                            ]}>
                              <TypeIcon size={20} color={getUrgencyColor(hint.urgency)} />
                            </View>
                            <View style={styles.questHintInfo}>
                              <Text style={styles.questHintTitle}>{hint.title}</Text>
                              <Text style={styles.questHintLocation}>
                                <MapPin size={12} color="#8B7355" /> {hint.location}
                              </Text>
                            </View>
                          </View>
                          <View style={[
                            styles.urgencyBadge,
                            { backgroundColor: `${getUrgencyColor(hint.urgency)}20` }
                          ]}>
                            <Text style={[
                              styles.urgencyText,
                              { color: getUrgencyColor(hint.urgency) }
                            ]}>
                              {hint.urgency === 'high' ? '紧急' : 
                               hint.urgency === 'medium' ? '一般' : '低'}
                            </Text>
                          </View>
                        </View>
                        
                        <Text style={styles.questHintDescription}>{hint.description}</Text>
                        
                        <View style={styles.questHintFooter}>
                          <View style={styles.questRewardContainer}>
                            <Coins size={14} color="#D4A017" />
                            <Text style={styles.questRewardText}>{hint.reward}</Text>
                          </View>
                          <ChevronRight size={16} color="#8B7355" />
                        </View>
                      </TouchableOpacity>
                    </BlurView>
                  );
                })}
              </Animated.View>

              {/* Exploration Stats */}
              <Animated.View 
                style={[
                  styles.statsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <BlurView intensity={30} style={styles.statsCard}>
                  <View style={styles.statsHeader}>
                    <Crown size={24} color="#D4A017" />
                    <Text style={styles.statsTitle}>探索统计</Text>
                  </View>
                  <View style={styles.statsGrid}>
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>
                        {explorationAreas.filter(a => a.discovered).length}
                      </Text>
                      <Text style={styles.statLabel}>已发现</Text>
                    </View>
                    <View style={styles.statDivider} />
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>
                        {explorationAreas.filter(a => a.completed).length}
                      </Text>
                      <Text style={styles.statLabel}>已完成</Text>
                    </View>
                    <View style={styles.statDivider} />
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>{questHints.length}</Text>
                      <Text style={styles.statLabel}>活跃线索</Text>
                    </View>
                  </View>
                </BlurView>
              </Animated.View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: width,
    height: height,
  },
  overlay: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 15,
  },
  hunterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  hunterFrame: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 3,
    borderColor: '#6B4E71',
    overflow: 'hidden',
    position: 'relative',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  hunterImage: {
    width: '100%',
    height: '100%',
  },
  purpleCloakOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  cloakGradient: {
    flex: 1,
  },
  bowBadge: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    backgroundColor: '#6B4E71',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#F5E8C7',
  },
  mistEffect: {
    position: 'absolute',
    bottom: -10,
    left: -10,
    right: -10,
    height: 20,
  },
  mistBlur: {
    flex: 1,
    borderRadius: 10,
  },
  hunterInfo: {
    flex: 1,
  },
  hunterTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 2,
  },
  hunterSubtitle: {
    fontSize: 14,
    color: '#8B7355',
  },
  titleContainer: {
    alignItems: 'center',
  },
  mainTitle: {
    fontSize: 28,
    color: '#4A7043',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  titleUnderline: {
    width: 80,
    height: 3,
    backgroundColor: '#4A7043',
    borderRadius: 2,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8B7355',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  mapToggleContainer: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  mapToggleBar: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(176, 183, 164, 0.3)',
  },
  toggleButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginHorizontal: 4,
  },
  toggleButtonActive: {
    backgroundColor: 'rgba(107, 78, 113, 0.15)',
  },
  toggleText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#B0B7A4',
  },
  toggleTextActive: {
    color: '#6B4E71',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  mapContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  mapCard: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  mapBorder: {
    borderWidth: 3,
    borderRadius: 16,
    margin: 4,
  },
  mapContent: {
    padding: 20,
  },
  mapTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#6B4E71',
    textAlign: 'center',
    marginBottom: 20,
  },
  mapWrapper: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  mapFallback: {
    height: 300,
    backgroundColor: 'rgba(176, 183, 164, 0.1)',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  mapFallbackText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B4E71',
    marginTop: 12,
    textAlign: 'center',
  },
  mapFallbackSubtext: {
    fontSize: 14,
    color: '#8B7355',
    marginTop: 4,
    textAlign: 'center',
  },
  mapGrid: {
    height: 200,
    backgroundColor: 'rgba(176, 183, 164, 0.1)',
    borderRadius: 12,
    position: 'relative',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  mapPoint: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(245, 232, 199, 0.8)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  completedBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#D4A017',
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  undiscoveredOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapLegend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: '#8B7355',
    fontWeight: '500',
  },
  areaDetailsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  areaDetailsCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  areaHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  areaLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  areaIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  areaInfo: {
    flex: 1,
  },
  areaName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 6,
  },
  areaMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 12,
  },
  difficultyText: {
    fontSize: 12,
    fontWeight: '600',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    color: '#8B7355',
    marginLeft: 4,
  },
  areaDescription: {
    fontSize: 14,
    color: '#8B7355',
    lineHeight: 20,
    marginBottom: 16,
  },
  requirementsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(220, 53, 69, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  requirementsText: {
    fontSize: 14,
    color: '#DC3545',
    marginLeft: 8,
    fontWeight: '500',
  },
  hintsContainer: {
    marginBottom: 16,
  },
  hintsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B4E71',
    marginBottom: 12,
  },
  hintItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  hintDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 10,
  },
  hintText: {
    fontSize: 14,
    flex: 1,
    lineHeight: 18,
  },
  rewardsContainer: {
    marginBottom: 20,
  },
  rewardsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B4E71',
    marginBottom: 12,
  },
  rewardsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  rewardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(212, 160, 23, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  rewardText: {
    fontSize: 12,
    color: '#D4A017',
    marginLeft: 4,
    fontWeight: '500',
  },
  exploreButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  exploreButtonDisabled: {
    opacity: 0.6,
  },
  exploreButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  exploreButtonText: {
    color: '#6B4E71',
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 8,
  },
  questHintsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#6B4E71',
    marginBottom: 16,
    textAlign: 'center',
  },
  questHintCard: {
    marginBottom: 12,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  questHintContent: {
    padding: 16,
  },
  questHintHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  questHintLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  questTypeContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  questHintInfo: {
    flex: 1,
  },
  questHintTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B4E71',
    marginBottom: 4,
  },
  questHintLocation: {
    fontSize: 12,
    color: '#8B7355',
    flexDirection: 'row',
    alignItems: 'center',
  },
  urgencyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  urgencyText: {
    fontSize: 12,
    fontWeight: '600',
  },
  questHintDescription: {
    fontSize: 14,
    color: '#8B7355',
    lineHeight: 20,
    marginBottom: 12,
  },
  questHintFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  questRewardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  questRewardText: {
    fontSize: 14,
    color: '#8B7355',
    marginLeft: 6,
    fontWeight: '500',
  },
  statsContainer: {
    paddingHorizontal: 20,
  },
  statsCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B4E71',
    marginLeft: 8,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#8B7355',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(139, 115, 85, 0.3)',
  },
});