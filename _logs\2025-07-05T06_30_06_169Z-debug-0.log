0 verbose cli E:\Node\node.exe e:\Node\node_modules\npm\bin\npm-cli.js
1 info using npm@10.7.0
2 info using node@v20.15.1
3 silly config:load:file:E:\Nvm\nvm\v20.15.1\node_modules\npm\npmrc
4 silly config:load:file:D:\.0000\scarboroughfair\.npmrc
5 silly config:load:file:C:\Users\<USER>\.npmrc
6 silly config:load:file:e:\Node\etc\npmrc
7 verbose title npm install cross-env
8 verbose argv "install" "--save-dev" "cross-env"
9 verbose logfile logs-max:10 dir:d:\.0000\scarboroughfair\_logs\2025-07-05T06_30_06_169Z-
10 verbose logfile d:\.0000\scarboroughfair\_logs\2025-07-05T06_30_06_169Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly logfile done cleaning log files
13 silly idealTree buildDeps
14 silly fetch manifest cross-env@*
15 http fetch GET 200 https://registry.npmjs.org/cross-env 4083ms (cache miss)
16 silly placeDep ROOT cross-env@7.0.3 OK for: bolt-expo-starter@1.0.0 want: *
17 silly reify moves {}
18 silly audit bulk request {
18 silly audit   '@0no-co/graphql.web': [ '1.1.2' ],
18 silly audit   '@ampproject/remapping': [ '2.3.0' ],
18 silly audit   '@babel/code-frame': [ '7.27.1', '7.10.4' ],
18 silly audit   '@babel/compat-data': [ '7.28.0' ],
18 silly audit   '@babel/core': [ '7.28.0' ],
18 silly audit   '@babel/generator': [ '7.28.0' ],
18 silly audit   '@babel/helper-annotate-as-pure': [ '7.27.3' ],
18 silly audit   '@babel/helper-compilation-targets': [ '7.27.2' ],
18 silly audit   '@babel/helper-create-class-features-plugin': [ '7.27.1' ],
18 silly audit   '@babel/helper-create-regexp-features-plugin': [ '7.27.1' ],
18 silly audit   '@babel/helper-define-polyfill-provider': [ '0.6.5' ],
18 silly audit   '@babel/helper-globals': [ '7.28.0' ],
18 silly audit   '@babel/helper-member-expression-to-functions': [ '7.27.1' ],
18 silly audit   '@babel/helper-module-imports': [ '7.27.1' ],
18 silly audit   '@babel/helper-module-transforms': [ '7.27.3' ],
18 silly audit   '@babel/helper-optimise-call-expression': [ '7.27.1' ],
18 silly audit   '@babel/helper-plugin-utils': [ '7.27.1' ],
18 silly audit   '@babel/helper-remap-async-to-generator': [ '7.27.1' ],
18 silly audit   '@babel/helper-replace-supers': [ '7.27.1' ],
18 silly audit   '@babel/helper-skip-transparent-expression-wrappers': [ '7.27.1' ],
18 silly audit   '@babel/helper-string-parser': [ '7.27.1' ],
18 silly audit   '@babel/helper-validator-identifier': [ '7.27.1' ],
18 silly audit   '@babel/helper-validator-option': [ '7.27.1' ],
18 silly audit   '@babel/helper-wrap-function': [ '7.27.1' ],
18 silly audit   '@babel/helpers': [ '7.27.6' ],
18 silly audit   '@babel/highlight': [ '7.25.9' ],
18 silly audit   'ansi-styles': [ '3.2.1', '6.2.1', '4.3.0', '5.2.0' ],
18 silly audit   chalk: [ '2.4.2', '4.1.2' ],
18 silly audit   'color-convert': [ '1.9.3', '2.0.1' ],
18 silly audit   'color-name': [ '1.1.3', '1.1.4' ],
18 silly audit   'escape-string-regexp': [ '1.0.5', '4.0.0', '2.0.0' ],
18 silly audit   'has-flag': [ '3.0.0', '4.0.0' ],
18 silly audit   'supports-color': [ '5.5.0', '8.1.1', '7.2.0' ],
18 silly audit   '@babel/parser': [ '7.28.0' ],
18 silly audit   '@babel/plugin-proposal-decorators': [ '7.28.0' ],
18 silly audit   '@babel/plugin-proposal-export-default-from': [ '7.27.1' ],
18 silly audit   '@babel/plugin-syntax-async-generators': [ '7.8.4' ],
18 silly audit   '@babel/plugin-syntax-bigint': [ '7.8.3' ],
18 silly audit   '@babel/plugin-syntax-class-properties': [ '7.12.13' ],
18 silly audit   '@babel/plugin-syntax-class-static-block': [ '7.14.5' ],
18 silly audit   '@babel/plugin-syntax-decorators': [ '7.27.1' ],
18 silly audit   '@babel/plugin-syntax-dynamic-import': [ '7.8.3' ],
18 silly audit   '@babel/plugin-syntax-export-default-from': [ '7.27.1' ],
18 silly audit   '@babel/plugin-syntax-flow': [ '7.27.1' ],
18 silly audit   '@babel/plugin-syntax-import-attributes': [ '7.27.1' ],
18 silly audit   '@babel/plugin-syntax-import-meta': [ '7.10.4' ],
18 silly audit   '@babel/plugin-syntax-json-strings': [ '7.8.3' ],
18 silly audit   '@babel/plugin-syntax-jsx': [ '7.27.1' ],
18 silly audit   '@babel/plugin-syntax-logical-assignment-operators': [ '7.10.4' ],
18 silly audit   '@babel/plugin-syntax-nullish-coalescing-operator': [ '7.8.3' ],
18 silly audit   '@babel/plugin-syntax-numeric-separator': [ '7.10.4' ],
18 silly audit   '@babel/plugin-syntax-object-rest-spread': [ '7.8.3' ],
18 silly audit   '@babel/plugin-syntax-optional-catch-binding': [ '7.8.3' ],
18 silly audit   '@babel/plugin-syntax-optional-chaining': [ '7.8.3' ],
18 silly audit   '@babel/plugin-syntax-private-property-in-object': [ '7.14.5' ],
18 silly audit   '@babel/plugin-syntax-top-level-await': [ '7.14.5' ],
18 silly audit   '@babel/plugin-syntax-typescript': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-arrow-functions': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-async-generator-functions': [ '7.28.0' ],
18 silly audit   '@babel/plugin-transform-async-to-generator': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-block-scoping': [ '7.28.0' ],
18 silly audit   '@babel/plugin-transform-class-properties': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-classes': [ '7.28.0' ],
18 silly audit   '@babel/plugin-transform-computed-properties': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-destructuring': [ '7.28.0' ],
18 silly audit   '@babel/plugin-transform-export-namespace-from': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-flow-strip-types': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-for-of': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-function-name': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-literals': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-logical-assignment-operators': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-modules-commonjs': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-named-capturing-groups-regex': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-nullish-coalescing-operator': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-numeric-separator': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-object-rest-spread': [ '7.28.0' ],
18 silly audit   '@babel/plugin-transform-optional-catch-binding': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-optional-chaining': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-parameters': [ '7.27.7' ],
18 silly audit   '@babel/plugin-transform-private-methods': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-private-property-in-object': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-react-display-name': [ '7.28.0' ],
18 silly audit   '@babel/plugin-transform-react-jsx': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-react-jsx-development': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-react-jsx-self': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-react-jsx-source': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-react-pure-annotations': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-regenerator': [ '7.28.0' ],
18 silly audit   '@babel/plugin-transform-runtime': [ '7.28.0' ],
18 silly audit   '@babel/plugin-transform-shorthand-properties': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-spread': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-sticky-regex': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-template-literals': [ '7.27.1' ],
18 silly audit   '@babel/plugin-transform-typescript': [ '7.28.0' ],
18 silly audit   '@babel/plugin-transform-unicode-regex': [ '7.27.1' ],
18 silly audit   '@babel/preset-react': [ '7.27.1' ],
18 silly audit   '@babel/preset-typescript': [ '7.27.1' ],
18 silly audit   '@babel/runtime': [ '7.27.6' ],
18 silly audit   '@babel/template': [ '7.27.2' ],
18 silly audit   '@babel/traverse': [ '7.28.0' ],
18 silly audit   '@babel/types': [ '7.28.0' ],
18 silly audit   '@egjs/hammerjs': [ '2.0.17' ],
18 silly audit   '@expo-google-fonts/ma-shan-zheng': [ '0.2.3' ],
18 silly audit   '@expo/cli': [ '0.24.17' ],
18 silly audit   semver: [ '7.7.2', '7.6.3', '6.3.1' ],
18 silly audit   '@expo/code-signing-certificates': [ '0.0.5' ],
18 silly audit   '@expo/config': [ '11.0.11' ],
18 silly audit   '@expo/config-plugins': [ '10.1.0', '10.0.3' ],
18 silly audit   '@expo/config-types': [ '53.0.4' ],
18 silly audit   '@expo/devcert': [ '1.2.0' ],
18 silly audit   debug: [ '3.2.7', '2.6.9', '4.4.1' ],
18 silly audit   '@expo/env': [ '1.0.6' ],
18 silly audit   '@expo/fingerprint': [ '0.13.3' ],
18 silly audit   '@expo/image-utils': [ '0.7.5' ],
18 silly audit   '@expo/json-file': [ '9.1.4' ],
18 silly audit   '@expo/metro-config': [ '0.20.16' ],
18 silly audit   '@expo/metro-runtime': [ '5.0.4' ],
18 silly audit   '@expo/osascript': [ '2.2.4' ],
18 silly audit   '@expo/package-manager': [ '1.8.5' ],
18 silly audit   '@expo/plist': [ '0.3.4' ],
18 silly audit   '@expo/prebuild-config': [ '9.0.9' ],
18 silly audit   '@expo/sdk-runtime-versions': [ '1.0.0' ],
18 silly audit   '@expo/server': [ '0.6.3' ],
18 silly audit   '@expo/spawn-async': [ '1.7.2' ],
18 silly audit   '@expo/sudo-prompt': [ '9.3.2' ],
18 silly audit   '@expo/vector-icons': [ '14.1.0' ],
18 silly audit   '@expo/ws-tunnel': [ '1.0.6' ],
18 silly audit   '@expo/xcpretty': [ '4.3.2' ],
18 silly audit   '@isaacs/cliui': [ '8.0.2' ],
18 silly audit   'wrap-ansi': [ '8.1.0', '7.0.0' ],
18 silly audit   '@isaacs/fs-minipass': [ '4.0.1' ],
18 silly audit   '@isaacs/ttlcache': [ '1.4.1' ],
18 silly audit   '@istanbuljs/load-nyc-config': [ '1.1.0' ],
18 silly audit   argparse: [ '1.0.10', '2.0.1' ],
18 silly audit   'find-up': [ '4.1.0', '5.0.0' ],
18 silly audit   'js-yaml': [ '3.14.1', '4.1.0' ],
18 silly audit   'locate-path': [ '5.0.0', '6.0.0' ],
18 silly audit   'p-limit': [ '2.3.0', '3.1.0' ],
18 silly audit   'p-locate': [ '4.1.0', '5.0.0' ],
18 silly audit   '@istanbuljs/schema': [ '0.1.3' ],
18 silly audit   '@jest/create-cache-key-function': [ '29.7.0' ],
18 silly audit   '@jest/environment': [ '29.7.0' ],
18 silly audit   '@jest/fake-timers': [ '29.7.0' ],
18 silly audit   '@jest/schemas': [ '29.6.3' ],
18 silly audit   '@jest/transform': [ '29.7.0' ],
18 silly audit   '@jest/types': [ '29.6.3' ],
18 silly audit   '@jridgewell/gen-mapping': [ '0.3.12' ],
18 silly audit   '@jridgewell/resolve-uri': [ '3.1.2' ],
18 silly audit   '@jridgewell/source-map': [ '0.3.10' ],
18 silly audit   '@jridgewell/sourcemap-codec': [ '1.5.4' ],
18 silly audit   '@jridgewell/trace-mapping': [ '0.3.29' ],
18 silly audit   '@lucide/lab': [ '0.1.2' ],
18 silly audit   '@pkgjs/parseargs': [ '0.11.0' ],
18 silly audit   '@radix-ui/react-compose-refs': [ '1.1.2' ],
18 silly audit   '@radix-ui/react-slot': [ '1.2.0' ],
18 silly audit   '@react-leaflet/core': [ '2.1.0' ],
18 silly audit   '@react-native-async-storage/async-storage': [ '2.2.0' ],
18 silly audit   '@react-native/assets-registry': [ '0.79.1' ],
18 silly audit   '@react-native/babel-plugin-codegen': [ '0.79.5' ],
18 silly audit   '@react-native/babel-preset': [ '0.79.5' ],
18 silly audit   '@react-native/codegen': [ '0.79.5', '0.79.1' ],
18 silly audit   'brace-expansion': [ '1.1.12', '2.0.2' ],
18 silly audit   glob: [ '7.2.3', '10.4.5' ],
18 silly audit   minimatch: [ '3.1.2', '9.0.5' ],
18 silly audit   '@react-native/community-cli-plugin': [ '0.79.1' ],
18 silly audit   '@react-native/debugger-frontend': [ '0.79.1', '0.79.5' ],
18 silly audit   '@react-native/dev-middleware': [ '0.79.1', '0.79.5' ],
18 silly audit   ms: [ '2.0.0', '2.1.3' ],
18 silly audit   ws: [ '6.2.3', '7.5.10', '8.18.3' ],
18 silly audit   '@react-native/gradle-plugin': [ '0.79.1' ],
18 silly audit   '@react-native/js-polyfills': [ '0.79.1' ],
18 silly audit   '@react-native/normalize-colors': [ '0.79.5', '0.74.89', '0.79.1' ],
18 silly audit   '@react-native/virtualized-lists': [ '0.79.1' ],
18 silly audit   '@react-navigation/bottom-tabs': [ '7.4.2' ],
18 silly audit   '@react-navigation/core': [ '7.12.1' ],
18 silly audit   '@react-navigation/elements': [ '2.5.2' ],
18 silly audit   '@react-navigation/native': [ '7.1.14' ],
18 silly audit   '@react-navigation/native-stack': [ '7.3.21' ],
18 silly audit   '@react-navigation/routers': [ '7.4.1' ],
18 silly audit   '@sinclair/typebox': [ '0.27.8' ],
18 silly audit   '@sinonjs/commons': [ '3.0.1' ],
18 silly audit   '@sinonjs/fake-timers': [ '10.3.0' ],
18 silly audit   '@supabase/auth-js': [ '2.70.0' ],
18 silly audit   '@supabase/functions-js': [ '2.4.5' ],
18 silly audit   '@supabase/node-fetch': [ '2.6.15' ],
18 silly audit   '@supabase/postgrest-js': [ '1.19.4' ],
18 silly audit   '@supabase/realtime-js': [ '2.11.15' ],
18 silly audit   '@supabase/storage-js': [ '2.7.1' ],
18 silly audit   '@supabase/supabase-js': [ '2.50.3' ],
18 silly audit   '@types/babel__core': [ '7.20.5' ],
18 silly audit   '@types/babel__generator': [ '7.27.0' ],
18 silly audit   '@types/babel__template': [ '7.4.4' ],
18 silly audit   '@types/babel__traverse': [ '7.20.7' ],
18 silly audit   '@types/geojson': [ '7946.0.16' ],
18 silly audit   '@types/graceful-fs': [ '4.1.9' ],
18 silly audit   '@types/hammerjs': [ '2.0.46' ],
18 silly audit   '@types/istanbul-lib-coverage': [ '2.0.6' ],
18 silly audit   '@types/istanbul-lib-report': [ '3.0.3' ],
18 silly audit   '@types/istanbul-reports': [ '3.0.4' ],
18 silly audit   '@types/json-schema': [ '7.0.15' ],
18 silly audit   '@types/leaflet': [ '1.9.19' ],
18 silly audit   '@types/node': [ '24.0.10' ],
18 silly audit   '@types/phoenix': [ '1.6.6' ],
18 silly audit   '@types/react': [ '19.0.14' ],
18 silly audit   '@types/stack-utils': [ '2.0.3' ],
18 silly audit   '@types/ws': [ '8.18.1' ],
18 silly audit   '@types/yargs': [ '17.0.33' ],
18 silly audit   '@types/yargs-parser': [ '21.0.3' ],
18 silly audit   '@urql/core': [ '5.2.0' ],
18 silly audit   '@urql/exchange-retry': [ '1.3.2' ],
18 silly audit   '@xmldom/xmldom': [ '0.8.10' ],
18 silly audit   'abort-controller': [ '3.0.0' ],
18 silly audit   accepts: [ '1.3.8' ],
18 silly audit   acorn: [ '8.15.0' ],
18 silly audit   'agent-base': [ '7.1.3' ],
18 silly audit   ajv: [ '8.17.1' ],
18 silly audit   'ajv-formats': [ '2.1.1' ],
18 silly audit   'ajv-keywords': [ '5.1.0' ],
18 silly audit   anser: [ '1.4.10' ],
18 silly audit   'ansi-escapes': [ '4.3.2' ],
18 silly audit   'type-fest': [ '0.21.3', '0.7.1' ],
18 silly audit   'ansi-regex': [ '5.0.1', '4.1.1', '6.1.0' ],
18 silly audit   'any-promise': [ '1.3.0' ],
18 silly audit   anymatch: [ '3.1.3' ],
18 silly audit   picomatch: [ '2.3.1', '3.0.1' ],
18 silly audit   arg: [ '5.0.2' ],
18 silly audit   asap: [ '2.0.6' ],
18 silly audit   'async-limiter': [ '1.0.1' ],
18 silly audit   'babel-jest': [ '29.7.0' ],
18 silly audit   'babel-plugin-istanbul': [ '6.1.1' ],
18 silly audit   'babel-plugin-jest-hoist': [ '29.6.3' ],
18 silly audit   'babel-plugin-polyfill-corejs2': [ '0.4.14' ],
18 silly audit   'babel-plugin-polyfill-corejs3': [ '0.13.0' ],
18 silly audit   'babel-plugin-polyfill-regenerator': [ '0.6.5' ],
18 silly audit   'babel-plugin-react-native-web': [ '0.19.13' ],
18 silly audit   'babel-plugin-syntax-hermes-parser': [ '0.25.1' ],
18 silly audit   'babel-plugin-transform-flow-enums': [ '0.0.2' ],
18 silly audit   'babel-preset-current-node-syntax': [ '1.1.0' ],
18 silly audit   'babel-preset-expo': [ '13.2.2' ],
18 silly audit   'babel-preset-jest': [ '29.6.3' ],
18 silly audit   'balanced-match': [ '1.0.2' ],
18 silly audit   'base64-js': [ '1.5.1' ],
18 silly audit   'better-opn': [ '3.0.2' ],
18 silly audit   open: [ '8.4.2', '7.4.2' ],
18 silly audit   'big-integer': [ '1.6.52' ],
18 silly audit   boolbase: [ '1.0.0' ],
18 silly audit   'bplist-creator': [ '0.1.0' ],
18 silly audit   'bplist-parser': [ '0.3.2', '0.3.1' ],
18 silly audit   braces: [ '3.0.3' ],
18 silly audit   browserslist: [ '4.25.1' ],
18 silly audit   bser: [ '2.1.1' ],
18 silly audit   buffer: [ '5.7.1' ],
18 silly audit   'buffer-from': [ '1.1.2' ],
18 silly audit   bytes: [ '3.1.2' ],
18 silly audit   'caller-callsite': [ '2.0.0' ],
18 silly audit   'caller-path': [ '2.0.0' ],
18 silly audit   callsites: [ '2.0.0' ],
18 silly audit   camelcase: [ '5.3.1', '6.3.0' ],
18 silly audit   'caniuse-lite': [ '1.0.30001726' ],
18 silly audit   chownr: [ '3.0.0' ],
18 silly audit   'chrome-launcher': [ '0.15.2' ],
18 silly audit   'chromium-edge-launcher': [ '0.2.0' ],
18 silly audit   'ci-info': [ '3.9.0', '2.0.0' ],
18 silly audit   'cli-cursor': [ '2.1.0' ],
18 silly audit   'cli-spinners': [ '2.9.2' ],
18 silly audit   'client-only': [ '0.0.1' ],
18 silly audit   cliui: [ '8.0.1' ],
18 silly audit   'emoji-regex': [ '8.0.0', '9.2.2' ],
18 silly audit   'string-width': [ '4.2.3', '5.1.2' ],
18 silly audit   'strip-ansi': [ '6.0.1', '5.2.0', '7.1.0' ],
18 silly audit   clone: [ '1.0.4' ],
18 silly audit   clsx: [ '2.1.1' ],
18 silly audit   color: [ '4.2.3' ],
18 silly audit   'color-string': [ '1.9.1' ],
18 silly audit   commander: [ '7.2.0', '12.1.0', '4.1.1', '2.20.3' ],
18 silly audit   compressible: [ '2.0.18' ],
18 silly audit   compression: [ '1.8.0' ],
18 silly audit   negotiator: [ '0.6.4', '0.6.3' ],
18 silly audit   'concat-map': [ '0.0.1' ],
18 silly audit   connect: [ '3.7.0' ],
18 silly audit   'convert-source-map': [ '2.0.0' ],
18 silly audit   'core-js-compat': [ '3.43.0' ],
18 silly audit   cosmiconfig: [ '5.2.1' ],
18 silly audit   'cross-fetch': [ '3.2.0' ],
18 silly audit   'cross-spawn': [ '7.0.6' ],
18 silly audit   'crypto-random-string': [ '2.0.0' ],
18 silly audit   'css-in-js-utils': [ '3.1.0' ],
18 silly audit   'css-select': [ '5.2.2' ],
18 silly audit   'css-tree': [ '1.1.3' ],
18 silly audit   'source-map': [ '0.6.1', '0.5.7' ],
18 silly audit   'css-what': [ '6.2.2' ],
18 silly audit   csstype: [ '3.1.3' ],
18 silly audit   'decode-uri-component': [ '0.2.2' ],
18 silly audit   'deep-extend': [ '0.6.0' ],
18 silly audit   deepmerge: [ '4.3.1' ],
18 silly audit   defaults: [ '1.0.4' ],
18 silly audit   'define-lazy-prop': [ '2.0.0' ],
18 silly audit   depd: [ '2.0.0' ],
18 silly audit   destroy: [ '1.2.0' ],
18 silly audit   'detect-libc': [ '1.0.3' ],
18 silly audit   'dom-serializer': [ '2.0.0' ],
18 silly audit   domelementtype: [ '2.3.0' ],
18 silly audit   domhandler: [ '5.0.3' ],
18 silly audit   domutils: [ '3.2.2' ],
18 silly audit   dotenv: [ '16.4.7' ],
18 silly audit   'dotenv-expand': [ '11.0.7' ],
18 silly audit   eastasianwidth: [ '0.2.0' ],
18 silly audit   'ee-first': [ '1.1.1' ],
18 silly audit   'electron-to-chromium': [ '1.5.179' ],
18 silly audit   encodeurl: [ '1.0.2', '2.0.0' ],
18 silly audit   entities: [ '4.5.0' ],
18 silly audit   'env-editor': [ '0.4.2' ],
18 silly audit   'error-ex': [ '1.3.2' ],
18 silly audit   'error-stack-parser': [ '2.1.4' ],
18 silly audit   escalade: [ '3.2.0' ],
18 silly audit   'escape-html': [ '1.0.3' ],
18 silly audit   esprima: [ '4.0.1' ],
18 silly audit   etag: [ '1.8.1' ],
18 silly audit   'event-target-shim': [ '5.0.1' ],
18 silly audit   'exec-async': [ '2.2.0' ],
18 silly audit   expo: [ '53.0.16' ],
18 silly audit   'expo-asset': [ '11.1.6' ],
18 silly audit   'expo-blur': [ '14.1.5' ],
18 silly audit   'expo-camera': [ '16.1.10' ],
18 silly audit   'expo-constants': [ '17.1.6' ],
18 silly audit   'expo-file-system': [ '18.1.11' ],
18 silly audit   'expo-font': [ '13.2.2', '13.3.2' ],
18 silly audit   'expo-haptics': [ '14.1.4' ],
18 silly audit   'expo-keep-awake': [ '14.1.4' ],
18 silly audit   'expo-linear-gradient': [ '14.1.5' ],
18 silly audit   'expo-linking': [ '7.1.6' ],
18 silly audit   'expo-modules-autolinking': [ '2.1.13' ],
18 silly audit   'expo-modules-core': [ '2.4.2' ],
18 silly audit   'expo-router': [ '5.0.7' ],
18 silly audit   'expo-splash-screen': [ '0.30.9' ],
18 silly audit   'expo-status-bar': [ '2.2.3' ],
18 silly audit   'expo-symbols': [ '0.4.5' ],
18 silly audit   'expo-system-ui': [ '5.0.10' ],
18 silly audit   'expo-web-browser': [ '14.1.6' ],
18 silly audit   'exponential-backoff': [ '3.1.2' ],
18 silly audit   'fast-deep-equal': [ '3.1.3' ],
18 silly audit   'fast-json-stable-stringify': [ '2.1.0' ],
18 silly audit   'fast-uri': [ '3.0.6' ],
18 silly audit   'fb-watchman': [ '2.0.2' ],
18 silly audit   fbjs: [ '3.0.5' ],
18 silly audit   'fbjs-css-vars': [ '1.0.2' ],
18 silly audit   promise: [ '7.3.1', '8.3.0' ],
18 silly audit   'fill-range': [ '7.1.1' ],
18 silly audit   'filter-obj': [ '1.1.0' ],
18 silly audit   finalhandler: [ '1.1.2' ],
18 silly audit   'flow-enums-runtime': [ '0.0.6' ],
18 silly audit   fontfaceobserver: [ '2.3.0' ],
18 silly audit   'foreground-child': [ '3.3.1' ],
18 silly audit   'freeport-async': [ '2.0.0' ],
18 silly audit   fresh: [ '0.5.2' ],
18 silly audit   'fs.realpath': [ '1.0.0' ],
18 silly audit   fsevents: [ '2.3.3' ],
18 silly audit   'function-bind': [ '1.1.2' ],
18 silly audit   gensync: [ '1.0.0-beta.2' ],
18 silly audit   'get-caller-file': [ '2.0.5' ],
18 silly audit   'get-package-type': [ '0.1.0' ],
18 silly audit   getenv: [ '2.0.0' ],
18 silly audit   'graceful-fs': [ '4.2.11' ],
18 silly audit   hasown: [ '2.0.2' ],
18 silly audit   'hermes-estree': [ '0.25.1', '0.28.1' ],
18 silly audit   'hermes-parser': [ '0.25.1', '0.28.1' ],
18 silly audit   'hoist-non-react-statics': [ '3.3.2' ],
18 silly audit   'react-is': [ '16.13.1', '18.3.1', '19.1.0' ],
18 silly audit   'hosted-git-info': [ '7.0.2' ],
18 silly audit   'lru-cache': [ '10.4.3', '5.1.1' ],
18 silly audit   'http-errors': [ '2.0.0' ],
18 silly audit   statuses: [ '2.0.1', '1.5.0' ],
18 silly audit   'https-proxy-agent': [ '7.0.6' ],
18 silly audit   'hyphenate-style-name': [ '1.1.0' ],
18 silly audit   ieee754: [ '1.2.1' ],
18 silly audit   ignore: [ '5.3.2' ],
18 silly audit   'image-size': [ '1.2.1' ],
18 silly audit   'import-fresh': [ '2.0.0' ],
18 silly audit   'resolve-from': [ '3.0.0', '5.0.0' ],
18 silly audit   imurmurhash: [ '0.1.4' ],
18 silly audit   inflight: [ '1.0.6' ],
18 silly audit   inherits: [ '2.0.4' ],
18 silly audit   ini: [ '1.3.8' ],
18 silly audit   'inline-style-prefixer': [ '7.0.1' ],
18 silly audit   invariant: [ '2.2.4' ],
18 silly audit   'is-arrayish': [ '0.2.1', '0.3.2' ],
18 silly audit   'is-core-module': [ '2.16.1' ],
18 silly audit   'is-directory': [ '0.3.1' ],
18 silly audit   'is-docker': [ '2.2.1' ],
18 silly audit   'is-fullwidth-code-point': [ '3.0.0' ],
18 silly audit   'is-number': [ '7.0.0' ],
18 silly audit   'is-plain-obj': [ '2.1.0' ],
18 silly audit   'is-wsl': [ '2.2.0' ],
18 silly audit   isexe: [ '2.0.0' ],
18 silly audit   isows: [ '1.0.7' ],
18 silly audit   'istanbul-lib-coverage': [ '3.2.2' ],
18 silly audit   'istanbul-lib-instrument': [ '5.2.1' ],
18 silly audit   jackspeak: [ '3.4.3' ],
18 silly audit   'jest-environment-node': [ '29.7.0' ],
18 silly audit   'jest-get-type': [ '29.6.3' ],
18 silly audit   'jest-haste-map': [ '29.7.0' ],
18 silly audit   'jest-message-util': [ '29.7.0' ],
18 silly audit   'jest-mock': [ '29.7.0' ],
18 silly audit   'jest-regex-util': [ '29.6.3' ],
18 silly audit   'jest-util': [ '29.7.0' ],
18 silly audit   'jest-validate': [ '29.7.0' ],
18 silly audit   'jest-worker': [ '29.7.0' ],
18 silly audit   'jimp-compact': [ '0.16.1' ],
18 silly audit   'js-tokens': [ '4.0.0' ],
18 silly audit   'jsc-safe-url': [ '0.2.4' ],
18 silly audit   jsesc: [ '3.1.0', '3.0.2' ],
18 silly audit   'json-parse-better-errors': [ '1.0.2' ],
18 silly audit   'json-schema-traverse': [ '1.0.0' ],
18 silly audit   json5: [ '2.2.3' ],
18 silly audit   kleur: [ '3.0.3' ],
18 silly audit   'lan-network': [ '0.1.7' ],
18 silly audit   leaflet: [ '1.9.4' ],
18 silly audit   leven: [ '3.1.0' ],
18 silly audit   'lighthouse-logger': [ '1.4.2' ],
18 silly audit   lightningcss: [ '1.27.0' ],
18 silly audit   'lightningcss-darwin-arm64': [ '1.27.0' ],
18 silly audit   'lightningcss-darwin-x64': [ '1.27.0' ],
18 silly audit   'lightningcss-freebsd-x64': [ '1.27.0' ],
18 silly audit   'lightningcss-linux-arm-gnueabihf': [ '1.27.0' ],
18 silly audit   'lightningcss-linux-arm64-gnu': [ '1.27.0' ],
18 silly audit   'lightningcss-linux-arm64-musl': [ '1.27.0' ],
18 silly audit   'lightningcss-linux-x64-gnu': [ '1.27.0' ],
18 silly audit   'lightningcss-linux-x64-musl': [ '1.27.0' ],
18 silly audit   'lightningcss-win32-arm64-msvc': [ '1.27.0' ],
18 silly audit   'lightningcss-win32-x64-msvc': [ '1.27.0' ],
18 silly audit   'lines-and-columns': [ '1.2.4' ],
18 silly audit   'lodash.debounce': [ '4.0.8' ],
18 silly audit   'lodash.throttle': [ '4.1.1' ],
18 silly audit   'log-symbols': [ '2.2.0' ],
18 silly audit   'loose-envify': [ '1.4.0' ],
18 silly audit   'lucide-react-native': [ '0.475.0' ],
18 silly audit   makeerror: [ '1.0.12' ],
18 silly audit   marky: [ '1.3.0' ],
18 silly audit   'mdn-data': [ '2.0.14' ],
18 silly audit   'memoize-one': [ '5.2.1', '6.0.0' ],
18 silly audit   'merge-options': [ '3.0.4' ],
18 silly audit   'merge-stream': [ '2.0.0' ],
18 silly audit   metro: [ '0.82.4' ],
18 silly audit   'metro-babel-transformer': [ '0.82.4' ],
18 silly audit   'metro-cache': [ '0.82.4' ],
18 silly audit   'metro-cache-key': [ '0.82.4' ],
18 silly audit   'metro-config': [ '0.82.4' ],
18 silly audit   'metro-core': [ '0.82.4' ],
18 silly audit   'metro-file-map': [ '0.82.4' ],
18 silly audit   'metro-minify-terser': [ '0.82.4' ],
18 silly audit   'metro-resolver': [ '0.82.4' ],
18 silly audit   'metro-runtime': [ '0.82.4' ],
18 silly audit   'metro-source-map': [ '0.82.4' ],
18 silly audit   'metro-symbolicate': [ '0.82.4' ],
18 silly audit   'metro-transform-plugins': [ '0.82.4' ],
18 silly audit   'metro-transform-worker': [ '0.82.4' ],
18 silly audit   micromatch: [ '4.0.8' ],
18 silly audit   mime: [ '1.6.0' ],
18 silly audit   'mime-db': [ '1.54.0', '1.52.0' ],
18 silly audit   'mime-types': [ '2.1.35' ],
18 silly audit   'mimic-fn': [ '1.2.0' ],
18 silly audit   minimist: [ '1.2.8' ],
18 silly audit   minipass: [ '7.1.2' ],
18 silly audit   minizlib: [ '3.0.2' ],
18 silly audit   mkdirp: [ '1.0.4', '3.0.1' ],
18 silly audit   mz: [ '2.7.0' ],
18 silly audit   nanoid: [ '3.3.11' ],
18 silly audit   'nested-error-stacks': [ '2.0.1' ],
18 silly audit   'node-fetch': [ '2.7.0' ],
18 silly audit   'node-forge': [ '1.3.1' ],
18 silly audit   'node-int64': [ '0.4.0' ],
18 silly audit   'node-releases': [ '2.0.19' ],
18 silly audit   'normalize-path': [ '3.0.0' ],
18 silly audit   'npm-package-arg': [ '11.0.3' ],
18 silly audit   'nth-check': [ '2.1.1' ],
18 silly audit   nullthrows: [ '1.1.1' ],
18 silly audit   ob1: [ '0.82.4' ],
18 silly audit   'object-assign': [ '4.1.1' ],
18 silly audit   'on-finished': [ '2.3.0', '2.4.1' ],
18 silly audit   'on-headers': [ '1.0.2' ],
18 silly audit   once: [ '1.4.0' ],
18 silly audit   onetime: [ '2.0.1' ],
18 silly audit   ora: [ '3.4.0' ],
18 silly audit   'p-try': [ '2.2.0' ],
18 silly audit   'package-json-from-dist': [ '1.0.1' ],
18 silly audit   'parse-json': [ '4.0.0' ],
18 silly audit   'parse-png': [ '2.1.0' ],
18 silly audit   parseurl: [ '1.3.3' ],
18 silly audit   'path-exists': [ '4.0.0' ],
18 silly audit   'path-is-absolute': [ '1.0.1' ],
18 silly audit   'path-key': [ '3.1.1' ],
18 silly audit   'path-parse': [ '1.0.7' ],
18 silly audit   'path-scurry': [ '1.11.1' ],
18 silly audit   picocolors: [ '1.1.1' ],
18 silly audit   pirates: [ '4.0.7' ],
18 silly audit   plist: [ '3.1.0' ],
18 silly audit   pngjs: [ '3.4.0' ],
18 silly audit   postcss: [ '8.4.49' ],
18 silly audit   'postcss-value-parser': [ '4.2.0' ],
18 silly audit   'pretty-bytes': [ '5.6.0' ],
18 silly audit   'pretty-format': [ '29.7.0' ],
18 silly audit   'proc-log': [ '4.2.0' ],
18 silly audit   progress: [ '2.0.3' ],
18 silly audit   prompts: [ '2.4.2' ],
18 silly audit   punycode: [ '2.3.1' ],
18 silly audit   'qrcode-terminal': [ '0.11.0' ],
18 silly audit   'query-string': [ '7.1.3' ],
18 silly audit   queue: [ '6.0.2' ],
18 silly audit   'range-parser': [ '1.2.1' ],
18 silly audit   rc: [ '1.2.8' ],
18 silly audit   react: [ '19.0.0' ],
18 silly audit   'react-devtools-core': [ '6.1.3' ],
18 silly audit   'react-dom': [ '19.0.0' ],
18 silly audit   'react-fast-compare': [ '3.2.2' ],
18 silly audit   'react-freeze': [ '1.0.4' ],
18 silly audit   'react-leaflet': [ '4.2.1' ],
18 silly audit   'react-native': [ '0.79.1' ],
18 silly audit   'react-native-edge-to-edge': [ '1.6.0' ],
18 silly audit   'react-native-gesture-handler': [ '2.24.0' ],
18 silly audit   'react-native-is-edge-to-edge': [ '1.2.1', '1.1.7' ],
18 silly audit   'react-native-reanimated': [ '3.17.5' ],
18 silly audit   'react-native-safe-area-context': [ '5.3.0' ],
18 silly audit   'react-native-screens': [ '4.10.0' ],
18 silly audit   'react-native-svg': [ '15.11.2' ],
18 silly audit   'react-native-url-polyfill': [ '2.0.0' ],
18 silly audit   'react-native-web': [ '0.20.0' ],
18 silly audit   'react-native-webview': [ '13.13.5' ],
18 silly audit   'react-refresh': [ '0.14.2' ],
18 silly audit   'react-toastify': [ '10.0.6' ],
18 silly audit   regenerate: [ '1.4.2' ],
18 silly audit   'regenerate-unicode-properties': [ '10.2.0' ],
18 silly audit   'regenerator-runtime': [ '0.13.11' ],
18 silly audit   'regexpu-core': [ '6.2.0' ],
18 silly audit   regjsgen: [ '0.8.0' ],
18 silly audit   regjsparser: [ '0.12.0' ],
18 silly audit   'require-directory': [ '2.1.1' ],
18 silly audit   'require-from-string': [ '2.0.2' ],
18 silly audit   requireg: [ '0.2.2' ],
18 silly audit   resolve: [ '1.7.1', '1.22.10' ],
18 silly audit   'resolve-workspace-root': [ '2.0.0' ],
18 silly audit   'resolve.exports': [ '2.0.3' ],
18 silly audit   'restore-cursor': [ '2.0.0' ],
18 silly audit   'signal-exit': [ '3.0.7', '4.1.0' ],
18 silly audit   rimraf: [ '3.0.2' ],
18 silly audit   'safe-buffer': [ '5.2.1' ],
18 silly audit   sax: [ '1.4.1' ],
18 silly audit   scheduler: [ '0.25.0' ],
18 silly audit   'schema-utils': [ '4.3.2' ],
18 silly audit   send: [ '0.19.1', '0.19.0' ],
18 silly audit   'serialize-error': [ '2.1.0' ],
18 silly audit   'serve-static': [ '1.16.2' ],
18 silly audit   'server-only': [ '0.0.1' ],
18 silly audit   setimmediate: [ '1.0.5' ],
18 silly audit   setprototypeof: [ '1.2.0' ],
18 silly audit   'sf-symbols-typescript': [ '2.1.0' ],
18 silly audit   shallowequal: [ '1.1.0' ],
18 silly audit   'shebang-command': [ '2.0.0' ],
18 silly audit   'shebang-regex': [ '3.0.0' ],
18 silly audit   'shell-quote': [ '1.8.3' ],
18 silly audit   'simple-plist': [ '1.3.1' ],
18 silly audit   'simple-swizzle': [ '0.2.2' ],
18 silly audit   sisteransi: [ '1.0.5' ],
18 silly audit   slash: [ '3.0.0' ],
18 silly audit   slugify: [ '1.6.6' ],
18 silly audit   'source-map-js': [ '1.2.1' ],
18 silly audit   'source-map-support': [ '0.5.21' ],
18 silly audit   'split-on-first': [ '1.1.0' ],
18 silly audit   'sprintf-js': [ '1.0.3' ],
18 silly audit   'stack-utils': [ '2.0.6' ],
18 silly audit   stackframe: [ '1.3.4' ],
18 silly audit   'stacktrace-parser': [ '0.1.11' ],
18 silly audit   'stream-buffers': [ '2.2.0' ],
18 silly audit   'strict-uri-encode': [ '2.0.0' ],
18 silly audit   'strip-json-comments': [ '2.0.1' ],
18 silly audit   'structured-headers': [ '0.4.1' ],
18 silly audit   styleq: [ '0.1.3' ],
18 silly audit   sucrase: [ '3.35.0' ],
18 silly audit   'supports-hyperlinks': [ '2.3.0' ],
18 silly audit   'supports-preserve-symlinks-flag': [ '1.0.0' ],
18 silly audit   tar: [ '7.4.3' ],
18 silly audit   yallist: [ '5.0.0', '3.1.1' ],
18 silly audit   'temp-dir': [ '2.0.0' ],
18 silly audit   'terminal-link': [ '2.1.1' ],
18 silly audit   terser: [ '5.43.1' ],
18 silly audit   'test-exclude': [ '6.0.0' ],
18 silly audit   thenify: [ '3.3.1' ],
18 silly audit   'thenify-all': [ '1.6.0' ],
18 silly audit   throat: [ '5.0.0' ],
18 silly audit   tmpl: [ '1.0.5' ],
18 silly audit   'to-regex-range': [ '5.0.1' ],
18 silly audit   toidentifier: [ '1.0.1' ],
18 silly audit   tr46: [ '0.0.3' ],
18 silly audit   'ts-interface-checker': [ '0.1.13' ],
18 silly audit   'type-detect': [ '4.0.8' ],
18 silly audit   typescript: [ '5.8.3' ],
18 silly audit   'ua-parser-js': [ '1.0.40' ],
18 silly audit   undici: [ '6.21.3' ],
18 silly audit   'undici-types': [ '7.8.0' ],
18 silly audit   'unicode-canonical-property-names-ecmascript': [ '2.0.1' ],
18 silly audit   'unicode-match-property-ecmascript': [ '2.0.0' ],
18 silly audit   'unicode-match-property-value-ecmascript': [ '2.2.0' ],
18 silly audit   'unicode-property-aliases-ecmascript': [ '2.1.0' ],
18 silly audit   'unique-string': [ '2.0.0' ],
18 silly audit   unpipe: [ '1.0.0' ],
18 silly audit   'update-browserslist-db': [ '1.1.3' ],
18 silly audit   'use-latest-callback': [ '0.2.4' ],
18 silly audit   'use-sync-external-store': [ '1.5.0' ],
18 silly audit   'utils-merge': [ '1.0.1' ],
18 silly audit   uuid: [ '7.0.3' ],
18 silly audit   'validate-npm-package-name': [ '5.0.1' ],
18 silly audit   vary: [ '1.1.2' ],
18 silly audit   vlq: [ '1.0.1' ],
18 silly audit   walker: [ '1.0.8' ],
18 silly audit   'warn-once': [ '0.1.1' ],
18 silly audit   wcwidth: [ '1.0.1' ],
18 silly audit   'webidl-conversions': [ '3.0.1', '5.0.0' ],
18 silly audit   'whatwg-fetch': [ '3.6.20' ],
18 silly audit   'whatwg-url': [ '5.0.0' ],
18 silly audit   'whatwg-url-without-unicode': [ '8.0.0-3' ],
18 silly audit   which: [ '2.0.2' ],
18 silly audit   wonka: [ '6.3.5' ],
18 silly audit   wrappy: [ '1.0.2' ],
18 silly audit   'write-file-atomic': [ '4.0.2' ],
18 silly audit   xcode: [ '3.0.1' ],
18 silly audit   xml2js: [ '0.6.0' ],
18 silly audit   xmlbuilder: [ '11.0.1', '15.1.1' ],
18 silly audit   y18n: [ '5.0.8' ],
18 silly audit   yargs: [ '17.7.2' ],
18 silly audit   'yargs-parser': [ '21.1.1' ],
18 silly audit   'yocto-queue': [ '0.1.0' ],
18 silly audit   'cross-env': [ '7.0.3' ]
18 silly audit }
19 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-win32-arm64-msvc
20 silly reify mark deleted [
20 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-win32-arm64-msvc'
20 silly reify ]
21 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-linux-x64-musl
22 silly reify mark deleted [
22 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-linux-x64-musl'
22 silly reify ]
23 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-linux-x64-gnu
24 silly reify mark deleted [
24 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-linux-x64-gnu'
24 silly reify ]
25 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-linux-arm64-musl
26 silly reify mark deleted [
26 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-linux-arm64-musl'
26 silly reify ]
27 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-linux-arm64-gnu
28 silly reify mark deleted [
28 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-linux-arm64-gnu'
28 silly reify ]
29 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-linux-arm-gnueabihf
30 silly reify mark deleted [
30 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-linux-arm-gnueabihf'
30 silly reify ]
31 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-freebsd-x64
32 silly reify mark deleted [
32 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-freebsd-x64'
32 silly reify ]
33 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-darwin-x64
34 silly reify mark deleted [ 'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-darwin-x64' ]
35 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-darwin-arm64
36 silly reify mark deleted [
36 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-darwin-arm64'
36 silly reify ]
37 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\fsevents
38 silly reify mark deleted [ 'D:\\.0000\\scarboroughfair\\node_modules\\fsevents' ]
39 silly tarball no local data for cross-env@https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz. Extracting by manifest.
40 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 1344ms
41 silly audit report {}
42 http fetch GET 200 https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz 4004ms (cache miss)
43 silly ADD node_modules/cross-env
44 silly ADD
45 silly ADD
46 silly ADD
47 silly ADD
48 silly ADD
49 silly ADD
50 silly ADD
51 silly ADD
52 silly ADD
53 silly ADD
54 verbose exit 0
55 info ok
