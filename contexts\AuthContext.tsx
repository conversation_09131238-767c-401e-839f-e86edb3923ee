import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { Database } from '@/types/database';

type Profile = Database['public']['Tables']['profiles']['Row'];

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (
    email: string,
    password: string,
    username: string
  ) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        loadProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        await loadProfile(session.user.id);
      } else {
        setProfile(null);
        setLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const loadProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle();

      if (error) {
        console.error('Error loading profile:', error);
      } else {
        setProfile(data);
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signUp = async (email: string, password: string, username: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });

    if (!error && data.user) {
      // Create profile
      const { error: profileError } = await supabase.from('profiles').insert({
        id: data.user.id,
        username,
        full_name: username,
        level: 1,
        experience: 0,
        gold: 100,
        reputation: 0,
      });

      if (profileError) {
        console.error('Error creating profile:', profileError);
        return { error: profileError };
      }
    }

    return { error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Error signing out:', error);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) return { error: new Error('No user logged in') };

    // 过滤掉可能不存在的字段，只保留基本字段
    const safeUpdates: any = {
      updated_at: new Date().toISOString(),
    };

    // 只添加已知存在的字段
    if (updates.username !== undefined) safeUpdates.username = updates.username;
    if (updates.email !== undefined) safeUpdates.email = updates.email;

    // 尝试添加可选字段，如果失败则忽略
    const optionalFields = ['bio', 'location', 'avatar_url'];
    for (const field of optionalFields) {
      if (updates[field as keyof Profile] !== undefined) {
        safeUpdates[field] = updates[field as keyof Profile];
      }
    }

    console.log('🔄 更新用户资料:', safeUpdates);

    const { error } = await supabase
      .from('profiles')
      .update(safeUpdates)
      .eq('id', user.id);

    if (error) {
      console.error('❌ 更新用户资料失败:', error);

      // 如果是字段不存在的错误，提供解决方案
      if (
        error.message.includes('column') &&
        error.message.includes('does not exist')
      ) {
        const missingColumn = error.message.match(/column "(\w+)"/)?.[1];
        console.log(
          `📝 缺少字段 ${missingColumn}，请在 Supabase Dashboard 中添加:`
        );
        console.log(
          `   ALTER TABLE profiles ADD COLUMN ${missingColumn} TEXT;`
        );

        // 尝试只更新基本字段
        const basicUpdates = {
          username: updates.username,
          updated_at: new Date().toISOString(),
        };

        console.log('🔄 尝试只更新基本字段:', basicUpdates);
        const { error: basicError } = await supabase
          .from('profiles')
          .update(basicUpdates)
          .eq('id', user.id);

        if (!basicError) {
          setProfile((prev) => (prev ? { ...prev, ...basicUpdates } : null));
          return {
            error: new Error(
              `字段 ${missingColumn} 不存在，已更新其他字段。请添加缺失字段后重试。`
            ),
          };
        }
      }
    } else {
      console.log('✅ 用户资料更新成功');
      setProfile((prev) => (prev ? { ...prev, ...safeUpdates } : null));
    }

    return { error };
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        profile,
        loading,
        signIn,
        signUp,
        signOut,
        updateProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
