0 verbose cli E:\Node\node.exe e:\Node\node_modules\npm\bin\npm-cli.js
1 info using npm@10.7.0
2 info using node@v20.15.1
3 silly config:load:file:E:\Nvm\nvm\v20.15.1\node_modules\npm\npmrc
4 silly config:load:file:D:\.0000\scarboroughfair\.npmrc
5 silly config:load:file:C:\Users\<USER>\.npmrc
6 silly config:load:file:e:\Node\etc\npmrc
7 verbose title npm install
8 verbose argv "install"
9 verbose logfile logs-max:10 dir:d:\.0000\scarboroughfair\_logs\2025-07-05T06_26_00_028Z-
10 verbose logfile d:\.0000\scarboroughfair\_logs\2025-07-05T06_26_00_028Z-debug-0.log
11 silly logfile done cleaning log files
12 silly idealTree buildDeps
13 silly reify moves {}
14 silly audit bulk request {
14 silly audit   '@0no-co/graphql.web': [ '1.1.2' ],
14 silly audit   '@ampproject/remapping': [ '2.3.0' ],
14 silly audit   '@babel/code-frame': [ '7.27.1', '7.10.4' ],
14 silly audit   '@babel/compat-data': [ '7.28.0' ],
14 silly audit   '@babel/core': [ '7.28.0' ],
14 silly audit   '@babel/generator': [ '7.28.0' ],
14 silly audit   '@babel/helper-annotate-as-pure': [ '7.27.3' ],
14 silly audit   '@babel/helper-compilation-targets': [ '7.27.2' ],
14 silly audit   '@babel/helper-create-class-features-plugin': [ '7.27.1' ],
14 silly audit   '@babel/helper-create-regexp-features-plugin': [ '7.27.1' ],
14 silly audit   '@babel/helper-define-polyfill-provider': [ '0.6.5' ],
14 silly audit   '@babel/helper-globals': [ '7.28.0' ],
14 silly audit   '@babel/helper-member-expression-to-functions': [ '7.27.1' ],
14 silly audit   '@babel/helper-module-imports': [ '7.27.1' ],
14 silly audit   '@babel/helper-module-transforms': [ '7.27.3' ],
14 silly audit   '@babel/helper-optimise-call-expression': [ '7.27.1' ],
14 silly audit   '@babel/helper-plugin-utils': [ '7.27.1' ],
14 silly audit   '@babel/helper-remap-async-to-generator': [ '7.27.1' ],
14 silly audit   '@babel/helper-replace-supers': [ '7.27.1' ],
14 silly audit   '@babel/helper-skip-transparent-expression-wrappers': [ '7.27.1' ],
14 silly audit   '@babel/helper-string-parser': [ '7.27.1' ],
14 silly audit   '@babel/helper-validator-identifier': [ '7.27.1' ],
14 silly audit   '@babel/helper-validator-option': [ '7.27.1' ],
14 silly audit   '@babel/helper-wrap-function': [ '7.27.1' ],
14 silly audit   '@babel/helpers': [ '7.27.6' ],
14 silly audit   '@babel/highlight': [ '7.25.9' ],
14 silly audit   'ansi-styles': [ '3.2.1', '6.2.1', '4.3.0', '5.2.0' ],
14 silly audit   chalk: [ '2.4.2', '4.1.2' ],
14 silly audit   'color-convert': [ '1.9.3', '2.0.1' ],
14 silly audit   'color-name': [ '1.1.3', '1.1.4' ],
14 silly audit   'escape-string-regexp': [ '1.0.5', '4.0.0', '2.0.0' ],
14 silly audit   'has-flag': [ '3.0.0', '4.0.0' ],
14 silly audit   'supports-color': [ '5.5.0', '8.1.1', '7.2.0' ],
14 silly audit   '@babel/parser': [ '7.28.0' ],
14 silly audit   '@babel/plugin-proposal-decorators': [ '7.28.0' ],
14 silly audit   '@babel/plugin-proposal-export-default-from': [ '7.27.1' ],
14 silly audit   '@babel/plugin-syntax-async-generators': [ '7.8.4' ],
14 silly audit   '@babel/plugin-syntax-bigint': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-class-properties': [ '7.12.13' ],
14 silly audit   '@babel/plugin-syntax-class-static-block': [ '7.14.5' ],
14 silly audit   '@babel/plugin-syntax-decorators': [ '7.27.1' ],
14 silly audit   '@babel/plugin-syntax-dynamic-import': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-export-default-from': [ '7.27.1' ],
14 silly audit   '@babel/plugin-syntax-flow': [ '7.27.1' ],
14 silly audit   '@babel/plugin-syntax-import-attributes': [ '7.27.1' ],
14 silly audit   '@babel/plugin-syntax-import-meta': [ '7.10.4' ],
14 silly audit   '@babel/plugin-syntax-json-strings': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-jsx': [ '7.27.1' ],
14 silly audit   '@babel/plugin-syntax-logical-assignment-operators': [ '7.10.4' ],
14 silly audit   '@babel/plugin-syntax-nullish-coalescing-operator': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-numeric-separator': [ '7.10.4' ],
14 silly audit   '@babel/plugin-syntax-object-rest-spread': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-optional-catch-binding': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-optional-chaining': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-private-property-in-object': [ '7.14.5' ],
14 silly audit   '@babel/plugin-syntax-top-level-await': [ '7.14.5' ],
14 silly audit   '@babel/plugin-syntax-typescript': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-arrow-functions': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-async-generator-functions': [ '7.28.0' ],
14 silly audit   '@babel/plugin-transform-async-to-generator': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-block-scoping': [ '7.28.0' ],
14 silly audit   '@babel/plugin-transform-class-properties': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-classes': [ '7.28.0' ],
14 silly audit   '@babel/plugin-transform-computed-properties': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-destructuring': [ '7.28.0' ],
14 silly audit   '@babel/plugin-transform-export-namespace-from': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-flow-strip-types': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-for-of': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-function-name': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-literals': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-logical-assignment-operators': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-modules-commonjs': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-named-capturing-groups-regex': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-nullish-coalescing-operator': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-numeric-separator': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-object-rest-spread': [ '7.28.0' ],
14 silly audit   '@babel/plugin-transform-optional-catch-binding': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-optional-chaining': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-parameters': [ '7.27.7' ],
14 silly audit   '@babel/plugin-transform-private-methods': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-private-property-in-object': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-react-display-name': [ '7.28.0' ],
14 silly audit   '@babel/plugin-transform-react-jsx': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-react-jsx-development': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-react-jsx-self': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-react-jsx-source': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-react-pure-annotations': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-regenerator': [ '7.28.0' ],
14 silly audit   '@babel/plugin-transform-runtime': [ '7.28.0' ],
14 silly audit   '@babel/plugin-transform-shorthand-properties': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-spread': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-sticky-regex': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-template-literals': [ '7.27.1' ],
14 silly audit   '@babel/plugin-transform-typescript': [ '7.28.0' ],
14 silly audit   '@babel/plugin-transform-unicode-regex': [ '7.27.1' ],
14 silly audit   '@babel/preset-react': [ '7.27.1' ],
14 silly audit   '@babel/preset-typescript': [ '7.27.1' ],
14 silly audit   '@babel/runtime': [ '7.27.6' ],
14 silly audit   '@babel/template': [ '7.27.2' ],
14 silly audit   '@babel/traverse': [ '7.28.0' ],
14 silly audit   '@babel/types': [ '7.28.0' ],
14 silly audit   '@egjs/hammerjs': [ '2.0.17' ],
14 silly audit   '@expo-google-fonts/ma-shan-zheng': [ '0.2.3' ],
14 silly audit   '@expo/cli': [ '0.24.17' ],
14 silly audit   semver: [ '7.7.2', '7.6.3', '6.3.1' ],
14 silly audit   '@expo/code-signing-certificates': [ '0.0.5' ],
14 silly audit   '@expo/config': [ '11.0.11' ],
14 silly audit   '@expo/config-plugins': [ '10.1.0', '10.0.3' ],
14 silly audit   '@expo/config-types': [ '53.0.4' ],
14 silly audit   '@expo/devcert': [ '1.2.0' ],
14 silly audit   debug: [ '3.2.7', '2.6.9', '4.4.1' ],
14 silly audit   '@expo/env': [ '1.0.6' ],
14 silly audit   '@expo/fingerprint': [ '0.13.3' ],
14 silly audit   '@expo/image-utils': [ '0.7.5' ],
14 silly audit   '@expo/json-file': [ '9.1.4' ],
14 silly audit   '@expo/metro-config': [ '0.20.16' ],
14 silly audit   '@expo/metro-runtime': [ '5.0.4' ],
14 silly audit   '@expo/osascript': [ '2.2.4' ],
14 silly audit   '@expo/package-manager': [ '1.8.5' ],
14 silly audit   '@expo/plist': [ '0.3.4' ],
14 silly audit   '@expo/prebuild-config': [ '9.0.9' ],
14 silly audit   '@expo/sdk-runtime-versions': [ '1.0.0' ],
14 silly audit   '@expo/server': [ '0.6.3' ],
14 silly audit   '@expo/spawn-async': [ '1.7.2' ],
14 silly audit   '@expo/sudo-prompt': [ '9.3.2' ],
14 silly audit   '@expo/vector-icons': [ '14.1.0' ],
14 silly audit   '@expo/ws-tunnel': [ '1.0.6' ],
14 silly audit   '@expo/xcpretty': [ '4.3.2' ],
14 silly audit   '@isaacs/cliui': [ '8.0.2' ],
14 silly audit   'wrap-ansi': [ '8.1.0', '7.0.0' ],
14 silly audit   '@isaacs/fs-minipass': [ '4.0.1' ],
14 silly audit   '@isaacs/ttlcache': [ '1.4.1' ],
14 silly audit   '@istanbuljs/load-nyc-config': [ '1.1.0' ],
14 silly audit   argparse: [ '1.0.10', '2.0.1' ],
14 silly audit   'find-up': [ '4.1.0', '5.0.0' ],
14 silly audit   'js-yaml': [ '3.14.1', '4.1.0' ],
14 silly audit   'locate-path': [ '5.0.0', '6.0.0' ],
14 silly audit   'p-limit': [ '2.3.0', '3.1.0' ],
14 silly audit   'p-locate': [ '4.1.0', '5.0.0' ],
14 silly audit   '@istanbuljs/schema': [ '0.1.3' ],
14 silly audit   '@jest/create-cache-key-function': [ '29.7.0' ],
14 silly audit   '@jest/environment': [ '29.7.0' ],
14 silly audit   '@jest/fake-timers': [ '29.7.0' ],
14 silly audit   '@jest/schemas': [ '29.6.3' ],
14 silly audit   '@jest/transform': [ '29.7.0' ],
14 silly audit   '@jest/types': [ '29.6.3' ],
14 silly audit   '@jridgewell/gen-mapping': [ '0.3.12' ],
14 silly audit   '@jridgewell/resolve-uri': [ '3.1.2' ],
14 silly audit   '@jridgewell/source-map': [ '0.3.10' ],
14 silly audit   '@jridgewell/sourcemap-codec': [ '1.5.4' ],
14 silly audit   '@jridgewell/trace-mapping': [ '0.3.29' ],
14 silly audit   '@lucide/lab': [ '0.1.2' ],
14 silly audit   '@pkgjs/parseargs': [ '0.11.0' ],
14 silly audit   '@radix-ui/react-compose-refs': [ '1.1.2' ],
14 silly audit   '@radix-ui/react-slot': [ '1.2.0' ],
14 silly audit   '@react-leaflet/core': [ '2.1.0' ],
14 silly audit   '@react-native-async-storage/async-storage': [ '2.2.0' ],
14 silly audit   '@react-native/assets-registry': [ '0.79.1' ],
14 silly audit   '@react-native/babel-plugin-codegen': [ '0.79.5' ],
14 silly audit   '@react-native/babel-preset': [ '0.79.5' ],
14 silly audit   '@react-native/codegen': [ '0.79.5', '0.79.1' ],
14 silly audit   'brace-expansion': [ '1.1.12', '2.0.2' ],
14 silly audit   glob: [ '7.2.3', '10.4.5' ],
14 silly audit   minimatch: [ '3.1.2', '9.0.5' ],
14 silly audit   '@react-native/community-cli-plugin': [ '0.79.1' ],
14 silly audit   '@react-native/debugger-frontend': [ '0.79.1', '0.79.5' ],
14 silly audit   '@react-native/dev-middleware': [ '0.79.1', '0.79.5' ],
14 silly audit   ms: [ '2.0.0', '2.1.3' ],
14 silly audit   ws: [ '6.2.3', '7.5.10', '8.18.3' ],
14 silly audit   '@react-native/gradle-plugin': [ '0.79.1' ],
14 silly audit   '@react-native/js-polyfills': [ '0.79.1' ],
14 silly audit   '@react-native/normalize-colors': [ '0.79.5', '0.74.89', '0.79.1' ],
14 silly audit   '@react-native/virtualized-lists': [ '0.79.1' ],
14 silly audit   '@react-navigation/bottom-tabs': [ '7.4.2' ],
14 silly audit   '@react-navigation/core': [ '7.12.1' ],
14 silly audit   '@react-navigation/elements': [ '2.5.2' ],
14 silly audit   '@react-navigation/native': [ '7.1.14' ],
14 silly audit   '@react-navigation/native-stack': [ '7.3.21' ],
14 silly audit   '@react-navigation/routers': [ '7.4.1' ],
14 silly audit   '@sinclair/typebox': [ '0.27.8' ],
14 silly audit   '@sinonjs/commons': [ '3.0.1' ],
14 silly audit   '@sinonjs/fake-timers': [ '10.3.0' ],
14 silly audit   '@supabase/auth-js': [ '2.70.0' ],
14 silly audit   '@supabase/functions-js': [ '2.4.5' ],
14 silly audit   '@supabase/node-fetch': [ '2.6.15' ],
14 silly audit   '@supabase/postgrest-js': [ '1.19.4' ],
14 silly audit   '@supabase/realtime-js': [ '2.11.15' ],
14 silly audit   '@supabase/storage-js': [ '2.7.1' ],
14 silly audit   '@supabase/supabase-js': [ '2.50.3' ],
14 silly audit   '@types/babel__core': [ '7.20.5' ],
14 silly audit   '@types/babel__generator': [ '7.27.0' ],
14 silly audit   '@types/babel__template': [ '7.4.4' ],
14 silly audit   '@types/babel__traverse': [ '7.20.7' ],
14 silly audit   '@types/geojson': [ '7946.0.16' ],
14 silly audit   '@types/graceful-fs': [ '4.1.9' ],
14 silly audit   '@types/hammerjs': [ '2.0.46' ],
14 silly audit   '@types/istanbul-lib-coverage': [ '2.0.6' ],
14 silly audit   '@types/istanbul-lib-report': [ '3.0.3' ],
14 silly audit   '@types/istanbul-reports': [ '3.0.4' ],
14 silly audit   '@types/json-schema': [ '7.0.15' ],
14 silly audit   '@types/leaflet': [ '1.9.19' ],
14 silly audit   '@types/node': [ '24.0.10' ],
14 silly audit   '@types/phoenix': [ '1.6.6' ],
14 silly audit   '@types/react': [ '19.0.14' ],
14 silly audit   '@types/stack-utils': [ '2.0.3' ],
14 silly audit   '@types/ws': [ '8.18.1' ],
14 silly audit   '@types/yargs': [ '17.0.33' ],
14 silly audit   '@types/yargs-parser': [ '21.0.3' ],
14 silly audit   '@urql/core': [ '5.2.0' ],
14 silly audit   '@urql/exchange-retry': [ '1.3.2' ],
14 silly audit   '@xmldom/xmldom': [ '0.8.10' ],
14 silly audit   'abort-controller': [ '3.0.0' ],
14 silly audit   accepts: [ '1.3.8' ],
14 silly audit   acorn: [ '8.15.0' ],
14 silly audit   'agent-base': [ '7.1.3' ],
14 silly audit   ajv: [ '8.17.1' ],
14 silly audit   'ajv-formats': [ '2.1.1' ],
14 silly audit   'ajv-keywords': [ '5.1.0' ],
14 silly audit   anser: [ '1.4.10' ],
14 silly audit   'ansi-escapes': [ '4.3.2' ],
14 silly audit   'type-fest': [ '0.21.3', '0.7.1' ],
14 silly audit   'ansi-regex': [ '5.0.1', '4.1.1', '6.1.0' ],
14 silly audit   'any-promise': [ '1.3.0' ],
14 silly audit   anymatch: [ '3.1.3' ],
14 silly audit   picomatch: [ '2.3.1', '3.0.1' ],
14 silly audit   arg: [ '5.0.2' ],
14 silly audit   asap: [ '2.0.6' ],
14 silly audit   'async-limiter': [ '1.0.1' ],
14 silly audit   'babel-jest': [ '29.7.0' ],
14 silly audit   'babel-plugin-istanbul': [ '6.1.1' ],
14 silly audit   'babel-plugin-jest-hoist': [ '29.6.3' ],
14 silly audit   'babel-plugin-polyfill-corejs2': [ '0.4.14' ],
14 silly audit   'babel-plugin-polyfill-corejs3': [ '0.13.0' ],
14 silly audit   'babel-plugin-polyfill-regenerator': [ '0.6.5' ],
14 silly audit   'babel-plugin-react-native-web': [ '0.19.13' ],
14 silly audit   'babel-plugin-syntax-hermes-parser': [ '0.25.1' ],
14 silly audit   'babel-plugin-transform-flow-enums': [ '0.0.2' ],
14 silly audit   'babel-preset-current-node-syntax': [ '1.1.0' ],
14 silly audit   'babel-preset-expo': [ '13.2.2' ],
14 silly audit   'babel-preset-jest': [ '29.6.3' ],
14 silly audit   'balanced-match': [ '1.0.2' ],
14 silly audit   'base64-js': [ '1.5.1' ],
14 silly audit   'better-opn': [ '3.0.2' ],
14 silly audit   open: [ '8.4.2', '7.4.2' ],
14 silly audit   'big-integer': [ '1.6.52' ],
14 silly audit   boolbase: [ '1.0.0' ],
14 silly audit   'bplist-creator': [ '0.1.0' ],
14 silly audit   'bplist-parser': [ '0.3.2', '0.3.1' ],
14 silly audit   braces: [ '3.0.3' ],
14 silly audit   browserslist: [ '4.25.1' ],
14 silly audit   bser: [ '2.1.1' ],
14 silly audit   buffer: [ '5.7.1' ],
14 silly audit   'buffer-from': [ '1.1.2' ],
14 silly audit   bytes: [ '3.1.2' ],
14 silly audit   'caller-callsite': [ '2.0.0' ],
14 silly audit   'caller-path': [ '2.0.0' ],
14 silly audit   callsites: [ '2.0.0' ],
14 silly audit   camelcase: [ '5.3.1', '6.3.0' ],
14 silly audit   'caniuse-lite': [ '1.0.30001726' ],
14 silly audit   chownr: [ '3.0.0' ],
14 silly audit   'chrome-launcher': [ '0.15.2' ],
14 silly audit   'chromium-edge-launcher': [ '0.2.0' ],
14 silly audit   'ci-info': [ '3.9.0', '2.0.0' ],
14 silly audit   'cli-cursor': [ '2.1.0' ],
14 silly audit   'cli-spinners': [ '2.9.2' ],
14 silly audit   'client-only': [ '0.0.1' ],
14 silly audit   cliui: [ '8.0.1' ],
14 silly audit   'emoji-regex': [ '8.0.0', '9.2.2' ],
14 silly audit   'string-width': [ '4.2.3', '5.1.2' ],
14 silly audit   'strip-ansi': [ '6.0.1', '5.2.0', '7.1.0' ],
14 silly audit   clone: [ '1.0.4' ],
14 silly audit   clsx: [ '2.1.1' ],
14 silly audit   color: [ '4.2.3' ],
14 silly audit   'color-string': [ '1.9.1' ],
14 silly audit   commander: [ '7.2.0', '12.1.0', '4.1.1', '2.20.3' ],
14 silly audit   compressible: [ '2.0.18' ],
14 silly audit   compression: [ '1.8.0' ],
14 silly audit   negotiator: [ '0.6.4', '0.6.3' ],
14 silly audit   'concat-map': [ '0.0.1' ],
14 silly audit   connect: [ '3.7.0' ],
14 silly audit   'convert-source-map': [ '2.0.0' ],
14 silly audit   'core-js-compat': [ '3.43.0' ],
14 silly audit   cosmiconfig: [ '5.2.1' ],
14 silly audit   'cross-fetch': [ '3.2.0' ],
14 silly audit   'cross-spawn': [ '7.0.6' ],
14 silly audit   'crypto-random-string': [ '2.0.0' ],
14 silly audit   'css-in-js-utils': [ '3.1.0' ],
14 silly audit   'css-select': [ '5.2.2' ],
14 silly audit   'css-tree': [ '1.1.3' ],
14 silly audit   'source-map': [ '0.6.1', '0.5.7' ],
14 silly audit   'css-what': [ '6.2.2' ],
14 silly audit   csstype: [ '3.1.3' ],
14 silly audit   'decode-uri-component': [ '0.2.2' ],
14 silly audit   'deep-extend': [ '0.6.0' ],
14 silly audit   deepmerge: [ '4.3.1' ],
14 silly audit   defaults: [ '1.0.4' ],
14 silly audit   'define-lazy-prop': [ '2.0.0' ],
14 silly audit   depd: [ '2.0.0' ],
14 silly audit   destroy: [ '1.2.0' ],
14 silly audit   'detect-libc': [ '1.0.3' ],
14 silly audit   'dom-serializer': [ '2.0.0' ],
14 silly audit   domelementtype: [ '2.3.0' ],
14 silly audit   domhandler: [ '5.0.3' ],
14 silly audit   domutils: [ '3.2.2' ],
14 silly audit   dotenv: [ '16.4.7' ],
14 silly audit   'dotenv-expand': [ '11.0.7' ],
14 silly audit   eastasianwidth: [ '0.2.0' ],
14 silly audit   'ee-first': [ '1.1.1' ],
14 silly audit   'electron-to-chromium': [ '1.5.179' ],
14 silly audit   encodeurl: [ '1.0.2', '2.0.0' ],
14 silly audit   entities: [ '4.5.0' ],
14 silly audit   'env-editor': [ '0.4.2' ],
14 silly audit   'error-ex': [ '1.3.2' ],
14 silly audit   'error-stack-parser': [ '2.1.4' ],
14 silly audit   escalade: [ '3.2.0' ],
14 silly audit   'escape-html': [ '1.0.3' ],
14 silly audit   esprima: [ '4.0.1' ],
14 silly audit   etag: [ '1.8.1' ],
14 silly audit   'event-target-shim': [ '5.0.1' ],
14 silly audit   'exec-async': [ '2.2.0' ],
14 silly audit   expo: [ '53.0.16' ],
14 silly audit   'expo-asset': [ '11.1.6' ],
14 silly audit   'expo-blur': [ '14.1.5' ],
14 silly audit   'expo-camera': [ '16.1.10' ],
14 silly audit   'expo-constants': [ '17.1.6' ],
14 silly audit   'expo-file-system': [ '18.1.11' ],
14 silly audit   'expo-font': [ '13.2.2', '13.3.2' ],
14 silly audit   'expo-haptics': [ '14.1.4' ],
14 silly audit   'expo-keep-awake': [ '14.1.4' ],
14 silly audit   'expo-linear-gradient': [ '14.1.5' ],
14 silly audit   'expo-linking': [ '7.1.6' ],
14 silly audit   'expo-modules-autolinking': [ '2.1.13' ],
14 silly audit   'expo-modules-core': [ '2.4.2' ],
14 silly audit   'expo-router': [ '5.0.7' ],
14 silly audit   'expo-splash-screen': [ '0.30.9' ],
14 silly audit   'expo-status-bar': [ '2.2.3' ],
14 silly audit   'expo-symbols': [ '0.4.5' ],
14 silly audit   'expo-system-ui': [ '5.0.10' ],
14 silly audit   'expo-web-browser': [ '14.1.6' ],
14 silly audit   'exponential-backoff': [ '3.1.2' ],
14 silly audit   'fast-deep-equal': [ '3.1.3' ],
14 silly audit   'fast-json-stable-stringify': [ '2.1.0' ],
14 silly audit   'fast-uri': [ '3.0.6' ],
14 silly audit   'fb-watchman': [ '2.0.2' ],
14 silly audit   fbjs: [ '3.0.5' ],
14 silly audit   'fbjs-css-vars': [ '1.0.2' ],
14 silly audit   promise: [ '7.3.1', '8.3.0' ],
14 silly audit   'fill-range': [ '7.1.1' ],
14 silly audit   'filter-obj': [ '1.1.0' ],
14 silly audit   finalhandler: [ '1.1.2' ],
14 silly audit   'flow-enums-runtime': [ '0.0.6' ],
14 silly audit   fontfaceobserver: [ '2.3.0' ],
14 silly audit   'foreground-child': [ '3.3.1' ],
14 silly audit   'freeport-async': [ '2.0.0' ],
14 silly audit   fresh: [ '0.5.2' ],
14 silly audit   'fs.realpath': [ '1.0.0' ],
14 silly audit   fsevents: [ '2.3.3' ],
14 silly audit   'function-bind': [ '1.1.2' ],
14 silly audit   gensync: [ '1.0.0-beta.2' ],
14 silly audit   'get-caller-file': [ '2.0.5' ],
14 silly audit   'get-package-type': [ '0.1.0' ],
14 silly audit   getenv: [ '2.0.0' ],
14 silly audit   'graceful-fs': [ '4.2.11' ],
14 silly audit   hasown: [ '2.0.2' ],
14 silly audit   'hermes-estree': [ '0.25.1', '0.28.1' ],
14 silly audit   'hermes-parser': [ '0.25.1', '0.28.1' ],
14 silly audit   'hoist-non-react-statics': [ '3.3.2' ],
14 silly audit   'react-is': [ '16.13.1', '18.3.1', '19.1.0' ],
14 silly audit   'hosted-git-info': [ '7.0.2' ],
14 silly audit   'lru-cache': [ '10.4.3', '5.1.1' ],
14 silly audit   'http-errors': [ '2.0.0' ],
14 silly audit   statuses: [ '2.0.1', '1.5.0' ],
14 silly audit   'https-proxy-agent': [ '7.0.6' ],
14 silly audit   'hyphenate-style-name': [ '1.1.0' ],
14 silly audit   ieee754: [ '1.2.1' ],
14 silly audit   ignore: [ '5.3.2' ],
14 silly audit   'image-size': [ '1.2.1' ],
14 silly audit   'import-fresh': [ '2.0.0' ],
14 silly audit   'resolve-from': [ '3.0.0', '5.0.0' ],
14 silly audit   imurmurhash: [ '0.1.4' ],
14 silly audit   inflight: [ '1.0.6' ],
14 silly audit   inherits: [ '2.0.4' ],
14 silly audit   ini: [ '1.3.8' ],
14 silly audit   'inline-style-prefixer': [ '7.0.1' ],
14 silly audit   invariant: [ '2.2.4' ],
14 silly audit   'is-arrayish': [ '0.2.1', '0.3.2' ],
14 silly audit   'is-core-module': [ '2.16.1' ],
14 silly audit   'is-directory': [ '0.3.1' ],
14 silly audit   'is-docker': [ '2.2.1' ],
14 silly audit   'is-fullwidth-code-point': [ '3.0.0' ],
14 silly audit   'is-number': [ '7.0.0' ],
14 silly audit   'is-plain-obj': [ '2.1.0' ],
14 silly audit   'is-wsl': [ '2.2.0' ],
14 silly audit   isexe: [ '2.0.0' ],
14 silly audit   isows: [ '1.0.7' ],
14 silly audit   'istanbul-lib-coverage': [ '3.2.2' ],
14 silly audit   'istanbul-lib-instrument': [ '5.2.1' ],
14 silly audit   jackspeak: [ '3.4.3' ],
14 silly audit   'jest-environment-node': [ '29.7.0' ],
14 silly audit   'jest-get-type': [ '29.6.3' ],
14 silly audit   'jest-haste-map': [ '29.7.0' ],
14 silly audit   'jest-message-util': [ '29.7.0' ],
14 silly audit   'jest-mock': [ '29.7.0' ],
14 silly audit   'jest-regex-util': [ '29.6.3' ],
14 silly audit   'jest-util': [ '29.7.0' ],
14 silly audit   'jest-validate': [ '29.7.0' ],
14 silly audit   'jest-worker': [ '29.7.0' ],
14 silly audit   'jimp-compact': [ '0.16.1' ],
14 silly audit   'js-tokens': [ '4.0.0' ],
14 silly audit   'jsc-safe-url': [ '0.2.4' ],
14 silly audit   jsesc: [ '3.1.0', '3.0.2' ],
14 silly audit   'json-parse-better-errors': [ '1.0.2' ],
14 silly audit   'json-schema-traverse': [ '1.0.0' ],
14 silly audit   json5: [ '2.2.3' ],
14 silly audit   kleur: [ '3.0.3' ],
14 silly audit   'lan-network': [ '0.1.7' ],
14 silly audit   leaflet: [ '1.9.4' ],
14 silly audit   leven: [ '3.1.0' ],
14 silly audit   'lighthouse-logger': [ '1.4.2' ],
14 silly audit   lightningcss: [ '1.27.0' ],
14 silly audit   'lightningcss-darwin-arm64': [ '1.27.0' ],
14 silly audit   'lightningcss-darwin-x64': [ '1.27.0' ],
14 silly audit   'lightningcss-freebsd-x64': [ '1.27.0' ],
14 silly audit   'lightningcss-linux-arm-gnueabihf': [ '1.27.0' ],
14 silly audit   'lightningcss-linux-arm64-gnu': [ '1.27.0' ],
14 silly audit   'lightningcss-linux-arm64-musl': [ '1.27.0' ],
14 silly audit   'lightningcss-linux-x64-gnu': [ '1.27.0' ],
14 silly audit   'lightningcss-linux-x64-musl': [ '1.27.0' ],
14 silly audit   'lightningcss-win32-arm64-msvc': [ '1.27.0' ],
14 silly audit   'lightningcss-win32-x64-msvc': [ '1.27.0' ],
14 silly audit   'lines-and-columns': [ '1.2.4' ],
14 silly audit   'lodash.debounce': [ '4.0.8' ],
14 silly audit   'lodash.throttle': [ '4.1.1' ],
14 silly audit   'log-symbols': [ '2.2.0' ],
14 silly audit   'loose-envify': [ '1.4.0' ],
14 silly audit   'lucide-react-native': [ '0.475.0' ],
14 silly audit   makeerror: [ '1.0.12' ],
14 silly audit   marky: [ '1.3.0' ],
14 silly audit   'mdn-data': [ '2.0.14' ],
14 silly audit   'memoize-one': [ '5.2.1', '6.0.0' ],
14 silly audit   'merge-options': [ '3.0.4' ],
14 silly audit   'merge-stream': [ '2.0.0' ],
14 silly audit   metro: [ '0.82.4' ],
14 silly audit   'metro-babel-transformer': [ '0.82.4' ],
14 silly audit   'metro-cache': [ '0.82.4' ],
14 silly audit   'metro-cache-key': [ '0.82.4' ],
14 silly audit   'metro-config': [ '0.82.4' ],
14 silly audit   'metro-core': [ '0.82.4' ],
14 silly audit   'metro-file-map': [ '0.82.4' ],
14 silly audit   'metro-minify-terser': [ '0.82.4' ],
14 silly audit   'metro-resolver': [ '0.82.4' ],
14 silly audit   'metro-runtime': [ '0.82.4' ],
14 silly audit   'metro-source-map': [ '0.82.4' ],
14 silly audit   'metro-symbolicate': [ '0.82.4' ],
14 silly audit   'metro-transform-plugins': [ '0.82.4' ],
14 silly audit   'metro-transform-worker': [ '0.82.4' ],
14 silly audit   micromatch: [ '4.0.8' ],
14 silly audit   mime: [ '1.6.0' ],
14 silly audit   'mime-db': [ '1.54.0', '1.52.0' ],
14 silly audit   'mime-types': [ '2.1.35' ],
14 silly audit   'mimic-fn': [ '1.2.0' ],
14 silly audit   minimist: [ '1.2.8' ],
14 silly audit   minipass: [ '7.1.2' ],
14 silly audit   minizlib: [ '3.0.2' ],
14 silly audit   mkdirp: [ '1.0.4', '3.0.1' ],
14 silly audit   mz: [ '2.7.0' ],
14 silly audit   nanoid: [ '3.3.11' ],
14 silly audit   'nested-error-stacks': [ '2.0.1' ],
14 silly audit   'node-fetch': [ '2.7.0' ],
14 silly audit   'node-forge': [ '1.3.1' ],
14 silly audit   'node-int64': [ '0.4.0' ],
14 silly audit   'node-releases': [ '2.0.19' ],
14 silly audit   'normalize-path': [ '3.0.0' ],
14 silly audit   'npm-package-arg': [ '11.0.3' ],
14 silly audit   'nth-check': [ '2.1.1' ],
14 silly audit   nullthrows: [ '1.1.1' ],
14 silly audit   ob1: [ '0.82.4' ],
14 silly audit   'object-assign': [ '4.1.1' ],
14 silly audit   'on-finished': [ '2.3.0', '2.4.1' ],
14 silly audit   'on-headers': [ '1.0.2' ],
14 silly audit   once: [ '1.4.0' ],
14 silly audit   onetime: [ '2.0.1' ],
14 silly audit   ora: [ '3.4.0' ],
14 silly audit   'p-try': [ '2.2.0' ],
14 silly audit   'package-json-from-dist': [ '1.0.1' ],
14 silly audit   'parse-json': [ '4.0.0' ],
14 silly audit   'parse-png': [ '2.1.0' ],
14 silly audit   parseurl: [ '1.3.3' ],
14 silly audit   'path-exists': [ '4.0.0' ],
14 silly audit   'path-is-absolute': [ '1.0.1' ],
14 silly audit   'path-key': [ '3.1.1' ],
14 silly audit   'path-parse': [ '1.0.7' ],
14 silly audit   'path-scurry': [ '1.11.1' ],
14 silly audit   picocolors: [ '1.1.1' ],
14 silly audit   pirates: [ '4.0.7' ],
14 silly audit   plist: [ '3.1.0' ],
14 silly audit   pngjs: [ '3.4.0' ],
14 silly audit   postcss: [ '8.4.49' ],
14 silly audit   'postcss-value-parser': [ '4.2.0' ],
14 silly audit   'pretty-bytes': [ '5.6.0' ],
14 silly audit   'pretty-format': [ '29.7.0' ],
14 silly audit   'proc-log': [ '4.2.0' ],
14 silly audit   progress: [ '2.0.3' ],
14 silly audit   prompts: [ '2.4.2' ],
14 silly audit   punycode: [ '2.3.1' ],
14 silly audit   'qrcode-terminal': [ '0.11.0' ],
14 silly audit   'query-string': [ '7.1.3' ],
14 silly audit   queue: [ '6.0.2' ],
14 silly audit   'range-parser': [ '1.2.1' ],
14 silly audit   rc: [ '1.2.8' ],
14 silly audit   react: [ '19.0.0' ],
14 silly audit   'react-devtools-core': [ '6.1.3' ],
14 silly audit   'react-dom': [ '19.0.0' ],
14 silly audit   'react-fast-compare': [ '3.2.2' ],
14 silly audit   'react-freeze': [ '1.0.4' ],
14 silly audit   'react-leaflet': [ '4.2.1' ],
14 silly audit   'react-native': [ '0.79.1' ],
14 silly audit   'react-native-edge-to-edge': [ '1.6.0' ],
14 silly audit   'react-native-gesture-handler': [ '2.24.0' ],
14 silly audit   'react-native-is-edge-to-edge': [ '1.2.1', '1.1.7' ],
14 silly audit   'react-native-reanimated': [ '3.17.5' ],
14 silly audit   'react-native-safe-area-context': [ '5.3.0' ],
14 silly audit   'react-native-screens': [ '4.10.0' ],
14 silly audit   'react-native-svg': [ '15.11.2' ],
14 silly audit   'react-native-url-polyfill': [ '2.0.0' ],
14 silly audit   'react-native-web': [ '0.20.0' ],
14 silly audit   'react-native-webview': [ '13.13.5' ],
14 silly audit   'react-refresh': [ '0.14.2' ],
14 silly audit   'react-toastify': [ '10.0.6' ],
14 silly audit   regenerate: [ '1.4.2' ],
14 silly audit   'regenerate-unicode-properties': [ '10.2.0' ],
14 silly audit   'regenerator-runtime': [ '0.13.11' ],
14 silly audit   'regexpu-core': [ '6.2.0' ],
14 silly audit   regjsgen: [ '0.8.0' ],
14 silly audit   regjsparser: [ '0.12.0' ],
14 silly audit   'require-directory': [ '2.1.1' ],
14 silly audit   'require-from-string': [ '2.0.2' ],
14 silly audit   requireg: [ '0.2.2' ],
14 silly audit   resolve: [ '1.7.1', '1.22.10' ],
14 silly audit   'resolve-workspace-root': [ '2.0.0' ],
14 silly audit   'resolve.exports': [ '2.0.3' ],
14 silly audit   'restore-cursor': [ '2.0.0' ],
14 silly audit   'signal-exit': [ '3.0.7', '4.1.0' ],
14 silly audit   rimraf: [ '3.0.2' ],
14 silly audit   'safe-buffer': [ '5.2.1' ],
14 silly audit   sax: [ '1.4.1' ],
14 silly audit   scheduler: [ '0.25.0' ],
14 silly audit   'schema-utils': [ '4.3.2' ],
14 silly audit   send: [ '0.19.1', '0.19.0' ],
14 silly audit   'serialize-error': [ '2.1.0' ],
14 silly audit   'serve-static': [ '1.16.2' ],
14 silly audit   'server-only': [ '0.0.1' ],
14 silly audit   setimmediate: [ '1.0.5' ],
14 silly audit   setprototypeof: [ '1.2.0' ],
14 silly audit   'sf-symbols-typescript': [ '2.1.0' ],
14 silly audit   shallowequal: [ '1.1.0' ],
14 silly audit   'shebang-command': [ '2.0.0' ],
14 silly audit   'shebang-regex': [ '3.0.0' ],
14 silly audit   'shell-quote': [ '1.8.3' ],
14 silly audit   'simple-plist': [ '1.3.1' ],
14 silly audit   'simple-swizzle': [ '0.2.2' ],
14 silly audit   sisteransi: [ '1.0.5' ],
14 silly audit   slash: [ '3.0.0' ],
14 silly audit   slugify: [ '1.6.6' ],
14 silly audit   'source-map-js': [ '1.2.1' ],
14 silly audit   'source-map-support': [ '0.5.21' ],
14 silly audit   'split-on-first': [ '1.1.0' ],
14 silly audit   'sprintf-js': [ '1.0.3' ],
14 silly audit   'stack-utils': [ '2.0.6' ],
14 silly audit   stackframe: [ '1.3.4' ],
14 silly audit   'stacktrace-parser': [ '0.1.11' ],
14 silly audit   'stream-buffers': [ '2.2.0' ],
14 silly audit   'strict-uri-encode': [ '2.0.0' ],
14 silly audit   'strip-json-comments': [ '2.0.1' ],
14 silly audit   'structured-headers': [ '0.4.1' ],
14 silly audit   styleq: [ '0.1.3' ],
14 silly audit   sucrase: [ '3.35.0' ],
14 silly audit   'supports-hyperlinks': [ '2.3.0' ],
14 silly audit   'supports-preserve-symlinks-flag': [ '1.0.0' ],
14 silly audit   tar: [ '7.4.3' ],
14 silly audit   yallist: [ '5.0.0', '3.1.1' ],
14 silly audit   'temp-dir': [ '2.0.0' ],
14 silly audit   'terminal-link': [ '2.1.1' ],
14 silly audit   terser: [ '5.43.1' ],
14 silly audit   'test-exclude': [ '6.0.0' ],
14 silly audit   thenify: [ '3.3.1' ],
14 silly audit   'thenify-all': [ '1.6.0' ],
14 silly audit   throat: [ '5.0.0' ],
14 silly audit   tmpl: [ '1.0.5' ],
14 silly audit   'to-regex-range': [ '5.0.1' ],
14 silly audit   toidentifier: [ '1.0.1' ],
14 silly audit   tr46: [ '0.0.3' ],
14 silly audit   'ts-interface-checker': [ '0.1.13' ],
14 silly audit   'type-detect': [ '4.0.8' ],
14 silly audit   typescript: [ '5.8.3' ],
14 silly audit   'ua-parser-js': [ '1.0.40' ],
14 silly audit   undici: [ '6.21.3' ],
14 silly audit   'undici-types': [ '7.8.0' ],
14 silly audit   'unicode-canonical-property-names-ecmascript': [ '2.0.1' ],
14 silly audit   'unicode-match-property-ecmascript': [ '2.0.0' ],
14 silly audit   'unicode-match-property-value-ecmascript': [ '2.2.0' ],
14 silly audit   'unicode-property-aliases-ecmascript': [ '2.1.0' ],
14 silly audit   'unique-string': [ '2.0.0' ],
14 silly audit   unpipe: [ '1.0.0' ],
14 silly audit   'update-browserslist-db': [ '1.1.3' ],
14 silly audit   'use-latest-callback': [ '0.2.4' ],
14 silly audit   'use-sync-external-store': [ '1.5.0' ],
14 silly audit   'utils-merge': [ '1.0.1' ],
14 silly audit   uuid: [ '7.0.3' ],
14 silly audit   'validate-npm-package-name': [ '5.0.1' ],
14 silly audit   vary: [ '1.1.2' ],
14 silly audit   vlq: [ '1.0.1' ],
14 silly audit   walker: [ '1.0.8' ],
14 silly audit   'warn-once': [ '0.1.1' ],
14 silly audit   wcwidth: [ '1.0.1' ],
14 silly audit   'webidl-conversions': [ '3.0.1', '5.0.0' ],
14 silly audit   'whatwg-fetch': [ '3.6.20' ],
14 silly audit   'whatwg-url': [ '5.0.0' ],
14 silly audit   'whatwg-url-without-unicode': [ '8.0.0-3' ],
14 silly audit   which: [ '2.0.2' ],
14 silly audit   wonka: [ '6.3.5' ],
14 silly audit   wrappy: [ '1.0.2' ],
14 silly audit   'write-file-atomic': [ '4.0.2' ],
14 silly audit   xcode: [ '3.0.1' ],
14 silly audit   xml2js: [ '0.6.0' ],
14 silly audit   xmlbuilder: [ '11.0.1', '15.1.1' ],
14 silly audit   y18n: [ '5.0.8' ],
14 silly audit   yargs: [ '17.7.2' ],
14 silly audit   'yargs-parser': [ '21.1.1' ],
14 silly audit   'yocto-queue': [ '0.1.0' ]
14 silly audit }
15 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-win32-arm64-msvc
16 silly reify mark deleted [
16 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-win32-arm64-msvc'
16 silly reify ]
17 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-linux-x64-musl
18 silly reify mark deleted [
18 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-linux-x64-musl'
18 silly reify ]
19 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-linux-x64-gnu
20 silly reify mark deleted [
20 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-linux-x64-gnu'
20 silly reify ]
21 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-linux-arm64-musl
22 silly reify mark deleted [
22 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-linux-arm64-musl'
22 silly reify ]
23 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-linux-arm64-gnu
24 silly reify mark deleted [
24 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-linux-arm64-gnu'
24 silly reify ]
25 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-linux-arm-gnueabihf
26 silly reify mark deleted [
26 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-linux-arm-gnueabihf'
26 silly reify ]
27 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-freebsd-x64
28 silly reify mark deleted [
28 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-freebsd-x64'
28 silly reify ]
29 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-darwin-x64
30 silly reify mark deleted [ 'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-darwin-x64' ]
31 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\lightningcss-darwin-arm64
32 silly reify mark deleted [
32 silly reify   'D:\\.0000\\scarboroughfair\\node_modules\\lightningcss-darwin-arm64'
32 silly reify ]
33 verbose reify failed optional dependency D:\.0000\scarboroughfair\node_modules\fsevents
34 silly reify mark deleted [ 'D:\\.0000\\scarboroughfair\\node_modules\\fsevents' ]
35 silly tarball no local data for yocto-queue@https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz. Extracting by manifest.
36 silly tarball no local data for yargs-parser@https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz. Extracting by manifest.
37 silly tarball no local data for yargs@https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz. Extracting by manifest.
38 silly tarball no local data for yallist@https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz. Extracting by manifest.
39 silly tarball no local data for y18n@https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz. Extracting by manifest.
40 silly tarball no local data for xmlbuilder@https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-15.1.1.tgz. Extracting by manifest.
41 silly tarball no local data for xml2js@https://registry.npmjs.org/xml2js/-/xml2js-0.6.0.tgz. Extracting by manifest.
42 silly tarball no local data for xcode@https://registry.npmjs.org/xcode/-/xcode-3.0.1.tgz. Extracting by manifest.
43 silly tarball no local data for ws@https://registry.npmjs.org/ws/-/ws-8.18.3.tgz. Extracting by manifest.
44 silly tarball no local data for wrappy@https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz. Extracting by manifest.
45 silly tarball no local data for write-file-atomic@https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz. Extracting by manifest.
46 silly tarball no local data for wrap-ansi@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz. Extracting by manifest.
47 silly tarball no local data for wrap-ansi-cjs@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz. Extracting by manifest.
48 silly tarball no local data for wonka@https://registry.npmjs.org/wonka/-/wonka-6.3.5.tgz. Extracting by manifest.
49 silly tarball no local data for which@https://registry.npmjs.org/which/-/which-2.0.2.tgz. Extracting by manifest.
50 silly tarball no local data for whatwg-url-without-unicode@https://registry.npmjs.org/whatwg-url-without-unicode/-/whatwg-url-without-unicode-8.0.0-3.tgz. Extracting by manifest.
51 silly tarball no local data for whatwg-url@https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz. Extracting by manifest.
52 silly tarball no local data for whatwg-fetch@https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz. Extracting by manifest.
53 silly tarball no local data for webidl-conversions@https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz. Extracting by manifest.
54 silly tarball no local data for wcwidth@https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz. Extracting by manifest.
55 silly tarball no local data for warn-once@https://registry.npmjs.org/warn-once/-/warn-once-0.1.1.tgz. Extracting by manifest.
56 silly tarball no local data for walker@https://registry.npmjs.org/walker/-/walker-1.0.8.tgz. Extracting by manifest.
57 silly tarball no local data for vlq@https://registry.npmjs.org/vlq/-/vlq-1.0.1.tgz. Extracting by manifest.
58 silly tarball no local data for vary@https://registry.npmjs.org/vary/-/vary-1.1.2.tgz. Extracting by manifest.
59 silly tarball no local data for validate-npm-package-name@https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-5.0.1.tgz. Extracting by manifest.
60 silly tarball no local data for uuid@https://registry.npmjs.org/uuid/-/uuid-7.0.3.tgz. Extracting by manifest.
61 silly tarball no local data for utils-merge@https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz. Extracting by manifest.
62 silly tarball no local data for use-sync-external-store@https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz. Extracting by manifest.
63 silly tarball no local data for use-latest-callback@https://registry.npmjs.org/use-latest-callback/-/use-latest-callback-0.2.4.tgz. Extracting by manifest.
64 silly tarball no local data for update-browserslist-db@https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz. Extracting by manifest.
65 silly tarball no local data for unpipe@https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz. Extracting by manifest.
66 silly tarball no local data for unique-string@https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz. Extracting by manifest.
67 silly tarball no local data for unicode-property-aliases-ecmascript@https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz. Extracting by manifest.
68 silly tarball no local data for unicode-match-property-value-ecmascript@https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz. Extracting by manifest.
69 silly tarball no local data for unicode-match-property-ecmascript@https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz. Extracting by manifest.
70 silly tarball no local data for unicode-canonical-property-names-ecmascript@https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz. Extracting by manifest.
71 silly tarball no local data for undici-types@https://registry.npmjs.org/undici-types/-/undici-types-7.8.0.tgz. Extracting by manifest.
72 silly tarball no local data for undici@https://registry.npmjs.org/undici/-/undici-6.21.3.tgz. Extracting by manifest.
73 silly tarball no local data for ua-parser-js@https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-1.0.40.tgz. Extracting by manifest.
74 silly tarball no local data for typescript@https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz. Extracting by manifest.
75 silly tarball no local data for type-fest@https://registry.npmjs.org/type-fest/-/type-fest-0.7.1.tgz. Extracting by manifest.
76 silly tarball no local data for type-detect@https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz. Extracting by manifest.
77 silly tarball no local data for ts-interface-checker@https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz. Extracting by manifest.
78 silly tarball no local data for tr46@https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz. Extracting by manifest.
79 silly tarball no local data for toidentifier@https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz. Extracting by manifest.
80 silly tarball no local data for to-regex-range@https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz. Extracting by manifest.
81 silly tarball no local data for tmpl@https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz. Extracting by manifest.
82 silly tarball no local data for throat@https://registry.npmjs.org/throat/-/throat-5.0.0.tgz. Extracting by manifest.
83 silly tarball no local data for thenify-all@https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz. Extracting by manifest.
84 silly tarball no local data for thenify@https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz. Extracting by manifest.
85 silly tarball no local data for terser@https://registry.npmjs.org/terser/-/terser-5.43.1.tgz. Extracting by manifest.
86 silly tarball no local data for test-exclude@https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz. Extracting by manifest.
87 silly tarball no local data for terminal-link@https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz. Extracting by manifest.
88 silly tarball no local data for temp-dir@https://registry.npmjs.org/temp-dir/-/temp-dir-2.0.0.tgz. Extracting by manifest.
89 silly tarball no local data for tar@https://registry.npmjs.org/tar/-/tar-7.4.3.tgz. Extracting by manifest.
90 silly tarball no local data for supports-hyperlinks@https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz. Extracting by manifest.
91 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz. Extracting by manifest.
92 silly tarball no local data for styleq@https://registry.npmjs.org/styleq/-/styleq-0.1.3.tgz. Extracting by manifest.
93 silly tarball no local data for sucrase@https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz. Extracting by manifest.
94 silly tarball no local data for supports-preserve-symlinks-flag@https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz. Extracting by manifest.
95 silly tarball no local data for structured-headers@https://registry.npmjs.org/structured-headers/-/structured-headers-0.4.1.tgz. Extracting by manifest.
96 silly tarball no local data for strip-ansi-cjs@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
97 silly tarball no local data for strip-json-comments@https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz. Extracting by manifest.
98 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz. Extracting by manifest.
99 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz. Extracting by manifest.
100 silly tarball no local data for string-width-cjs@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
101 silly tarball no local data for strict-uri-encode@https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz. Extracting by manifest.
102 silly tarball no local data for statuses@https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz. Extracting by manifest.
103 silly tarball no local data for stream-buffers@https://registry.npmjs.org/stream-buffers/-/stream-buffers-2.2.0.tgz. Extracting by manifest.
104 silly tarball no local data for stacktrace-parser@https://registry.npmjs.org/stacktrace-parser/-/stacktrace-parser-0.1.11.tgz. Extracting by manifest.
105 silly tarball no local data for stackframe@https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz. Extracting by manifest.
106 silly tarball no local data for stack-utils@https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz. Extracting by manifest.
107 silly tarball no local data for sprintf-js@https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz. Extracting by manifest.
108 silly tarball no local data for split-on-first@https://registry.npmjs.org/split-on-first/-/split-on-first-1.1.0.tgz. Extracting by manifest.
109 silly tarball no local data for source-map-support@https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz. Extracting by manifest.
110 silly tarball no local data for source-map-js@https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz. Extracting by manifest.
111 silly tarball no local data for source-map@https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz. Extracting by manifest.
112 silly tarball no local data for slugify@https://registry.npmjs.org/slugify/-/slugify-1.6.6.tgz. Extracting by manifest.
113 silly tarball no local data for slash@https://registry.npmjs.org/slash/-/slash-3.0.0.tgz. Extracting by manifest.
114 silly tarball no local data for sisteransi@https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz. Extracting by manifest.
115 silly tarball no local data for simple-swizzle@https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz. Extracting by manifest.
116 silly tarball no local data for simple-plist@https://registry.npmjs.org/simple-plist/-/simple-plist-1.3.1.tgz. Extracting by manifest.
117 silly tarball no local data for signal-exit@https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz. Extracting by manifest.
118 silly tarball no local data for shell-quote@https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.3.tgz. Extracting by manifest.
119 silly tarball no local data for shebang-regex@https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz. Extracting by manifest.
120 silly tarball no local data for shebang-command@https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz. Extracting by manifest.
121 silly tarball no local data for sf-symbols-typescript@https://registry.npmjs.org/sf-symbols-typescript/-/sf-symbols-typescript-2.1.0.tgz. Extracting by manifest.
122 silly tarball no local data for shallowequal@https://registry.npmjs.org/shallowequal/-/shallowequal-1.1.0.tgz. Extracting by manifest.
123 silly tarball no local data for setprototypeof@https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz. Extracting by manifest.
124 silly tarball no local data for server-only@https://registry.npmjs.org/server-only/-/server-only-0.0.1.tgz. Extracting by manifest.
125 silly tarball no local data for setimmediate@https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz. Extracting by manifest.
126 silly tarball no local data for serve-static@https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz. Extracting by manifest.
127 silly tarball no local data for serialize-error@https://registry.npmjs.org/serialize-error/-/serialize-error-2.1.0.tgz. Extracting by manifest.
128 silly tarball no local data for send@https://registry.npmjs.org/send/-/send-0.19.1.tgz. Extracting by manifest.
129 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-6.3.1.tgz. Extracting by manifest.
130 silly tarball no local data for schema-utils@https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz. Extracting by manifest.
131 silly tarball no local data for scheduler@https://registry.npmjs.org/scheduler/-/scheduler-0.25.0.tgz. Extracting by manifest.
132 silly tarball no local data for sax@https://registry.npmjs.org/sax/-/sax-1.4.1.tgz. Extracting by manifest.
133 silly tarball no local data for safe-buffer@https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz. Extracting by manifest.
134 silly tarball no local data for rimraf@https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz. Extracting by manifest.
135 silly tarball no local data for restore-cursor@https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz. Extracting by manifest.
136 silly tarball no local data for resolve.exports@https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz. Extracting by manifest.
137 silly tarball no local data for resolve-workspace-root@https://registry.npmjs.org/resolve-workspace-root/-/resolve-workspace-root-2.0.0.tgz. Extracting by manifest.
138 silly tarball no local data for resolve-from@https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz. Extracting by manifest.
139 silly tarball no local data for resolve@https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz. Extracting by manifest.
140 silly tarball no local data for requireg@https://registry.npmjs.org/requireg/-/requireg-0.2.2.tgz. Extracting by manifest.
141 silly tarball no local data for require-from-string@https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz. Extracting by manifest.
142 silly tarball no local data for require-directory@https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz. Extracting by manifest.
143 silly tarball no local data for regjsparser@https://registry.npmjs.org/regjsparser/-/regjsparser-0.12.0.tgz. Extracting by manifest.
144 silly tarball no local data for regjsgen@https://registry.npmjs.org/regjsgen/-/regjsgen-0.8.0.tgz. Extracting by manifest.
145 silly tarball no local data for regexpu-core@https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.2.0.tgz. Extracting by manifest.
146 silly tarball no local data for regenerator-runtime@https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz. Extracting by manifest.
147 silly tarball no local data for regenerate-unicode-properties@https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz. Extracting by manifest.
148 silly tarball no local data for regenerate@https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz. Extracting by manifest.
149 silly tarball no local data for react-toastify@https://registry.npmjs.org/react-toastify/-/react-toastify-10.0.6.tgz. Extracting by manifest.
150 silly tarball no local data for react-refresh@https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.2.tgz. Extracting by manifest.
151 silly tarball no local data for react-native-webview@https://registry.npmjs.org/react-native-webview/-/react-native-webview-13.13.5.tgz. Extracting by manifest.
152 silly tarball no local data for react-native-web@https://registry.npmjs.org/react-native-web/-/react-native-web-0.20.0.tgz. Extracting by manifest.
153 silly tarball no local data for react-native-svg@https://registry.npmjs.org/react-native-svg/-/react-native-svg-15.11.2.tgz. Extracting by manifest.
154 silly tarball no local data for react-native-url-polyfill@https://registry.npmjs.org/react-native-url-polyfill/-/react-native-url-polyfill-2.0.0.tgz. Extracting by manifest.
155 silly tarball no local data for react-native-screens@https://registry.npmjs.org/react-native-screens/-/react-native-screens-4.10.0.tgz. Extracting by manifest.
156 silly tarball no local data for react-native-safe-area-context@https://registry.npmjs.org/react-native-safe-area-context/-/react-native-safe-area-context-5.3.0.tgz. Extracting by manifest.
157 silly tarball no local data for react-native-is-edge-to-edge@https://registry.npmjs.org/react-native-is-edge-to-edge/-/react-native-is-edge-to-edge-1.2.1.tgz. Extracting by manifest.
158 silly tarball no local data for react-native-reanimated@https://registry.npmjs.org/react-native-reanimated/-/react-native-reanimated-3.17.5.tgz. Extracting by manifest.
159 silly tarball no local data for react-native-gesture-handler@https://registry.npmjs.org/react-native-gesture-handler/-/react-native-gesture-handler-2.24.0.tgz. Extracting by manifest.
160 silly tarball no local data for react-native-edge-to-edge@https://registry.npmjs.org/react-native-edge-to-edge/-/react-native-edge-to-edge-1.6.0.tgz. Extracting by manifest.
161 silly tarball no local data for react-native@https://registry.npmjs.org/react-native/-/react-native-0.79.1.tgz. Extracting by manifest.
162 silly tarball no local data for react-leaflet@https://registry.npmjs.org/react-leaflet/-/react-leaflet-4.2.1.tgz. Extracting by manifest.
163 silly tarball no local data for react-is@https://registry.npmjs.org/react-is/-/react-is-19.1.0.tgz. Extracting by manifest.
164 silly tarball no local data for react-freeze@https://registry.npmjs.org/react-freeze/-/react-freeze-1.0.4.tgz. Extracting by manifest.
165 silly tarball no local data for react-fast-compare@https://registry.npmjs.org/react-fast-compare/-/react-fast-compare-3.2.2.tgz. Extracting by manifest.
166 silly tarball no local data for react-dom@https://registry.npmjs.org/react-dom/-/react-dom-19.0.0.tgz. Extracting by manifest.
167 silly tarball no local data for react-devtools-core@https://registry.npmjs.org/react-devtools-core/-/react-devtools-core-6.1.3.tgz. Extracting by manifest.
168 silly tarball no local data for react@https://registry.npmjs.org/react/-/react-19.0.0.tgz. Extracting by manifest.
169 silly tarball no local data for rc@https://registry.npmjs.org/rc/-/rc-1.2.8.tgz. Extracting by manifest.
170 silly tarball no local data for range-parser@https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz. Extracting by manifest.
171 silly tarball no local data for queue@https://registry.npmjs.org/queue/-/queue-6.0.2.tgz. Extracting by manifest.
172 silly tarball no local data for query-string@https://registry.npmjs.org/query-string/-/query-string-7.1.3.tgz. Extracting by manifest.
173 silly tarball no local data for qrcode-terminal@https://registry.npmjs.org/qrcode-terminal/-/qrcode-terminal-0.11.0.tgz. Extracting by manifest.
174 silly tarball no local data for punycode@https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz. Extracting by manifest.
175 silly tarball no local data for promise@https://registry.npmjs.org/promise/-/promise-8.3.0.tgz. Extracting by manifest.
176 silly tarball no local data for prompts@https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz. Extracting by manifest.
177 silly tarball no local data for proc-log@https://registry.npmjs.org/proc-log/-/proc-log-4.2.0.tgz. Extracting by manifest.
178 silly tarball no local data for progress@https://registry.npmjs.org/progress/-/progress-2.0.3.tgz. Extracting by manifest.
179 silly tarball no local data for pretty-bytes@https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz. Extracting by manifest.
180 silly tarball no local data for postcss-value-parser@https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz. Extracting by manifest.
181 silly tarball no local data for postcss@https://registry.npmjs.org/postcss/-/postcss-8.4.49.tgz. Extracting by manifest.
182 silly tarball no local data for pngjs@https://registry.npmjs.org/pngjs/-/pngjs-3.4.0.tgz. Extracting by manifest.
183 silly tarball no local data for pretty-format@https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz. Extracting by manifest.
184 silly tarball no local data for plist@https://registry.npmjs.org/plist/-/plist-3.1.0.tgz. Extracting by manifest.
185 silly tarball no local data for pirates@https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz. Extracting by manifest.
186 silly tarball no local data for picocolors@https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz. Extracting by manifest.
187 silly tarball no local data for picomatch@https://registry.npmjs.org/picomatch/-/picomatch-3.0.1.tgz. Extracting by manifest.
188 silly tarball no local data for path-scurry@https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz. Extracting by manifest.
189 silly tarball no local data for path-parse@https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz. Extracting by manifest.
190 silly tarball no local data for path-is-absolute@https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz. Extracting by manifest.
191 silly tarball no local data for path-key@https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz. Extracting by manifest.
192 silly tarball no local data for path-exists@https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz. Extracting by manifest.
193 silly tarball no local data for parseurl@https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz. Extracting by manifest.
194 silly tarball no local data for parse-png@https://registry.npmjs.org/parse-png/-/parse-png-2.1.0.tgz. Extracting by manifest.
195 silly tarball no local data for parse-json@https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz. Extracting by manifest.
196 silly tarball no local data for package-json-from-dist@https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz. Extracting by manifest.
197 silly tarball no local data for p-try@https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz. Extracting by manifest.
198 silly tarball no local data for p-locate@https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz. Extracting by manifest.
199 silly tarball no local data for p-limit@https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz. Extracting by manifest.
200 silly tarball no local data for ora@https://registry.npmjs.org/ora/-/ora-3.4.0.tgz. Extracting by manifest.
201 silly tarball no local data for open@https://registry.npmjs.org/open/-/open-7.4.2.tgz. Extracting by manifest.
202 silly tarball no local data for onetime@https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz. Extracting by manifest.
203 silly tarball no local data for once@https://registry.npmjs.org/once/-/once-1.4.0.tgz. Extracting by manifest.
204 silly tarball no local data for on-finished@https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz. Extracting by manifest.
205 silly tarball no local data for object-assign@https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz. Extracting by manifest.
206 silly tarball no local data for ob1@https://registry.npmjs.org/ob1/-/ob1-0.82.4.tgz. Extracting by manifest.
207 silly tarball no local data for nth-check@https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz. Extracting by manifest.
208 silly tarball no local data for npm-package-arg@https://registry.npmjs.org/npm-package-arg/-/npm-package-arg-11.0.3.tgz. Extracting by manifest.
209 silly tarball no local data for normalize-path@https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz. Extracting by manifest.
210 silly tarball no local data for on-headers@https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz. Extracting by manifest.
211 silly tarball no local data for nullthrows@https://registry.npmjs.org/nullthrows/-/nullthrows-1.1.1.tgz. Extracting by manifest.
212 silly tarball no local data for node-releases@https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz. Extracting by manifest.
213 silly tarball no local data for node-forge@https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz. Extracting by manifest.
214 silly tarball no local data for node-fetch@https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz. Extracting by manifest.
215 silly tarball no local data for node-int64@https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz. Extracting by manifest.
216 silly tarball no local data for nested-error-stacks@https://registry.npmjs.org/nested-error-stacks/-/nested-error-stacks-2.0.1.tgz. Extracting by manifest.
217 silly tarball no local data for negotiator@https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz. Extracting by manifest.
218 silly tarball no local data for nanoid@https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz. Extracting by manifest.
219 silly tarball no local data for mz@https://registry.npmjs.org/mz/-/mz-2.7.0.tgz. Extracting by manifest.
220 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.1.3.tgz. Extracting by manifest.
221 silly tarball no local data for mkdirp@https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz. Extracting by manifest.
222 silly tarball no local data for minizlib@https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz. Extracting by manifest.
223 silly tarball no local data for minipass@https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz. Extracting by manifest.
224 silly tarball no local data for minimist@https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz. Extracting by manifest.
225 silly tarball no local data for mimic-fn@https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz. Extracting by manifest.
226 silly tarball no local data for minimatch@https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz. Extracting by manifest.
227 silly tarball no local data for mime-db@https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz. Extracting by manifest.
228 silly tarball no local data for mime-types@https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz. Extracting by manifest.
229 silly tarball no local data for micromatch@https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz. Extracting by manifest.
230 silly tarball no local data for mime@https://registry.npmjs.org/mime/-/mime-1.6.0.tgz. Extracting by manifest.
231 silly tarball no local data for metro-transform-worker@https://registry.npmjs.org/metro-transform-worker/-/metro-transform-worker-0.82.4.tgz. Extracting by manifest.
232 silly tarball no local data for metro-transform-plugins@https://registry.npmjs.org/metro-transform-plugins/-/metro-transform-plugins-0.82.4.tgz. Extracting by manifest.
233 silly tarball no local data for metro-symbolicate@https://registry.npmjs.org/metro-symbolicate/-/metro-symbolicate-0.82.4.tgz. Extracting by manifest.
234 silly tarball no local data for metro-runtime@https://registry.npmjs.org/metro-runtime/-/metro-runtime-0.82.4.tgz. Extracting by manifest.
235 silly tarball no local data for metro-source-map@https://registry.npmjs.org/metro-source-map/-/metro-source-map-0.82.4.tgz. Extracting by manifest.
236 silly tarball no local data for metro-resolver@https://registry.npmjs.org/metro-resolver/-/metro-resolver-0.82.4.tgz. Extracting by manifest.
237 silly tarball no local data for metro-minify-terser@https://registry.npmjs.org/metro-minify-terser/-/metro-minify-terser-0.82.4.tgz. Extracting by manifest.
238 silly tarball no local data for metro-file-map@https://registry.npmjs.org/metro-file-map/-/metro-file-map-0.82.4.tgz. Extracting by manifest.
239 silly tarball no local data for metro-core@https://registry.npmjs.org/metro-core/-/metro-core-0.82.4.tgz. Extracting by manifest.
240 silly tarball no local data for metro-config@https://registry.npmjs.org/metro-config/-/metro-config-0.82.4.tgz. Extracting by manifest.
241 silly tarball no local data for metro-babel-transformer@https://registry.npmjs.org/metro-babel-transformer/-/metro-babel-transformer-0.82.4.tgz. Extracting by manifest.
242 silly tarball no local data for metro-cache@https://registry.npmjs.org/metro-cache/-/metro-cache-0.82.4.tgz. Extracting by manifest.
243 silly tarball no local data for metro-cache-key@https://registry.npmjs.org/metro-cache-key/-/metro-cache-key-0.82.4.tgz. Extracting by manifest.
244 silly tarball no local data for metro@https://registry.npmjs.org/metro/-/metro-0.82.4.tgz. Extracting by manifest.
245 silly tarball no local data for merge-stream@https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz. Extracting by manifest.
246 silly tarball no local data for memoize-one@https://registry.npmjs.org/memoize-one/-/memoize-one-5.2.1.tgz. Extracting by manifest.
247 silly tarball no local data for mdn-data@https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz. Extracting by manifest.
248 silly tarball no local data for marky@https://registry.npmjs.org/marky/-/marky-1.3.0.tgz. Extracting by manifest.
249 silly tarball no local data for merge-options@https://registry.npmjs.org/merge-options/-/merge-options-3.0.4.tgz. Extracting by manifest.
250 silly tarball no local data for lucide-react-native@https://registry.npmjs.org/lucide-react-native/-/lucide-react-native-0.475.0.tgz. Extracting by manifest.
251 silly tarball no local data for makeerror@https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz. Extracting by manifest.
252 silly tarball no local data for lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz. Extracting by manifest.
253 silly tarball no local data for loose-envify@https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz. Extracting by manifest.
254 silly tarball no local data for lodash.throttle@https://registry.npmjs.org/lodash.throttle/-/lodash.throttle-4.1.1.tgz. Extracting by manifest.
255 silly tarball no local data for lodash.debounce@https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz. Extracting by manifest.
256 silly tarball no local data for log-symbols@https://registry.npmjs.org/log-symbols/-/log-symbols-2.2.0.tgz. Extracting by manifest.
257 silly tarball no local data for locate-path@https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz. Extracting by manifest.
258 silly tarball no local data for lines-and-columns@https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz. Extracting by manifest.
259 silly tarball no local data for lightningcss@https://registry.npmjs.org/lightningcss/-/lightningcss-1.27.0.tgz. Extracting by manifest.
260 silly tarball no local data for lighthouse-logger@https://registry.npmjs.org/lighthouse-logger/-/lighthouse-logger-1.4.2.tgz. Extracting by manifest.
261 silly tarball no local data for leven@https://registry.npmjs.org/leven/-/leven-3.1.0.tgz. Extracting by manifest.
262 silly tarball no local data for leaflet@https://registry.npmjs.org/leaflet/-/leaflet-1.9.4.tgz. Extracting by manifest.
263 silly tarball no local data for lightningcss-win32-x64-msvc@https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.27.0.tgz. Extracting by manifest.
264 silly tarball no local data for lan-network@https://registry.npmjs.org/lan-network/-/lan-network-0.1.7.tgz. Extracting by manifest.
265 silly tarball no local data for kleur@https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz. Extracting by manifest.
266 silly tarball no local data for json-schema-traverse@https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz. Extracting by manifest.
267 silly tarball no local data for json5@https://registry.npmjs.org/json5/-/json5-2.2.3.tgz. Extracting by manifest.
268 silly tarball no local data for jsesc@https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz. Extracting by manifest.
269 silly tarball no local data for jsc-safe-url@https://registry.npmjs.org/jsc-safe-url/-/jsc-safe-url-0.2.4.tgz. Extracting by manifest.
270 silly tarball no local data for js-yaml@https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz. Extracting by manifest.
271 silly tarball no local data for json-parse-better-errors@https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz. Extracting by manifest.
272 silly tarball no local data for js-tokens@https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz. Extracting by manifest.
273 silly tarball no local data for jimp-compact@https://registry.npmjs.org/jimp-compact/-/jimp-compact-0.16.1.tgz. Extracting by manifest.
274 silly tarball no local data for jest-validate@https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz. Extracting by manifest.
275 silly tarball no local data for jest-worker@https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz. Extracting by manifest.
276 silly tarball no local data for jest-util@https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz. Extracting by manifest.
277 silly tarball no local data for jest-regex-util@https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz. Extracting by manifest.
278 silly tarball no local data for jest-mock@https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz. Extracting by manifest.
279 silly tarball no local data for jest-haste-map@https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz. Extracting by manifest.
280 silly tarball no local data for jest-message-util@https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz. Extracting by manifest.
281 silly tarball no local data for jest-environment-node@https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz. Extracting by manifest.
282 silly tarball no local data for jest-get-type@https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz. Extracting by manifest.
283 silly tarball no local data for jackspeak@https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz. Extracting by manifest.
284 silly tarball no local data for istanbul-lib-instrument@https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz. Extracting by manifest.
285 silly tarball no local data for isows@https://registry.npmjs.org/isows/-/isows-1.0.7.tgz. Extracting by manifest.
286 silly tarball no local data for isexe@https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz. Extracting by manifest.
287 silly tarball no local data for istanbul-lib-coverage@https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz. Extracting by manifest.
288 silly tarball no local data for is-wsl@https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz. Extracting by manifest.
289 silly tarball no local data for is-plain-obj@https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-2.1.0.tgz. Extracting by manifest.
290 silly tarball no local data for is-fullwidth-code-point@https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz. Extracting by manifest.
291 silly tarball no local data for is-number@https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz. Extracting by manifest.
292 silly tarball no local data for is-docker@https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz. Extracting by manifest.
293 silly tarball no local data for is-directory@https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz. Extracting by manifest.
294 silly tarball no local data for is-arrayish@https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz. Extracting by manifest.
295 silly tarball no local data for is-core-module@https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz. Extracting by manifest.
296 silly tarball no local data for invariant@https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz. Extracting by manifest.
297 silly tarball no local data for inline-style-prefixer@https://registry.npmjs.org/inline-style-prefixer/-/inline-style-prefixer-7.0.1.tgz. Extracting by manifest.
298 silly tarball no local data for inherits@https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz. Extracting by manifest.
299 silly tarball no local data for ini@https://registry.npmjs.org/ini/-/ini-1.3.8.tgz. Extracting by manifest.
300 silly tarball no local data for inflight@https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz. Extracting by manifest.
301 silly tarball no local data for imurmurhash@https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz. Extracting by manifest.
302 silly tarball no local data for import-fresh@https://registry.npmjs.org/import-fresh/-/import-fresh-2.0.0.tgz. Extracting by manifest.
303 silly tarball no local data for image-size@https://registry.npmjs.org/image-size/-/image-size-1.2.1.tgz. Extracting by manifest.
304 silly tarball no local data for ieee754@https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz. Extracting by manifest.
305 silly tarball no local data for ignore@https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz. Extracting by manifest.
306 silly tarball no local data for hyphenate-style-name@https://registry.npmjs.org/hyphenate-style-name/-/hyphenate-style-name-1.1.0.tgz. Extracting by manifest.
307 silly tarball no local data for https-proxy-agent@https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz. Extracting by manifest.
308 silly tarball no local data for http-errors@https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz. Extracting by manifest.
309 silly tarball no local data for hosted-git-info@https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-7.0.2.tgz. Extracting by manifest.
310 silly tarball no local data for hoist-non-react-statics@https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz. Extracting by manifest.
311 silly tarball no local data for hermes-parser@https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.25.1.tgz. Extracting by manifest.
312 silly tarball no local data for hermes-estree@https://registry.npmjs.org/hermes-estree/-/hermes-estree-0.25.1.tgz. Extracting by manifest.
313 silly tarball no local data for hasown@https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz. Extracting by manifest.
314 silly tarball no local data for has-flag@https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz. Extracting by manifest.
315 silly tarball no local data for graceful-fs@https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz. Extracting by manifest.
316 silly tarball no local data for glob@https://registry.npmjs.org/glob/-/glob-10.4.5.tgz. Extracting by manifest.
317 silly tarball no local data for getenv@https://registry.npmjs.org/getenv/-/getenv-2.0.0.tgz. Extracting by manifest.
318 silly tarball no local data for get-package-type@https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz. Extracting by manifest.
319 silly tarball no local data for get-caller-file@https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz. Extracting by manifest.
320 silly tarball no local data for function-bind@https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz. Extracting by manifest.
321 silly tarball no local data for gensync@https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz. Extracting by manifest.
322 silly tarball no local data for fs.realpath@https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz. Extracting by manifest.
323 silly tarball no local data for fresh@https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz. Extracting by manifest.
324 silly tarball no local data for freeport-async@https://registry.npmjs.org/freeport-async/-/freeport-async-2.0.0.tgz. Extracting by manifest.
325 silly tarball no local data for foreground-child@https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz. Extracting by manifest.
326 silly tarball no local data for fontfaceobserver@https://registry.npmjs.org/fontfaceobserver/-/fontfaceobserver-2.3.0.tgz. Extracting by manifest.
327 silly tarball no local data for flow-enums-runtime@https://registry.npmjs.org/flow-enums-runtime/-/flow-enums-runtime-0.0.6.tgz. Extracting by manifest.
328 silly tarball no local data for find-up@https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz. Extracting by manifest.
329 silly tarball no local data for finalhandler@https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz. Extracting by manifest.
330 silly tarball no local data for filter-obj@https://registry.npmjs.org/filter-obj/-/filter-obj-1.1.0.tgz. Extracting by manifest.
331 silly tarball no local data for fill-range@https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz. Extracting by manifest.
332 silly tarball no local data for fbjs-css-vars@https://registry.npmjs.org/fbjs-css-vars/-/fbjs-css-vars-1.0.2.tgz. Extracting by manifest.
333 silly tarball no local data for fbjs@https://registry.npmjs.org/fbjs/-/fbjs-3.0.5.tgz. Extracting by manifest.
334 silly tarball no local data for fb-watchman@https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz. Extracting by manifest.
335 silly tarball no local data for fast-uri@https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.6.tgz. Extracting by manifest.
336 silly tarball no local data for fast-json-stable-stringify@https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz. Extracting by manifest.
337 silly tarball no local data for fast-deep-equal@https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz. Extracting by manifest.
338 silly tarball no local data for exponential-backoff@https://registry.npmjs.org/exponential-backoff/-/exponential-backoff-3.1.2.tgz. Extracting by manifest.
339 silly tarball no local data for expo-web-browser@https://registry.npmjs.org/expo-web-browser/-/expo-web-browser-14.1.6.tgz. Extracting by manifest.
340 silly tarball no local data for expo-system-ui@https://registry.npmjs.org/expo-system-ui/-/expo-system-ui-5.0.10.tgz. Extracting by manifest.
341 silly tarball no local data for expo-symbols@https://registry.npmjs.org/expo-symbols/-/expo-symbols-0.4.5.tgz. Extracting by manifest.
342 silly tarball no local data for expo-status-bar@https://registry.npmjs.org/expo-status-bar/-/expo-status-bar-2.2.3.tgz. Extracting by manifest.
343 silly tarball no local data for expo-router@https://registry.npmjs.org/expo-router/-/expo-router-5.0.7.tgz. Extracting by manifest.
344 silly tarball no local data for expo-splash-screen@https://registry.npmjs.org/expo-splash-screen/-/expo-splash-screen-0.30.9.tgz. Extracting by manifest.
345 silly tarball no local data for expo-modules-autolinking@https://registry.npmjs.org/expo-modules-autolinking/-/expo-modules-autolinking-2.1.13.tgz. Extracting by manifest.
346 silly tarball no local data for expo-modules-core@https://registry.npmjs.org/expo-modules-core/-/expo-modules-core-2.4.2.tgz. Extracting by manifest.
347 silly tarball no local data for expo-linking@https://registry.npmjs.org/expo-linking/-/expo-linking-7.1.6.tgz. Extracting by manifest.
348 silly tarball no local data for expo-linear-gradient@https://registry.npmjs.org/expo-linear-gradient/-/expo-linear-gradient-14.1.5.tgz. Extracting by manifest.
349 silly tarball no local data for expo-keep-awake@https://registry.npmjs.org/expo-keep-awake/-/expo-keep-awake-14.1.4.tgz. Extracting by manifest.
350 silly tarball no local data for expo-haptics@https://registry.npmjs.org/expo-haptics/-/expo-haptics-14.1.4.tgz. Extracting by manifest.
351 silly tarball no local data for expo-font@https://registry.npmjs.org/expo-font/-/expo-font-13.2.2.tgz. Extracting by manifest.
352 silly tarball no local data for expo-file-system@https://registry.npmjs.org/expo-file-system/-/expo-file-system-18.1.11.tgz. Extracting by manifest.
353 silly tarball no local data for expo-constants@https://registry.npmjs.org/expo-constants/-/expo-constants-17.1.6.tgz. Extracting by manifest.
354 silly tarball no local data for expo-blur@https://registry.npmjs.org/expo-blur/-/expo-blur-14.1.5.tgz. Extracting by manifest.
355 silly tarball no local data for expo-asset@https://registry.npmjs.org/expo-asset/-/expo-asset-11.1.6.tgz. Extracting by manifest.
356 silly tarball no local data for expo-camera@https://registry.npmjs.org/expo-camera/-/expo-camera-16.1.10.tgz. Extracting by manifest.
357 silly tarball no local data for expo@https://registry.npmjs.org/expo/-/expo-53.0.16.tgz. Extracting by manifest.
358 silly tarball no local data for exec-async@https://registry.npmjs.org/exec-async/-/exec-async-2.2.0.tgz. Extracting by manifest.
359 silly tarball no local data for event-target-shim@https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz. Extracting by manifest.
360 silly tarball no local data for esprima@https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz. Extracting by manifest.
361 silly tarball no local data for escape-string-regexp@https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz. Extracting by manifest.
362 silly tarball no local data for escalade@https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz. Extracting by manifest.
363 silly tarball no local data for escape-html@https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz. Extracting by manifest.
364 silly tarball no local data for etag@https://registry.npmjs.org/etag/-/etag-1.8.1.tgz. Extracting by manifest.
365 silly tarball no local data for error-stack-parser@https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz. Extracting by manifest.
366 silly tarball no local data for error-ex@https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz. Extracting by manifest.
367 silly tarball no local data for env-editor@https://registry.npmjs.org/env-editor/-/env-editor-0.4.2.tgz. Extracting by manifest.
368 silly tarball no local data for entities@https://registry.npmjs.org/entities/-/entities-4.5.0.tgz. Extracting by manifest.
369 silly tarball no local data for encodeurl@https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz. Extracting by manifest.
370 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz. Extracting by manifest.
371 silly tarball no local data for electron-to-chromium@https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.179.tgz. Extracting by manifest.
372 silly tarball no local data for ee-first@https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz. Extracting by manifest.
373 silly tarball no local data for eastasianwidth@https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz. Extracting by manifest.
374 silly tarball no local data for domutils@https://registry.npmjs.org/domutils/-/domutils-3.2.2.tgz. Extracting by manifest.
375 silly tarball no local data for dotenv-expand@https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-11.0.7.tgz. Extracting by manifest.
376 silly tarball no local data for dotenv@https://registry.npmjs.org/dotenv/-/dotenv-16.4.7.tgz. Extracting by manifest.
377 silly tarball no local data for domhandler@https://registry.npmjs.org/domhandler/-/domhandler-5.0.3.tgz. Extracting by manifest.
378 silly tarball no local data for domelementtype@https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz. Extracting by manifest.
379 silly tarball no local data for dom-serializer@https://registry.npmjs.org/dom-serializer/-/dom-serializer-2.0.0.tgz. Extracting by manifest.
380 silly tarball no local data for detect-libc@https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz. Extracting by manifest.
381 silly tarball no local data for destroy@https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz. Extracting by manifest.
382 silly tarball no local data for depd@https://registry.npmjs.org/depd/-/depd-2.0.0.tgz. Extracting by manifest.
383 silly tarball no local data for define-lazy-prop@https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz. Extracting by manifest.
384 silly tarball no local data for deepmerge@https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz. Extracting by manifest.
385 silly tarball no local data for deep-extend@https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz. Extracting by manifest.
386 silly tarball no local data for decode-uri-component@https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz. Extracting by manifest.
387 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-4.4.1.tgz. Extracting by manifest.
388 silly tarball no local data for csstype@https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz. Extracting by manifest.
389 silly tarball no local data for css-what@https://registry.npmjs.org/css-what/-/css-what-6.2.2.tgz. Extracting by manifest.
390 silly tarball no local data for css-tree@https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz. Extracting by manifest.
391 silly tarball no local data for css-select@https://registry.npmjs.org/css-select/-/css-select-5.2.2.tgz. Extracting by manifest.
392 silly tarball no local data for css-in-js-utils@https://registry.npmjs.org/css-in-js-utils/-/css-in-js-utils-3.1.0.tgz. Extracting by manifest.
393 silly tarball no local data for crypto-random-string@https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-2.0.0.tgz. Extracting by manifest.
394 silly tarball no local data for cross-spawn@https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz. Extracting by manifest.
395 silly tarball no local data for cross-fetch@https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.2.0.tgz. Extracting by manifest.
396 silly tarball no local data for cosmiconfig@https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz. Extracting by manifest.
397 silly tarball no local data for core-js-compat@https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.43.0.tgz. Extracting by manifest.
398 silly tarball no local data for convert-source-map@https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz. Extracting by manifest.
399 silly tarball no local data for connect@https://registry.npmjs.org/connect/-/connect-3.7.0.tgz. Extracting by manifest.
400 silly tarball no local data for concat-map@https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz. Extracting by manifest.
401 silly tarball no local data for compression@https://registry.npmjs.org/compression/-/compression-1.8.0.tgz. Extracting by manifest.
402 silly tarball no local data for compressible@https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz. Extracting by manifest.
403 silly tarball no local data for commander@https://registry.npmjs.org/commander/-/commander-7.2.0.tgz. Extracting by manifest.
404 silly tarball no local data for color-string@https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz. Extracting by manifest.
405 silly tarball no local data for color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz. Extracting by manifest.
406 silly tarball no local data for color-convert@https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz. Extracting by manifest.
407 silly tarball no local data for clone@https://registry.npmjs.org/clone/-/clone-1.0.4.tgz. Extracting by manifest.
408 silly tarball no local data for color@https://registry.npmjs.org/color/-/color-4.2.3.tgz. Extracting by manifest.
409 silly tarball no local data for cliui@https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz. Extracting by manifest.
410 silly tarball no local data for clsx@https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz. Extracting by manifest.
411 silly tarball no local data for client-only@https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz. Extracting by manifest.
412 silly tarball no local data for cli-spinners@https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz. Extracting by manifest.
413 silly tarball no local data for cli-cursor@https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz. Extracting by manifest.
414 silly tarball no local data for ci-info@https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz. Extracting by manifest.
415 silly tarball no local data for chrome-launcher@https://registry.npmjs.org/chrome-launcher/-/chrome-launcher-0.15.2.tgz. Extracting by manifest.
416 silly tarball no local data for chromium-edge-launcher@https://registry.npmjs.org/chromium-edge-launcher/-/chromium-edge-launcher-0.2.0.tgz. Extracting by manifest.
417 silly tarball no local data for defaults@https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz. Extracting by manifest.
418 silly tarball no local data for chownr@https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz. Extracting by manifest.
419 silly tarball no local data for chalk@https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz. Extracting by manifest.
420 silly tarball no local data for caniuse-lite@https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001726.tgz. Extracting by manifest.
421 silly tarball no local data for callsites@https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz. Extracting by manifest.
422 silly tarball no local data for camelcase@https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz. Extracting by manifest.
423 silly tarball no local data for caller-path@https://registry.npmjs.org/caller-path/-/caller-path-2.0.0.tgz. Extracting by manifest.
424 silly tarball no local data for caller-callsite@https://registry.npmjs.org/caller-callsite/-/caller-callsite-2.0.0.tgz. Extracting by manifest.
425 silly tarball no local data for bytes@https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz. Extracting by manifest.
426 silly tarball no local data for buffer-from@https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz. Extracting by manifest.
427 silly tarball no local data for buffer@https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz. Extracting by manifest.
428 silly tarball no local data for bser@https://registry.npmjs.org/bser/-/bser-2.1.1.tgz. Extracting by manifest.
429 silly tarball no local data for braces@https://registry.npmjs.org/braces/-/braces-3.0.3.tgz. Extracting by manifest.
430 silly tarball no local data for browserslist@https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz. Extracting by manifest.
431 silly tarball no local data for brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz. Extracting by manifest.
432 silly tarball no local data for bplist-creator@https://registry.npmjs.org/bplist-creator/-/bplist-creator-0.1.0.tgz. Extracting by manifest.
433 silly tarball no local data for bplist-parser@https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.3.2.tgz. Extracting by manifest.
434 silly tarball no local data for boolbase@https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz. Extracting by manifest.
435 silly tarball no local data for balanced-match@https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz. Extracting by manifest.
436 silly tarball no local data for base64-js@https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz. Extracting by manifest.
437 silly tarball no local data for babel-preset-jest@https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz. Extracting by manifest.
438 silly tarball no local data for better-opn@https://registry.npmjs.org/better-opn/-/better-opn-3.0.2.tgz. Extracting by manifest.
439 silly tarball no local data for babel-preset-expo@https://registry.npmjs.org/babel-preset-expo/-/babel-preset-expo-13.2.2.tgz. Extracting by manifest.
440 silly tarball no local data for babel-preset-current-node-syntax@https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz. Extracting by manifest.
441 silly tarball no local data for babel-plugin-transform-flow-enums@https://registry.npmjs.org/babel-plugin-transform-flow-enums/-/babel-plugin-transform-flow-enums-0.0.2.tgz. Extracting by manifest.
442 silly tarball no local data for babel-plugin-syntax-hermes-parser@https://registry.npmjs.org/babel-plugin-syntax-hermes-parser/-/babel-plugin-syntax-hermes-parser-0.25.1.tgz. Extracting by manifest.
443 silly tarball no local data for big-integer@https://registry.npmjs.org/big-integer/-/big-integer-1.6.52.tgz. Extracting by manifest.
444 silly tarball no local data for babel-plugin-polyfill-regenerator@https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.5.tgz. Extracting by manifest.
445 silly tarball no local data for babel-plugin-react-native-web@https://registry.npmjs.org/babel-plugin-react-native-web/-/babel-plugin-react-native-web-0.19.13.tgz. Extracting by manifest.
446 silly tarball no local data for babel-plugin-polyfill-corejs3@https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.13.0.tgz. Extracting by manifest.
447 silly tarball no local data for babel-plugin-polyfill-corejs2@https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.14.tgz. Extracting by manifest.
448 silly tarball no local data for babel-plugin-jest-hoist@https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz. Extracting by manifest.
449 silly tarball no local data for babel-jest@https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz. Extracting by manifest.
450 silly tarball no local data for babel-plugin-istanbul@https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz. Extracting by manifest.
451 silly tarball no local data for async-limiter@https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.1.tgz. Extracting by manifest.
452 silly tarball no local data for asap@https://registry.npmjs.org/asap/-/asap-2.0.6.tgz. Extracting by manifest.
453 silly tarball no local data for argparse@https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz. Extracting by manifest.
454 silly tarball no local data for arg@https://registry.npmjs.org/arg/-/arg-5.0.2.tgz. Extracting by manifest.
455 silly tarball no local data for any-promise@https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz. Extracting by manifest.
456 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz. Extracting by manifest.
457 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
458 silly tarball no local data for ansi-escapes@https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz. Extracting by manifest.
459 silly tarball no local data for anser@https://registry.npmjs.org/anser/-/anser-1.4.10.tgz. Extracting by manifest.
460 silly tarball no local data for ajv-keywords@https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz. Extracting by manifest.
461 silly tarball no local data for ajv@https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz. Extracting by manifest.
462 silly tarball no local data for ajv-formats@https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz. Extracting by manifest.
463 silly tarball no local data for agent-base@https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz. Extracting by manifest.
464 silly tarball no local data for acorn@https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz. Extracting by manifest.
465 silly tarball no local data for @xmldom/xmldom@https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.8.10.tgz. Extracting by manifest.
466 silly tarball no local data for abort-controller@https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz. Extracting by manifest.
467 silly tarball no local data for accepts@https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz. Extracting by manifest.
468 silly tarball no local data for @urql/exchange-retry@https://registry.npmjs.org/@urql/exchange-retry/-/exchange-retry-1.3.2.tgz. Extracting by manifest.
469 silly tarball no local data for @urql/core@https://registry.npmjs.org/@urql/core/-/core-5.2.0.tgz. Extracting by manifest.
470 silly tarball no local data for @types/yargs-parser@https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz. Extracting by manifest.
471 silly tarball no local data for @types/yargs@https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz. Extracting by manifest.
472 silly tarball no local data for @types/stack-utils@https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz. Extracting by manifest.
473 silly tarball no local data for @types/ws@https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz. Extracting by manifest.
474 silly tarball no local data for @types/react@https://registry.npmjs.org/@types/react/-/react-19.0.14.tgz. Extracting by manifest.
475 silly tarball no local data for @types/node@https://registry.npmjs.org/@types/node/-/node-24.0.10.tgz. Extracting by manifest.
476 silly tarball no local data for @types/phoenix@https://registry.npmjs.org/@types/phoenix/-/phoenix-1.6.6.tgz. Extracting by manifest.
477 silly tarball no local data for @types/json-schema@https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz. Extracting by manifest.
478 silly tarball no local data for @types/leaflet@https://registry.npmjs.org/@types/leaflet/-/leaflet-1.9.19.tgz. Extracting by manifest.
479 silly tarball no local data for @types/istanbul-reports@https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz. Extracting by manifest.
480 silly tarball no local data for @types/istanbul-lib-report@https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz. Extracting by manifest.
481 silly tarball no local data for @types/istanbul-lib-coverage@https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz. Extracting by manifest.
482 silly tarball no local data for @types/hammerjs@https://registry.npmjs.org/@types/hammerjs/-/hammerjs-2.0.46.tgz. Extracting by manifest.
483 silly tarball no local data for @types/graceful-fs@https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz. Extracting by manifest.
484 silly tarball no local data for @types/geojson@https://registry.npmjs.org/@types/geojson/-/geojson-7946.0.16.tgz. Extracting by manifest.
485 silly tarball no local data for @types/babel__template@https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz. Extracting by manifest.
486 silly tarball no local data for @types/babel__traverse@https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz. Extracting by manifest.
487 silly tarball no local data for @types/babel__generator@https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz. Extracting by manifest.
488 silly tarball no local data for @types/babel__core@https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz. Extracting by manifest.
489 silly tarball no local data for @supabase/storage-js@https://registry.npmjs.org/@supabase/storage-js/-/storage-js-2.7.1.tgz. Extracting by manifest.
490 silly tarball no local data for @supabase/realtime-js@https://registry.npmjs.org/@supabase/realtime-js/-/realtime-js-2.11.15.tgz. Extracting by manifest.
491 silly tarball no local data for @supabase/supabase-js@https://registry.npmjs.org/@supabase/supabase-js/-/supabase-js-2.50.3.tgz. Extracting by manifest.
492 silly tarball no local data for @supabase/postgrest-js@https://registry.npmjs.org/@supabase/postgrest-js/-/postgrest-js-1.19.4.tgz. Extracting by manifest.
493 silly tarball no local data for @supabase/node-fetch@https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.15.tgz. Extracting by manifest.
494 silly tarball no local data for @supabase/functions-js@https://registry.npmjs.org/@supabase/functions-js/-/functions-js-2.4.5.tgz. Extracting by manifest.
495 silly tarball no local data for @supabase/auth-js@https://registry.npmjs.org/@supabase/auth-js/-/auth-js-2.70.0.tgz. Extracting by manifest.
496 silly tarball no local data for @sinonjs/fake-timers@https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz. Extracting by manifest.
497 silly tarball no local data for @sinonjs/commons@https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz. Extracting by manifest.
498 silly tarball no local data for @sinclair/typebox@https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz. Extracting by manifest.
499 silly tarball no local data for @react-navigation/routers@https://registry.npmjs.org/@react-navigation/routers/-/routers-7.4.1.tgz. Extracting by manifest.
500 silly tarball no local data for @react-navigation/native-stack@https://registry.npmjs.org/@react-navigation/native-stack/-/native-stack-7.3.21.tgz. Extracting by manifest.
501 silly tarball no local data for anymatch@https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz. Extracting by manifest.
502 silly tarball no local data for @react-navigation/elements@https://registry.npmjs.org/@react-navigation/elements/-/elements-2.5.2.tgz. Extracting by manifest.
503 silly tarball no local data for @react-navigation/core@https://registry.npmjs.org/@react-navigation/core/-/core-7.12.1.tgz. Extracting by manifest.
504 silly tarball no local data for @react-navigation/native@https://registry.npmjs.org/@react-navigation/native/-/native-7.1.14.tgz. Extracting by manifest.
505 silly tarball no local data for @react-navigation/bottom-tabs@https://registry.npmjs.org/@react-navigation/bottom-tabs/-/bottom-tabs-7.4.2.tgz. Extracting by manifest.
506 silly tarball no local data for @react-native/virtualized-lists@https://registry.npmjs.org/@react-native/virtualized-lists/-/virtualized-lists-0.79.1.tgz. Extracting by manifest.
507 silly tarball no local data for @react-native/normalize-colors@https://registry.npmjs.org/@react-native/normalize-colors/-/normalize-colors-0.79.5.tgz. Extracting by manifest.
508 silly tarball no local data for @react-native/dev-middleware@https://registry.npmjs.org/@react-native/dev-middleware/-/dev-middleware-0.79.5.tgz. Extracting by manifest.
509 silly tarball no local data for @react-native/debugger-frontend@https://registry.npmjs.org/@react-native/debugger-frontend/-/debugger-frontend-0.79.5.tgz. Extracting by manifest.
510 silly tarball no local data for @react-native/js-polyfills@https://registry.npmjs.org/@react-native/js-polyfills/-/js-polyfills-0.79.1.tgz. Extracting by manifest.
511 silly tarball no local data for @react-native/gradle-plugin@https://registry.npmjs.org/@react-native/gradle-plugin/-/gradle-plugin-0.79.1.tgz. Extracting by manifest.
512 silly tarball no local data for @react-native/community-cli-plugin@https://registry.npmjs.org/@react-native/community-cli-plugin/-/community-cli-plugin-0.79.1.tgz. Extracting by manifest.
513 silly tarball no local data for @react-native/codegen@https://registry.npmjs.org/@react-native/codegen/-/codegen-0.79.5.tgz. Extracting by manifest.
514 silly tarball no local data for @react-native/babel-preset@https://registry.npmjs.org/@react-native/babel-preset/-/babel-preset-0.79.5.tgz. Extracting by manifest.
515 silly tarball no local data for @react-native/assets-registry@https://registry.npmjs.org/@react-native/assets-registry/-/assets-registry-0.79.1.tgz. Extracting by manifest.
516 silly tarball no local data for @react-leaflet/core@https://registry.npmjs.org/@react-leaflet/core/-/core-2.1.0.tgz. Extracting by manifest.
517 silly tarball no local data for @radix-ui/react-slot@https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.0.tgz. Extracting by manifest.
518 silly tarball no local data for @react-native-async-storage/async-storage@https://registry.npmjs.org/@react-native-async-storage/async-storage/-/async-storage-2.2.0.tgz. Extracting by manifest.
519 silly tarball no local data for @radix-ui/react-compose-refs@https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz. Extracting by manifest.
520 silly tarball no local data for @jridgewell/sourcemap-codec@https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz. Extracting by manifest.
521 silly tarball no local data for @jridgewell/trace-mapping@https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz. Extracting by manifest.
522 silly tarball no local data for @pkgjs/parseargs@https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz. Extracting by manifest.
523 silly tarball no local data for @jridgewell/source-map@https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.10.tgz. Extracting by manifest.
524 silly tarball no local data for @jridgewell/resolve-uri@https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz. Extracting by manifest.
525 silly tarball no local data for @jridgewell/gen-mapping@https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz. Extracting by manifest.
526 silly tarball no local data for @jest/types@https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz. Extracting by manifest.
527 silly tarball no local data for @jest/transform@https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz. Extracting by manifest.
528 silly tarball no local data for @jest/schemas@https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz. Extracting by manifest.
529 silly tarball no local data for @lucide/lab@https://registry.npmjs.org/@lucide/lab/-/lab-0.1.2.tgz. Extracting by manifest.
530 silly tarball no local data for @jest/fake-timers@https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz. Extracting by manifest.
531 silly tarball no local data for @jest/create-cache-key-function@https://registry.npmjs.org/@jest/create-cache-key-function/-/create-cache-key-function-29.7.0.tgz. Extracting by manifest.
532 silly tarball no local data for @jest/environment@https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz. Extracting by manifest.
533 silly tarball no local data for @istanbuljs/schema@https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz. Extracting by manifest.
534 silly tarball no local data for @isaacs/ttlcache@https://registry.npmjs.org/@isaacs/ttlcache/-/ttlcache-1.4.1.tgz. Extracting by manifest.
535 silly tarball no local data for @istanbuljs/load-nyc-config@https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz. Extracting by manifest.
536 silly tarball no local data for @isaacs/fs-minipass@https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz. Extracting by manifest.
537 silly tarball no local data for @react-native/babel-plugin-codegen@https://registry.npmjs.org/@react-native/babel-plugin-codegen/-/babel-plugin-codegen-0.79.5.tgz. Extracting by manifest.
538 silly tarball no local data for @isaacs/cliui@https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz. Extracting by manifest.
539 silly tarball no local data for @expo/xcpretty@https://registry.npmjs.org/@expo/xcpretty/-/xcpretty-4.3.2.tgz. Extracting by manifest.
540 silly tarball no local data for @expo/ws-tunnel@https://registry.npmjs.org/@expo/ws-tunnel/-/ws-tunnel-1.0.6.tgz. Extracting by manifest.
541 silly tarball no local data for @expo/spawn-async@https://registry.npmjs.org/@expo/spawn-async/-/spawn-async-1.7.2.tgz. Extracting by manifest.
542 silly tarball no local data for @expo/sudo-prompt@https://registry.npmjs.org/@expo/sudo-prompt/-/sudo-prompt-9.3.2.tgz. Extracting by manifest.
543 silly tarball no local data for @expo/server@https://registry.npmjs.org/@expo/server/-/server-0.6.3.tgz. Extracting by manifest.
544 silly tarball no local data for @expo/sdk-runtime-versions@https://registry.npmjs.org/@expo/sdk-runtime-versions/-/sdk-runtime-versions-1.0.0.tgz. Extracting by manifest.
545 silly tarball no local data for @expo/plist@https://registry.npmjs.org/@expo/plist/-/plist-0.3.4.tgz. Extracting by manifest.
546 silly tarball no local data for @expo/prebuild-config@https://registry.npmjs.org/@expo/prebuild-config/-/prebuild-config-9.0.9.tgz. Extracting by manifest.
547 silly tarball no local data for @expo/metro-runtime@https://registry.npmjs.org/@expo/metro-runtime/-/metro-runtime-5.0.4.tgz. Extracting by manifest.
548 silly tarball no local data for @expo/package-manager@https://registry.npmjs.org/@expo/package-manager/-/package-manager-1.8.5.tgz. Extracting by manifest.
549 silly tarball no local data for @expo/metro-config@https://registry.npmjs.org/@expo/metro-config/-/metro-config-0.20.16.tgz. Extracting by manifest.
550 silly tarball no local data for @expo/osascript@https://registry.npmjs.org/@expo/osascript/-/osascript-2.2.4.tgz. Extracting by manifest.
551 silly tarball no local data for @expo/vector-icons@https://registry.npmjs.org/@expo/vector-icons/-/vector-icons-14.1.0.tgz. Extracting by manifest.
552 silly tarball no local data for @expo/json-file@https://registry.npmjs.org/@expo/json-file/-/json-file-9.1.4.tgz. Extracting by manifest.
553 silly tarball no local data for @expo/image-utils@https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.7.5.tgz. Extracting by manifest.
554 silly tarball no local data for @expo/devcert@https://registry.npmjs.org/@expo/devcert/-/devcert-1.2.0.tgz. Extracting by manifest.
555 silly tarball no local data for @expo/fingerprint@https://registry.npmjs.org/@expo/fingerprint/-/fingerprint-0.13.3.tgz. Extracting by manifest.
556 silly tarball no local data for @expo/config-types@https://registry.npmjs.org/@expo/config-types/-/config-types-53.0.4.tgz. Extracting by manifest.
557 silly tarball no local data for @expo/config-plugins@https://registry.npmjs.org/@expo/config-plugins/-/config-plugins-10.1.0.tgz. Extracting by manifest.
558 silly tarball no local data for @expo/code-signing-certificates@https://registry.npmjs.org/@expo/code-signing-certificates/-/code-signing-certificates-0.0.5.tgz. Extracting by manifest.
559 silly tarball no local data for @expo/cli@https://registry.npmjs.org/@expo/cli/-/cli-0.24.17.tgz. Extracting by manifest.
560 silly tarball no local data for @expo/config@https://registry.npmjs.org/@expo/config/-/config-11.0.11.tgz. Extracting by manifest.
561 silly tarball no local data for @expo-google-fonts/ma-shan-zheng@https://registry.npmjs.org/@expo-google-fonts/ma-shan-zheng/-/ma-shan-zheng-0.2.3.tgz. Extracting by manifest.
562 silly tarball no local data for @egjs/hammerjs@https://registry.npmjs.org/@egjs/hammerjs/-/hammerjs-2.0.17.tgz. Extracting by manifest.
563 silly tarball no local data for @babel/types@https://registry.npmjs.org/@babel/types/-/types-7.28.0.tgz. Extracting by manifest.
564 silly tarball no local data for @babel/traverse--for-generate-function-map@https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz. Extracting by manifest.
565 silly tarball no local data for @babel/traverse@https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz. Extracting by manifest.
566 silly tarball no local data for @babel/runtime@https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz. Extracting by manifest.
567 silly tarball no local data for @babel/preset-typescript@https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.27.1.tgz. Extracting by manifest.
568 silly tarball no local data for @babel/template@https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz. Extracting by manifest.
569 silly tarball no local data for @babel/plugin-transform-unicode-regex@https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.27.1.tgz. Extracting by manifest.
570 silly tarball no local data for @babel/preset-react@https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.27.1.tgz. Extracting by manifest.
571 silly tarball no local data for @babel/plugin-transform-typescript@https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.28.0.tgz. Extracting by manifest.
572 silly tarball no local data for @babel/plugin-transform-template-literals@https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.27.1.tgz. Extracting by manifest.
573 silly tarball no local data for @babel/plugin-transform-sticky-regex@https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.27.1.tgz. Extracting by manifest.
574 silly tarball no local data for @expo/env@https://registry.npmjs.org/@expo/env/-/env-1.0.6.tgz. Extracting by manifest.
575 silly tarball no local data for @babel/plugin-transform-spread@https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.27.1.tgz. Extracting by manifest.
576 silly tarball no local data for @babel/plugin-transform-shorthand-properties@https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz. Extracting by manifest.
577 silly tarball no local data for @babel/plugin-transform-runtime@https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.28.0.tgz. Extracting by manifest.
578 silly tarball no local data for @babel/plugin-transform-react-pure-annotations@https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.27.1.tgz. Extracting by manifest.
579 silly tarball no local data for @babel/plugin-transform-react-jsx-source@https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz. Extracting by manifest.
580 silly tarball no local data for @babel/plugin-transform-react-jsx-self@https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz. Extracting by manifest.
581 silly tarball no local data for @babel/plugin-transform-react-jsx@https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz. Extracting by manifest.
582 silly tarball no local data for @babel/plugin-transform-react-jsx-development@https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.27.1.tgz. Extracting by manifest.
583 silly tarball no local data for @babel/plugin-transform-react-display-name@https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.28.0.tgz. Extracting by manifest.
584 silly tarball no local data for @babel/plugin-transform-private-property-in-object@https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.27.1.tgz. Extracting by manifest.
585 silly tarball no local data for @babel/plugin-transform-private-methods@https://registry.npmjs.org/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.27.1.tgz. Extracting by manifest.
586 silly tarball no local data for @babel/plugin-transform-parameters@https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.7.tgz. Extracting by manifest.
587 silly tarball no local data for @babel/plugin-transform-optional-chaining@https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.27.1.tgz. Extracting by manifest.
588 silly tarball no local data for @babel/plugin-transform-object-rest-spread@https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.28.0.tgz. Extracting by manifest.
589 silly tarball no local data for @babel/plugin-transform-optional-catch-binding@https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.27.1.tgz. Extracting by manifest.
590 silly tarball no local data for @babel/plugin-transform-regenerator@https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.28.0.tgz. Extracting by manifest.
591 silly tarball no local data for @babel/plugin-transform-numeric-separator@https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.27.1.tgz. Extracting by manifest.
592 silly tarball no local data for @babel/plugin-transform-named-capturing-groups-regex@https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.27.1.tgz. Extracting by manifest.
593 silly tarball no local data for @babel/plugin-transform-nullish-coalescing-operator@https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.27.1.tgz. Extracting by manifest.
594 silly tarball no local data for @babel/plugin-transform-modules-commonjs@https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.27.1.tgz. Extracting by manifest.
595 silly tarball no local data for @babel/plugin-transform-logical-assignment-operators@https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.27.1.tgz. Extracting by manifest.
596 silly tarball no local data for @babel/plugin-transform-literals@https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.27.1.tgz. Extracting by manifest.
597 silly tarball no local data for @babel/plugin-transform-function-name@https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.27.1.tgz. Extracting by manifest.
598 silly tarball no local data for @babel/plugin-transform-flow-strip-types@https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.27.1.tgz. Extracting by manifest.
599 silly tarball no local data for @babel/plugin-transform-for-of@https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.27.1.tgz. Extracting by manifest.
600 silly tarball no local data for @babel/plugin-transform-export-namespace-from@https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.27.1.tgz. Extracting by manifest.
601 silly tarball no local data for @babel/plugin-transform-destructuring@https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.28.0.tgz. Extracting by manifest.
602 silly tarball no local data for @babel/plugin-transform-computed-properties@https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.27.1.tgz. Extracting by manifest.
603 silly tarball no local data for @babel/plugin-transform-class-properties@https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.27.1.tgz. Extracting by manifest.
604 silly tarball no local data for @babel/plugin-transform-classes@https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.28.0.tgz. Extracting by manifest.
605 silly tarball no local data for @babel/plugin-transform-block-scoping@https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.28.0.tgz. Extracting by manifest.
606 silly tarball no local data for @babel/plugin-transform-async-to-generator@https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.27.1.tgz. Extracting by manifest.
607 silly tarball no local data for @babel/plugin-transform-async-generator-functions@https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.28.0.tgz. Extracting by manifest.
608 silly tarball no local data for @babel/plugin-syntax-typescript@https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz. Extracting by manifest.
609 silly tarball no local data for @babel/plugin-syntax-top-level-await@https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz. Extracting by manifest.
610 silly tarball no local data for @babel/plugin-transform-arrow-functions@https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz. Extracting by manifest.
611 silly tarball no local data for @babel/plugin-syntax-optional-chaining@https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz. Extracting by manifest.
612 silly tarball no local data for @babel/plugin-syntax-private-property-in-object@https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz. Extracting by manifest.
613 silly tarball no local data for @babel/plugin-syntax-optional-catch-binding@https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz. Extracting by manifest.
614 silly tarball no local data for @babel/plugin-syntax-object-rest-spread@https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz. Extracting by manifest.
615 silly tarball no local data for @babel/plugin-syntax-numeric-separator@https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz. Extracting by manifest.
616 silly tarball no local data for @babel/plugin-syntax-nullish-coalescing-operator@https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz. Extracting by manifest.
617 silly tarball no local data for @babel/plugin-syntax-logical-assignment-operators@https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz. Extracting by manifest.
618 silly tarball no local data for @babel/plugin-syntax-json-strings@https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz. Extracting by manifest.
619 silly tarball no local data for @babel/plugin-syntax-jsx@https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz. Extracting by manifest.
620 silly tarball no local data for @babel/plugin-syntax-import-attributes@https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz. Extracting by manifest.
621 silly tarball no local data for @babel/plugin-syntax-import-meta@https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz. Extracting by manifest.
622 silly tarball no local data for @babel/plugin-syntax-flow@https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.27.1.tgz. Extracting by manifest.
623 silly tarball no local data for @babel/plugin-syntax-decorators@https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.27.1.tgz. Extracting by manifest.
624 silly tarball no local data for @babel/plugin-syntax-dynamic-import@https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz. Extracting by manifest.
625 silly tarball no local data for @babel/plugin-syntax-export-default-from@https://registry.npmjs.org/@babel/plugin-syntax-export-default-from/-/plugin-syntax-export-default-from-7.27.1.tgz. Extracting by manifest.
626 silly tarball no local data for @babel/plugin-syntax-class-static-block@https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz. Extracting by manifest.
627 silly tarball no local data for @babel/plugin-syntax-class-properties@https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz. Extracting by manifest.
628 silly tarball no local data for @babel/plugin-syntax-async-generators@https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz. Extracting by manifest.
629 silly tarball no local data for @babel/plugin-syntax-bigint@https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz. Extracting by manifest.
630 silly tarball no local data for @babel/plugin-proposal-export-default-from@https://registry.npmjs.org/@babel/plugin-proposal-export-default-from/-/plugin-proposal-export-default-from-7.27.1.tgz. Extracting by manifest.
631 silly tarball no local data for @babel/plugin-proposal-decorators@https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.28.0.tgz. Extracting by manifest.
632 silly tarball no local data for @babel/parser@https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz. Extracting by manifest.
633 silly tarball no local data for @babel/helpers@https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz. Extracting by manifest.
634 silly tarball no local data for @babel/helper-validator-option@https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz. Extracting by manifest.
635 silly tarball no local data for @babel/helper-validator-identifier@https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz. Extracting by manifest.
636 silly tarball no local data for @babel/highlight@https://registry.npmjs.org/@babel/highlight/-/highlight-7.25.9.tgz. Extracting by manifest.
637 silly tarball no local data for @babel/helper-string-parser@https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz. Extracting by manifest.
638 silly tarball no local data for @babel/helper-skip-transparent-expression-wrappers@https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz. Extracting by manifest.
639 silly tarball no local data for @babel/helper-wrap-function@https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.27.1.tgz. Extracting by manifest.
640 silly tarball no local data for @babel/helper-replace-supers@https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz. Extracting by manifest.
641 silly tarball no local data for @babel/helper-plugin-utils@https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz. Extracting by manifest.
642 silly tarball no local data for @babel/helper-optimise-call-expression@https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz. Extracting by manifest.
643 silly tarball no local data for @babel/helper-module-transforms@https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz. Extracting by manifest.
644 silly tarball no local data for @babel/helper-remap-async-to-generator@https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz. Extracting by manifest.
645 silly tarball no local data for @babel/helper-member-expression-to-functions@https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz. Extracting by manifest.
646 silly tarball no local data for @babel/helper-module-imports@https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz. Extracting by manifest.
647 silly tarball no local data for @babel/helper-globals@https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz. Extracting by manifest.
648 silly tarball no local data for @babel/helper-create-regexp-features-plugin@https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz. Extracting by manifest.
649 silly tarball no local data for @babel/helper-create-class-features-plugin@https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz. Extracting by manifest.
650 silly tarball no local data for @babel/helper-define-polyfill-provider@https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.5.tgz. Extracting by manifest.
651 silly tarball no local data for @babel/helper-compilation-targets@https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz. Extracting by manifest.
652 silly tarball no local data for @babel/helper-annotate-as-pure@https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz. Extracting by manifest.
653 silly tarball no local data for @babel/generator@https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz. Extracting by manifest.
654 silly tarball no local data for @babel/compat-data@https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz. Extracting by manifest.
655 silly tarball no local data for @babel/core@https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz. Extracting by manifest.
656 silly tarball no local data for @babel/code-frame@https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz. Extracting by manifest.
657 silly tarball no local data for @ampproject/remapping@https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz. Extracting by manifest.
658 silly tarball no local data for xmlbuilder@https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz. Extracting by manifest.
659 silly tarball no local data for @0no-co/graphql.web@https://registry.npmjs.org/@0no-co/graphql.web/-/graphql.web-1.1.2.tgz. Extracting by manifest.
660 silly tarball no local data for signal-exit@https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz. Extracting by manifest.
661 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
662 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
663 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
664 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
665 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
666 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
667 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
668 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
669 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
670 silly tarball no local data for commander@https://registry.npmjs.org/commander/-/commander-2.20.3.tgz. Extracting by manifest.
671 silly tarball no local data for webidl-conversions@https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz. Extracting by manifest.
672 silly tarball no local data for minimatch@https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz. Extracting by manifest.
673 silly tarball no local data for glob@https://registry.npmjs.org/glob/-/glob-7.2.3.tgz. Extracting by manifest.
674 silly tarball no local data for yallist@https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz. Extracting by manifest.
675 silly tarball no local data for brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz. Extracting by manifest.
676 silly tarball no local data for commander@https://registry.npmjs.org/commander/-/commander-4.1.1.tgz. Extracting by manifest.
677 silly tarball no local data for mkdirp@https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz. Extracting by manifest.
678 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz. Extracting by manifest.
679 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
680 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
681 silly tarball no local data for escape-string-regexp@https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz. Extracting by manifest.
682 silly tarball no local data for source-map@https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz. Extracting by manifest.
683 silly tarball no local data for is-arrayish@https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz. Extracting by manifest.
684 silly tarball no local data for statuses@https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz. Extracting by manifest.
685 silly tarball no local data for send@https://registry.npmjs.org/send/-/send-0.19.0.tgz. Extracting by manifest.
686 silly tarball no local data for bplist-parser@https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.3.1.tgz. Extracting by manifest.
687 silly tarball no local data for on-finished@https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz. Extracting by manifest.
688 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-2.6.9.tgz. Extracting by manifest.
689 silly tarball no local data for encodeurl@https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz. Extracting by manifest.
690 silly tarball no local data for encodeurl@https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz. Extracting by manifest.
691 silly tarball no local data for statuses@https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz. Extracting by manifest.
692 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.0.0.tgz. Extracting by manifest.
693 silly tarball no local data for on-finished@https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz. Extracting by manifest.
694 silly tarball no local data for encodeurl@https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz. Extracting by manifest.
695 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.0.0.tgz. Extracting by manifest.
696 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-2.6.9.tgz. Extracting by manifest.
697 silly tarball no local data for glob@https://registry.npmjs.org/glob/-/glob-7.2.3.tgz. Extracting by manifest.
698 silly tarball no local data for signal-exit@https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz. Extracting by manifest.
699 silly tarball no local data for minimatch@https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz. Extracting by manifest.
700 silly tarball no local data for brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz. Extracting by manifest.
701 silly tarball no local data for resolve@https://registry.npmjs.org/resolve/-/resolve-1.7.1.tgz. Extracting by manifest.
702 silly tarball no local data for jsesc@https://registry.npmjs.org/jsesc/-/jsesc-3.0.2.tgz. Extracting by manifest.
703 silly tarball no local data for ws@https://registry.npmjs.org/ws/-/ws-6.2.3.tgz. Extracting by manifest.
704 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
705 silly tarball no local data for @react-native/normalize-colors@https://registry.npmjs.org/@react-native/normalize-colors/-/normalize-colors-0.74.89.tgz. Extracting by manifest.
706 silly tarball no local data for react-native-is-edge-to-edge@https://registry.npmjs.org/react-native-is-edge-to-edge/-/react-native-is-edge-to-edge-1.1.7.tgz. Extracting by manifest.
707 silly tarball no local data for memoize-one@https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz. Extracting by manifest.
708 silly tarball no local data for @react-native/normalize-colors@https://registry.npmjs.org/@react-native/normalize-colors/-/normalize-colors-0.79.1.tgz. Extracting by manifest.
709 silly tarball no local data for commander@https://registry.npmjs.org/commander/-/commander-12.1.0.tgz. Extracting by manifest.
710 silly tarball no local data for glob@https://registry.npmjs.org/glob/-/glob-7.2.3.tgz. Extracting by manifest.
711 silly tarball no local data for brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz. Extracting by manifest.
712 silly tarball no local data for @react-native/codegen@https://registry.npmjs.org/@react-native/codegen/-/codegen-0.79.1.tgz. Extracting by manifest.
713 silly tarball no local data for ws@https://registry.npmjs.org/ws/-/ws-7.5.10.tgz. Extracting by manifest.
714 silly tarball no local data for react-is@https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz. Extracting by manifest.
715 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz. Extracting by manifest.
716 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz. Extracting by manifest.
717 silly tarball no local data for lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz. Extracting by manifest.
718 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz. Extracting by manifest.
719 silly tarball no local data for has-flag@https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz. Extracting by manifest.
720 silly tarball no local data for escape-string-regexp@https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz. Extracting by manifest.
721 silly tarball no local data for color-convert@https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz. Extracting by manifest.
722 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz. Extracting by manifest.
723 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.1.tgz. Extracting by manifest.
724 silly tarball no local data for color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz. Extracting by manifest.
725 silly tarball no local data for chalk@https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz. Extracting by manifest.
726 silly tarball no local data for mime-db@https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz. Extracting by manifest.
727 silly tarball no local data for picomatch@https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz. Extracting by manifest.
728 silly tarball no local data for hermes-parser@https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.28.1.tgz. Extracting by manifest.
729 silly tarball no local data for hermes-estree@https://registry.npmjs.org/hermes-estree/-/hermes-estree-0.28.1.tgz. Extracting by manifest.
730 silly tarball no local data for ws@https://registry.npmjs.org/ws/-/ws-7.5.10.tgz. Extracting by manifest.
731 silly tarball no local data for hermes-estree@https://registry.npmjs.org/hermes-estree/-/hermes-estree-0.28.1.tgz. Extracting by manifest.
732 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
733 silly tarball no local data for ci-info@https://registry.npmjs.org/ci-info/-/ci-info-2.0.0.tgz. Extracting by manifest.
734 silly tarball no local data for hermes-parser@https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.28.1.tgz. Extracting by manifest.
735 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz. Extracting by manifest.
736 silly tarball no local data for escape-string-regexp@https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz. Extracting by manifest.
737 silly tarball no local data for color-convert@https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz. Extracting by manifest.
738 silly tarball no local data for color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz. Extracting by manifest.
739 silly tarball no local data for chalk@https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz. Extracting by manifest.
740 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz. Extracting by manifest.
741 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.0.0.tgz. Extracting by manifest.
742 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz. Extracting by manifest.
743 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-2.6.9.tgz. Extracting by manifest.
744 silly tarball no local data for camelcase@https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz. Extracting by manifest.
745 silly tarball no local data for has-flag@https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz. Extracting by manifest.
746 silly tarball no local data for resolve-from@https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz. Extracting by manifest.
747 silly tarball no local data for picomatch@https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz. Extracting by manifest.
748 silly tarball no local data for statuses@https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz. Extracting by manifest.
749 silly tarball no local data for lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz. Extracting by manifest.
750 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.0.0.tgz. Extracting by manifest.
751 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-2.6.9.tgz. Extracting by manifest.
752 silly tarball no local data for promise@https://registry.npmjs.org/promise/-/promise-7.3.1.tgz. Extracting by manifest.
753 silly tarball no local data for react-is@https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz. Extracting by manifest.
754 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.6.3.tgz. Extracting by manifest.
755 silly tarball no local data for expo-font@https://registry.npmjs.org/expo-font/-/expo-font-13.3.2.tgz. Extracting by manifest.
756 silly tarball no local data for source-map@https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz. Extracting by manifest.
757 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.0.0.tgz. Extracting by manifest.
758 silly tarball no local data for argparse@https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz. Extracting by manifest.
759 silly tarball no local data for js-yaml@https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz. Extracting by manifest.
760 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-2.6.9.tgz. Extracting by manifest.
761 silly tarball no local data for negotiator@https://registry.npmjs.org/negotiator/-/negotiator-0.6.4.tgz. Extracting by manifest.
762 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.0.0.tgz. Extracting by manifest.
763 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-2.6.9.tgz. Extracting by manifest.
764 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
765 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
766 silly tarball no local data for open@https://registry.npmjs.org/open/-/open-8.4.2.tgz. Extracting by manifest.
767 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
768 silly tarball no local data for picomatch@https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz. Extracting by manifest.
769 silly tarball no local data for type-fest@https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz. Extracting by manifest.
770 silly tarball no local data for ws@https://registry.npmjs.org/ws/-/ws-6.2.3.tgz. Extracting by manifest.
771 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.0.0.tgz. Extracting by manifest.
772 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-2.6.9.tgz. Extracting by manifest.
773 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
774 silly tarball no local data for ws@https://registry.npmjs.org/ws/-/ws-6.2.3.tgz. Extracting by manifest.
775 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.0.0.tgz. Extracting by manifest.
776 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-2.6.9.tgz. Extracting by manifest.
777 silly tarball no local data for glob@https://registry.npmjs.org/glob/-/glob-7.2.3.tgz. Extracting by manifest.
778 silly tarball no local data for @react-native/dev-middleware@https://registry.npmjs.org/@react-native/dev-middleware/-/dev-middleware-0.79.1.tgz. Extracting by manifest.
779 silly tarball no local data for minimatch@https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz. Extracting by manifest.
780 silly tarball no local data for brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz. Extracting by manifest.
781 silly tarball no local data for @react-native/debugger-frontend@https://registry.npmjs.org/@react-native/debugger-frontend/-/debugger-frontend-0.79.1.tgz. Extracting by manifest.
782 silly tarball no local data for p-locate@https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz. Extracting by manifest.
783 silly tarball no local data for p-limit@https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz. Extracting by manifest.
784 silly tarball no local data for locate-path@https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz. Extracting by manifest.
785 silly tarball no local data for js-yaml@https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz. Extracting by manifest.
786 silly tarball no local data for argparse@https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz. Extracting by manifest.
787 silly tarball no local data for wrap-ansi@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz. Extracting by manifest.
788 silly tarball no local data for find-up@https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz. Extracting by manifest.
789 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz. Extracting by manifest.
790 silly tarball no local data for @babel/code-frame@https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz. Extracting by manifest.
791 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
792 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
793 silly tarball no local data for @babel/code-frame@https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz. Extracting by manifest.
794 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-3.2.7.tgz. Extracting by manifest.
795 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
796 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
797 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
798 silly tarball no local data for @expo/config-plugins@https://registry.npmjs.org/@expo/config-plugins/-/config-plugins-10.0.3.tgz. Extracting by manifest.
799 silly tarball no local data for @babel/code-frame@https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz. Extracting by manifest.
800 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
801 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz. Extracting by manifest.
802 silly tarball no local data for has-flag@https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz. Extracting by manifest.
803 silly tarball no local data for escape-string-regexp@https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz. Extracting by manifest.
804 silly tarball no local data for color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz. Extracting by manifest.
805 silly tarball no local data for color-convert@https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz. Extracting by manifest.
806 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz. Extracting by manifest.
807 silly tarball no local data for chalk@https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz. Extracting by manifest.
808 silly tarball no local data for minimatch@https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz. Extracting by manifest.
809 http fetch GET 200 https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz 4840ms (cache miss)
810 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 5229ms
811 silly audit report {}
812 http fetch GET 200 https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz 4902ms (cache miss)
813 http fetch GET 200 https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz 4901ms (cache miss)
814 http fetch GET 200 https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz 4903ms (cache miss)
815 http fetch GET 200 https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz 4904ms (cache miss)
816 http fetch GET 200 https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz 4913ms (cache miss)
817 http fetch GET 200 https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz 4949ms (cache miss)
818 http fetch GET 200 https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz 5053ms (cache miss)
819 http fetch GET 200 https://registry.npmjs.org/xml2js/-/xml2js-0.6.0.tgz 5076ms (cache miss)
820 http fetch GET 200 https://registry.npmjs.org/which/-/which-2.0.2.tgz 5370ms (cache miss)
821 http fetch GET 200 https://registry.npmjs.org/warn-once/-/warn-once-0.1.1.tgz 5382ms (cache miss)
822 http fetch GET 200 https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-5.0.1.tgz 5389ms (cache miss)
823 http fetch GET 200 https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz 5405ms (cache miss)
824 http fetch GET 200 https://registry.npmjs.org/walker/-/walker-1.0.8.tgz 5421ms (cache miss)
825 http fetch GET 200 https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz 5416ms (cache miss)
826 http fetch GET 200 https://registry.npmjs.org/vary/-/vary-1.1.2.tgz 5419ms (cache miss)
827 http fetch GET 200 https://registry.npmjs.org/use-latest-callback/-/use-latest-callback-0.2.4.tgz 5485ms (cache miss)
828 http fetch GET 200 https://registry.npmjs.org/xcode/-/xcode-3.0.1.tgz 5508ms (cache miss)
829 http fetch GET 200 https://registry.npmjs.org/vlq/-/vlq-1.0.1.tgz 5586ms (cache miss)
830 http fetch GET 200 https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz 5632ms (cache miss)
831 http fetch GET 200 https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz 5724ms (cache miss)
832 http fetch GET 200 https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz 5710ms (cache miss)
833 http fetch GET 200 https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz 5712ms (cache miss)
834 http fetch GET 200 https://registry.npmjs.org/ws/-/ws-8.18.3.tgz 5877ms (cache miss)
835 http fetch GET 200 https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz 5882ms (cache miss)
836 http fetch GET 200 https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz 5900ms (cache miss)
837 http fetch GET 200 https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz 5898ms (cache miss)
838 http fetch GET 200 https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz 6493ms (cache miss)
839 http fetch GET 200 https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz 6504ms (cache miss)
840 http fetch GET 200 https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz 6544ms (cache miss)
841 http fetch GET 200 https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz 6546ms (cache miss)
842 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz 6546ms (cache miss)
843 http fetch GET 200 https://registry.npmjs.org/temp-dir/-/temp-dir-2.0.0.tgz 6558ms (cache miss)
844 http fetch GET 200 https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz 6587ms (cache miss)
845 http fetch GET 200 https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz 6600ms (cache miss)
846 http fetch GET 200 https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz 6598ms (cache miss)
847 http fetch GET 200 https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz 6588ms (cache miss)
848 http fetch GET 200 https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz 6743ms (cache miss)
849 http fetch GET 200 https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz 6744ms (cache miss)
850 http fetch GET 200 https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz 6751ms (cache miss)
851 http fetch GET 200 https://registry.npmjs.org/throat/-/throat-5.0.0.tgz 6774ms (cache miss)
852 http fetch GET 200 https://registry.npmjs.org/stacktrace-parser/-/stacktrace-parser-0.1.11.tgz 6883ms (cache miss)
853 http fetch GET 200 https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz 7014ms (cache miss)
854 http fetch GET 200 https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz 7011ms (cache miss)
855 http fetch GET 200 https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz 7008ms (cache miss)
856 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz 7010ms (cache miss)
857 http fetch GET 200 https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz 7003ms (cache miss)
858 http fetch GET 200 https://registry.npmjs.org/split-on-first/-/split-on-first-1.1.0.tgz 7003ms (cache miss)
859 http fetch GET 200 https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz 6996ms (cache miss)
860 http fetch GET 200 https://registry.npmjs.org/slash/-/slash-3.0.0.tgz 6997ms (cache miss)
861 http fetch GET 200 https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz 7007ms (cache miss)
862 http fetch GET 200 https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.3.tgz 7013ms (cache miss)
863 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 7043ms (cache miss)
864 http fetch GET 200 https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz 7033ms (cache miss)
865 http fetch GET 200 https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz 7031ms (cache miss)
866 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz 7103ms (cache miss)
867 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 7102ms (cache miss)
868 http fetch GET 200 https://registry.npmjs.org/whatwg-url-without-unicode/-/whatwg-url-without-unicode-8.0.0-3.tgz 7268ms (cache miss)
869 http fetch GET 200 https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz 7313ms (cache miss)
870 http fetch GET 200 https://registry.npmjs.org/ua-parser-js/-/ua-parser-js-1.0.40.tgz 7389ms (cache miss)
871 http fetch GET 200 https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz 7508ms (cache miss)
872 http fetch GET 200 https://registry.npmjs.org/slugify/-/slugify-1.6.6.tgz 8127ms (cache miss)
873 http fetch GET 200 https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz 8114ms (cache miss)
874 http fetch GET 200 https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz 8112ms (cache miss)
875 http fetch GET 200 https://registry.npmjs.org/serialize-error/-/serialize-error-2.1.0.tgz 8114ms (cache miss)
876 http fetch GET 200 https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz 8177ms (cache miss)
877 http fetch GET 200 https://registry.npmjs.org/sax/-/sax-1.4.1.tgz 8115ms (cache miss)
878 http fetch GET 200 https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz 8139ms (cache miss)
879 http fetch GET 200 https://registry.npmjs.org/shallowequal/-/shallowequal-1.1.0.tgz 8138ms (cache miss)
880 http fetch GET 200 https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz 8139ms (cache miss)
881 http fetch GET 200 https://registry.npmjs.org/server-only/-/server-only-0.0.1.tgz 8146ms (cache miss)
882 http fetch GET 200 https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz 8222ms (cache miss)
883 http fetch GET 200 https://registry.npmjs.org/scheduler/-/scheduler-0.25.0.tgz 8145ms (cache miss)
884 http fetch GET 200 https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz 8144ms (cache miss)
885 http fetch GET 200 https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz 8231ms (cache miss)
886 http fetch GET 200 https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz 8304ms (cache miss)
887 http fetch GET 200 https://registry.npmjs.org/regjsgen/-/regjsgen-0.8.0.tgz 8374ms (cache miss)
888 http fetch GET 200 https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz 8413ms (cache miss)
889 http fetch GET 200 https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz 9670ms (cache miss)
890 http fetch GET 200 https://registry.npmjs.org/send/-/send-0.19.1.tgz 9689ms (cache miss)
891 http fetch GET 200 https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz 9689ms (cache miss)
892 http fetch GET 200 https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz 9993ms (cache miss)
893 http fetch GET 200 https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz 9958ms (cache miss)
894 http fetch GET 200 https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz 9948ms (cache miss)
895 http fetch GET 200 https://registry.npmjs.org/type-fest/-/type-fest-0.7.1.tgz 10072ms (cache miss)
896 http fetch GET 200 https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.2.tgz 10003ms (cache miss)
897 http fetch GET 200 https://registry.npmjs.org/resolve-workspace-root/-/resolve-workspace-root-2.0.0.tgz 10076ms (cache miss)
898 http fetch GET 200 https://registry.npmjs.org/progress/-/progress-2.0.3.tgz 10045ms (cache miss)
899 warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
900 http fetch GET 200 https://registry.npmjs.org/react-leaflet/-/react-leaflet-4.2.1.tgz 10143ms (cache miss)
901 http fetch GET 200 https://registry.npmjs.org/react-is/-/react-is-19.1.0.tgz 10144ms (cache miss)
902 http fetch GET 200 https://registry.npmjs.org/react-freeze/-/react-freeze-1.0.4.tgz 10178ms (cache miss)
903 http fetch GET 200 https://registry.npmjs.org/rc/-/rc-1.2.8.tgz 10196ms (cache miss)
904 http fetch GET 200 https://registry.npmjs.org/queue/-/queue-6.0.2.tgz 10253ms (cache miss)
905 http fetch GET 200 https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz 10255ms (cache miss)
906 http fetch GET 200 https://registry.npmjs.org/promise/-/promise-8.3.0.tgz 10253ms (cache miss)
907 http fetch GET 200 https://registry.npmjs.org/proc-log/-/proc-log-4.2.0.tgz 10254ms (cache miss)
908 http fetch GET 200 https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz 10249ms (cache miss)
909 http fetch GET 200 https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz 10254ms (cache miss)
910 http fetch GET 200 https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz 10345ms (cache miss)
911 http fetch GET 200 https://registry.npmjs.org/react-fast-compare/-/react-fast-compare-3.2.2.tgz 10375ms (cache miss)
912 http fetch GET 200 https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz 10431ms (cache miss)
913 http fetch GET 200 https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz 10444ms (cache miss)
914 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-6.3.1.tgz 10525ms (cache miss)
915 http fetch GET 200 https://registry.npmjs.org/sf-symbols-typescript/-/sf-symbols-typescript-2.1.0.tgz 10564ms (cache miss)
916 http fetch GET 200 https://registry.npmjs.org/regjsparser/-/regjsparser-0.12.0.tgz 10646ms (cache miss)
917 http fetch GET 200 https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz 10604ms (cache miss)
918 http fetch GET 200 https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz 10617ms (cache miss)
919 http fetch GET 200 https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz 10647ms (cache miss)
920 http fetch GET 200 https://registry.npmjs.org/query-string/-/query-string-7.1.3.tgz 10668ms (cache miss)
921 http fetch GET 200 https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz 10715ms (cache miss)
922 http fetch GET 200 https://registry.npmjs.org/ora/-/ora-3.4.0.tgz 10709ms (cache miss)
923 http fetch GET 200 https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz 10703ms (cache miss)
924 http fetch GET 200 https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz 10759ms (cache miss)
925 http fetch GET 200 https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz 10777ms (cache miss)
926 http fetch GET 200 https://registry.npmjs.org/styleq/-/styleq-0.1.3.tgz 10911ms (cache miss)
927 http fetch GET 200 https://registry.npmjs.org/parse-png/-/parse-png-2.1.0.tgz 10808ms (cache miss)
928 http fetch GET 200 https://registry.npmjs.org/mz/-/mz-2.7.0.tgz 10833ms (cache miss)
929 http fetch GET 200 https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz 10827ms (cache miss)
930 http fetch GET 200 https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz 10915ms (cache miss)
931 http fetch GET 200 https://registry.npmjs.org/lodash.throttle/-/lodash.throttle-4.1.1.tgz 10884ms (cache miss)
932 http fetch GET 200 https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz 10939ms (cache miss)
933 http fetch GET 200 https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz 10940ms (cache miss)
934 http fetch GET 200 https://registry.npmjs.org/open/-/open-7.4.2.tgz 10938ms (cache miss)
935 http fetch GET 200 https://registry.npmjs.org/once/-/once-1.4.0.tgz 10940ms (cache miss)
936 http fetch GET 200 https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz 10942ms (cache miss)
937 http fetch GET 200 https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz 10940ms (cache miss)
938 http fetch GET 200 https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz 10940ms (cache miss)
939 http fetch GET 200 https://registry.npmjs.org/nullthrows/-/nullthrows-1.1.1.tgz 10939ms (cache miss)
940 http fetch GET 200 https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz 10936ms (cache miss)
941 http fetch GET 200 https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz 10926ms (cache miss)
942 http fetch GET 200 https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz 10925ms (cache miss)
943 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.1.3.tgz 10934ms (cache miss)
944 http fetch GET 200 https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz 10925ms (cache miss)
945 http fetch GET 200 https://registry.npmjs.org/metro-transform-worker/-/metro-transform-worker-0.82.4.tgz 10924ms (cache miss)
946 http fetch GET 200 https://registry.npmjs.org/metro-core/-/metro-core-0.82.4.tgz 10927ms (cache miss)
947 http fetch GET 200 https://registry.npmjs.org/metro-babel-transformer/-/metro-babel-transformer-0.82.4.tgz 10926ms (cache miss)
948 http fetch GET 200 https://registry.npmjs.org/metro-config/-/metro-config-0.82.4.tgz 10927ms (cache miss)
949 http fetch GET 200 https://registry.npmjs.org/metro-cache/-/metro-cache-0.82.4.tgz 10926ms (cache miss)
950 http fetch GET 200 https://registry.npmjs.org/marky/-/marky-1.3.0.tgz 10925ms (cache miss)
951 http fetch GET 200 https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz 10924ms (cache miss)
952 http fetch GET 200 https://registry.npmjs.org/merge-options/-/merge-options-3.0.4.tgz 10930ms (cache miss)
953 http fetch GET 200 https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz 10999ms (cache miss)
954 http fetch GET 200 https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz 10980ms (cache miss)
955 http fetch GET 200 https://registry.npmjs.org/metro-cache-key/-/metro-cache-key-0.82.4.tgz 10993ms (cache miss)
956 http fetch GET 200 https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz 11216ms (cache miss)
957 http fetch GET 200 https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz 11029ms (cache miss)
958 http fetch GET 200 https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz 11090ms (cache miss)
959 http fetch GET 200 https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz 11090ms (cache miss)
960 http fetch GET 200 https://registry.npmjs.org/ob1/-/ob1-0.82.4.tgz 11083ms (cache miss)
961 http fetch GET 200 https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz 11081ms (cache miss)
962 http fetch GET 200 https://registry.npmjs.org/npm-package-arg/-/npm-package-arg-11.0.3.tgz 11081ms (cache miss)
963 http fetch GET 200 https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz 11081ms (cache miss)
964 http fetch GET 200 https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz 11080ms (cache miss)
965 http fetch GET 200 https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz 11047ms (cache miss)
966 http fetch GET 200 https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz 11081ms (cache miss)
967 http fetch GET 200 https://registry.npmjs.org/nested-error-stacks/-/nested-error-stacks-2.0.1.tgz 11078ms (cache miss)
968 http fetch GET 200 https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz 11047ms (cache miss)
969 http fetch GET 200 https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz 11077ms (cache miss)
970 http fetch GET 200 https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz 11069ms (cache miss)
971 http fetch GET 200 https://registry.npmjs.org/metro-minify-terser/-/metro-minify-terser-0.82.4.tgz 11065ms (cache miss)
972 http fetch GET 200 https://registry.npmjs.org/memoize-one/-/memoize-one-5.2.1.tgz 11060ms (cache miss)
973 http fetch GET 200 https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz 11060ms (cache miss)
974 http fetch GET 200 https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz 11201ms (cache miss)
975 http fetch GET 200 https://registry.npmjs.org/mime/-/mime-1.6.0.tgz 11136ms (cache miss)
976 http fetch GET 200 https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz 11138ms (cache miss)
977 http fetch GET 200 https://registry.npmjs.org/leven/-/leven-3.1.0.tgz 11177ms (cache miss)
978 http fetch GET 200 https://registry.npmjs.org/lighthouse-logger/-/lighthouse-logger-1.4.2.tgz 11190ms (cache miss)
979 http fetch GET 200 https://registry.npmjs.org/log-symbols/-/log-symbols-2.2.0.tgz 11198ms (cache miss)
980 http fetch GET 200 https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz 11375ms (cache miss)
981 http fetch GET 200 https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz 11213ms (cache miss)
982 http fetch GET 200 https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz 11257ms (cache miss)
983 http fetch GET 200 https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz 11290ms (cache miss)
984 http fetch GET 200 https://registry.npmjs.org/lan-network/-/lan-network-0.1.7.tgz 11298ms (cache miss)
985 http fetch GET 200 https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz 11318ms (cache miss)
986 http fetch GET 200 https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz 11327ms (cache miss)
987 http fetch GET 200 https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz 11364ms (cache miss)
988 http fetch GET 200 https://registry.npmjs.org/jsc-safe-url/-/jsc-safe-url-0.2.4.tgz 11377ms (cache miss)
989 http fetch GET 200 https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz 11377ms (cache miss)
990 http fetch GET 200 https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz 11388ms (cache miss)
991 http fetch GET 200 https://registry.npmjs.org/isows/-/isows-1.0.7.tgz 11439ms (cache miss)
992 http fetch GET 200 https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz 11450ms (cache miss)
993 http fetch GET 200 https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz 11450ms (cache miss)
994 http fetch GET 200 https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz 11447ms (cache miss)
995 http fetch GET 200 https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz 11458ms (cache miss)
996 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz 11471ms (cache miss)
997 http fetch GET 200 https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz 11474ms (cache miss)
998 http fetch GET 200 https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz 11491ms (cache miss)
999 http fetch GET 200 https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.2.0.tgz 11635ms (cache miss)
1000 http fetch GET 200 https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz 11508ms (cache miss)
1001 http fetch GET 200 https://registry.npmjs.org/inline-style-prefixer/-/inline-style-prefixer-7.0.1.tgz 11562ms (cache miss)
1002 http fetch GET 200 https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-2.1.0.tgz 11618ms (cache miss)
1003 http fetch GET 200 https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz 11651ms (cache miss)
1004 http fetch GET 200 https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz 11653ms (cache miss)
1005 http fetch GET 200 https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz 11656ms (cache miss)
1006 http fetch GET 200 https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz 11677ms (cache miss)
1007 http fetch GET 200 https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz 11683ms (cache miss)
1008 http fetch GET 200 https://registry.npmjs.org/ini/-/ini-1.3.8.tgz 11692ms (cache miss)
1009 http fetch GET 200 https://registry.npmjs.org/wonka/-/wonka-6.3.5.tgz 11993ms (cache miss)
1010 http fetch GET 200 https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz 11743ms (cache miss)
1011 http fetch GET 200 https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz 11753ms (cache miss)
1012 http fetch GET 200 https://registry.npmjs.org/image-size/-/image-size-1.2.1.tgz 11770ms (cache miss)
1013 http fetch GET 200 https://registry.npmjs.org/import-fresh/-/import-fresh-2.0.0.tgz 11770ms (cache miss)
1014 http fetch GET 200 https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz 11808ms (cache miss)
1015 http fetch GET 200 https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz 11837ms (cache miss)
1016 http fetch GET 200 https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz 11908ms (cache miss)
1017 warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
1018 http fetch GET 200 https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz 11932ms (cache miss)
1019 http fetch GET 200 https://registry.npmjs.org/hyphenate-style-name/-/hyphenate-style-name-1.1.0.tgz 11981ms (cache miss)
1020 http fetch GET 200 https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz 11984ms (cache miss)
1021 http fetch GET 200 https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-7.0.2.tgz 11986ms (cache miss)
1022 http fetch GET 200 https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz 11985ms (cache miss)
1023 http fetch GET 200 https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz 11987ms (cache miss)
1024 http fetch GET 200 https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz 11990ms (cache miss)
1025 http fetch GET 200 https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz 11988ms (cache miss)
1026 http fetch GET 200 https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz 12057ms (cache miss)
1027 http fetch GET 200 https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz 12109ms (cache miss)
1028 http fetch GET 200 https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz 12147ms (cache miss)
1029 http fetch GET 200 https://registry.npmjs.org/getenv/-/getenv-2.0.0.tgz 12171ms (cache miss)
1030 http fetch GET 200 https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz 12164ms (cache miss)
1031 http fetch GET 200 https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz 12320ms (cache miss)
1032 http fetch GET 200 https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz 12243ms (cache miss)
1033 http fetch GET 200 https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz 12318ms (cache miss)
1034 http fetch GET 200 https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz 12267ms (cache miss)
1035 http fetch GET 200 https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz 12287ms (cache miss)
1036 http fetch GET 200 https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz 12326ms (cache miss)
1037 http fetch GET 200 https://registry.npmjs.org/fontfaceobserver/-/fontfaceobserver-2.3.0.tgz 12345ms (cache miss)
1038 http fetch GET 200 https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz 12372ms (cache miss)
1039 http fetch GET 200 https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz 12384ms (cache miss)
1040 http fetch GET 200 https://registry.npmjs.org/freeport-async/-/freeport-async-2.0.0.tgz 12392ms (cache miss)
1041 http fetch GET 200 https://registry.npmjs.org/flow-enums-runtime/-/flow-enums-runtime-0.0.6.tgz 12467ms (cache miss)
1042 http fetch GET 200 https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz 12503ms (cache miss)
1043 http fetch GET 200 https://registry.npmjs.org/react-native-is-edge-to-edge/-/react-native-is-edge-to-edge-1.2.1.tgz 12660ms (cache miss)
1044 http fetch GET 200 https://registry.npmjs.org/fbjs-css-vars/-/fbjs-css-vars-1.0.2.tgz 12546ms (cache miss)
1045 http fetch GET 200 https://registry.npmjs.org/picomatch/-/picomatch-3.0.1.tgz 12723ms (cache miss)
1046 http fetch GET 200 https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz 12614ms (cache miss)
1047 http fetch GET 200 https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz 12621ms (cache miss)
1048 http fetch GET 200 https://registry.npmjs.org/expo-status-bar/-/expo-status-bar-2.2.3.tgz 12639ms (cache miss)
1049 http fetch GET 200 https://registry.npmjs.org/filter-obj/-/filter-obj-1.1.0.tgz 12651ms (cache miss)
1050 http fetch GET 200 https://registry.npmjs.org/expo-symbols/-/expo-symbols-0.4.5.tgz 12737ms (cache miss)
1051 http fetch GET 200 https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz 12761ms (cache miss)
1052 http fetch GET 200 https://registry.npmjs.org/exponential-backoff/-/exponential-backoff-3.1.2.tgz 12765ms (cache miss)
1053 http fetch GET 200 https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz 13696ms (cache miss)
1054 http fetch GET 200 https://registry.npmjs.org/etag/-/etag-1.8.1.tgz 13697ms (cache miss)
1055 http fetch GET 200 https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz 13766ms (cache miss)
1056 http fetch GET 200 https://registry.npmjs.org/env-editor/-/env-editor-0.4.2.tgz 14000ms (cache miss)
1057 http fetch GET 200 https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz 14003ms (cache miss)
1058 http fetch GET 200 https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz 14257ms (cache miss)
1059 http fetch GET 200 https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz 14470ms (cache miss)
1060 http fetch GET 200 https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz 14585ms (cache miss)
1061 http fetch GET 200 https://registry.npmjs.org/exec-async/-/exec-async-2.2.0.tgz 14620ms (cache miss)
1062 http fetch GET 200 https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz 14615ms (cache miss)
1063 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz 14880ms (cache miss)
1064 http fetch GET 200 https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz 14900ms (cache miss)
1065 http fetch GET 200 https://registry.npmjs.org/requireg/-/requireg-0.2.2.tgz 16586ms (cache miss)
1066 http fetch GET 200 https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz 16385ms (cache miss)
1067 http fetch GET 200 https://registry.npmjs.org/depd/-/depd-2.0.0.tgz 16408ms (cache miss)
1068 http fetch GET 200 https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-11.0.7.tgz 16425ms (cache miss)
1069 http fetch GET 200 https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz 16428ms (cache miss)
1070 http fetch GET 200 https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz 16482ms (cache miss)
1071 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz 16547ms (cache miss)
1072 http fetch GET 200 https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz 16482ms (cache miss)
1073 http fetch GET 200 https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz 16486ms (cache miss)
1074 http fetch GET 200 https://registry.npmjs.org/dom-serializer/-/dom-serializer-2.0.0.tgz 16572ms (cache miss)
1075 http fetch GET 200 https://registry.npmjs.org/jimp-compact/-/jimp-compact-0.16.1.tgz 16660ms (cache miss)
1076 http fetch GET 200 https://registry.npmjs.org/css-what/-/css-what-6.2.2.tgz 16641ms (cache miss)
1077 http fetch GET 200 https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz 16656ms (cache miss)
1078 http fetch GET 200 https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz 16715ms (cache miss)
1079 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-4.4.1.tgz 16720ms (cache miss)
1080 http fetch GET 200 https://registry.npmjs.org/simple-plist/-/simple-plist-1.3.1.tgz 16976ms (cache miss)
1081 http fetch GET 200 https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz 16759ms (cache miss)
1082 http fetch GET 200 https://registry.npmjs.org/metro-symbolicate/-/metro-symbolicate-0.82.4.tgz 16893ms (cache miss)
1083 http fetch GET 200 https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz 16907ms (cache miss)
1084 http fetch GET 200 https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz 16924ms (cache miss)
1085 http fetch GET 200 https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz 16941ms (cache miss)
1086 http fetch GET 200 https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz 16940ms (cache miss)
1087 http fetch GET 200 https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-2.0.0.tgz 16952ms (cache miss)
1088 http fetch GET 200 https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz 17203ms (cache miss)
1089 http fetch GET 200 https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz 16956ms (cache miss)
1090 http fetch GET 200 https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz 16969ms (cache miss)
1091 http fetch GET 200 https://registry.npmjs.org/compression/-/compression-1.8.0.tgz 16971ms (cache miss)
1092 http fetch GET 200 https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz 16964ms (cache miss)
1093 http fetch GET 200 https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz 17032ms (cache miss)
1094 http fetch GET 200 https://registry.npmjs.org/client-only/-/client-only-0.0.1.tgz 17044ms (cache miss)
1095 http fetch GET 200 https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz 17067ms (cache miss)
1096 http fetch GET 200 https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz 17057ms (cache miss)
1097 http fetch GET 200 https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz 17061ms (cache miss)
1098 http fetch GET 200 https://registry.npmjs.org/stream-buffers/-/stream-buffers-2.2.0.tgz 17336ms (cache miss)
1099 http fetch GET 200 https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz 17063ms (cache miss)
1100 http fetch GET 200 https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz 17056ms (cache miss)
1101 http fetch GET 200 https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz 17057ms (cache miss)
1102 http fetch GET 200 https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz 17057ms (cache miss)
1103 http fetch GET 200 https://registry.npmjs.org/caller-path/-/caller-path-2.0.0.tgz 17056ms (cache miss)
1104 http fetch GET 200 https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz 17058ms (cache miss)
1105 http fetch GET 200 https://registry.npmjs.org/caller-callsite/-/caller-callsite-2.0.0.tgz 17058ms (cache miss)
1106 http fetch GET 200 https://registry.npmjs.org/braces/-/braces-3.0.3.tgz 17055ms (cache miss)
1107 http fetch GET 200 https://registry.npmjs.org/bser/-/bser-2.1.1.tgz 17064ms (cache miss)
1108 http fetch GET 200 https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.3.2.tgz 17061ms (cache miss)
1109 http fetch GET 200 https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz 17062ms (cache miss)
1110 http fetch GET 200 https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz 17060ms (cache miss)
1111 http fetch GET 200 https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz 17061ms (cache miss)
1112 http fetch GET 200 https://registry.npmjs.org/babel-plugin-syntax-hermes-parser/-/babel-plugin-syntax-hermes-parser-0.25.1.tgz 17054ms (cache miss)
1113 http fetch GET 200 https://registry.npmjs.org/babel-plugin-transform-flow-enums/-/babel-plugin-transform-flow-enums-0.0.2.tgz 17056ms (cache miss)
1114 http fetch GET 200 https://registry.npmjs.org/asap/-/asap-2.0.6.tgz 17091ms (cache miss)
1115 http fetch GET 200 https://registry.npmjs.org/clone/-/clone-1.0.4.tgz 17150ms (cache miss)
1116 http fetch GET 200 https://registry.npmjs.org/arg/-/arg-5.0.2.tgz 17112ms (cache miss)
1117 http fetch GET 200 https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz 17112ms (cache miss)
1118 http fetch GET 200 https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz 17112ms (cache miss)
1119 http fetch GET 200 https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz 17253ms (cache miss)
1120 http fetch GET 200 https://registry.npmjs.org/@urql/exchange-retry/-/exchange-retry-1.3.2.tgz 17280ms (cache miss)
1121 http fetch GET 200 https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz 17295ms (cache miss)
1122 http fetch GET 200 https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz 17292ms (cache miss)
1123 http fetch GET 200 https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz 17295ms (cache miss)
1124 http fetch GET 200 https://registry.npmjs.org/@types/phoenix/-/phoenix-1.6.6.tgz 17292ms (cache miss)
1125 http fetch GET 200 https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz 17365ms (cache miss)
1126 http fetch GET 200 https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz 17386ms (cache miss)
1127 http fetch GET 200 https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz 17377ms (cache miss)
1128 http fetch GET 200 https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz 17385ms (cache miss)
1129 http fetch GET 200 https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.5.tgz 17381ms (cache miss)
1130 http fetch GET 200 https://registry.npmjs.org/babel-plugin-react-native-web/-/babel-plugin-react-native-web-0.19.13.tgz 17380ms (cache miss)
1131 http fetch GET 200 https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz 17384ms (cache miss)
1132 http fetch GET 200 https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz 17355ms (cache miss)
1133 http fetch GET 200 https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz 17381ms (cache miss)
1134 http fetch GET 200 https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz 17412ms (cache miss)
1135 http fetch GET 200 https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz 17456ms (cache miss)
1136 http fetch GET 200 https://registry.npmjs.org/@types/geojson/-/geojson-7946.0.16.tgz 17424ms (cache miss)
1137 http fetch GET 200 https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz 17458ms (cache miss)
1138 http fetch GET 200 https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.1.tgz 17478ms (cache miss)
1139 http fetch GET 200 https://registry.npmjs.org/color/-/color-4.2.3.tgz 17529ms (cache miss)
1140 http fetch GET 200 https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz 17515ms (cache miss)
1141 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz 17545ms (cache miss)
1142 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 17544ms (cache miss)
1143 http fetch GET 200 https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz 17556ms (cache miss)
1144 http fetch GET 200 https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz 17584ms (cache miss)
1145 http fetch GET 200 https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz 17609ms (cache miss)
1146 http fetch GET 200 https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz 17593ms (cache miss)
1147 http fetch GET 200 https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz 17634ms (cache miss)
1148 http fetch GET 200 https://registry.npmjs.org/@supabase/functions-js/-/functions-js-2.4.5.tgz 17626ms (cache miss)
1149 http fetch GET 200 https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz 17695ms (cache miss)
1150 http fetch GET 200 https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz 17630ms (cache miss)
1151 http fetch GET 200 https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz 17635ms (cache miss)
1152 http fetch GET 200 https://registry.npmjs.org/@types/hammerjs/-/hammerjs-2.0.46.tgz 17670ms (cache miss)
1153 http fetch GET 200 https://registry.npmjs.org/@react-native/babel-preset/-/babel-preset-0.79.5.tgz 17688ms (cache miss)
1154 http fetch GET 200 https://registry.npmjs.org/@react-leaflet/core/-/core-2.1.0.tgz 17689ms (cache miss)
1155 http fetch GET 200 https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.0.tgz 17689ms (cache miss)
1156 http fetch GET 200 https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz 17688ms (cache miss)
1157 http fetch GET 200 https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz 17679ms (cache miss)
1158 http fetch GET 200 https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz 17680ms (cache miss)
1159 http fetch GET 200 https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz 17680ms (cache miss)
1160 http fetch GET 200 https://registry.npmjs.org/@react-native/assets-registry/-/assets-registry-0.79.1.tgz 17692ms (cache miss)
1161 http fetch GET 200 https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz 17676ms (cache miss)
1162 http fetch GET 200 https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz 17676ms (cache miss)
1163 http fetch GET 200 https://registry.npmjs.org/@react-native/babel-plugin-codegen/-/babel-plugin-codegen-0.79.5.tgz 17675ms (cache miss)
1164 http fetch GET 200 https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz 17678ms (cache miss)
1165 http fetch GET 200 https://registry.npmjs.org/@isaacs/ttlcache/-/ttlcache-1.4.1.tgz 17678ms (cache miss)
1166 http fetch GET 200 https://registry.npmjs.org/@expo/spawn-async/-/spawn-async-1.7.2.tgz 17679ms (cache miss)
1167 http fetch GET 200 https://registry.npmjs.org/@expo/server/-/server-0.6.3.tgz 17678ms (cache miss)
1168 http fetch GET 200 https://registry.npmjs.org/@expo/json-file/-/json-file-9.1.4.tgz 17671ms (cache miss)
1169 http fetch GET 200 https://registry.npmjs.org/@expo/osascript/-/osascript-2.2.4.tgz 17673ms (cache miss)
1170 http fetch GET 200 https://registry.npmjs.org/anser/-/anser-1.4.10.tgz 17781ms (cache miss)
1171 http fetch GET 200 https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz 17807ms (cache miss)
1172 http fetch GET 200 https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz 17814ms (cache miss)
1173 http fetch GET 200 https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz 17900ms (cache miss)
1174 http fetch GET 200 https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz 18097ms (cache miss)
1175 http fetch GET 200 https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.10.tgz 18102ms (cache miss)
1176 http fetch GET 200 https://registry.npmjs.org/@react-native/js-polyfills/-/js-polyfills-0.79.1.tgz 18112ms (cache miss)
1177 http fetch GET 200 https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz 18094ms (cache miss)
1178 http fetch GET 200 https://registry.npmjs.org/@jest/create-cache-key-function/-/create-cache-key-function-29.7.0.tgz 18095ms (cache miss)
1179 http fetch GET 200 https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz 18091ms (cache miss)
1180 http fetch GET 200 https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz 18093ms (cache miss)
1181 http fetch GET 200 https://registry.npmjs.org/@expo/plist/-/plist-0.3.4.tgz 18089ms (cache miss)
1182 http fetch GET 200 https://registry.npmjs.org/connect/-/connect-3.7.0.tgz 18287ms (cache miss)
1183 http fetch GET 200 https://registry.npmjs.org/@expo/code-signing-certificates/-/code-signing-certificates-0.0.5.tgz 18148ms (cache miss)
1184 http fetch GET 200 https://registry.npmjs.org/@expo/config-types/-/config-types-53.0.4.tgz 18151ms (cache miss)
1185 http fetch GET 200 https://registry.npmjs.org/@react-native/normalize-colors/-/normalize-colors-0.79.5.tgz 18217ms (cache miss)
1186 http fetch GET 200 https://registry.npmjs.org/@expo/sdk-runtime-versions/-/sdk-runtime-versions-1.0.0.tgz 18227ms (cache miss)
1187 http fetch GET 200 https://registry.npmjs.org/@types/leaflet/-/leaflet-1.9.19.tgz 18334ms (cache miss)
1188 http fetch GET 200 https://registry.npmjs.org/metro-runtime/-/metro-runtime-0.82.4.tgz 19355ms (cache miss)
1189 http fetch GET 200 https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz 19164ms (cache miss)
1190 http fetch GET 200 https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz 19221ms (cache miss)
1191 http fetch GET 200 https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz 20777ms (cache miss)
1192 http fetch GET 200 https://registry.npmjs.org/metro-transform-plugins/-/metro-transform-plugins-0.82.4.tgz 21128ms (cache miss)
1193 http fetch GET 200 https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz 21251ms (cache miss)
1194 http fetch GET 200 https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.27.1.tgz 21132ms (cache miss)
1195 http fetch GET 200 https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz 24606ms (cache miss)
1196 http fetch GET 200 https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.27.1.tgz 24259ms (cache miss)
1197 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.27.1.tgz 24275ms (cache miss)
1198 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.27.1.tgz 24275ms (cache miss)
1199 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.27.1.tgz 24284ms (cache miss)
1200 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.27.1.tgz 24323ms (cache miss)
1201 http fetch GET 200 https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz 24564ms (cache miss)
1202 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz 24404ms (cache miss)
1203 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.27.1.tgz 24403ms (cache miss)
1204 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.28.0.tgz 24427ms (cache miss)
1205 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.27.1.tgz 24480ms (cache miss)
1206 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.27.1.tgz 24590ms (cache miss)
1207 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz 24604ms (cache miss)
1208 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.28.0.tgz 24613ms (cache miss)
1209 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz 24743ms (cache miss)
1210 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.27.1.tgz 24755ms (cache miss)
1211 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.27.1.tgz 24783ms (cache miss)
1212 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.27.1.tgz 24791ms (cache miss)
1213 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.27.1.tgz 24791ms (cache miss)
1214 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.27.1.tgz 24797ms (cache miss)
1215 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.27.1.tgz 24813ms (cache miss)
1216 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.27.1.tgz 24813ms (cache miss)
1217 http fetch GET 200 https://registry.npmjs.org/@expo/env/-/env-1.0.6.tgz 24841ms (cache miss)
1218 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.27.1.tgz 24816ms (cache miss)
1219 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.27.1.tgz 24837ms (cache miss)
1220 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.27.1.tgz 24827ms (cache miss)
1221 http fetch GET 200 https://registry.npmjs.org/dotenv/-/dotenv-16.4.7.tgz 25022ms (cache miss)
1222 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.27.1.tgz 24841ms (cache miss)
1223 http fetch GET 200 https://registry.npmjs.org/@expo/sudo-prompt/-/sudo-prompt-9.3.2.tgz 24912ms (cache miss)
1224 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.28.0.tgz 24860ms (cache miss)
1225 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz 24861ms (cache miss)
1226 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.27.1.tgz 24881ms (cache miss)
1227 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.27.1.tgz 24893ms (cache miss)
1228 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz 24883ms (cache miss)
1229 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz 24885ms (cache miss)
1230 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz 24934ms (cache miss)
1231 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.27.1.tgz 24949ms (cache miss)
1232 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.27.1.tgz 24949ms (cache miss)
1233 http fetch GET 200 https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz 25151ms (cache miss)
1234 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.27.1.tgz 24955ms (cache miss)
1235 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz 24945ms (cache miss)
1236 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz 24950ms (cache miss)
1237 http fetch GET 200 https://registry.npmjs.org/structured-headers/-/structured-headers-0.4.1.tgz 25417ms (cache miss)
1238 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz 25038ms (cache miss)
1239 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz 25227ms (cache miss)
1240 http fetch GET 200 https://registry.npmjs.org/commander/-/commander-7.2.0.tgz 25599ms (cache miss)
1241 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz 25545ms (cache miss)
1242 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz 25564ms (cache miss)
1243 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz 25564ms (cache miss)
1244 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.27.1.tgz 25563ms (cache miss)
1245 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz 25564ms (cache miss)
1246 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.27.1.tgz 25562ms (cache miss)
1247 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz 25562ms (cache miss)
1248 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz 25561ms (cache miss)
1249 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz 25559ms (cache miss)
1250 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz 25558ms (cache miss)
1251 http fetch GET 200 https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz 25556ms (cache miss)
1252 http fetch GET 200 https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz 25566ms (cache miss)
1253 http fetch GET 200 https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz 25566ms (cache miss)
1254 http fetch GET 200 https://registry.npmjs.org/@babel/highlight/-/highlight-7.25.9.tgz 25567ms (cache miss)
1255 http fetch GET 200 https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz 25566ms (cache miss)
1256 http fetch GET 200 https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz 25721ms (cache miss)
1257 http fetch GET 200 https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz 25564ms (cache miss)
1258 http fetch GET 200 https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz 25564ms (cache miss)
1259 http fetch GET 200 https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz 25559ms (cache miss)
1260 http fetch GET 200 https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz 25566ms (cache miss)
1261 http fetch GET 200 https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz 25558ms (cache miss)
1262 http fetch GET 200 https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz 25558ms (cache miss)
1263 http fetch GET 200 https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz 25557ms (cache miss)
1264 http fetch GET 200 https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz 25556ms (cache miss)
1265 http fetch GET 200 https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz 25600ms (cache miss)
1266 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 25626ms (cache miss)
1267 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 25625ms (cache miss)
1268 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 25624ms (cache miss)
1269 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 25627ms (cache miss)
1270 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 25628ms (cache miss)
1271 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 25624ms (cache miss)
1272 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 25614ms (cache miss)
1273 http fetch GET 200 https://registry.npmjs.org/react-native-url-polyfill/-/react-native-url-polyfill-2.0.0.tgz 26038ms (cache miss)
1274 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 25627ms (cache miss)
1275 http fetch GET 200 https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz 25644ms (cache miss)
1276 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz 25691ms (cache miss)
1277 http fetch GET 200 https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz 25655ms (cache miss)
1278 http fetch GET 200 https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz 25669ms (cache miss)
1279 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz 25668ms (cache miss)
1280 http fetch GET 200 https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz 25717ms (cache miss)
1281 http fetch GET 200 https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.3.1.tgz 25777ms (cache miss)
1282 http fetch GET 200 https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.27.1.tgz 25828ms (cache miss)
1283 http fetch GET 200 https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz 25798ms (cache miss)
1284 http fetch GET 200 https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz 25800ms (cache miss)
1285 http fetch GET 200 https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz 25798ms (cache miss)
1286 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.0.0.tgz 25799ms (cache miss)
1287 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz 26545ms (cache miss)
1288 http fetch GET 200 https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz 27020ms (cache miss)
1289 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-export-default-from/-/plugin-syntax-export-default-from-7.27.1.tgz 26833ms (cache miss)
1290 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.28.0.tgz 26886ms (cache miss)
1291 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-proposal-export-default-from/-/plugin-proposal-export-default-from-7.27.1.tgz 26886ms (cache miss)
1292 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz 26983ms (cache miss)
1293 http fetch GET 200 https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz 27480ms (cache miss)
1294 http fetch GET 200 https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz 27548ms (cache miss)
1295 http fetch GET 200 https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-15.1.1.tgz 28086ms (cache miss)
1296 http fetch GET 200 https://registry.npmjs.org/jsesc/-/jsesc-3.0.2.tgz 27705ms (cache miss)
1297 http fetch GET 200 https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz 27800ms (cache miss)
1298 http fetch GET 200 https://registry.npmjs.org/resolve/-/resolve-1.7.1.tgz 27905ms (cache miss)
1299 http fetch GET 200 https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz 28043ms (cache miss)
1300 http fetch GET 200 https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz 28041ms (cache miss)
1301 http fetch GET 200 https://registry.npmjs.org/big-integer/-/big-integer-1.6.52.tgz 28292ms (cache miss)
1302 http fetch GET 200 https://registry.npmjs.org/@react-native/normalize-colors/-/normalize-colors-0.74.89.tgz 28465ms (cache miss)
1303 http fetch GET 200 https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz 28542ms (cache miss)
1304 http fetch GET 200 https://registry.npmjs.org/glob/-/glob-7.2.3.tgz 28538ms (cache miss)
1305 http fetch GET 200 https://registry.npmjs.org/react-native-is-edge-to-edge/-/react-native-is-edge-to-edge-1.1.7.tgz 28615ms (cache miss)
1306 http fetch GET 200 https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz 29105ms (cache miss)
1307 http fetch GET 200 https://registry.npmjs.org/memoize-one/-/memoize-one-6.0.0.tgz 28898ms (cache miss)
1308 http fetch GET 200 https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.15.tgz 32273ms (cache miss)
1309 http fetch GET 200 https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.14.tgz 32378ms (cache miss)
1310 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.28.0.tgz 32370ms (cache miss)
1311 http fetch GET 200 https://registry.npmjs.org/send/-/send-0.19.0.tgz 32489ms (cache miss)
1312 http fetch GET 200 https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz 32467ms (cache miss)
1313 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz 32467ms (cache miss)
1314 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz 32467ms (cache miss)
1315 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz 32479ms (cache miss)
1316 http fetch GET 200 https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz 32507ms (cache miss)
1317 http fetch GET 200 https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz 32529ms (cache miss)
1318 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 32544ms (cache miss)
1319 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 32556ms (cache miss)
1320 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 32554ms (cache miss)
1321 http fetch GET 200 https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz 32516ms (cache miss)
1322 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 32481ms (cache miss)
1323 http fetch GET 200 https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz 32515ms (cache miss)
1324 http fetch GET 200 https://registry.npmjs.org/glob/-/glob-7.2.3.tgz 32558ms (cache miss)
1325 http fetch GET 200 https://registry.npmjs.org/glob/-/glob-7.2.3.tgz 32571ms (cache miss)
1326 http fetch GET 200 https://registry.npmjs.org/glob/-/glob-7.2.3.tgz 32503ms (cache miss)
1327 http fetch GET 200 https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz 32573ms (cache miss)
1328 http fetch GET 200 https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz 32507ms (cache miss)
1329 http fetch GET 200 https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz 32564ms (cache miss)
1330 http fetch GET 200 https://registry.npmjs.org/lightningcss/-/lightningcss-1.27.0.tgz 32952ms (cache miss)
1331 http fetch GET 200 https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz 32598ms (cache miss)
1332 http fetch GET 200 https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz 32608ms (cache miss)
1333 http fetch GET 200 https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.179.tgz 32937ms (cache miss)
1334 http fetch GET 200 https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz 32682ms (cache miss)
1335 http fetch GET 200 https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz 32641ms (cache miss)
1336 http fetch GET 200 https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz 32684ms (cache miss)
1337 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.0.0.tgz 32681ms (cache miss)
1338 http fetch GET 200 https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz 32682ms (cache miss)
1339 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.0.0.tgz 32646ms (cache miss)
1340 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.0.0.tgz 32642ms (cache miss)
1341 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.0.0.tgz 32638ms (cache miss)
1342 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.0.0.tgz 32621ms (cache miss)
1343 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.0.0.tgz 32615ms (cache miss)
1344 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.0.0.tgz 32632ms (cache miss)
1345 http fetch GET 200 https://registry.npmjs.org/ci-info/-/ci-info-2.0.0.tgz 32701ms (cache miss)
1346 http fetch GET 200 https://registry.npmjs.org/@react-native/normalize-colors/-/normalize-colors-0.79.1.tgz 32725ms (cache miss)
1347 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz 32728ms (cache miss)
1348 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz 32721ms (cache miss)
1349 http fetch GET 200 https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz 32720ms (cache miss)
1350 http fetch GET 200 https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz 32720ms (cache miss)
1351 http fetch GET 200 https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz 32716ms (cache miss)
1352 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 32705ms (cache miss)
1353 http fetch GET 200 https://registry.npmjs.org/open/-/open-8.4.2.tgz 32704ms (cache miss)
1354 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 32707ms (cache miss)
1355 http fetch GET 200 https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz 32691ms (cache miss)
1356 http fetch GET 200 https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz 32691ms (cache miss)
1357 http fetch GET 200 https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz 32690ms (cache miss)
1358 http fetch GET 200 https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz 32687ms (cache miss)
1359 http fetch GET 200 https://registry.npmjs.org/qrcode-terminal/-/qrcode-terminal-0.11.0.tgz 33287ms (cache miss)
1360 http fetch GET 200 https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz 32902ms (cache miss)
1361 http fetch GET 200 https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz 32836ms (cache miss)
1362 http fetch GET 200 https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz 32630ms (cache miss)
1363 http fetch GET 200 https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz 32893ms (cache miss)
1364 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.1.tgz 32915ms (cache miss)
1365 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.28.0.tgz 33058ms (cache miss)
1366 http fetch GET 200 https://registry.npmjs.org/negotiator/-/negotiator-0.6.4.tgz 33111ms (cache miss)
1367 http fetch GET 200 https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz 33082ms (cache miss)
1368 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz 33089ms (cache miss)
1369 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz 33167ms (cache miss)
1370 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz 33115ms (cache miss)
1371 http fetch GET 200 https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz 33208ms (cache miss)
1372 http fetch GET 200 https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz 33637ms (cache miss)
1373 http fetch GET 200 https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz 33241ms (cache miss)
1374 http fetch GET 200 https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz 33194ms (cache miss)
1375 http fetch GET 200 https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz 33194ms (cache miss)
1376 http fetch GET 200 https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz 33246ms (cache miss)
1377 http fetch GET 200 https://registry.npmjs.org/better-opn/-/better-opn-3.0.2.tgz 33487ms (cache miss)
1378 http fetch GET 200 https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz 33268ms (cache miss)
1379 http fetch GET 200 https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz 33214ms (cache miss)
1380 http fetch GET 200 https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz 33288ms (cache miss)
1381 http fetch GET 200 https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz 33328ms (cache miss)
1382 http fetch GET 200 https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz 33276ms (cache miss)
1383 http fetch GET 200 https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz 33341ms (cache miss)
1384 http fetch GET 200 https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz 33235ms (cache miss)
1385 http fetch GET 200 https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz 33684ms (cache miss)
1386 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz 33510ms (cache miss)
1387 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz 33455ms (cache miss)
1388 http fetch GET 200 https://registry.npmjs.org/hermes-estree/-/hermes-estree-0.25.1.tgz 33912ms (cache miss)
1389 http fetch GET 200 https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz 33550ms (cache miss)
1390 http fetch GET 200 https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz 33547ms (cache miss)
1391 http fetch GET 200 https://registry.npmjs.org/commander/-/commander-2.20.3.tgz 34396ms (cache miss)
1392 http fetch GET 200 https://registry.npmjs.org/json5/-/json5-2.2.3.tgz 35408ms (cache miss)
1393 http fetch GET 200 https://registry.npmjs.org/plist/-/plist-3.1.0.tgz 35525ms (cache miss)
1394 warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
1395 http fetch GET 200 https://registry.npmjs.org/bplist-creator/-/bplist-creator-0.1.0.tgz 35375ms (cache miss)
1396 http fetch GET 200 https://registry.npmjs.org/cross-fetch/-/cross-fetch-3.2.0.tgz 35447ms (cache miss)
1397 warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
1398 http fetch GET 200 https://registry.npmjs.org/commander/-/commander-4.1.1.tgz 35333ms (cache miss)
1399 warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
1400 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz 38763ms (cache miss)
1401 warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
1402 http fetch GET 200 https://registry.npmjs.org/domhandler/-/domhandler-5.0.3.tgz 39316ms (cache miss)
1403 http fetch GET 200 https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz 39254ms (cache miss)
1404 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.28.0.tgz 39157ms (cache miss)
1405 http fetch GET 200 https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz 39690ms (cache miss)
1406 http fetch GET 200 https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz 39111ms (cache miss)
1407 http fetch GET 200 https://registry.npmjs.org/@0no-co/graphql.web/-/graphql.web-1.1.2.tgz 39168ms (cache miss)
1408 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.7.tgz 39336ms (cache miss)
1409 http fetch GET 200 https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.8.10.tgz 39514ms (cache miss)
1410 http fetch GET 200 https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz 39683ms (cache miss)
1411 http fetch GET 200 https://registry.npmjs.org/react/-/react-19.0.0.tgz 39811ms (cache miss)
1412 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-3.2.7.tgz 39369ms (cache miss)
1413 http fetch GET 200 https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz 39837ms (cache miss)
1414 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.28.0.tgz 39621ms (cache miss)
1415 http fetch GET 200 https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz 39870ms (cache miss)
1416 http fetch GET 200 https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz 39899ms (cache miss)
1417 http fetch GET 200 https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.13.0.tgz 40196ms (cache miss)
1418 http fetch GET 200 https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz 39957ms (cache miss)
1419 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.28.0.tgz 40114ms (cache miss)
1420 http fetch GET 200 https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz 40171ms (cache miss)
1421 http fetch GET 200 https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz 43180ms (cache miss)
1422 http fetch GET 200 https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz 42875ms (cache miss)
1423 http fetch GET 200 https://registry.npmjs.org/uuid/-/uuid-7.0.3.tgz 43497ms (cache miss)
1424 http fetch GET 200 https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz 43374ms (cache miss)
1425 http fetch GET 200 https://registry.npmjs.org/@expo/ws-tunnel/-/ws-tunnel-1.0.6.tgz 43197ms (cache miss)
1426 http fetch GET 200 https://registry.npmjs.org/undici-types/-/undici-types-7.8.0.tgz 43872ms (cache miss)
1427 http fetch GET 200 https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.6.tgz 44421ms (cache miss)
1428 http fetch GET 200 https://registry.npmjs.org/ws/-/ws-6.2.3.tgz 44191ms (cache miss)
1429 http fetch GET 200 https://registry.npmjs.org/@react-native/virtualized-lists/-/virtualized-lists-0.79.1.tgz 44376ms (cache miss)
1430 http fetch GET 200 https://registry.npmjs.org/commander/-/commander-12.1.0.tgz 44221ms (cache miss)
1431 http fetch GET 200 https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz 44524ms (cache miss)
1432 http fetch GET 200 https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz 44421ms (cache miss)
1433 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-2.6.9.tgz 44362ms (cache miss)
1434 http fetch GET 200 https://registry.npmjs.org/ws/-/ws-6.2.3.tgz 44299ms (cache miss)
1435 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-2.6.9.tgz 44370ms (cache miss)
1436 http fetch GET 200 https://registry.npmjs.org/ws/-/ws-6.2.3.tgz 44329ms (cache miss)
1437 http fetch GET 200 https://registry.npmjs.org/@urql/core/-/core-5.2.0.tgz 44619ms (cache miss)
1438 http fetch GET 200 https://registry.npmjs.org/pngjs/-/pngjs-3.4.0.tgz 44841ms (cache miss)
1439 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-2.6.9.tgz 44512ms (cache miss)
1440 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-2.6.9.tgz 44509ms (cache miss)
1441 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-2.6.9.tgz 44495ms (cache miss)
1442 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-2.6.9.tgz 44497ms (cache miss)
1443 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-2.6.9.tgz 44529ms (cache miss)
1444 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-2.6.9.tgz 44515ms (cache miss)
1445 http fetch GET 200 https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz 44704ms (cache miss)
1446 http fetch GET 200 https://registry.npmjs.org/ws/-/ws-7.5.10.tgz 45008ms (cache miss)
1447 http fetch GET 200 https://registry.npmjs.org/ws/-/ws-7.5.10.tgz 45038ms (cache miss)
1448 http fetch GET 200 https://registry.npmjs.org/chromium-edge-launcher/-/chromium-edge-launcher-0.2.0.tgz 45470ms (cache miss)
1449 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.28.0.tgz 45328ms (cache miss)
1450 http fetch GET 200 https://registry.npmjs.org/hermes-estree/-/hermes-estree-0.28.1.tgz 45280ms (cache miss)
1451 http fetch GET 200 https://registry.npmjs.org/hermes-estree/-/hermes-estree-0.28.1.tgz 45286ms (cache miss)
1452 http fetch GET 200 https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.43.0.tgz 45587ms (cache miss)
1453 http fetch GET 200 https://registry.npmjs.org/metro-source-map/-/metro-source-map-0.82.4.tgz 45966ms (cache miss)
1454 http fetch GET 200 https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz 47495ms (cache miss)
1455 http fetch GET 200 https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz 47423ms (cache miss)
1456 http fetch GET 200 https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz 47695ms (cache miss)
1457 http fetch GET 200 https://registry.npmjs.org/chrome-launcher/-/chrome-launcher-0.15.2.tgz 48651ms (cache miss)
1458 http fetch GET 200 https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.5.tgz 48550ms (cache miss)
1459 http fetch GET 200 https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz 49729ms (cache miss)
1460 http fetch GET 200 https://registry.npmjs.org/babel-preset-expo/-/babel-preset-expo-13.2.2.tgz 50500ms (cache miss)
1461 http fetch GET 200 https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.7.5.tgz 50761ms (cache miss)
1462 http fetch GET 200 https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz 50577ms (cache miss)
1463 http fetch GET 200 https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz 50614ms (cache miss)
1464 http fetch GET 200 https://registry.npmjs.org/metro-resolver/-/metro-resolver-0.82.4.tgz 51038ms (cache miss)
1465 http fetch GET 200 https://registry.npmjs.org/@types/react/-/react-19.0.14.tgz 50872ms (cache miss)
1466 http fetch GET 200 https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz 50719ms (cache miss)
1467 http fetch GET 200 https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz 50723ms (cache miss)
1468 http fetch GET 200 https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz 50897ms (cache miss)
1469 http fetch GET 200 https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz 50910ms (cache miss)
1470 http fetch GET 200 https://registry.npmjs.org/postcss/-/postcss-8.4.49.tgz 54223ms (cache miss)
1471 http fetch GET 200 https://registry.npmjs.org/@expo/package-manager/-/package-manager-1.8.5.tgz 54021ms (cache miss)
1472 http fetch GET 200 https://registry.npmjs.org/@react-navigation/routers/-/routers-7.4.1.tgz 54559ms (cache miss)
1473 http fetch GET 200 https://registry.npmjs.org/css-in-js-utils/-/css-in-js-utils-3.1.0.tgz 54912ms (cache miss)
1474 http fetch GET 200 https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz 54950ms (cache miss)
1475 http fetch GET 200 https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz 55698ms (cache miss)
1476 http fetch GET 200 https://registry.npmjs.org/@react-native/community-cli-plugin/-/community-cli-plugin-0.79.1.tgz 55579ms (cache miss)
1477 http fetch GET 200 https://registry.npmjs.org/@expo/devcert/-/devcert-1.2.0.tgz 55562ms (cache miss)
1478 http fetch GET 200 https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz 56362ms (cache miss)
1479 http fetch GET 200 https://registry.npmjs.org/promise/-/promise-7.3.1.tgz 56214ms (cache miss)
1480 http fetch GET 200 https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz 56197ms (cache miss)
1481 http fetch GET 200 https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz 56230ms (cache miss)
1482 http fetch GET 200 https://registry.npmjs.org/@expo/xcpretty/-/xcpretty-4.3.2.tgz 56410ms (cache miss)
1483 http fetch GET 200 https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz 56331ms (cache miss)
1484 http fetch GET 200 https://registry.npmjs.org/expo-constants/-/expo-constants-17.1.6.tgz 56611ms (cache miss)
1485 http fetch GET 200 https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz 56543ms (cache miss)
1486 http fetch GET 200 https://registry.npmjs.org/react-toastify/-/react-toastify-10.0.6.tgz 56903ms (cache miss)
1487 http fetch GET 200 https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz 56427ms (cache miss)
1488 http fetch GET 200 https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.25.1.tgz 62103ms (cache miss)
1489 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 61811ms (cache miss)
1490 http fetch GET 200 https://registry.npmjs.org/expo-haptics/-/expo-haptics-14.1.4.tgz 62096ms (cache miss)
1491 http fetch GET 200 https://registry.npmjs.org/@react-native/dev-middleware/-/dev-middleware-0.79.5.tgz 61989ms (cache miss)
1492 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.6.3.tgz 61810ms (cache miss)
1493 http fetch GET 200 https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz 61889ms (cache miss)
1494 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 61784ms (cache miss)
1495 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 61835ms (cache miss)
1496 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 61803ms (cache miss)
1497 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 61785ms (cache miss)
1498 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 61788ms (cache miss)
1499 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 61794ms (cache miss)
1500 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 61792ms (cache miss)
1501 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 61796ms (cache miss)
1502 http fetch GET 200 https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz 62307ms (cache miss)
1503 http fetch GET 200 https://registry.npmjs.org/@expo/config/-/config-11.0.11.tgz 62049ms (cache miss)
1504 http fetch GET 200 https://registry.npmjs.org/expo-keep-awake/-/expo-keep-awake-14.1.4.tgz 62223ms (cache miss)
1505 http fetch GET 200 https://registry.npmjs.org/terser/-/terser-5.43.1.tgz 62501ms (cache miss)
1506 http fetch GET 200 https://registry.npmjs.org/react-native-edge-to-edge/-/react-native-edge-to-edge-1.6.0.tgz 62484ms (cache miss)
1507 http fetch GET 200 https://registry.npmjs.org/glob/-/glob-10.4.5.tgz 62385ms (cache miss)
1508 http fetch GET 200 https://registry.npmjs.org/domutils/-/domutils-3.2.2.tgz 62371ms (cache miss)
1509 http fetch GET 200 https://registry.npmjs.org/@react-native/dev-middleware/-/dev-middleware-0.79.1.tgz 62046ms (cache miss)
1510 http fetch GET 200 https://registry.npmjs.org/expo-splash-screen/-/expo-splash-screen-0.30.9.tgz 62409ms (cache miss)
1511 http fetch GET 200 https://registry.npmjs.org/entities/-/entities-4.5.0.tgz 62575ms (cache miss)
1512 http fetch GET 200 https://registry.npmjs.org/expo-blur/-/expo-blur-14.1.5.tgz 64090ms (cache miss)
1513 http fetch GET 200 https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz 63942ms (cache miss)
1514 http fetch GET 200 https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz 64801ms (cache miss)
1515 http fetch GET 200 https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz 65057ms (cache miss)
1516 http fetch GET 200 https://registry.npmjs.org/expo-linear-gradient/-/expo-linear-gradient-14.1.5.tgz 65242ms (cache miss)
1517 http fetch GET 200 https://registry.npmjs.org/expo-system-ui/-/expo-system-ui-5.0.10.tgz 66049ms (cache miss)
1518 http fetch GET 200 https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz 66197ms (cache miss)
1519 http fetch GET 200 https://registry.npmjs.org/expo-web-browser/-/expo-web-browser-14.1.6.tgz 66141ms (cache miss)
1520 http fetch GET 200 https://registry.npmjs.org/@supabase/postgrest-js/-/postgrest-js-1.19.4.tgz 66092ms (cache miss)
1521 http fetch GET 200 https://registry.npmjs.org/@supabase/supabase-js/-/supabase-js-2.50.3.tgz 66213ms (cache miss)
1522 http fetch GET 200 https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.28.1.tgz 66044ms (cache miss)
1523 http fetch GET 200 https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.28.1.tgz 66112ms (cache miss)
1524 http fetch GET 200 https://registry.npmjs.org/@react-navigation/native-stack/-/native-stack-7.3.21.tgz 66436ms (cache miss)
1525 http fetch GET 200 https://registry.npmjs.org/metro-file-map/-/metro-file-map-0.82.4.tgz 66738ms (cache miss)
1526 http fetch GET 200 https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz 67724ms (cache miss)
1527 http fetch GET 200 https://registry.npmjs.org/@supabase/realtime-js/-/realtime-js-2.11.15.tgz 67504ms (cache miss)
1528 http fetch GET 200 https://registry.npmjs.org/expo-linking/-/expo-linking-7.1.6.tgz 67913ms (cache miss)
1529 http fetch GET 200 https://registry.npmjs.org/@expo/fingerprint/-/fingerprint-0.13.3.tgz 68100ms (cache miss)
1530 http fetch GET 200 https://registry.npmjs.org/@react-navigation/bottom-tabs/-/bottom-tabs-7.4.2.tgz 68192ms (cache miss)
1531 http fetch GET 200 https://registry.npmjs.org/@types/node/-/node-24.0.10.tgz 72435ms (cache miss)
1532 http fetch GET 200 https://registry.npmjs.org/@supabase/storage-js/-/storage-js-2.7.1.tgz 72493ms (cache miss)
1533 http fetch GET 200 https://registry.npmjs.org/css-select/-/css-select-5.2.2.tgz 72849ms (cache miss)
1534 http fetch GET 200 https://registry.npmjs.org/react-native-webview/-/react-native-webview-13.13.5.tgz 73473ms (cache miss)
1535 http fetch GET 200 https://registry.npmjs.org/@expo-google-fonts/ma-shan-zheng/-/ma-shan-zheng-0.2.3.tgz 73336ms (cache miss)
1536 http fetch GET 200 https://registry.npmjs.org/@egjs/hammerjs/-/hammerjs-2.0.17.tgz 73358ms (cache miss)
1537 http fetch GET 200 https://registry.npmjs.org/react-dom/-/react-dom-19.0.0.tgz 76414ms (cache miss)
1538 http fetch GET 200 https://registry.npmjs.org/expo-font/-/expo-font-13.2.2.tgz 76535ms (cache miss)
1539 http fetch GET 200 https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz 77067ms (cache miss)
1540 http fetch GET 200 https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz 77937ms (cache miss)
1541 http fetch GET 200 https://registry.npmjs.org/@react-native/gradle-plugin/-/gradle-plugin-0.79.1.tgz 77870ms (cache miss)
1542 http fetch GET 200 https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.27.0.tgz 78077ms (cache miss)
1543 http fetch GET 200 https://registry.npmjs.org/@react-native-async-storage/async-storage/-/async-storage-2.2.0.tgz 77894ms (cache miss)
1544 http fetch GET 200 https://registry.npmjs.org/expo-font/-/expo-font-13.3.2.tgz 77718ms (cache miss)
1545 http fetch GET 200 https://registry.npmjs.org/@expo/metro-config/-/metro-config-0.20.16.tgz 77955ms (cache miss)
1546 http fetch GET 200 https://registry.npmjs.org/expo-asset/-/expo-asset-11.1.6.tgz 78182ms (cache miss)
1547 http fetch GET 200 https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz 78165ms (cache miss)
1548 http fetch GET 200 https://registry.npmjs.org/expo-file-system/-/expo-file-system-18.1.11.tgz 78318ms (cache miss)
1549 http fetch GET 200 https://registry.npmjs.org/@react-navigation/native/-/native-7.1.14.tgz 78312ms (cache miss)
1550 http fetch GET 200 https://registry.npmjs.org/@supabase/auth-js/-/auth-js-2.70.0.tgz 78354ms (cache miss)
1551 http fetch GET 200 https://registry.npmjs.org/leaflet/-/leaflet-1.9.4.tgz 78656ms (cache miss)
1552 http fetch GET 200 https://registry.npmjs.org/@expo/prebuild-config/-/prebuild-config-9.0.9.tgz 78596ms (cache miss)
1553 http fetch GET 200 https://registry.npmjs.org/expo-modules-autolinking/-/expo-modules-autolinking-2.1.13.tgz 78829ms (cache miss)
1554 http fetch GET 200 https://registry.npmjs.org/react-native-safe-area-context/-/react-native-safe-area-context-5.3.0.tgz 79043ms (cache miss)
1555 http fetch GET 200 https://registry.npmjs.org/undici/-/undici-6.21.3.tgz 79411ms (cache miss)
1556 http fetch GET 200 https://registry.npmjs.org/expo-camera/-/expo-camera-16.1.10.tgz 79171ms (cache miss)
1557 http fetch GET 200 https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz 79723ms (cache miss)
1558 http fetch GET 200 https://registry.npmjs.org/@react-navigation/elements/-/elements-2.5.2.tgz 85134ms (cache miss)
1559 http fetch GET 200 https://registry.npmjs.org/@expo/metro-runtime/-/metro-runtime-5.0.4.tgz 85758ms (cache miss)
1560 http fetch GET 200 https://registry.npmjs.org/@babel/types/-/types-7.28.0.tgz 85871ms (cache miss)
1561 http fetch GET 200 https://registry.npmjs.org/tar/-/tar-7.4.3.tgz 86492ms (cache miss)
1562 http fetch GET 200 https://registry.npmjs.org/@react-native/codegen/-/codegen-0.79.5.tgz 86153ms (cache miss)
1563 http fetch GET 200 https://registry.npmjs.org/fbjs/-/fbjs-3.0.5.tgz 86342ms (cache miss)
1564 http fetch GET 200 https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz 86177ms (cache miss)
1565 http fetch GET 200 https://registry.npmjs.org/@react-native/codegen/-/codegen-0.79.1.tgz 86093ms (cache miss)
1566 http fetch GET 200 https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz 86631ms (cache miss)
1567 http fetch GET 200 https://registry.npmjs.org/react-devtools-core/-/react-devtools-core-6.1.3.tgz 87082ms (cache miss)
1568 http fetch GET 200 https://registry.npmjs.org/@expo/config-plugins/-/config-plugins-10.0.3.tgz 87865ms (cache miss)
1569 http fetch GET 200 https://registry.npmjs.org/@expo/config-plugins/-/config-plugins-10.1.0.tgz 88087ms (cache miss)
1570 http fetch GET 200 https://registry.npmjs.org/metro/-/metro-0.82.4.tgz 93898ms (cache miss)
1571 http fetch GET 200 https://registry.npmjs.org/@expo/vector-icons/-/vector-icons-14.1.0.tgz 93710ms (cache miss)
1572 http fetch GET 200 https://registry.npmjs.org/expo/-/expo-53.0.16.tgz 93965ms (cache miss)
1573 http fetch GET 200 https://registry.npmjs.org/@react-navigation/core/-/core-7.12.1.tgz 94788ms (cache miss)
1574 http fetch GET 200 https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz 98328ms (cache miss)
1575 http fetch GET 200 https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz 99168ms (cache miss)
1576 http fetch GET 200 https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz 100064ms (cache miss)
1577 http fetch GET 200 https://registry.npmjs.org/expo-router/-/expo-router-5.0.7.tgz 105505ms (cache miss)
1578 http fetch GET 200 https://registry.npmjs.org/react-native-screens/-/react-native-screens-4.10.0.tgz 111256ms (cache miss)
1579 http fetch GET 200 https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001726.tgz 112198ms (cache miss)
1580 http fetch GET 200 https://registry.npmjs.org/expo-modules-core/-/expo-modules-core-2.4.2.tgz 116505ms (cache miss)
1581 http fetch GET 200 https://registry.npmjs.org/@lucide/lab/-/lab-0.1.2.tgz 117358ms (cache miss)
1582 http fetch GET 200 https://registry.npmjs.org/@react-native/debugger-frontend/-/debugger-frontend-0.79.5.tgz 117764ms (cache miss)
1583 http fetch GET 200 https://registry.npmjs.org/react-native-gesture-handler/-/react-native-gesture-handler-2.24.0.tgz 118059ms (cache miss)
1584 http fetch GET 200 https://registry.npmjs.org/@react-native/debugger-frontend/-/debugger-frontend-0.79.1.tgz 117587ms (cache miss)
1585 http fetch GET 200 https://registry.npmjs.org/@expo/cli/-/cli-0.24.17.tgz 117806ms (cache miss)
1586 http fetch GET 200 https://registry.npmjs.org/react-native-web/-/react-native-web-0.20.0.tgz 118279ms (cache miss)
1587 http fetch GET 200 https://registry.npmjs.org/react-native-reanimated/-/react-native-reanimated-3.17.5.tgz 122022ms (cache miss)
1588 http fetch GET 200 https://registry.npmjs.org/react-native-svg/-/react-native-svg-15.11.2.tgz 124212ms (cache miss)
1589 http fetch GET 200 https://registry.npmjs.org/react-native/-/react-native-0.79.1.tgz 138577ms (cache miss)
1590 http fetch GET 200 https://registry.npmjs.org/lucide-react-native/-/lucide-react-native-0.475.0.tgz 140050ms (cache miss)
1591 silly ADD node_modules/yocto-queue
1592 silly ADD node_modules/yargs-parser
1593 silly ADD node_modules/yargs
1594 silly ADD node_modules/yargs/node_modules/strip-ansi
1595 silly ADD node_modules/yargs/node_modules/string-width
1596 silly ADD node_modules/yargs/node_modules/emoji-regex
1597 silly ADD node_modules/yallist
1598 silly ADD node_modules/y18n
1599 silly ADD node_modules/xmlbuilder
1600 silly ADD node_modules/xml2js
1601 silly ADD node_modules/xml2js/node_modules/xmlbuilder
1602 silly ADD node_modules/xcode
1603 silly ADD node_modules/ws
1604 silly ADD node_modules/write-file-atomic
1605 silly ADD node_modules/write-file-atomic/node_modules/signal-exit
1606 silly ADD node_modules/wrappy
1607 silly ADD node_modules/wrap-ansi-cjs
1608 silly ADD node_modules/wrap-ansi-cjs/node_modules/strip-ansi
1609 silly ADD node_modules/wrap-ansi-cjs/node_modules/string-width
1610 silly ADD node_modules/wrap-ansi-cjs/node_modules/emoji-regex
1611 silly ADD node_modules/wrap-ansi
1612 silly ADD node_modules/wrap-ansi/node_modules/strip-ansi
1613 silly ADD node_modules/wrap-ansi/node_modules/string-width
1614 silly ADD node_modules/wrap-ansi/node_modules/emoji-regex
1615 silly ADD node_modules/wonka
1616 silly ADD node_modules/which
1617 silly ADD node_modules/whatwg-url-without-unicode
1618 silly ADD node_modules/whatwg-url-without-unicode/node_modules/webidl-conversions
1619 silly ADD node_modules/whatwg-url
1620 silly ADD node_modules/whatwg-fetch
1621 silly ADD node_modules/webidl-conversions
1622 silly ADD node_modules/wcwidth
1623 silly ADD node_modules/warn-once
1624 silly ADD node_modules/walker
1625 silly ADD node_modules/vlq
1626 silly ADD node_modules/vary
1627 silly ADD node_modules/validate-npm-package-name
1628 silly ADD node_modules/uuid
1629 silly ADD node_modules/utils-merge
1630 silly ADD node_modules/use-sync-external-store
1631 silly ADD node_modules/use-latest-callback
1632 silly ADD node_modules/update-browserslist-db
1633 silly ADD node_modules/unpipe
1634 silly ADD node_modules/unique-string
1635 silly ADD node_modules/unicode-property-aliases-ecmascript
1636 silly ADD node_modules/unicode-match-property-value-ecmascript
1637 silly ADD node_modules/unicode-match-property-ecmascript
1638 silly ADD node_modules/unicode-canonical-property-names-ecmascript
1639 silly ADD node_modules/undici-types
1640 silly ADD node_modules/undici
1641 silly ADD node_modules/ua-parser-js
1642 silly ADD node_modules/typescript
1643 silly ADD node_modules/type-fest
1644 silly ADD node_modules/type-detect
1645 silly ADD node_modules/ts-interface-checker
1646 silly ADD node_modules/tr46
1647 silly ADD node_modules/toidentifier
1648 silly ADD node_modules/to-regex-range
1649 silly ADD node_modules/tmpl
1650 silly ADD node_modules/throat
1651 silly ADD node_modules/thenify-all
1652 silly ADD node_modules/thenify
1653 silly ADD node_modules/test-exclude
1654 silly ADD node_modules/test-exclude/node_modules/minimatch
1655 silly ADD node_modules/test-exclude/node_modules/glob
1656 silly ADD node_modules/test-exclude/node_modules/brace-expansion
1657 silly ADD node_modules/terser
1658 silly ADD node_modules/terser/node_modules/commander
1659 silly ADD node_modules/terminal-link
1660 silly ADD node_modules/temp-dir
1661 silly ADD node_modules/tar
1662 silly ADD node_modules/tar/node_modules/yallist
1663 silly ADD node_modules/tar/node_modules/mkdirp
1664 silly ADD node_modules/supports-preserve-symlinks-flag
1665 silly ADD node_modules/supports-hyperlinks
1666 silly ADD node_modules/supports-color
1667 silly ADD node_modules/sucrase
1668 silly ADD node_modules/sucrase/node_modules/commander
1669 silly ADD node_modules/styleq
1670 silly ADD node_modules/structured-headers
1671 silly ADD node_modules/strip-json-comments
1672 silly ADD node_modules/strip-ansi-cjs
1673 silly ADD node_modules/strip-ansi
1674 silly ADD node_modules/strip-ansi/node_modules/ansi-regex
1675 silly ADD node_modules/string-width-cjs
1676 silly ADD node_modules/string-width-cjs/node_modules/strip-ansi
1677 silly ADD node_modules/string-width-cjs/node_modules/emoji-regex
1678 silly ADD node_modules/string-width
1679 silly ADD node_modules/strict-uri-encode
1680 silly ADD node_modules/stream-buffers
1681 silly ADD node_modules/statuses
1682 silly ADD node_modules/stacktrace-parser
1683 silly ADD node_modules/stackframe
1684 silly ADD node_modules/stack-utils
1685 silly ADD node_modules/stack-utils/node_modules/escape-string-regexp
1686 silly ADD node_modules/sprintf-js
1687 silly ADD node_modules/split-on-first
1688 silly ADD node_modules/source-map-support
1689 silly ADD node_modules/source-map-support/node_modules/source-map
1690 silly ADD node_modules/source-map-js
1691 silly ADD node_modules/source-map
1692 silly ADD node_modules/slugify
1693 silly ADD node_modules/slash
1694 silly ADD node_modules/sisteransi
1695 silly ADD node_modules/simple-swizzle
1696 silly ADD node_modules/simple-swizzle/node_modules/is-arrayish
1697 silly ADD node_modules/simple-plist
1698 silly ADD node_modules/simple-plist/node_modules/bplist-parser
1699 silly ADD node_modules/signal-exit
1700 silly ADD node_modules/shell-quote
1701 silly ADD node_modules/shebang-regex
1702 silly ADD node_modules/shebang-command
1703 silly ADD node_modules/shallowequal
1704 silly ADD node_modules/sf-symbols-typescript
1705 silly ADD node_modules/setprototypeof
1706 silly ADD node_modules/setimmediate
1707 silly ADD node_modules/server-only
1708 silly ADD node_modules/serve-static
1709 silly ADD node_modules/serve-static/node_modules/statuses
1710 silly ADD node_modules/serve-static/node_modules/send
1711 silly ADD node_modules/serve-static/node_modules/send/node_modules/encodeurl
1712 silly ADD node_modules/serve-static/node_modules/on-finished
1713 silly ADD node_modules/serve-static/node_modules/encodeurl
1714 silly ADD node_modules/serve-static/node_modules/debug
1715 silly ADD node_modules/serve-static/node_modules/debug/node_modules/ms
1716 silly ADD node_modules/serialize-error
1717 silly ADD node_modules/send
1718 silly ADD node_modules/send/node_modules/statuses
1719 silly ADD node_modules/send/node_modules/on-finished
1720 silly ADD node_modules/send/node_modules/encodeurl
1721 silly ADD node_modules/send/node_modules/debug
1722 silly ADD node_modules/send/node_modules/debug/node_modules/ms
1723 silly ADD node_modules/semver
1724 silly ADD node_modules/schema-utils
1725 silly ADD node_modules/scheduler
1726 silly ADD node_modules/sax
1727 silly ADD node_modules/safe-buffer
1728 silly ADD node_modules/rimraf
1729 silly ADD node_modules/rimraf/node_modules/minimatch
1730 silly ADD node_modules/rimraf/node_modules/glob
1731 silly ADD node_modules/rimraf/node_modules/brace-expansion
1732 silly ADD node_modules/restore-cursor
1733 silly ADD node_modules/restore-cursor/node_modules/signal-exit
1734 silly ADD node_modules/resolve.exports
1735 silly ADD node_modules/resolve-workspace-root
1736 silly ADD node_modules/resolve-from
1737 silly ADD node_modules/resolve
1738 silly ADD node_modules/requireg
1739 silly ADD node_modules/requireg/node_modules/resolve
1740 silly ADD node_modules/require-from-string
1741 silly ADD node_modules/require-directory
1742 silly ADD node_modules/regjsparser
1743 silly ADD node_modules/regjsparser/node_modules/jsesc
1744 silly ADD node_modules/regjsgen
1745 silly ADD node_modules/regexpu-core
1746 silly ADD node_modules/regenerator-runtime
1747 silly ADD node_modules/regenerate-unicode-properties
1748 silly ADD node_modules/regenerate
1749 silly ADD node_modules/react-toastify
1750 silly ADD node_modules/react-refresh
1751 silly ADD node_modules/react-native-webview
1752 silly ADD node_modules/react-native-web
1753 silly ADD node_modules/react-native-web/node_modules/memoize-one
1754 silly ADD node_modules/react-native-web/node_modules/@react-native/normalize-colors
1755 silly ADD node_modules/react-native-url-polyfill
1756 silly ADD node_modules/react-native-svg
1757 silly ADD node_modules/react-native-screens
1758 silly ADD node_modules/react-native-safe-area-context
1759 silly ADD node_modules/react-native-reanimated
1760 silly ADD node_modules/react-native-reanimated/node_modules/react-native-is-edge-to-edge
1761 silly ADD node_modules/react-native-is-edge-to-edge
1762 silly ADD node_modules/react-native-gesture-handler
1763 silly ADD node_modules/react-native-edge-to-edge
1764 silly ADD node_modules/react-native
1765 silly ADD node_modules/react-native/node_modules/ws
1766 silly ADD node_modules/react-native/node_modules/semver
1767 silly ADD node_modules/react-native/node_modules/minimatch
1768 silly ADD node_modules/react-native/node_modules/glob
1769 silly ADD node_modules/react-native/node_modules/commander
1770 silly ADD node_modules/react-native/node_modules/brace-expansion
1771 silly ADD node_modules/react-native/node_modules/@react-native/normalize-colors
1772 silly ADD node_modules/react-native/node_modules/@react-native/codegen
1773 silly ADD node_modules/react-leaflet
1774 silly ADD node_modules/react-is
1775 silly ADD node_modules/react-freeze
1776 silly ADD node_modules/react-fast-compare
1777 silly ADD node_modules/react-dom
1778 silly ADD node_modules/react-devtools-core
1779 silly ADD node_modules/react-devtools-core/node_modules/ws
1780 silly ADD node_modules/react
1781 silly ADD node_modules/rc
1782 silly ADD node_modules/range-parser
1783 silly ADD node_modules/queue
1784 silly ADD node_modules/query-string
1785 silly ADD node_modules/qrcode-terminal
1786 silly ADD node_modules/punycode
1787 silly ADD node_modules/prompts
1788 silly ADD node_modules/promise
1789 silly ADD node_modules/progress
1790 silly ADD node_modules/proc-log
1791 silly ADD node_modules/pretty-format
1792 silly ADD node_modules/pretty-format/node_modules/react-is
1793 silly ADD node_modules/pretty-format/node_modules/ansi-styles
1794 silly ADD node_modules/pretty-bytes
1795 silly ADD node_modules/postcss-value-parser
1796 silly ADD node_modules/postcss
1797 silly ADD node_modules/pngjs
1798 silly ADD node_modules/plist
1799 silly ADD node_modules/pirates
1800 silly ADD node_modules/picomatch
1801 silly ADD node_modules/picocolors
1802 silly ADD node_modules/path-scurry
1803 silly ADD node_modules/path-scurry/node_modules/lru-cache
1804 silly ADD node_modules/path-parse
1805 silly ADD node_modules/path-key
1806 silly ADD node_modules/path-is-absolute
1807 silly ADD node_modules/path-exists
1808 silly ADD node_modules/parseurl
1809 silly ADD node_modules/parse-png
1810 silly ADD node_modules/parse-json
1811 silly ADD node_modules/package-json-from-dist
1812 silly ADD node_modules/p-try
1813 silly ADD node_modules/p-locate
1814 silly ADD node_modules/p-limit
1815 silly ADD node_modules/ora
1816 silly ADD node_modules/ora/node_modules/supports-color
1817 silly ADD node_modules/ora/node_modules/strip-ansi
1818 silly ADD node_modules/ora/node_modules/has-flag
1819 silly ADD node_modules/ora/node_modules/escape-string-regexp
1820 silly ADD node_modules/ora/node_modules/color-name
1821 silly ADD node_modules/ora/node_modules/color-convert
1822 silly ADD node_modules/ora/node_modules/chalk
1823 silly ADD node_modules/ora/node_modules/ansi-styles
1824 silly ADD node_modules/ora/node_modules/ansi-regex
1825 silly ADD node_modules/open
1826 silly ADD node_modules/onetime
1827 silly ADD node_modules/once
1828 silly ADD node_modules/on-headers
1829 silly ADD node_modules/on-finished
1830 silly ADD node_modules/object-assign
1831 silly ADD node_modules/ob1
1832 silly ADD node_modules/nullthrows
1833 silly ADD node_modules/nth-check
1834 silly ADD node_modules/npm-package-arg
1835 silly ADD node_modules/npm-package-arg/node_modules/semver
1836 silly ADD node_modules/normalize-path
1837 silly ADD node_modules/node-releases
1838 silly ADD node_modules/node-int64
1839 silly ADD node_modules/node-forge
1840 silly ADD node_modules/node-fetch
1841 silly ADD node_modules/nested-error-stacks
1842 silly ADD node_modules/negotiator
1843 silly ADD node_modules/nanoid
1844 silly ADD node_modules/mz
1845 silly ADD node_modules/ms
1846 silly ADD node_modules/mkdirp
1847 silly ADD node_modules/minizlib
1848 silly ADD node_modules/minipass
1849 silly ADD node_modules/minimist
1850 silly ADD node_modules/minimatch
1851 silly ADD node_modules/mimic-fn
1852 silly ADD node_modules/mime-types
1853 silly ADD node_modules/mime-types/node_modules/mime-db
1854 silly ADD node_modules/mime-db
1855 silly ADD node_modules/mime
1856 silly ADD node_modules/micromatch
1857 silly ADD node_modules/micromatch/node_modules/picomatch
1858 silly ADD node_modules/metro-transform-worker
1859 silly ADD node_modules/metro-transform-plugins
1860 silly ADD node_modules/metro-symbolicate
1861 silly ADD node_modules/metro-source-map
1862 silly ADD node_modules/metro-runtime
1863 silly ADD node_modules/metro-resolver
1864 silly ADD node_modules/metro-minify-terser
1865 silly ADD node_modules/metro-file-map
1866 silly ADD node_modules/metro-core
1867 silly ADD node_modules/metro-config
1868 silly ADD node_modules/metro-cache-key
1869 silly ADD node_modules/metro-cache
1870 silly ADD node_modules/metro-babel-transformer
1871 silly ADD node_modules/metro-babel-transformer/node_modules/hermes-parser
1872 silly ADD node_modules/metro-babel-transformer/node_modules/hermes-estree
1873 silly ADD node_modules/metro
1874 silly ADD node_modules/metro/node_modules/ws
1875 silly ADD node_modules/metro/node_modules/hermes-parser
1876 silly ADD node_modules/metro/node_modules/hermes-estree
1877 silly ADD node_modules/metro/node_modules/ci-info
1878 silly ADD node_modules/merge-stream
1879 silly ADD node_modules/merge-options
1880 silly ADD node_modules/memoize-one
1881 silly ADD node_modules/mdn-data
1882 silly ADD node_modules/marky
1883 silly ADD node_modules/makeerror
1884 silly ADD node_modules/lucide-react-native
1885 silly ADD node_modules/lru-cache
1886 silly ADD node_modules/loose-envify
1887 silly ADD node_modules/log-symbols
1888 silly ADD node_modules/log-symbols/node_modules/supports-color
1889 silly ADD node_modules/log-symbols/node_modules/has-flag
1890 silly ADD node_modules/log-symbols/node_modules/escape-string-regexp
1891 silly ADD node_modules/log-symbols/node_modules/color-name
1892 silly ADD node_modules/log-symbols/node_modules/color-convert
1893 silly ADD node_modules/log-symbols/node_modules/chalk
1894 silly ADD node_modules/log-symbols/node_modules/ansi-styles
1895 silly ADD node_modules/lodash.throttle
1896 silly ADD node_modules/lodash.debounce
1897 silly ADD node_modules/locate-path
1898 silly ADD node_modules/lines-and-columns
1899 silly ADD node_modules/lightningcss-win32-x64-msvc
1900 silly ADD
1901 silly ADD
1902 silly ADD
1903 silly ADD
1904 silly ADD
1905 silly ADD
1906 silly ADD
1907 silly ADD
1908 silly ADD
1909 silly ADD node_modules/lightningcss
1910 silly ADD node_modules/lighthouse-logger
1911 silly ADD node_modules/lighthouse-logger/node_modules/ms
1912 silly ADD node_modules/lighthouse-logger/node_modules/debug
1913 silly ADD node_modules/leven
1914 silly ADD node_modules/leaflet
1915 silly ADD node_modules/lan-network
1916 silly ADD node_modules/kleur
1917 silly ADD node_modules/json5
1918 silly ADD node_modules/json-schema-traverse
1919 silly ADD node_modules/json-parse-better-errors
1920 silly ADD node_modules/jsesc
1921 silly ADD node_modules/jsc-safe-url
1922 silly ADD node_modules/js-yaml
1923 silly ADD node_modules/js-tokens
1924 silly ADD node_modules/jimp-compact
1925 silly ADD node_modules/jest-worker
1926 silly ADD node_modules/jest-worker/node_modules/supports-color
1927 silly ADD node_modules/jest-validate
1928 silly ADD node_modules/jest-validate/node_modules/camelcase
1929 silly ADD node_modules/jest-util
1930 silly ADD node_modules/jest-util/node_modules/picomatch
1931 silly ADD node_modules/jest-regex-util
1932 silly ADD node_modules/jest-mock
1933 silly ADD node_modules/jest-message-util
1934 silly ADD node_modules/jest-haste-map
1935 silly ADD node_modules/jest-get-type
1936 silly ADD node_modules/jest-environment-node
1937 silly ADD node_modules/jackspeak
1938 silly ADD node_modules/istanbul-lib-instrument
1939 silly ADD node_modules/istanbul-lib-coverage
1940 silly ADD node_modules/isows
1941 silly ADD node_modules/isexe
1942 silly ADD node_modules/is-wsl
1943 silly ADD node_modules/is-plain-obj
1944 silly ADD node_modules/is-number
1945 silly ADD node_modules/is-fullwidth-code-point
1946 silly ADD node_modules/is-docker
1947 silly ADD node_modules/is-directory
1948 silly ADD node_modules/is-core-module
1949 silly ADD node_modules/is-arrayish
1950 silly ADD node_modules/invariant
1951 silly ADD node_modules/inline-style-prefixer
1952 silly ADD node_modules/ini
1953 silly ADD node_modules/inherits
1954 silly ADD node_modules/inflight
1955 silly ADD node_modules/imurmurhash
1956 silly ADD node_modules/import-fresh
1957 silly ADD node_modules/import-fresh/node_modules/resolve-from
1958 silly ADD node_modules/image-size
1959 silly ADD node_modules/ignore
1960 silly ADD node_modules/ieee754
1961 silly ADD node_modules/hyphenate-style-name
1962 silly ADD node_modules/https-proxy-agent
1963 silly ADD node_modules/http-errors
1964 silly ADD node_modules/http-errors/node_modules/statuses
1965 silly ADD node_modules/hosted-git-info
1966 silly ADD node_modules/hosted-git-info/node_modules/lru-cache
1967 silly ADD node_modules/hoist-non-react-statics
1968 silly ADD node_modules/hoist-non-react-statics/node_modules/react-is
1969 silly ADD node_modules/hermes-parser
1970 silly ADD node_modules/hermes-estree
1971 silly ADD node_modules/hasown
1972 silly ADD node_modules/has-flag
1973 silly ADD node_modules/graceful-fs
1974 silly ADD node_modules/glob
1975 silly ADD node_modules/getenv
1976 silly ADD node_modules/get-package-type
1977 silly ADD node_modules/get-caller-file
1978 silly ADD node_modules/gensync
1979 silly ADD node_modules/function-bind
1980 silly ADD
1981 silly ADD node_modules/fs.realpath
1982 silly ADD node_modules/fresh
1983 silly ADD node_modules/freeport-async
1984 silly ADD node_modules/foreground-child
1985 silly ADD node_modules/fontfaceobserver
1986 silly ADD node_modules/flow-enums-runtime
1987 silly ADD node_modules/find-up
1988 silly ADD node_modules/finalhandler
1989 silly ADD node_modules/finalhandler/node_modules/ms
1990 silly ADD node_modules/finalhandler/node_modules/debug
1991 silly ADD node_modules/filter-obj
1992 silly ADD node_modules/fill-range
1993 silly ADD node_modules/fbjs-css-vars
1994 silly ADD node_modules/fbjs
1995 silly ADD node_modules/fbjs/node_modules/promise
1996 silly ADD node_modules/fb-watchman
1997 silly ADD node_modules/fast-uri
1998 silly ADD node_modules/fast-json-stable-stringify
1999 silly ADD node_modules/fast-deep-equal
2000 silly ADD node_modules/exponential-backoff
2001 silly ADD node_modules/expo-web-browser
2002 silly ADD node_modules/expo-system-ui
2003 silly ADD node_modules/expo-symbols
2004 silly ADD node_modules/expo-status-bar
2005 silly ADD node_modules/expo-splash-screen
2006 silly ADD node_modules/expo-router
2007 silly ADD node_modules/expo-router/node_modules/semver
2008 silly ADD node_modules/expo-modules-core
2009 silly ADD node_modules/expo-modules-autolinking
2010 silly ADD node_modules/expo-linking
2011 silly ADD node_modules/expo-linear-gradient
2012 silly ADD node_modules/expo-keep-awake
2013 silly ADD node_modules/expo-haptics
2014 silly ADD node_modules/expo-font
2015 silly ADD node_modules/expo-file-system
2016 silly ADD node_modules/expo-constants
2017 silly ADD node_modules/expo-camera
2018 silly ADD node_modules/expo-blur
2019 silly ADD node_modules/expo-asset
2020 silly ADD node_modules/expo
2021 silly ADD node_modules/expo/node_modules/expo-font
2022 silly ADD node_modules/exec-async
2023 silly ADD node_modules/event-target-shim
2024 silly ADD node_modules/etag
2025 silly ADD node_modules/esprima
2026 silly ADD node_modules/escape-string-regexp
2027 silly ADD node_modules/escape-html
2028 silly ADD node_modules/escalade
2029 silly ADD node_modules/error-stack-parser
2030 silly ADD node_modules/error-ex
2031 silly ADD node_modules/env-editor
2032 silly ADD node_modules/entities
2033 silly ADD node_modules/encodeurl
2034 silly ADD node_modules/emoji-regex
2035 silly ADD node_modules/electron-to-chromium
2036 silly ADD node_modules/ee-first
2037 silly ADD node_modules/eastasianwidth
2038 silly ADD node_modules/dotenv-expand
2039 silly ADD node_modules/dotenv
2040 silly ADD node_modules/domutils
2041 silly ADD node_modules/domhandler
2042 silly ADD node_modules/domelementtype
2043 silly ADD node_modules/dom-serializer
2044 silly ADD node_modules/detect-libc
2045 silly ADD node_modules/destroy
2046 silly ADD node_modules/depd
2047 silly ADD node_modules/define-lazy-prop
2048 silly ADD node_modules/defaults
2049 silly ADD node_modules/deepmerge
2050 silly ADD node_modules/deep-extend
2051 silly ADD node_modules/decode-uri-component
2052 silly ADD node_modules/debug
2053 silly ADD node_modules/csstype
2054 silly ADD node_modules/css-what
2055 silly ADD node_modules/css-tree
2056 silly ADD node_modules/css-tree/node_modules/source-map
2057 silly ADD node_modules/css-select
2058 silly ADD node_modules/css-in-js-utils
2059 silly ADD node_modules/crypto-random-string
2060 silly ADD node_modules/cross-spawn
2061 silly ADD node_modules/cross-fetch
2062 silly ADD node_modules/cosmiconfig
2063 silly ADD node_modules/cosmiconfig/node_modules/js-yaml
2064 silly ADD node_modules/cosmiconfig/node_modules/argparse
2065 silly ADD node_modules/core-js-compat
2066 silly ADD node_modules/convert-source-map
2067 silly ADD node_modules/connect
2068 silly ADD node_modules/connect/node_modules/ms
2069 silly ADD node_modules/connect/node_modules/debug
2070 silly ADD node_modules/concat-map
2071 silly ADD node_modules/compression
2072 silly ADD node_modules/compression/node_modules/negotiator
2073 silly ADD node_modules/compression/node_modules/ms
2074 silly ADD node_modules/compression/node_modules/debug
2075 silly ADD node_modules/compressible
2076 silly ADD node_modules/commander
2077 silly ADD node_modules/color-string
2078 silly ADD node_modules/color-name
2079 silly ADD node_modules/color-convert
2080 silly ADD node_modules/color
2081 silly ADD node_modules/clsx
2082 silly ADD node_modules/clone
2083 silly ADD node_modules/cliui
2084 silly ADD node_modules/cliui/node_modules/strip-ansi
2085 silly ADD node_modules/cliui/node_modules/string-width
2086 silly ADD node_modules/cliui/node_modules/emoji-regex
2087 silly ADD node_modules/client-only
2088 silly ADD node_modules/cli-spinners
2089 silly ADD node_modules/cli-cursor
2090 silly ADD node_modules/ci-info
2091 silly ADD node_modules/chromium-edge-launcher
2092 silly ADD node_modules/chrome-launcher
2093 silly ADD node_modules/chownr
2094 silly ADD node_modules/chalk
2095 silly ADD node_modules/caniuse-lite
2096 silly ADD node_modules/camelcase
2097 silly ADD node_modules/callsites
2098 silly ADD node_modules/caller-path
2099 silly ADD node_modules/caller-callsite
2100 silly ADD node_modules/bytes
2101 silly ADD node_modules/buffer-from
2102 silly ADD node_modules/buffer
2103 silly ADD node_modules/bser
2104 silly ADD node_modules/browserslist
2105 silly ADD node_modules/braces
2106 silly ADD node_modules/brace-expansion
2107 silly ADD node_modules/bplist-parser
2108 silly ADD node_modules/bplist-creator
2109 silly ADD node_modules/boolbase
2110 silly ADD node_modules/big-integer
2111 silly ADD node_modules/better-opn
2112 silly ADD node_modules/better-opn/node_modules/open
2113 silly ADD node_modules/base64-js
2114 silly ADD node_modules/balanced-match
2115 silly ADD node_modules/babel-preset-jest
2116 silly ADD node_modules/babel-preset-expo
2117 silly ADD node_modules/babel-preset-current-node-syntax
2118 silly ADD node_modules/babel-plugin-transform-flow-enums
2119 silly ADD node_modules/babel-plugin-syntax-hermes-parser
2120 silly ADD node_modules/babel-plugin-react-native-web
2121 silly ADD node_modules/babel-plugin-polyfill-regenerator
2122 silly ADD node_modules/babel-plugin-polyfill-corejs3
2123 silly ADD node_modules/babel-plugin-polyfill-corejs2
2124 silly ADD node_modules/babel-plugin-jest-hoist
2125 silly ADD node_modules/babel-plugin-istanbul
2126 silly ADD node_modules/babel-jest
2127 silly ADD node_modules/async-limiter
2128 silly ADD node_modules/asap
2129 silly ADD node_modules/argparse
2130 silly ADD node_modules/arg
2131 silly ADD node_modules/anymatch
2132 silly ADD node_modules/anymatch/node_modules/picomatch
2133 silly ADD node_modules/any-promise
2134 silly ADD node_modules/ansi-styles
2135 silly ADD node_modules/ansi-regex
2136 silly ADD node_modules/ansi-escapes
2137 silly ADD node_modules/ansi-escapes/node_modules/type-fest
2138 silly ADD node_modules/anser
2139 silly ADD node_modules/ajv-keywords
2140 silly ADD node_modules/ajv-formats
2141 silly ADD node_modules/ajv
2142 silly ADD node_modules/agent-base
2143 silly ADD node_modules/acorn
2144 silly ADD node_modules/accepts
2145 silly ADD node_modules/abort-controller
2146 silly ADD node_modules/@xmldom/xmldom
2147 silly ADD node_modules/@urql/exchange-retry
2148 silly ADD node_modules/@urql/core
2149 silly ADD node_modules/@types/yargs-parser
2150 silly ADD node_modules/@types/yargs
2151 silly ADD node_modules/@types/ws
2152 silly ADD node_modules/@types/stack-utils
2153 silly ADD node_modules/@types/react
2154 silly ADD node_modules/@types/phoenix
2155 silly ADD node_modules/@types/node
2156 silly ADD node_modules/@types/leaflet
2157 silly ADD node_modules/@types/json-schema
2158 silly ADD node_modules/@types/istanbul-reports
2159 silly ADD node_modules/@types/istanbul-lib-report
2160 silly ADD node_modules/@types/istanbul-lib-coverage
2161 silly ADD node_modules/@types/hammerjs
2162 silly ADD node_modules/@types/graceful-fs
2163 silly ADD node_modules/@types/geojson
2164 silly ADD node_modules/@types/babel__traverse
2165 silly ADD node_modules/@types/babel__template
2166 silly ADD node_modules/@types/babel__generator
2167 silly ADD node_modules/@types/babel__core
2168 silly ADD node_modules/@supabase/supabase-js
2169 silly ADD node_modules/@supabase/storage-js
2170 silly ADD node_modules/@supabase/realtime-js
2171 silly ADD node_modules/@supabase/postgrest-js
2172 silly ADD node_modules/@supabase/node-fetch
2173 silly ADD node_modules/@supabase/functions-js
2174 silly ADD node_modules/@supabase/auth-js
2175 silly ADD node_modules/@sinonjs/fake-timers
2176 silly ADD node_modules/@sinonjs/commons
2177 silly ADD node_modules/@sinclair/typebox
2178 silly ADD node_modules/@react-navigation/routers
2179 silly ADD node_modules/@react-navigation/native-stack
2180 silly ADD node_modules/@react-navigation/native
2181 silly ADD node_modules/@react-navigation/elements
2182 silly ADD node_modules/@react-navigation/core
2183 silly ADD node_modules/@react-navigation/bottom-tabs
2184 silly ADD node_modules/@react-native/virtualized-lists
2185 silly ADD node_modules/@react-native/normalize-colors
2186 silly ADD node_modules/@react-native/js-polyfills
2187 silly ADD node_modules/@react-native/gradle-plugin
2188 silly ADD node_modules/@react-native/dev-middleware
2189 silly ADD node_modules/@react-native/dev-middleware/node_modules/ws
2190 silly ADD node_modules/@react-native/dev-middleware/node_modules/ms
2191 silly ADD node_modules/@react-native/dev-middleware/node_modules/debug
2192 silly ADD node_modules/@react-native/debugger-frontend
2193 silly ADD node_modules/@react-native/community-cli-plugin
2194 silly ADD node_modules/@react-native/community-cli-plugin/node_modules/ws
2195 silly ADD node_modules/@react-native/community-cli-plugin/node_modules/semver
2196 silly ADD node_modules/@react-native/community-cli-plugin/node_modules/ms
2197 silly ADD node_modules/@react-native/community-cli-plugin/node_modules/debug
2198 silly ADD node_modules/@react-native/community-cli-plugin/node_modules/@react-native/dev-middleware
2199 silly ADD node_modules/@react-native/community-cli-plugin/node_modules/@react-native/debugger-frontend
2200 silly ADD node_modules/@react-native/codegen
2201 silly ADD node_modules/@react-native/codegen/node_modules/minimatch
2202 silly ADD node_modules/@react-native/codegen/node_modules/glob
2203 silly ADD node_modules/@react-native/codegen/node_modules/brace-expansion
2204 silly ADD node_modules/@react-native/babel-preset
2205 silly ADD node_modules/@react-native/babel-plugin-codegen
2206 silly ADD node_modules/@react-native/assets-registry
2207 silly ADD node_modules/@react-native-async-storage/async-storage
2208 silly ADD node_modules/@react-leaflet/core
2209 silly ADD node_modules/@radix-ui/react-slot
2210 silly ADD node_modules/@radix-ui/react-compose-refs
2211 silly ADD node_modules/@pkgjs/parseargs
2212 silly ADD node_modules/@lucide/lab
2213 silly ADD node_modules/@jridgewell/trace-mapping
2214 silly ADD node_modules/@jridgewell/sourcemap-codec
2215 silly ADD node_modules/@jridgewell/source-map
2216 silly ADD node_modules/@jridgewell/resolve-uri
2217 silly ADD node_modules/@jridgewell/gen-mapping
2218 silly ADD node_modules/@jest/types
2219 silly ADD node_modules/@jest/transform
2220 silly ADD node_modules/@jest/schemas
2221 silly ADD node_modules/@jest/fake-timers
2222 silly ADD node_modules/@jest/environment
2223 silly ADD node_modules/@jest/create-cache-key-function
2224 silly ADD node_modules/@istanbuljs/schema
2225 silly ADD node_modules/@istanbuljs/load-nyc-config
2226 silly ADD node_modules/@istanbuljs/load-nyc-config/node_modules/p-locate
2227 silly ADD node_modules/@istanbuljs/load-nyc-config/node_modules/p-limit
2228 silly ADD node_modules/@istanbuljs/load-nyc-config/node_modules/locate-path
2229 silly ADD node_modules/@istanbuljs/load-nyc-config/node_modules/js-yaml
2230 silly ADD node_modules/@istanbuljs/load-nyc-config/node_modules/find-up
2231 silly ADD node_modules/@istanbuljs/load-nyc-config/node_modules/argparse
2232 silly ADD node_modules/@isaacs/ttlcache
2233 silly ADD node_modules/@isaacs/fs-minipass
2234 silly ADD node_modules/@isaacs/cliui
2235 silly ADD node_modules/@isaacs/cliui/node_modules/wrap-ansi
2236 silly ADD node_modules/@isaacs/cliui/node_modules/ansi-styles
2237 silly ADD node_modules/@expo/xcpretty
2238 silly ADD node_modules/@expo/xcpretty/node_modules/@babel/code-frame
2239 silly ADD node_modules/@expo/ws-tunnel
2240 silly ADD node_modules/@expo/vector-icons
2241 silly ADD node_modules/@expo/sudo-prompt
2242 silly ADD node_modules/@expo/spawn-async
2243 silly ADD node_modules/@expo/server
2244 silly ADD node_modules/@expo/sdk-runtime-versions
2245 silly ADD node_modules/@expo/prebuild-config
2246 silly ADD node_modules/@expo/prebuild-config/node_modules/semver
2247 silly ADD node_modules/@expo/plist
2248 silly ADD node_modules/@expo/package-manager
2249 silly ADD node_modules/@expo/osascript
2250 silly ADD node_modules/@expo/metro-runtime
2251 silly ADD node_modules/@expo/metro-config
2252 silly ADD node_modules/@expo/json-file
2253 silly ADD node_modules/@expo/json-file/node_modules/@babel/code-frame
2254 silly ADD node_modules/@expo/image-utils
2255 silly ADD node_modules/@expo/image-utils/node_modules/semver
2256 silly ADD node_modules/@expo/fingerprint
2257 silly ADD node_modules/@expo/fingerprint/node_modules/semver
2258 silly ADD node_modules/@expo/env
2259 silly ADD node_modules/@expo/devcert
2260 silly ADD node_modules/@expo/devcert/node_modules/debug
2261 silly ADD node_modules/@expo/config-types
2262 silly ADD node_modules/@expo/config-plugins
2263 silly ADD node_modules/@expo/config-plugins/node_modules/semver
2264 silly ADD node_modules/@expo/config
2265 silly ADD node_modules/@expo/config/node_modules/semver
2266 silly ADD node_modules/@expo/config/node_modules/@expo/config-plugins
2267 silly ADD node_modules/@expo/config/node_modules/@babel/code-frame
2268 silly ADD node_modules/@expo/code-signing-certificates
2269 silly ADD node_modules/@expo/cli
2270 silly ADD node_modules/@expo/cli/node_modules/semver
2271 silly ADD node_modules/@expo-google-fonts/ma-shan-zheng
2272 silly ADD node_modules/@egjs/hammerjs
2273 silly ADD node_modules/@babel/types
2274 silly ADD node_modules/@babel/traverse--for-generate-function-map
2275 silly ADD node_modules/@babel/traverse
2276 silly ADD node_modules/@babel/template
2277 silly ADD node_modules/@babel/runtime
2278 silly ADD node_modules/@babel/preset-typescript
2279 silly ADD node_modules/@babel/preset-react
2280 silly ADD node_modules/@babel/plugin-transform-unicode-regex
2281 silly ADD node_modules/@babel/plugin-transform-typescript
2282 silly ADD node_modules/@babel/plugin-transform-template-literals
2283 silly ADD node_modules/@babel/plugin-transform-sticky-regex
2284 silly ADD node_modules/@babel/plugin-transform-spread
2285 silly ADD node_modules/@babel/plugin-transform-shorthand-properties
2286 silly ADD node_modules/@babel/plugin-transform-runtime
2287 silly ADD node_modules/@babel/plugin-transform-regenerator
2288 silly ADD node_modules/@babel/plugin-transform-react-pure-annotations
2289 silly ADD node_modules/@babel/plugin-transform-react-jsx-source
2290 silly ADD node_modules/@babel/plugin-transform-react-jsx-self
2291 silly ADD node_modules/@babel/plugin-transform-react-jsx-development
2292 silly ADD node_modules/@babel/plugin-transform-react-jsx
2293 silly ADD node_modules/@babel/plugin-transform-react-display-name
2294 silly ADD node_modules/@babel/plugin-transform-private-property-in-object
2295 silly ADD node_modules/@babel/plugin-transform-private-methods
2296 silly ADD node_modules/@babel/plugin-transform-parameters
2297 silly ADD node_modules/@babel/plugin-transform-optional-chaining
2298 silly ADD node_modules/@babel/plugin-transform-optional-catch-binding
2299 silly ADD node_modules/@babel/plugin-transform-object-rest-spread
2300 silly ADD node_modules/@babel/plugin-transform-numeric-separator
2301 silly ADD node_modules/@babel/plugin-transform-nullish-coalescing-operator
2302 silly ADD node_modules/@babel/plugin-transform-named-capturing-groups-regex
2303 silly ADD node_modules/@babel/plugin-transform-modules-commonjs
2304 silly ADD node_modules/@babel/plugin-transform-logical-assignment-operators
2305 silly ADD node_modules/@babel/plugin-transform-literals
2306 silly ADD node_modules/@babel/plugin-transform-function-name
2307 silly ADD node_modules/@babel/plugin-transform-for-of
2308 silly ADD node_modules/@babel/plugin-transform-flow-strip-types
2309 silly ADD node_modules/@babel/plugin-transform-export-namespace-from
2310 silly ADD node_modules/@babel/plugin-transform-destructuring
2311 silly ADD node_modules/@babel/plugin-transform-computed-properties
2312 silly ADD node_modules/@babel/plugin-transform-classes
2313 silly ADD node_modules/@babel/plugin-transform-class-properties
2314 silly ADD node_modules/@babel/plugin-transform-block-scoping
2315 silly ADD node_modules/@babel/plugin-transform-async-to-generator
2316 silly ADD node_modules/@babel/plugin-transform-async-generator-functions
2317 silly ADD node_modules/@babel/plugin-transform-arrow-functions
2318 silly ADD node_modules/@babel/plugin-syntax-typescript
2319 silly ADD node_modules/@babel/plugin-syntax-top-level-await
2320 silly ADD node_modules/@babel/plugin-syntax-private-property-in-object
2321 silly ADD node_modules/@babel/plugin-syntax-optional-chaining
2322 silly ADD node_modules/@babel/plugin-syntax-optional-catch-binding
2323 silly ADD node_modules/@babel/plugin-syntax-object-rest-spread
2324 silly ADD node_modules/@babel/plugin-syntax-numeric-separator
2325 silly ADD node_modules/@babel/plugin-syntax-nullish-coalescing-operator
2326 silly ADD node_modules/@babel/plugin-syntax-logical-assignment-operators
2327 silly ADD node_modules/@babel/plugin-syntax-jsx
2328 silly ADD node_modules/@babel/plugin-syntax-json-strings
2329 silly ADD node_modules/@babel/plugin-syntax-import-meta
2330 silly ADD node_modules/@babel/plugin-syntax-import-attributes
2331 silly ADD node_modules/@babel/plugin-syntax-flow
2332 silly ADD node_modules/@babel/plugin-syntax-export-default-from
2333 silly ADD node_modules/@babel/plugin-syntax-dynamic-import
2334 silly ADD node_modules/@babel/plugin-syntax-decorators
2335 silly ADD node_modules/@babel/plugin-syntax-class-static-block
2336 silly ADD node_modules/@babel/plugin-syntax-class-properties
2337 silly ADD node_modules/@babel/plugin-syntax-bigint
2338 silly ADD node_modules/@babel/plugin-syntax-async-generators
2339 silly ADD node_modules/@babel/plugin-proposal-export-default-from
2340 silly ADD node_modules/@babel/plugin-proposal-decorators
2341 silly ADD node_modules/@babel/parser
2342 silly ADD node_modules/@babel/highlight
2343 silly ADD node_modules/@babel/highlight/node_modules/supports-color
2344 silly ADD node_modules/@babel/highlight/node_modules/has-flag
2345 silly ADD node_modules/@babel/highlight/node_modules/escape-string-regexp
2346 silly ADD node_modules/@babel/highlight/node_modules/color-name
2347 silly ADD node_modules/@babel/highlight/node_modules/color-convert
2348 silly ADD node_modules/@babel/highlight/node_modules/chalk
2349 silly ADD node_modules/@babel/highlight/node_modules/ansi-styles
2350 silly ADD node_modules/@babel/helpers
2351 silly ADD node_modules/@babel/helper-wrap-function
2352 silly ADD node_modules/@babel/helper-validator-option
2353 silly ADD node_modules/@babel/helper-validator-identifier
2354 silly ADD node_modules/@babel/helper-string-parser
2355 silly ADD node_modules/@babel/helper-skip-transparent-expression-wrappers
2356 silly ADD node_modules/@babel/helper-replace-supers
2357 silly ADD node_modules/@babel/helper-remap-async-to-generator
2358 silly ADD node_modules/@babel/helper-plugin-utils
2359 silly ADD node_modules/@babel/helper-optimise-call-expression
2360 silly ADD node_modules/@babel/helper-module-transforms
2361 silly ADD node_modules/@babel/helper-module-imports
2362 silly ADD node_modules/@babel/helper-member-expression-to-functions
2363 silly ADD node_modules/@babel/helper-globals
2364 silly ADD node_modules/@babel/helper-define-polyfill-provider
2365 silly ADD node_modules/@babel/helper-create-regexp-features-plugin
2366 silly ADD node_modules/@babel/helper-create-class-features-plugin
2367 silly ADD node_modules/@babel/helper-compilation-targets
2368 silly ADD node_modules/@babel/helper-annotate-as-pure
2369 silly ADD node_modules/@babel/generator
2370 silly ADD node_modules/@babel/core
2371 silly ADD node_modules/@babel/compat-data
2372 silly ADD node_modules/@babel/code-frame
2373 silly ADD node_modules/@ampproject/remapping
2374 silly ADD node_modules/@0no-co/graphql.web
2375 verbose exit 0
2376 info ok
