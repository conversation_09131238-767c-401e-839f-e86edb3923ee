{"_id": "web-streams-polyfill", "_rev": "49-cc703f797c3a9cc38bcf3fae5cd34879", "name": "web-streams-polyfill", "dist-tags": {"next": "4.0.0-beta.3", "latest": "4.1.0"}, "versions": {"1.0.0": {"name": "web-streams-polyfill", "version": "1.0.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "dist": {"shasum": "afac5218e5b56c915d502bfc9b49af32659485f8", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-1.0.0.tgz", "integrity": "sha512-MTVBbn2a1pmkJaIc/UsuaHfMV0RSGgMlAGYuOWzTPA3IBcfqYR6XBR4MJO5tK//8v4UG9ulcv7bmVRsNBYLvnA==", "signatures": [{"sig": "MEYCIQD3IIsPOyP5d02VuXLPQn519JbSwV/vv+/sSWXNg+W4/QIhAOi4cMiht+9XzdZFZddJxUSKjc/3CQ8MrNufl4iARSZ5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/polyfill.js", "_from": ".", "files": ["./dist"], "_shasum": "afac5218e5b56c915d502bfc9b49af32659485f8", "browser": "dist/polyfill.min.js", "gitHead": "d43894012bc72a4e086a698e4008aa74c3229726", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "build": "npm run bundle && npm run transpile && npm run minify", "bundle": "rollup index.es6.js > dist/polyfill.es6.js", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "transpile": "babel dist/polyfill.es6.js -o dist/polyfill.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/creatorrr/web-stream-polyfill.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "5.7.0", "devDependencies": {"rollup": "^0.25.4", "babel-cli": "^6.6.0", "uglify-js": "^2.6.2", "babel-polyfill": "^6.6.1", "babel-register": "^6.6.0", "babel-preset-es2015": "^6.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill-1.0.0.tgz_1456943387426_0.8151088957674801", "host": "packages-13-west.internal.npmjs.com"}}, "1.0.1": {"name": "web-streams-polyfill", "version": "1.0.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "dist": {"shasum": "2e261c60506000d68718e6953bcf20c9cf1cbd4e", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-1.0.1.tgz", "integrity": "sha512-qk9IL43G3oIZXd/g2gx7+KOqbLclMrRk+F65bTV1IDl/dgY4syr05psFsua/52B2dQhduNZikS9cUd7r2N9GFA==", "signatures": [{"sig": "MEYCIQCUXGfyblXt7PxVPnCTMCK+T1g/IWhqyHDrVGSbLHB+GQIhAMB9wJ19kX8xBfBZAEOfJnSK4GSC/ts4jsvca9zxTLK2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/polyfill.js", "_from": ".", "files": ["dist"], "_shasum": "2e261c60506000d68718e6953bcf20c9cf1cbd4e", "browser": "dist/polyfill.min.js", "gitHead": "c7cc6ba1b79b87841c31e40e656f10cec7fc40eb", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "build": "npm run bundle && npm run transpile && npm run minify", "bundle": "rollup index.es6.js > dist/polyfill.es6.js", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "transpile": "babel dist/polyfill.es6.js -o dist/polyfill.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/creatorrr/web-stream-polyfill.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "5.7.0", "devDependencies": {"rollup": "^0.25.4", "babel-cli": "^6.6.0", "uglify-js": "^2.6.2", "babel-polyfill": "^6.6.1", "babel-register": "^6.6.0", "babel-preset-es2015": "^6.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill-1.0.1.tgz_1457043469694_0.1993605128955096", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0": {"name": "web-streams-polyfill", "version": "1.1.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "dist": {"shasum": "55d198a3c059e7dcdf4450b3512551baf6c094c1", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-1.1.0.tgz", "integrity": "sha512-rLUQhnTnBOzbxlbWdeVebYPvRBl+ZiUHFBoS4JFczNxnsn8Wu4C1AFHQvGa2wTLgUD8B4FbqVGsn/Ze4+74kdw==", "signatures": [{"sig": "MEUCIHuBC0kE02WAwreTkuipeeFykRUVBmVJ7sRzu+wlxeR/AiEAoV+3rPq3JviuNWs7306amOuj4H7tbaD0QHGPvVNFO1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/polyfill.js", "_from": ".", "files": ["dist"], "_shasum": "55d198a3c059e7dcdf4450b3512551baf6c094c1", "browser": "dist/polyfill.min.js", "gitHead": "29c7d91e9f062426eb423bb34af5d4193f4183c1", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "build": "npm run bundle && npm run transpile && npm run minify", "bundle": "rollup index.es6.js > dist/polyfill.es6.js", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "transpile": "babel dist/polyfill.es6.js -o dist/polyfill.js"}, "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/creatorrr/web-stream-polyfill.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "5.7.0", "devDependencies": {"rollup": "^0.25.4", "babel-cli": "^6.6.0", "uglify-js": "^2.6.2", "babel-polyfill": "^6.6.1", "babel-register": "^6.6.0", "babel-preset-es2015": "^6.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill-1.1.0.tgz_1459664818337_0.024501644540578127", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.1": {"name": "web-streams-polyfill", "version": "1.1.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "dist": {"shasum": "4ce0065390fa9865cfaaa52cb2722e06dc1e0e61", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-1.1.1.tgz", "integrity": "sha512-P71q/i52COL0tcovq8xPCT9ih5J43MJXaU4Orde5GqxFlt2vuibCG5wBqa9YEeI4EcNXGaljPhhzzpakTHW62w==", "signatures": [{"sig": "MEUCIQCsCgPX7mL/Bo13wD0tlLKofwTxHQNR43ysjI4gqK75NwIgRh9+qLzah2k8YNaGQXCxNwjf1d8XkVhEvHKw1rMzI1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/polyfill.js", "_from": ".", "files": ["dist"], "_shasum": "4ce0065390fa9865cfaaa52cb2722e06dc1e0e61", "browser": "dist/polyfill.min.js", "gitHead": "c13da6314dcc45abc01dcc377d193923be7d7bd2", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "build": "npm run bundle && npm run transpile && npm run minify", "bundle": "rollup index.es6.js > dist/polyfill.es6.js", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "transpile": "babel dist/polyfill.es6.js -o dist/polyfill.js"}, "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/creatorrr/web-stream-polyfill.git", "type": "git"}, "_npmVersion": "3.8.1", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "5.7.0", "devDependencies": {"rollup": "^0.25.4", "babel-cli": "^6.6.0", "uglify-js": "^2.6.2", "babel-polyfill": "^6.6.1", "babel-register": "^6.6.0", "babel-preset-es2015": "^6.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill-1.1.1.tgz_1459665315733_0.4818312537390739", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.0": {"name": "web-streams-polyfill", "version": "1.2.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "dist": {"shasum": "9c2de7b27e86e8ad98fb575766249835f1524184", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-1.2.0.tgz", "integrity": "sha512-7sSOIzzmKNArJ+tKHC66di3/lfrPm6HTG7QSYjmIy6DYua7ZoIhaencSM81tTkDXl/b5z0lqjsHKCqzvrWTQ6Q==", "signatures": [{"sig": "MEYCIQD9Mv+w/SrsV+J2L/Rlmd+CPkh/SvIrDS7D3HrVhztXlAIhAJa/uHxfiLl/HfPRa9L4S6vQByx+t6G4JDpnRkOQOgSY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/polyfill.js", "_from": ".", "files": ["dist"], "_shasum": "9c2de7b27e86e8ad98fb575766249835f1524184", "browser": "dist/polyfill.min.js", "gitHead": "ca572561139bd5ee5e0eb9e31132c51a10c3712d", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "build": "npm run bundle && npm run transpile && npm run minify", "bundle": "rollup index.es6.js > dist/polyfill.es6.js", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c", "transpile": "babel dist/polyfill.es6.js -o dist/polyfill.js"}, "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/creatorrr/web-stream-polyfill.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"rollup": "^0.25.4", "babel-cli": "^6.6.0", "uglify-js": "^2.6.2", "babel-polyfill": "^6.6.1", "babel-register": "^6.6.0", "babel-preset-es2015": "^6.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill-1.2.0.tgz_1468655614205_0.11036600591614842", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.1": {"name": "web-streams-polyfill", "version": "1.2.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "dist": {"shasum": "440e7a03c59812cb3ff2eeb77297b154121fcd23", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-1.2.1.tgz", "integrity": "sha512-OEkpnn8WIgIXXg4lKNqOXdhfkmghjYPjfGQBQqDuJTOz/wfcoSf/w5tpyB9iq7ivZdSXQyMIwiStRo3UkxfoCg==", "signatures": [{"sig": "MEQCIFmeH6Pp3YTjCdylVa8OzScB2Ztf3uhBgEuUS3EAS0ZmAiBqPJuBEX5009lIyqFwVq8LoiLWKTQ7phbVLoUiLpcibg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/polyfill.js", "_from": ".", "files": ["dist"], "_shasum": "440e7a03c59812cb3ff2eeb77297b154121fcd23", "browser": "dist/polyfill.min.js", "gitHead": "22953f20cb48afdd1cbf02b762fcb702aade38d2", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "build": "npm run bundle && npm run minify", "bundle": "browserify index.es6.js -o dist/polyfill.js -t [ babelify ]", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c"}, "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/creatorrr/web-stream-polyfill.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "5.12.0", "devDependencies": {"babelify": "^7.3.0", "babel-cli": "^6.6.0", "uglify-js": "^2.6.2", "browserify": "^13.0.1", "babel-polyfill": "^6.6.1", "babel-register": "^6.6.0", "babel-preset-es2015": "^6.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill-1.2.1.tgz_1468657934300_0.29969602823257446", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.2": {"name": "web-streams-polyfill", "version": "1.2.2", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@1.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "dist": {"shasum": "363dce238cd20bf57847403701b7cbd17c231ee6", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-1.2.2.tgz", "integrity": "sha512-9ZXPF7a2xfcxkKPZk2WPQEDu8QFdLlN8SMV+epwb3N+tcY/in0o6/RLrG1ovUcAfMbDwVEngQ2DUnlhYtNO/ZQ==", "signatures": [{"sig": "MEQCIBMepyfM9SCezsos8kqr5FOTw+TQ86FrqpKIBR/eKrRIAiAn3kPIMWXmbo+34eQtzPvaQLNJCLYMb5+uLxcLeZ42lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/polyfill.js", "_from": ".", "files": ["dist"], "_shasum": "363dce238cd20bf57847403701b7cbd17c231ee6", "browser": "dist/polyfill.min.js", "gitHead": "77d2ed141fbc2d9170ebf9e2bcefb8b39a513be9", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "build": "npm run bundle && npm run minify", "bundle": "browserify index.es6.js -s default -o dist/polyfill.js -t [ babelify ]", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c"}, "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/creatorrr/web-stream-polyfill.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "5.12.0", "devDependencies": {"babelify": "^7.3.0", "babel-cli": "^6.6.0", "uglify-js": "^2.6.2", "browserify": "^13.0.1", "babel-polyfill": "^6.6.1", "babel-register": "^6.6.0", "babel-preset-es2015": "^6.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill-1.2.2.tgz_1468659125303_0.8623745448421687", "host": "packages-16-east.internal.npmjs.com"}}, "1.3.0": {"name": "web-streams-polyfill", "version": "1.3.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "dist": {"shasum": "2ae58ef3ffb7a9ba3a8669eff236c12e8888e4ba", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-1.3.0.tgz", "integrity": "sha512-SckEDFJTEHvfm7KqHOV66xV90f+RUsOKGjt4vVbDxpoxBIYvsG9ltoL6g3BRjszi0DQ2ApT9PNeIMaricienhg==", "signatures": [{"sig": "MEQCIEE5Z7zvI4KyvF1gxjBiW4F9A7/7nx8ETjWF+0b07uI4AiBKDvRnZY1XAJhQD8Doy5upFrnmSp8T1Vcl2dNWveDllw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/polyfill.js", "_from": ".", "files": ["dist"], "_shasum": "2ae58ef3ffb7a9ba3a8669eff236c12e8888e4ba", "browser": "dist/polyfill.min.js", "gitHead": "68f93d7240d925d27b175ce39133f57993c3f109", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test)", "build": "npm run bundle && npm run minify", "bundle": "browserify index.es6.js -s default -o dist/polyfill.js -t [ babelify ]", "minify": "uglifyjs dist/polyfill.js -o dist/polyfill.min.js -c"}, "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/creatorrr/web-stream-polyfill.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "6.3.1", "devDependencies": {"babelify": "^7.3.0", "babel-cli": "^6.6.0", "uglify-js": "^2.6.2", "browserify": "^13.0.1", "babel-polyfill": "^6.6.1", "babel-register": "^6.6.0", "babel-preset-es2015": "^6.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill-1.3.0.tgz_1470559504593_0.028606666019186378", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.1": {"name": "web-streams-polyfill", "version": "1.3.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "dist": {"shasum": "bd890cfed004130d502eb08ea261e2240b0e5918", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-1.3.1.tgz", "integrity": "sha512-c5aLpuNFMAvhxX5PQgL7uRVkEDedHobcdA9S4T3lyY2Ye3WCK6/15JiCTltBt+9YwMiZ/1mhsWDiJsVtKWUmUA==", "signatures": [{"sig": "MEYCIQCiDz/98U9Jpo62agcnEqMtBex28HsWyVmv6ifpyWy5ggIhAJs+q5xX/UGp8o4z7cx3v80UCutFDDvAsaHNIGG+0nXf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/polyfill.js", "_from": ".", "files": ["dist"], "_shasum": "bd890cfed004130d502eb08ea261e2240b0e5918", "browser": "dist/polyfill.min.js", "gitHead": "213e356231918901d8e4cc54b3828956515255f2", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test && cp ../../run-web-platform-tests-on-bundle.js ./ && node --expose_gc run-web-platform-tests-on-bundle.js && rm ./run-web-platform-tests-on-bundle.js)", "build": "npm run bundle", "bundle": "npm-run-all bundle:*", "prebuild": "git submodule update --init", "bundle:min": "browserify -g uglifyify index.es6.js -d -i ./spec/reference-implementation/lib/utils.js -s default -t [ babelify --plugins babel-plugin-unassert uglifyify ] | derequire | exorcist ./dist/polyfill.min.js.map > ./dist/polyfill.min.js", "prepublish": "npm run build", "bundle:no-min": "browserify index.es6.js -d -s default -t [ babelify ] | derequire | exorcist ./dist/polyfill.js.map > ./dist/polyfill.js"}, "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/creatorrr/web-stream-polyfill.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "jsnext:main": "index.es6.js", "_nodeVersion": "6.9.2", "dependencies": {}, "devDependencies": {"yarn": "^0.17.0", "babelify": "^7.3.0", "exorcist": "^0.4.0", "babel-cli": "^6.11.4", "derequire": "^2.0.3", "uglifyify": "^3.0.2", "browserify": "^13.0.1", "npm-run-all": "^2.3.0", "babel-polyfill": "^6.6.1", "babel-register": "^6.6.0", "babel-preset-es2015": "^6.9.0", "babel-plugin-unassert": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill-1.3.1.tgz_1481701992618_0.44352244632318616", "host": "packages-18-east.internal.npmjs.com"}}, "1.3.2": {"name": "web-streams-polyfill", "version": "1.3.2", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@1.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "homepage": "https://github.com/creatorrr/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/creatorrr/web-streams-polyfill/issues"}, "dist": {"shasum": "3719245e909282d93967825f44bcd550e9c03995", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-1.3.2.tgz", "integrity": "sha512-zU+H6o+0cRbwxHAA2rsHeZ3Cd9gPyWElxuPSKF/+o3vQcdoRa7fbONimwItgR8c48N4UHFcuimxLgAIamP0p4A==", "signatures": [{"sig": "MEUCIQCQ/Feji0kme/Er+4yzVDVFX8mfBMVJR3oZr4WfMnSzmQIgBgTbVVIofIoDa3nQWg78pYPlTitO2QgYygJdpc73EOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/polyfill.js", "_from": ".", "files": ["dist"], "_shasum": "3719245e909282d93967825f44bcd550e9c03995", "browser": "dist/polyfill.min.js", "gitHead": "385e96b0b2fd0a5afa9f216c2f3b803f2f1dab7a", "scripts": {"test": "(cd spec/reference-implementation ; npm install && npm run test && cp ../../run-web-platform-tests-on-bundle.js ./ && node --expose_gc run-web-platform-tests-on-bundle.js && rm ./run-web-platform-tests-on-bundle.js)", "build": "npm run bundle", "bundle": "npm-run-all bundle:*", "prebuild": "git submodule update --init", "bundle:min": "browserify -g uglifyify index.es6.js -d -i ./spec/reference-implementation/lib/utils.js -s default -t [ babelify --plugins babel-plugin-unassert uglifyify ] | derequire | exorcist ./dist/polyfill.min.js.map > ./dist/polyfill.min.js", "prepublish": "npm run build", "bundle:no-min": "browserify index.es6.js -d -s default -t [ babelify ] | derequire | exorcist ./dist/polyfill.js.map > ./dist/polyfill.js"}, "_npmUser": {"name": "pipes", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/creatorrr/web-stream-polyfill.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "jsnext:main": "index.es6.js", "_nodeVersion": "6.9.2", "dependencies": {}, "devDependencies": {"yarn": "^0.17.0", "babelify": "^7.3.0", "exorcist": "^0.4.0", "babel-cli": "^6.11.4", "derequire": "^2.0.3", "uglifyify": "^3.0.2", "browserify": "^13.0.1", "npm-run-all": "^2.3.0", "babel-polyfill": "^6.6.1", "babel-register": "^6.6.0", "babel-preset-es2015": "^6.9.0", "babel-plugin-unassert": "^2.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill-1.3.2.tgz_1481725468091_0.8838668977841735", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.0": {"name": "web-streams-polyfill", "version": "2.0.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "a7a58b042fdcd93c55c69c6524231e08b577ed02", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-2.0.0.tgz", "fileCount": 40, "integrity": "sha512-CZrboLwoXbJmdUokO7TN1Re4K8a+PcHDbsewQTVWQRUUZI30OOUpECeW3I3GidmalHkTdWsi4w3ftj6bK8Nwog==", "signatures": [{"sig": "MEQCIApcvb3by9o+/Mdb3eRPC32slaeQZaMo5j0xhUyqpGhnAiBCmpU1KuDwh2U8hsB/326I4FpKeUl7kWNYK9k+nwAZEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2858406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchR3LCRA9TVsSAnZWagAABlgP+QEPcZKtlUHlVx6Rs0eQ\njuU9h02nwZpBYHUjHTHNdz2zBqCWsWuoHXkSGJVps+XUAjfiqOKMs+iC7p5v\nIt9WDaf9mwC/ii9eSMmfBJ/5c5sv1iZNmHTxQ9A7DcHHr607ipHl/zyNgRr5\nmn09dWBSoVqzHgUIVMni/mLO34GN/IF3+YgdeNAxM0iMjbhzQYa14VUon6Vm\nScL1me/CNJsHuAKOm0+Tm3beAtQ9VztywDUQtg3xuubmxSVMoMaFk7ls+Cc6\nuSq6ZbyiGdADQVqM3X34auHv+49/PTCq2YxIPhGrMOVNQrPJbG3onTtQ0ETT\n7lmjuI69tWcYJYUbwQOqNFBdBt/eaU9UAcj/LGzknf2yJXhseQTo/P912AOi\nAZFZv26h/W2LntSolaROWN2NJDuR2snCxy70WmeT1lpXi/3xCOr+nDObI3aW\ng+3RJdIId7wmpx9G6+FSimfdCZPxllceYE7iB9Q93v9oQpr3v6O8zbCj2Wwr\nBHDQ/EyjIiqW/UxtLz7Ezjqr9tjRRPIRlj8NIZknNHsp6uDCLyp2Otn1n3xU\npgJmaAIKWCfpOkQSlQyRwm/f0GD/+hz70jqV3eCam92CTpob8xIHrDZrKyQ4\n+ZUzuVeYeFpVC9fBDyjhHOfKQUHUkRvdfn4S+zCbUEznG9G9iK6qoKVcoKKS\nqTIn\r\n=bnus\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "8bf71918f87174ebe2a9ca7546f8afc83482efda", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:wpt", "build": "npm run build:types && npm run build:bundle", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "rollup -c rollup-types.config.js", "pretest:wpt": "git submodule update --init --recursive", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"eslint": "^5.15.1", "rollup": "^1.6.0", "micromatch": "^3.1.10", "typescript": "^3.3.3", "wpt-runner": "^2.7.1", "rollup-plugin-dts": "^0.13.0", "rollup-plugin-strip": "^1.2.1", "rollup-plugin-inject": "^2.2.0", "rollup-plugin-terser": "^4.0.4", "@typescript-eslint/eslint-plugin": "^1.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_2.0.0_1552227786738_0.8309113432612656", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "web-streams-polyfill", "version": "2.0.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "15b7f0e7337caa90645167d302e716747b18744b", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-2.0.1.tgz", "fileCount": 40, "integrity": "sha512-H<PERSON>OMKJ26i5bPVSDXsEPE7OU+twuqCWLC8ZSfhIedeQnjzGjCrC4S7n5w9Ymh0rqZyzsfdbRmOGlEmB069dmjuA==", "signatures": [{"sig": "MEQCIDd7SWjgOAk6izkdIcNFZKo79qH4xtlCh+DpBZEJym61AiBnQKYjNmmPPG8UFPpE3twt+lOI/nCsE/ZYlumSuC2xcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2892422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcjOoJCRA9TVsSAnZWagAAi9MP/isVTjwSt+WMk81Bga1W\nraB+fON7s523Kwh8TP82wCl5kv4uHf8VuAK8IaNiqquOQSWr5bWuFb5M1g57\n9YXPerIe29wW2Hj6COCjjKFkpOeBv107saEjTkMnwsoTJvfeKkJYZ/XdtUow\nZ16PKMGO6kwLMr2YpeBM7E+wU4xvMSbdZNUCgbyXnkqqP8d5clgH1rkTzThg\nqt2WgSJh+X88+dvuvS1Y72Wu3zPwcoK37Y5qFPl003SI9ldN8l3V/VxRoL4T\nz5LtjXY5zZSOMvEu62xcKt59zwkb3WzowIXyJuOOFiBs4I9ZvRd2fQ9kaLO2\nm6Dmmrv8+F9NeIuhBWaxnK2nLYzo5/6zFovuB+NmxpGPTIzX+OTe6FTNusmM\nL3bIArRm48TPQWDHx5yplecBVP77YYSyA0Dm7RCjPrAaBArnTKyEaZweO088\nu7y13ucj5mVvxjNFkXl2sxx5clcRYcwYQZnAEDybUV0AMurbdkoqQJ6k/bwq\nqY7M+jeEksPOZjPKTe7FBFliZuaea3o8ipoSg9E6F7iCZtTlGbemGoGygKpi\npaEB2P78Yauc5QwctBZfjE33TwXk99tSoBQtdwcbJBf3U0szTeVdo2XXWUws\nh0MIWdOLP4GHcjM3lUux933LOQ9SZrpE/Ya0n6V/fkFDqaeU+uuCVNQqKp2k\nh39j\r\n=RNoe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "e70a76162c5de7dffe3b716131e6aeb6bdf826d4", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:wpt", "build": "npm run build:types && npm run build:bundle", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "rollup -c rollup-types.config.js", "pretest:wpt": "git submodule update --init --recursive", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^5.15.1", "rollup": "^1.6.0", "micromatch": "^3.1.10", "typescript": "^3.3.3", "wpt-runner": "^2.7.1", "rollup-plugin-dts": "^0.13.0", "rollup-plugin-strip": "^1.2.1", "rollup-plugin-inject": "^2.2.0", "rollup-plugin-terser": "^4.0.4", "@typescript-eslint/eslint-plugin": "^1.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_2.0.1_1552738824639_0.9583898337163212", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "web-streams-polyfill", "version": "2.0.2", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@2.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "de70800b0d08ec9be99b5cf6c7c628906e4af95d", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-2.0.2.tgz", "fileCount": 40, "integrity": "sha512-ieGrNJWWtDD4ywBWWxqBh2OvHJL6KKFag63cxTEHcXKHyeLRhLcH6wI7gVhMVk3rDSDZLpZjGmgkDbL1RRDkPQ==", "signatures": [{"sig": "MEQCIG0K82zbLOeHDANs3NKOFzs0qcHFUM9H8EuPqukB2ZjhAiB4IoiXlGD8wd+tuC/CXnaABrNhSnl199QIjixAkxRs+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2935983, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcjntOCRA9TVsSAnZWagAAPAUP/A9ws5VKnuBIRxs9y+KW\n/IrE68MoYmftnN+Tr7Yh4xTH5nSN3MPNzValKxiU5crpTZVkR5z2NbvEWKYK\nYkGVqiGHIUS9rGYmIA9jrDP/MBzXkmtcT6xlrU0+1S3bWzo8L0tdJgTx9Qz7\nV4peiTZJ64OdUPxKnRIG52bBr4qqQkm1hiHXASDq9qyQK88nwlzq9l9OH9av\nxT5h7Jp+hmZkgZCd9KQOopGWJwWNSMtprjDuh8d7MOclS/OetSfsSVLjl3z5\nM7ZKApfLzSs9qRu+o6HWJaoRoGYN3iXt7Oj976ZDkcHn9Kjo06RzbzNtQSnU\nl8NdGCf/baGDinSLjEDA85VGfsmgyGTpI7wrJVWw6PmOi9fSp0pYfWxy+pOT\nggMuntxz6CUqnpGheNxUgHEZbBKkqtvlvQuIhp+OkjqLVMvNL5nON2MLINRR\nP7iL7wIAGGRBZIcR1mF6z6S5RfEM1scvAKM8O4hrQu8kaAxy1MyMb386Gwh2\nwsJqTPfiDVHO/UOYn2NsvMtKPaM+qIMvkLD3qlTX2k8p0YYdlz7FsvtW7osC\nIUmcHdnwGdwrFRML7+tu3gMhGaMKmtozIxlLtMPobvb/ZjRZSzDJ9CaQQnMq\ntETNZmiZWosmJkifkIDZgbXt22ovCWkO4UXuq9KjfSePt8sYTm/vFFPSVSxT\noTXX\r\n=4OoO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "216008e4778a3992fb9872c9f29fb25f7475e8a2", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:wpt", "build": "npm run build:types && npm run build:bundle", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "rollup -c rollup-types.config.js", "pretest:wpt": "git submodule update --init --recursive", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "devDependencies": {"eslint": "^5.15.1", "rollup": "^1.6.0", "micromatch": "^3.1.10", "typescript": "^3.3.3", "wpt-runner": "^2.7.1", "rollup-plugin-dts": "^0.13.0", "rollup-plugin-strip": "^1.2.1", "rollup-plugin-inject": "^2.2.0", "rollup-plugin-terser": "^4.0.4", "@typescript-eslint/eslint-plugin": "^1.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_2.0.2_1552841549312_0.513413141185949", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "web-streams-polyfill", "version": "2.0.3", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@2.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "0c396f069a5eedc96c711393b12f2c67cf283a00", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-2.0.3.tgz", "fileCount": 40, "integrity": "sha512-pOqiHmL3RBAGS+SgOR42RbPU6nc8/n15N2rsOXFYHLnTfs2Z8QHs8AizOeOaYEnhwPN4+hu3M2D9XvAqzvt6MA==", "signatures": [{"sig": "MEUCIFZvEw3hjjTDp8hHTnxaoYbI6vyicD8pfZULNQFbh90ZAiEA7V5K1vH6v0M/492WnUExyX+sRbMUDCtiQggnq+oOlho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2945489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpn79CRA9TVsSAnZWagAAuuUP/3L2Cv8UBeRG6yrxMzaj\nAn4tISwKvSQm7q1qwDbCbVpRbHXIiL5QQSj40wu/dYAv+RFenqcUfxXHOvei\n8rpVbhg/BLOnlYpTwDE78UDqZgeXWJ0GK/5P+eR5Un3Mi1TJcgt1s80p80Eu\nnmYy8fOiAF+ixvXf/qVghxODCdJrYmvQSTVWpy2WkHi5jnjgdaMWojeJydNA\nftzlKScnyNbL01NKJpKRwFy2a5tDYy8nRsHToVzrFCOldERow3ZxvwWqIF/s\nD5CerTsq0X7vMwvx48bnf21RaskiPAvZdzj7CMzrMfH/SKCBR4pePFhWypYd\nxm1gyPX0oj27w0IacEjHu9JVUiuarapcto8tu2iOJJY7d0mXUdIkB4EOGnvL\nAwvr60HDf/vcTPmSDIDPEyAnKREceKUclPYIPITySrUDLgi8VW8I6TBUg6bD\ny8Rze9nEh41+9lHbnoYsPdYwLh1x9yTcLrA21I2C3HhFgPCqtBO+D0Rbni0H\n5dbjCAZYf3vrcf96qPCNE2IV/imOQUOv9F3P3YzVEkws8SUcuaBfRgXlnT52\nOYv2S3pcv7dBZptsVF7x7fi+x8MJ2w05xReyGpHrcE6ievE4lp8LTtlu3OPg\nYQ2MG/7h9czHZK0MVWjH7cDGl2LmYEQGsC/ewV6WPuYalQSxm5E3XQEgmbQ3\nibTA\r\n=H/38\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "6b678f2fae982d08d60c907a96a1aa86259f4bbf", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:wpt", "build": "rollup -c", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:types": "tsc -p ./test/types/tsconfig.json", "pretest:wpt": "git submodule update --init --recursive"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "10.15.0", "_hasShrinkwrap": false, "devDependencies": {"tslib": "^1.9.3", "eslint": "^5.15.1", "rollup": "^1.6.0", "micromatch": "^3.1.10", "typescript": "^3.3.3", "wpt-runner": "^2.7.1", "rollup-plugin-dts": "^0.13.0", "rollup-plugin-strip": "^1.2.1", "rollup-plugin-inject": "^2.2.0", "rollup-plugin-terser": "^4.0.4", "rollup-plugin-replace": "^2.1.1", "@typescript-eslint/eslint-plugin": "^1.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_2.0.3_1554415356959_0.2688065309882972", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "web-streams-polyfill", "version": "2.0.4", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@2.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "pipes", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "67ba73e42ae7c1d6dd64498e6e0a7952fb3993eb", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-2.0.4.tgz", "fileCount": 41, "integrity": "sha512-hsLbiIZou2pZoht4VxfITEbCUz09YhZaaYFNICaZo7SOJrtW3/35LsJoAXeB8HITOlKpuWU0tFvtItOS2PpbsA==", "signatures": [{"sig": "MEUCICzqLTQxxQSA5UaXio7rW/uR3UEKyltykmWXhobXIN9GAiEA8Xb8vnGentyB3Z+MXfUu9wIV2+AVm3YfuUxl1tWHyk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1721642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQy5XCRA9TVsSAnZWagAAspAP/iVUuV2sxxxuvxDP0wiV\n8wf8441ni9G6S0ZDnr2pAmVwygZxyXDsCplTVbNudBpF045EL0i0WyslFXRb\n1U6rzMdoIjFmm6+GkJ980TbiFHe4J6Pvp9bvpm5J0Rtqi5y1pTXOHTeGN041\nt8OO/Nzh0z6F837ml3iMwsgdDaJJiHuyc5OUyEyBrAJ85JRr4DW8kwYsVYUx\n8LxeorwHX6uHCxJO89RyvcqmE+TdxwuIPOjoX+7/Ya/Nfxd63YDyJuouIyiw\nqh26/V/9mQswYIe5wJjNywLbeY0BIghFMA1p9ZhS5UPkG9d92eHRLEB+cF1b\nn0C08NzkqcKugcqKatXVk+hA2+Evy5VzPAiVzXKn0Veagr0lhi6RjNA6TRho\nP+mKKVzbxwYon9k3RrYj8FhoWVR4mW2K+rANkMlu28qny4zYwoslUMmd56o9\nEPtSfG/JoD0b0Ak058zhhDGYI0IctQ0x7pVsbZm7ZQUEdzUTYHo/WX2Ap7yn\n8jjNxNC+GzGYIW9xF+Qaft/MmDVjroOhArUHUUeOrKMIKQi+I7pQaAkbKbHq\nbZoQ608C7yjKndX+5fudNLq37XFJVa6cAJu8fnay+4xCU1hE6GDs6C5NxRwx\n9U/1zu2TP9j+OKI9WnkTlKW3fxI37jTvTGrTRbS3qIFYu2tZZXXCGOq6JUyr\nTCag\r\n=Xd6+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "cf49b9643f2e840c4afa4f4521c5c1d034d1609b", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project src --emitDeclarationOnly && api-extractor run --local", "pretest:wpt": "git submodule update --init --recursive", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "10.15.0", "_hasShrinkwrap": false, "devDependencies": {"tslib": "^1.10.0", "eslint": "^6.1.0", "rollup": "^1.18.0", "micromatch": "^4.0.2", "typescript": "^3.5.3", "wpt-runner": "^2.8.0", "rollup-plugin-strip": "^1.2.2", "rollup-plugin-inject": "^3.0.1", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-replace": "^2.2.0", "@microsoft/api-extractor": "^7.3.4", "@typescript-eslint/parser": "^1.13.0", "rollup-plugin-typescript2": "^0.22.1", "@typescript-eslint/eslint-plugin": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_2.0.4_1564683862797_0.2947333946350075", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "web-streams-polyfill", "version": "2.0.5", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@2.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "63defc04cd3b56d6f8bf2c6cdf996b626f812821", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-2.0.5.tgz", "fileCount": 41, "integrity": "sha512-jECu/7ilpv3Q3bRP9yVtC+/DkEKM4imnIJMT7DD5Dx0TB9ylWpPtanpnQSxf4x2q1iyMr6kYEYD9hGipbRhh5w==", "signatures": [{"sig": "MEUCIH7yYWmoyx3sKpB0DUwerM3XMVfYfJ1NVW5ioE3OZNctAiEA+jr+fkjRlU7lgkJDmquAjO2IUcy+64mlSyVkFofVzEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1757656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnIRoCRA9TVsSAnZWagAAxBMP/j4QdrJVGYjk3wyYtHDp\neQglziVdF5sWGP5DhrdPKthtJNXWgHobsv2nXuCQAVJBJ5d2Bb08ENdg0qIP\nqyXtK6p9syblYmW4d8I3Im1EwHxsVXfBRt6xvDdseJ1gM1xM1elUZ7MV79tf\nqv8Yvu/TjLHQ4pMydclVuhn9WhRVP64USJd70iiQZsOjf7rskUpR0JzD3m3m\n7mTt4QEK0wSdNZgxPriE05aMrx8fbxf6ABEwYO2T9z+RxtTLZx5535Vc00EK\nTPKEipsRzpN6lrsKUa6CxitJYMUyRSFaYbUTGweE22PxsACuNeGrmQBSrIQ5\nW7grrzrQtf+Rpt9U6EBbEgUHXJvOm/6H6RAn4Ydk/kQkL0SG3k2/Y34jR4vs\n+aGz9GvjhqHtkhEnYazvp3sN495BtGGFciCfVh6RfUDCX6Bc68m5Ze5PYnVH\nRG3+YYSfW1uRE6tuebCfCB/m9lO3IwnGlESGVXeFItuaPm5rFqkYRYFdaiBv\nl/fVgD0BEgFnCVBRcQYfvkobPCiYQfhnIrItK/IwmZz3dHgFpGd9Ji19zI08\nYb8b+i1+UTmigDdf1NN2hdFu+eJ+GeM0+DKDQqSlKElbvCZrEa5ocpiW6AqO\n25gRPk/X3Q5H5AcmWYDUl/c6zJgAtUC/5USz5zK4tt447OCmVn1M11a/JY+X\n6pxC\r\n=wo51\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "3adecd0a89706c40dd5fe1da3fa212eab6ae3dbe", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project src --emitDeclarationOnly && api-extractor run --local", "pretest:wpt": "git submodule update --init --recursive", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.10.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "10.16.0", "_hasShrinkwrap": false, "devDependencies": {"tslib": "^1.10.0", "eslint": "^6.1.0", "rollup": "^1.18.0", "jasmine": "^3.5.0", "micromatch": "^4.0.2", "typescript": "^3.5.3", "wpt-runner": "^2.8.0", "@types/node": "^12.7.11", "rollup-plugin-strip": "^1.2.2", "rollup-plugin-inject": "^3.0.1", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-replace": "^2.2.0", "@microsoft/api-extractor": "^7.3.4", "@typescript-eslint/parser": "^1.13.0", "rollup-plugin-typescript2": "^0.22.1", "@typescript-eslint/eslint-plugin": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_2.0.5_1570538599384_0.21601584840462507", "host": "s3://npm-registry-packages"}}, "2.0.6": {"name": "web-streams-polyfill", "version": "2.0.6", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@2.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "76e0504b4f15a6310cfc9e78e27637b648f9741e", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-2.0.6.tgz", "fileCount": 41, "integrity": "sha512-nXOi4fBykO4LzyQhZX3MAGib635KGZBoNTkNXrNIkz0zthEf2QokEWxRb0H632xNLDWtHFb1R6dFGzksjYMSDw==", "signatures": [{"sig": "MEUCIEtQaLlKJplUNQfDTICHI5DxRbnZL6v31tGTwn720jdMAiEAwMzZNCQ7WNBfF5HwbmY1i+bGmbO42bNG3xOFqbmxSEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1757906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxV3WCRA9TVsSAnZWagAAymUP/3G+gzg/+pMXEnott2Pi\nQsYNJmIOrpObSKGTs8LEkvjYKBEJebf52t7RvucknxHXMhaaSGo1R93o7ZM8\nObrqc0KX/JomFxL6rjSjG6DkWLeC7jp4xZtIeD/zRoeVmfTSg6JPIYgrcNti\nJDofqoNwAM90YU7mrM1zSffoDBRij/cDerXYdl9dknRWIJo1kykukrfe6ADQ\nWV3e0J/wx8YSOJIK1NBdNyls6/jQ5mi3AvQlkKTBPH2nX89Az9nj/TFXXytb\nDk1YpD7jvO84dpaS9cqKxth7EktMFXI4c066xtPG0lQtvyItwcvoasbH/oMg\nQaccjqMkdRpZowLLqhnDgP79wFe7Q5IeR5kP2I0l/FWnhS32ioU0rc9wqhvq\n0Xee5nsqXCizukolIO2KW5K4nMLKYB7693M2LRWqU0zSQ0lyz1HzCYvEqql4\nPFdpvpSnlsGRamHJASfwGO5B4HUHVxoHsO6d8gwq0VD5ZOFkdRcyRp1UgqUR\nV+g2uZy2UDDzY38yeLSO4I3LA6TQW+JU4WjIaTXJimoeT+bs++maRW3lUx/J\nGFj5VhAMhNFdCd5IB3R/oXDVdjcMrkj62YKLPnRPayBa/98iI7t0Msmb1WRe\nt/DC1TVOfVJSaeTdEd9KgR1ywCTjZMCSiV5ravkm5VcehoiWBkqIj4bwSZu6\no5du\r\n=xQw3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "a719b21d7cbb5536a0e0424aced3e914af7514e2", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project src --emitDeclarationOnly && api-extractor run --local", "pretest:wpt": "git submodule update --init --recursive", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "10.16.0", "_hasShrinkwrap": false, "devDependencies": {"tslib": "^1.10.0", "eslint": "^6.1.0", "rollup": "^1.18.0", "jasmine": "^3.5.0", "micromatch": "^4.0.2", "typescript": "^3.5.3", "wpt-runner": "^2.8.0", "@types/node": "^12.7.11", "rollup-plugin-strip": "^1.2.2", "rollup-plugin-inject": "^3.0.1", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-replace": "^2.2.0", "@microsoft/api-extractor": "^7.3.4", "@typescript-eslint/parser": "^1.13.0", "rollup-plugin-typescript2": "^0.22.1", "@typescript-eslint/eslint-plugin": "^1.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_2.0.6_1573215701826_0.2716769871954485", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "web-streams-polyfill", "version": "2.1.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "a4673244f92338e714bad632d2e0ef0004098271", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-2.1.0.tgz", "fileCount": 42, "integrity": "sha512-ZgJ7lsxTM0GR77Nwzt950NHd/CjWzVEct2DH48Dy8/u+3r4TD9o8Bo1VNFK04QajgcEjr0CZPlRhau8LtR3YHQ==", "signatures": [{"sig": "MEUCIDgN6ol3jxr5DZhbMm/umycMFaUOzgMX0nzSUPWATe6KAiEAvn7MiROufpwUBk7qLtZaKKbABCtfFANpvk2Hhc1RkKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1776951, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeUmfkCRA9TVsSAnZWagAAif8P/j8HzvyCqzRM9VsNm33i\nYc4oHXDHa98eXR57co3lsysq9OH1y8h9ge5dh7YPVQQgrYBm3+iwp/8+dxCy\ncaouYGFbWZL2vjgvuoqy3tRplUKZ9d9hmJp199gI4O/VpvBaHQ72bVpWJNns\noiNOSlX2Ll/IHUsmtdW7/YmjkGkE3ccXgWH+COrBf+rjEITdr//I4kud5QEb\nTIqN6TWQkZQkHkwGu+uYYZylhDJMIRJYC484G4SsjRLP0k5eICk83NdkN+YC\nEZVDEqC+IQdMQYcOIsgPdHI9pv81E3xBPdIBXLPLALzkkknA0tnQpbPMIYDE\nEZYMpAsnU7QVz8Nu5p+Z8TjzptNE0mC38TDwupa3vqPqoOPRI84Ii6TlW1Y8\nsPZkF+t9LknWi4EjRtjGVpCtVPzP9fyhnQCR/3NZFOz0HGWEw5nA/lGu+219\nSeu4HB7GZPx+mHA8eudM4vNMyxpvf264Qd7QTIuilWRnyJ6BcocvuzAOCW9q\nS1Qt5r1+FCdjWdp2h5YJ1qgahBGQB0TqWV6hcC6sTXTD5mTGdMk07qENMfbN\nyMnQ8EjMo5POY9jLsV6/r9LcadLHVzhH+qLqIPA4bLF0XDb9HUjA5uNvpoz0\nlbbzZvfdcMWgF3mHAgCL/2iWmy3PLytdcczqBhQTrlGhAcYQKlQQI/WWZH5s\nvs8P\r\n=O+UA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "2160765a54ba3177e3f8ac289bc3bba231ba5166", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project src --emitDeclarationOnly && api-extractor run --local && node ./build/downlevel-dts.js", "pretest:wpt": "git submodule update --init --recursive", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "10.15.3", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^1.10.0", "eslint": "^6.7.2", "rollup": "^1.27.10", "jasmine": "^3.5.0", "ts-morph": "^6.0.2", "micromatch": "^4.0.2", "typescript": "^3.7.3", "wpt-runner": "^2.8.0", "@types/node": "^12.12.17", "rollup-plugin-strip": "^1.2.2", "rollup-plugin-inject": "^3.0.2", "rollup-plugin-terser": "^5.1.3", "rollup-plugin-replace": "^2.2.0", "@microsoft/api-extractor": "^7.7.0", "@typescript-eslint/parser": "^2.11.0", "rollup-plugin-typescript2": "^0.25.3", "@typescript-eslint/eslint-plugin": "^2.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_2.1.0_1582458851474_0.9842460362145058", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "web-streams-polyfill", "version": "2.1.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@2.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "2c82b6193849ccb9efaa267772c28260ef68d6d2", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-2.1.1.tgz", "fileCount": 42, "integrity": "sha512-dlNpL2aab3g8CKfGz6rl8FNmGaRWLLn2g/DtSc9IjB30mEdE6XxzPfPSig5BwGSzI+oLxHyETrQGKjrVVhbLCg==", "signatures": [{"sig": "MEQCIHftCk5BDy/owQbZHpH8Q9JGFgzG/gpyuftMb8VZ09lsAiA92yRx7IFrLF1v6MHPLTXZL8FyJ6TGDVlt/55Lk4NH4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2958946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekYOGCRA9TVsSAnZWagAACO4QAInI/H6s6CpNQKR/1xJ9\nQ2C/XLC/s9MfXwDneIPZArYodk+TPyC3GI7+vz+EKQOo76YoSVwfe89hOWKZ\nY00krYP549rrupKGNoza54ZKhpW4Yul04iGR3DFwr/A3XVRnjEToWwuFgz2f\nllWhpK4AaIoC4kQMpwCvMbYyzzz5un/R1vLvK/G85oFQWA9rTPB/jxuNnlqA\nDQPtM4ssstyccor4lv/m10CZTRfc1nUXmBWzsrE0JwmdFkagMcbZBI9ysNwm\nfUpF9gHV5MRM4pqDUSFZFyELmcZPP5j3i9dqcA/wbWI6tambmzn5uilZjcqk\n3ihmLap704BsN53HaULjo059SyYepToFgW8B7VrKhDT0sKbYv11zjhpSVbBQ\n1hQjc6t8KYoHnOxWzPLwzY+G90gi9dRWIwFK534/4wQhsYPHWy32XFtHRX8F\nxQ3AIN2HQxDf9qYhpq46q3KkbCyFtDAs4QOQMTflttfxMOkKSiLT3WLSSF7c\n2o/p8/Mt8zbqGtHZaKRIKd0sN+RPIsWPI5914US5LeFywDSdgWNUXYl8CWhl\nYboXb3Gr6FGbvvUL1Jqdwui641aFP3xUdQa6Lb6zW9BXcjHGt/6zGJoDd9mW\n949pCv3JTGZJek9jIbz2QF52usKW44EwxcBDQPXL4tiykZfF2ClFm3DksBkq\nuDO6\r\n=+chB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "92aa5943d095c61570d7b63be7c1bd7a5570399a", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "pretest:wpt": "git submodule update --init --recursive", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "12.16.1", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^1.11.1", "eslint": "^6.8.0", "rollup": "^2.6.0", "jasmine": "^3.5.0", "ts-morph": "^6.0.3", "micromatch": "^4.0.2", "typescript": "^3.8.3", "wpt-runner": "^2.8.0", "@types/node": "^12.12.30", "@rollup/plugin-strip": "^1.3.2", "rollup-plugin-terser": "^5.3.0", "@rollup/plugin-inject": "^4.0.1", "@rollup/plugin-replace": "^2.3.1", "@microsoft/api-extractor": "^7.7.13", "@rollup/plugin-typescript": "^4.0.0", "@typescript-eslint/parser": "^2.23.0", "@typescript-eslint/eslint-plugin": "^2.23.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_2.1.1_1586594694397_0.06891971739233527", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "web-streams-polyfill", "version": "3.0.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "c8bcb1e2ec1820088ca201674486a0f1feae848b", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.0.0.tgz", "fileCount": 42, "integrity": "sha512-tcZlIJ+VBxuDXdRFF3PCZTJ3yUISGklG4hkl3CDGOlZ8XwpN90L5YsJNoSPH72wZ4nbsatE/OfIaxfM3p+6W7w==", "signatures": [{"sig": "MEUCIQD3+Qep/5zNIUejIqiTSyQvbC9wUk25wImuIWD7MVIoZAIgYs+AhZiToULsH0ev7kDSLeubY25ZuJyF2uwyChT19og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6503628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFe4oCRA9TVsSAnZWagAAdC4P/35H9CSw1nKGcaWtW7bS\nLT61r8JxKvbQTfZTaAlvYUWaYwcu8XVvq6UvGepPZ1tW2hfJmEftQiDxw//A\n9W1ftB/EgEAzY3o85s9AOsEgxjFWAgbAz2JXGRRNoakk/TkVFqekHXdhMTkD\ndsC3BG3cbl0td+L9TZChcXxx8W+g4REEpmcHZ1uPdgs+FL4zsdiW0zIminQJ\n0/2Cp5Th3iGgPpNpBT1hITRnNhcJ2ObC1GtAOWqDzHcZlfq4NKuVzF2/Ytd2\nZ26oTSD+IO+mTVbKKpSf/oAvGQ6szytfAmOneVU7C86jBODbcT5TmnjBuvJk\npv23jZmBq7fhJk/jRY1dSXMrYgwHim6CKcDtFpkHFL+Zct833I+jehPTYBEB\nJaco1WQPJEcW1JwhlrLyvVnfc1OK95tdQ9HBisBMzHHr61+C3OS83ZVk44my\nL8BZ0Qd2zDjovLLvm12UYCytbK7rQartfvnIUsABqbkeVGcQAg6h3PpKW0OB\n1PtpFFy6DWEoeDKXUF1RsOaI69IAuO8VN5plAfViohdohn6TbVZBLImsFxfw\nfxyeSxblX9HXlheb4OXIgu3FSDRtp/+rAktdvSnuUaspgYcZAUTPQTI6s4e1\ngX1Iz7c3qIl5dwsfJft6GIo6LxGV5X5+pNaBkXXir/1MJCJpgijiDrxEGptU\n7J5G\r\n=s4eD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "787ec35cbc2eca4d87a398aea08e0b2aeb7b61d9", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "pretest:wpt": "git submodule update --init --recursive", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "12.17.0", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.0.0", "eslint": "^7.5.0", "rollup": "^2.22.1", "jasmine": "^3.5.0", "ts-morph": "^7.1.2", "micromatch": "^4.0.2", "typescript": "^3.9.7", "wpt-runner": "^3.2.0", "@types/node": "^12.12.50", "@rollup/plugin-strip": "^2.0.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.3.3", "@microsoft/api-extractor": "^7.9.2", "@rollup/plugin-typescript": "^5.0.2", "@typescript-eslint/parser": "^3.6.1", "@ungap/promise-all-settled": "^1.1.0", "@typescript-eslint/eslint-plugin": "^3.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.0.0_1595272743491_0.1276916544321085", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "web-streams-polyfill", "version": "3.0.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.0.1", "maintainers": [{"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "1f836eea307e8f4af15758ee473c7af755eb879e", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.0.1.tgz", "fileCount": 42, "integrity": "sha512-M+EmTdszMWINywOZaqpZ6VIEDUmNpRaTOuizF0ZKPjSDC8paMRe/jBBwFv0Yeyn5WYnM5pMqMQa82vpaE+IJRw==", "signatures": [{"sig": "MEUCIGWP3R+EYIo2iVLWLdAzgkzuWnoy2JRSuueh669UgGq1AiEA3NI10hVuyFK541IfNyBEnSRykR0uBEBgghn7jpMjHRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6971083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrHzjCRA9TVsSAnZWagAA1psP/RCLsZVs4Ici7d3XZiAi\nGlyVhEj1gscRw6P/HO06phAnZjJqrzwFNlK9wDwEwXhpQSyPl2wGa+nl6MTJ\nA4SG+CsKUBlxCl8nDy/XetmO8BNW01rDVlI1MZMHbnhReyKpSZecHVSx/ySp\nCJbXVLF/nRgcQWqJIyUANLMF9L9syHxvcrMrOF0egB/QghqdPP1FGprTalz0\nFVx8t+vyvdiZxU3FHtBH2RVGOEi09aSzck08ycb8w1q2yNVlm+XfLc4nKg5Q\nrfCe6HsuHiX4PLWlTYnmg9UbYFD7mQ7p0WsE48QeXMnyYGGMj068z1nSfeKh\nuOz3SOKm8Nz5iMSAtys3sBGcJRISOv7vmz6dF/l48n8vAtDpPIOQmd84DgQM\nnmSDLAcSaW8W689TbFZa6gdRqvLwPzPQujlGyPv/K8X6x72UdJzCJD+YWe0i\ne5PGfsVtAwZcIv2KHoWwo+p+EvkyNvYXkLk7cG2XWIlisjF30fvWGvAeES+o\nW8vBG65yjL2no+mDAU9KrIFgiSsAl5S3slMTiDCseJVIZtosgNuNy4SIDz/l\nfBSUvIY7ACjiR/g0zhKFsUCxT0Xj9cFkSFoCT+fHtH/Fd4tU9H+J4MGKTYdq\n9F1xBofoZwTU/dRbS+2ZcL+EFJeBCx57jqU/M//bXlam54j5U3tTm85PRHwg\n9nvN\r\n=vsOY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "6e93b8f0b09c47e1702bf2b5273696279f211de4", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "pretest:wpt": "git submodule update --init --recursive", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "12.18.3", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.0.0", "eslint": "^7.5.0", "rollup": "^2.22.1", "jasmine": "^3.5.0", "ts-morph": "^7.1.2", "micromatch": "^4.0.2", "typescript": "^3.9.7", "wpt-runner": "^3.2.0", "@types/node": "^12.12.50", "@rollup/plugin-strip": "^2.0.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.3.3", "@microsoft/api-extractor": "^7.9.2", "@rollup/plugin-typescript": "^5.0.2", "@typescript-eslint/parser": "^3.6.1", "@ungap/promise-all-settled": "^1.1.0", "@typescript-eslint/eslint-plugin": "^3.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.0.1_1605139683130_0.8118750364520677", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "web-streams-polyfill", "version": "3.0.2", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "402061089a61a2465457938abaa1b9e4db1bcc0f", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.0.2.tgz", "fileCount": 42, "integrity": "sha512-JTNkNbAKoSo8NKiqu2UUaqRFCDWWZaCOsXuJEsToWopikTA0YHKKUf91GNkS/SnD8JixOkJjVsiacNlrFnRECA==", "signatures": [{"sig": "MEYCIQChJu3JhsIrt//qooxuQjW2Zgypdru694t1o4kYvgsCDAIhAPxTH131bk6ImaOlV6wljytUKXPWNybsi3vud+pU9T5Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6975916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIx7ECRA9TVsSAnZWagAAVLQP/jsgExQ0wON0izHboos2\npJNffQ31E5fO/OvhURa6qSczQVeRHyO++nu+wIPRqQBItx/vsqRaa78RiSsp\n8Dsk7PFmzbcmeel9Bybm+WckCq0LmBouxWnBY2YwyERdFVkbet0TMuqjC5S9\nKDM2RwvHsel7s9IK9J9fhT6sY5tyWUm2Tb+PE6HK2qR//GXJkMdJ45t7OOt9\nq393KY0KcDMIdIBYxCWdtskDCMaCb3GX/Uwp7eLk9oAgZPf/p9COafc56vii\nEle8yVtaBa5vUj9x+mEN6E+GpRzNZHeYR4pJViX50WalcRaunYNUucugI4ck\nSXJUG0cWXZHwQrmfSY1LmSPtKAEYdDQuijRO5vodFFAvCelB727hRjoANUFg\nIO+46wUeaws3HJLARbwAybrxDRkPPrdkOPFPtPjyM54mCmSm1IZLwlFWuwTy\nLHUft7F4JGQ85dkaoNC5vs26qH2H1+kGlcLZY0AkcHH79RX417pgBpS13HK1\nJBG3PNtOCfLR3n0y1g428jBgaYQRFsGa4MU9vCpR1H0NqbPSt1Sgao9Xhmde\nPFaeYFMMUxi2E26l1d54uv3dWOzF8ltEAGdFAcKyz0jQz1385gmnY6my34DA\nCGmyrpIVbPnAzb1gRw8QoDFlJuNzNlb73Djdcnn/+OVhpq0AlLrhV3pJZjcW\n7ebZ\r\n=3nZ1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "6915a01aa6c92b4d3cb306fb7a66299e881a7bcc", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "pretest:wpt": "git submodule update --init --recursive", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "12.18.3", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.0.0", "eslint": "^7.5.0", "rollup": "^2.22.1", "jasmine": "^3.5.0", "ts-morph": "^7.1.2", "micromatch": "^4.0.2", "typescript": "^3.9.7", "wpt-runner": "^3.2.0", "@types/node": "^12.12.50", "@rollup/plugin-strip": "^2.0.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.3.3", "@microsoft/api-extractor": "^7.9.2", "@rollup/plugin-typescript": "^5.0.2", "@typescript-eslint/parser": "^3.6.1", "@ungap/promise-all-settled": "^1.1.0", "@typescript-eslint/eslint-plugin": "^3.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.0.2_1612914371546_0.6477250639036616", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "web-streams-polyfill", "version": "3.0.3", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "f49e487eedeca47a207c1aee41ee5578f884b42f", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.0.3.tgz", "fileCount": 42, "integrity": "sha512-d2H/t0eqRNM4w2WvmTdoeIvzAUSpK7JmATB8Nr2lb7nQ9BTIJVjbQ/TRFVEh2gUH1HwclPdoPtfMoFfetXaZnA==", "signatures": [{"sig": "MEYCIQDXBZanH5rUfsK5TOHbwN8DjlegRdilYCfJ5IiysU+5iQIhAMFOBMWZyC0JQ2Kqbq+NKKcMZ06nseYaQyPhPccdMTXq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6987235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcIsQCRA9TVsSAnZWagAAuBgP+QD3boMvmQOwGLbfMxhL\n/npNIZZObwQrPR2Oe5fwxlFF8JMC9J3c3FEIv9qKebYShKVJCERTnvHBvahF\nWtFRuzZRJz4Rjoiz8UoZElDX+sSpCHwcf7wlEt6I6HqmgJLPWr0DI16Y4nj6\n3BSHBKc2/jT1okQVaralLzQrWuyfdcFtCIiXT2JBKH74zxLpjTUGOOaET9hh\nPBgHl/+6lh9NK2H+SjRrTJzBwCjPZBMRtKH6nwkUlFdf2B/3NA0578c/OLh5\nXuwqx+w30ge7zUw+PLnlqt83gmO330hdD0phRTmJumgitDgBx0twI3Ok+1R3\ns+Y+lhPR5Z5raks4C5DlYsaAaCMG7osNovt9JgUOtF356XcWjhtP4vkr1605\nWzSdsvRnCgtQGp5C+wTua+ZdUtQxq/fsvsU8u2ZmIX1y93D2jSqpuCTPkScV\n7VUO55dSeZzw0gNpb2JpmN42gT6Bb+vygElOIEB+/39mjujs5eZbu5C48eiy\ntSCG9MSZe6AerYmAiT0KoiyjynGaJTO/DxwYB05GTmaZMpmq4rvs3JhKwurh\nIkzxXEIT5CckNv4of8I2s1bofaMiOxhPH/pR1P2L5uKMrTc8jRbjVXoy6SwW\nFgxC1TpJOgCB+iAbNUyyZAYp2gBYfNVE6ejA3vWpopwV7tJyQaOSxQiJEO4e\nQLhb\r\n=4CBd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "69422f7be3d3f59e9b3cc37ead8b474e67d78f95", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "pretest:wpt": "git submodule update --init --recursive", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "12.18.3", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.2.0", "eslint": "^7.23.0", "rollup": "^2.44.0", "jasmine": "^3.7.0", "ts-morph": "^10.0.2", "micromatch": "^4.0.2", "typescript": "^4.2.4", "wpt-runner": "^3.2.1", "@types/node": "^14.14.37", "@rollup/plugin-strip": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.4.2", "@microsoft/api-extractor": "^7.13.4", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^4.21.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.0.3_1617988368328_0.6748844068144739", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "web-streams-polyfill", "version": "3.1.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "86f983b4f44745502b0d8563d9ef3afc609d4465", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.1.0.tgz", "fileCount": 42, "integrity": "sha512-wO9r1YnYe7kFBLHyyVEhV1H8VRWoNiNnuP+v/HUUmSTaRF8F93Kmd3JMrETx0f11GXxRek6OcL2QtjFIdc5WYw==", "signatures": [{"sig": "MEQCIDsDX01LjEx2xptBoft9BSy3gV19r3PYW5uj0pvZJjxAAiBGGL8Vqp5Fu0trNWa38M9ovdcAJxoPtU8W5BFea7LCug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7557913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+JgcCRA9TVsSAnZWagAA/9YP/ifsFlWY/q7yc/sF+Ww8\nPLM68QYVBD+2vzSjtVtWO8k3U4L1jWvk0Ff0v1R0gePvQUEFfO2X0WsPQzVa\n2/HYi0GBm172MWG4XqpMfBqgT0vgYWpO06MWf2LLvs9M6wcjyRHB6PglFYya\nhvTJLof5qUnJVsB0q2x0TVMg+/LySZu1KlncM9JPq7JIu1OYe7jsAEc5RDDD\n8c9biRtu9ovKFUdn9T3cbXhk7tT4d6fupa05Ymm06zl2SZuTVITbop9I1DhT\n4kH0D8FaaqnPdD9btY4qHs0WZFqb73sk9C6g1Eb6z1cFE4HQzkB9pSIensNk\nNg7YzPdvSAlLcifIw+KipE7V+imTIaBUL3i9LpouJXL86Bk1PgArV/Ok7t+e\nEWfMDL7zoh5Idd+EkUJ3U/1ou8pY53o4mW3SD15tqSwKGWN9Bga/fZSaFbUn\nP83+Ah0ME2/UKxfOix2/Otm0lzeuOpeRhg1vCrkY1ngnvYvIeX6hHgcp2EAC\nAbJN4kOkpSUdx/X1gBm7hpG+rENuHOlozWDA5C4fjKzU+NxrDUtUgMx3Od+p\naUpPY+LXOTfF4M8iqyfuol42es9TSLW3x8KFSMTxPZgJBisHhNYMeyIdQX7q\ns44tBS38VmN9PV2Px5clZ40rmr96UbDbrX7WekFSJgRxKiTTgFtj6327tRAF\naROf\r\n=l3PY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "80a46678d3b0062a1f5db3b36e436facbe9f5614", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "pretest:wpt": "git submodule update --init --recursive", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "14.17.0", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.2.0", "eslint": "^7.23.0", "rollup": "^2.44.0", "jasmine": "^3.7.0", "ts-morph": "^10.0.2", "micromatch": "^4.0.2", "typescript": "^4.2.4", "wpt-runner": "^3.2.1", "@types/node": "^14.14.37", "@rollup/plugin-strip": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.4.2", "@microsoft/api-extractor": "^7.13.4", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^4.21.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.1.0_1626904604156_0.990376894300866", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "web-streams-polyfill", "version": "3.1.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "1516f2d4ea8f1bdbfed15eb65cb2df87098c8364", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.1.1.tgz", "fileCount": 42, "integrity": "sha512-Czi3fG883e96T4DLEPRvufrF2ydhOOW1+1a6c3gNjH2aIh50DNFBdfwh2AKoOf1rXvpvavAoA11Qdq9+BKjE0Q==", "signatures": [{"sig": "MEUCIH5lBm/7f/tNCYZekNhMKCGQeBxG/1YAhO/4VyMjHgdpAiEA7bXm5G3SieeoXoQ+rJlCFpelfjE4wIaeVaizlJtpVQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7558905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNULaCRA9TVsSAnZWagAActEP/2DOesMviw3qIgElYulS\nMnvQYL+157bhfL9VkYz433Jpqoc1PYnf+/iS3bmEZ+F/Tv3lvF5M/k/MkGyH\n1AW5GjmsxB6fTo0+GPSN7van60puREs/JwY1tN+sLWGtyaUWO+HhNmUdtX2E\nkHJsV7Q6zpCZj9R3xy/YJfZRQdx3NeEkrwzerfeyVasDLD7ofjskGN/jBn8X\ny3q78bQnmoMfxjH6lk9NkkeASmCDn+JqbvwI1ddYdBszQHIJNPgIGYQn9Zhj\n6fpi61cAJ5DaL6t13u7GcrGwmswTgwlD5vr3/SwGBJVVQ9eM0CQQLb8evCuj\n8l7KObJ9EvW+W6IGmjm5aq1u8Y4zC8eB4iBn4ngijXBszmocaC98/vMNRwCs\nEdOrQOF3QGcS3b/hvEtSZRWVu73DMbwDjSdNStIqZnnV6JxuT6Oq3Q+lh6Ow\ncWYHfeqir+7BtWluhvtunPGWC2YxMoAJY8CgLgYx61QqO7xJ6kUdmofuxAo+\nWX6VYv4DMm4CPDppx7IXG1hiXSwgdOWDuJd+s+TI9GUVB9IcJLQ7GR5qvwLj\n0FsYuYigqxYqMPdffcUjoM2P882cLs/ltI0UFLbXCOv6PomfRc7nKB5F2+Qn\nhg9DC3uiWQYa15gZVr5kTucupb/WQHFYpFTvX63jKGEHeG2Ga2EAohE+d8S2\nnxat\r\n=2d6L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "270791329d8d9c56769ff9806e430344ce18dbec", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "pretest:wpt": "git submodule update --init --recursive", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "14.17.0", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.2.0", "eslint": "^7.23.0", "rollup": "^2.44.0", "jasmine": "^3.7.0", "ts-morph": "^10.0.2", "micromatch": "^4.0.2", "typescript": "^4.2.4", "wpt-runner": "^3.2.1", "@types/node": "^14.14.37", "@rollup/plugin-strip": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.4.2", "@microsoft/api-extractor": "^7.13.4", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^4.21.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.1.1_1630880473934_0.1325941418478258", "host": "s3://npm-registry-packages"}}, "4.0.0-beta.1": {"name": "web-streams-polyfill", "version": "4.0.0-beta.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@4.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "3b19b9817374b7cee06d374ba7eeb3aeb80e8c95", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-3ux37gEX670UUphBF9AMCq8XM6iQ8Ac6A+DSRRjDoRBm1ufCkaCDdNVbaqq60PsEkdNlLKrGtv/YBP4EJXqNtQ==", "signatures": [{"sig": "MEQCIFg2EXNm5SO+T/+ip9f1/oWwx0jStr4guenSt5DlryAYAiB086bHlHjot9bP+E8Rxswa+7Z8jV1HA2zl5Zr7uC66Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 401920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNnaeCRA9TVsSAnZWagAAF3EP/3XA+kLCr2GP/FMLAzvh\nMK2t9CKVJ6h6ONPDmsnqJdlmHZUf+Fxie1n+euiSQii7fC0CyZfbHpMHVLLJ\nVjeEXLS56ys5UWXcs0plXhyWHLd6lQi9UWwgpWcqoqhUp2tl6VIghX0mRz29\nSqCEQve8Y8OiwOmJhTL7ZwvjSok1AfZRxfUXJG1dnE8cl76lMXE/ekR6ZzX/\nZpfpBT8xkvgwofyl+11q+beDm2uVwxSCoGaqR2CQ2tNLutjnEQaVtZgkBQzN\nowbMeQ/hFDdRweWnHBLnial8JQT1C7QxP4kx7kedjeTYFA9rWWso2ax/zpl3\nBRRg1flVUgSEXqTLgOQbtUDOsZkcfet2gK6YyFGf+5Bs/VCb8OsC7EYYSeT0\n0Ulq1q0o3SNbxKX537J22kNsGSg6q5sz+CSUXSw9+/1xa4UYDe+iOiz381WN\nICEUWdPIYnkBKiN6fNWP5KBHkgfGNQ5k+jp5QyeJOKBRbU1OJ2kPIJ58gWjn\n0/3VxaiicyyDOHr/2+RcxYwbdXBKHtDTKHC0OJJHG1myYs0CWLJkUOWjSVyX\nzB9JmqQnzyitSuluvy7CdHIoBgxwnlzXzeFMtPHU/PLn5RhK9g1b9zFnxR+a\n1e604kFovLQ9uPbcVYgo6/GNvPikoh84ABZj8ZooBYl+bfeK6d1G8yyA/EW2\neOfq\r\n=YsNa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/ponyfill.js", "types": "types/ponyfill.d.ts", "module": "dist/ponyfill.mjs", "engines": {"node": ">= 12"}, "exports": {".": {"import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./dist/*": "./dist/*", "./package": "./package.json", "./types/*": "./types/*", "./polyfill": "./dist/polyfill.js", "./package.json": "./package.json", "./polyfill/es5": "./dist/polyfill.es5.js"}, "gitHead": "3a3536ffc092f420c9ee1c2945bc7e5e0a1e78bf", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundlers", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "pretest:wpt": "git submodule update --init --recursive", "test:rollup": "cd test/rollup && npm ci && npm test", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local", "build:bundle": "rollup -c", "test:webpack": "cd test/webpack && npm ci && npm test", "test:bundlers": "npm run test:rollup && npm run test:webpack"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "14.17.0", "typesVersions": {">=3.6": {".": ["./types/ponyfill.d.ts"], "./es5": ["./types/ponyfill.d.ts"], "./polyfill": ["./types/polyfill.d.ts"], "./polyfill/es5": ["./types/polyfill.d.ts"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tslib": "^2.3.0", "eslint": "^7.32.0", "rollup": "^2.56.3", "jasmine": "^3.9.0", "micromatch": "^4.0.4", "typescript": "~4.3.5", "wpt-runner": "^3.2.1", "@types/node": "^14.17.12", "@rollup/plugin-strip": "^2.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^3.0.0", "@microsoft/api-extractor": "^7.18.7", "@rollup/plugin-typescript": "^8.2.5", "@typescript-eslint/parser": "^4.29.3", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^4.29.3"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_4.0.0-beta.1_1630959261984_0.21839278606178159", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "web-streams-polyfill", "version": "3.2.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "a6b74026b38e4885869fb5c589e90b95ccfc7965", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.2.0.tgz", "fileCount": 42, "integrity": "sha512-EqPmREeOzttaLRm5HS7io98goBgZ7IVz79aDvqjD0kYXLtFZTc0T/U6wHTPKyIjb+MdN7DFIIX6hgdBEpWmfPA==", "signatures": [{"sig": "MEYCIQCoxwrpDnxvur+EruRbIKbDE+ABlLFV7XhceQS2asSRUwIhAIS2M06933/NP01UpJGr8Prn7IBT4bDfD7b0VNYJ76Ds", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7610299}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "224d8538eb2df5950a29de97c7409f32e2b2981f", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "pretest:wpt": "git submodule update --init --recursive", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "14.17.0", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.2.0", "eslint": "^7.23.0", "rollup": "^2.44.0", "jasmine": "^3.7.0", "ts-morph": "^10.0.2", "micromatch": "^4.0.2", "typescript": "^4.2.4", "wpt-runner": "^3.2.1", "@types/node": "^14.14.37", "@rollup/plugin-strip": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.4.2", "@microsoft/api-extractor": "^7.13.4", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^4.21.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.2.0_1636211135718_0.6988981602649902", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "web-streams-polyfill", "version": "3.2.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "71c2718c52b45fd49dbeee88634b3a60ceab42a6", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.2.1.tgz", "fileCount": 41, "integrity": "sha512-e0MO3wdXWKrLbL0DgGnUV7WHVuw9OUvL4hjgnPkIeEvESk74gAITi5G606JtZPp39cd8HA9VQzCIvA49LpPN5Q==", "signatures": [{"sig": "MEYCIQDxjmEcKQOCW2LCExN/9QlHW+3r1JwWYuS15x+COnk1FgIhAL7fbqsDXk8E651JFe7eGR71CEmeWUlJRE9TD7zwtYKa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7614396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiT0mMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZ3w/8D02QSw+mnRW7LUJpnkHOzUZfLFTRlB10TQzM4nkf4GeZsXHB\r\nNqoAvelkv3iuFlq4NwSL5W5xhx62L1caah4QSl7KtFN4tCaaEaKFz3wTH4Xt\r\n+9cg8h3C5pMxEsYGI2AvE2wTe6H4KgmpJGb0y7hv1DiHdl30hCcJ5Ss8LYBj\r\n05eEvl5tzJ9R+eZJ1pSSXMKBbEH43n/OmGS/D2QmYe5VQVOARX/313Au+VHh\r\n+iEMqeTY4+dIdktulyFMnoobmPdIG7a5CuIkbKLGWwDV6+UETMuQoIqeatr7\r\nUsZnoU+vmAv5UqPYTV3gsoBP4uiHG8wnAt0/7DMu85MjiLswE5cmdsInDEru\r\nc9sJZ6Hl4jANnBum3FX6GwD+tII5SJ52am8HSQ23tJfxu46gqyLC/QGumjkg\r\nRL7Pw8n7KjXpqFUofb8rxH77N581+/xMszsM83x0OOPS/89CSbPaewBJZMAw\r\nF/d8SMffLVqb3JYPX6wslZmgl0C4xpcQ5lD64XZ9fzo+Z5zkxAhNn6aDPuV1\r\nASPIRyECKTmVwWBLPlIV3pBpbep1K9CxPLVhgEDFdn+xmqWkvQ1ONOzpzFja\r\nyQTpBqGc+pd4AKetNgey1ccGdz+cW1hJXR52OdJqD0uosGcJn5PRjI3ptDUo\r\nFQNo4OflgjrY0R4ObMpkwhWTYSIcugEv3cg=\r\n=UZnV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "d354a7457ca8a24030dbd0a135ee40baed7c774d", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "node --expose_gc ./test/run-web-platform-tests.js", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run && node ./build/downlevel-dts.js", "pretest:wpt": "git submodule update --init --recursive", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local && node ./build/downlevel-dts.js", "build:bundle": "rollup -c"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "16.14.2", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.2.0", "eslint": "^7.23.0", "rollup": "^2.44.0", "jasmine": "^3.7.0", "ts-morph": "^10.0.2", "micromatch": "^4.0.2", "typescript": "^4.2.4", "wpt-runner": "^3.2.1", "@types/node": "^14.14.37", "@rollup/plugin-strip": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-replace": "^2.4.2", "@microsoft/api-extractor": "^7.13.4", "@rollup/plugin-typescript": "^8.2.1", "@typescript-eslint/parser": "^4.21.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^4.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.2.1_1649363340359_0.8115155521118684", "host": "s3://npm-registry-packages"}}, "4.0.0-beta.2": {"name": "web-streams-polyfill", "version": "4.0.0-beta.2", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@4.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "a534d1a8a4bbbb8d2bba07eb27722fde8ee8d077", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.2.tgz", "fileCount": 15, "integrity": "sha512-UHhhnoe2M40uh2r0KVdJTN7qjFytm6o0Yp3VcjwV3bfo6rz8uqvxNoE5yNmGF0y3eFfXaFeb6M09MDSwwLmq4w==", "signatures": [{"sig": "MEYCIQCbnNqCr1+LPsQsbgaMfod8gUgJ9z22eibNf4gNI7dC7wIhALVEnlmac6j7P9eEJtBf8XYkxBnActb1Y6xO+yxyrZ9G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 420006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVK2WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpITQ/6A4vQ3vrix554zW4e9dCBKBW1vq2uWIHm0MeJUq9phdHn4Swm\r\ntbmijJWEfk9jTUlXNVSNNkSs6XoaE0cQluvOX+n2zmPk59Ise4E9krEJtXv3\r\nethwNezctoZZJORKUnzi+wauwYDC/j/6gcyc41hm0AkmY44vqgIj7vDCRTmX\r\nyknhGa3GPkWD4fPepUxoy5yNp+ooT8fjsmTO1xJqXBJIqImM0IB7OYbVYHJq\r\nJuNbtiQ4seUa3l33LS5rwWp7CRt2Mhdxh6FW2y0KzvTuWH4Z8tx/5Py8Vxby\r\nHYBSXWCrr5JoHqD3/LDaqd+QVjjMCaKGehBpdQYxG+K4q92R7TMd7KJPs4Pm\r\nOgtpmO4qhLL1WoM4tgkuEbQcx0urpNXHM/kP/RzvQEehRVOPluqOXdHCiFYm\r\nFJ5grHA8iEcdCc8AOOMHEyhohmBaQTOE7f5gQl+ANavHp806nozIq+i0xVew\r\n/+oFHFWRHF4e+7V2UBoSbbJRdZuZZOIobwMLKxLAzsGRMHY0B95LP/xCc9u/\r\njBME2HRUZ4tvuFikOKgIUSJjEFj7PMSL87vZwJvLf1ix2djr9xoVSSl9kMTr\r\nW5i9Hev2Fp81R9xZl7qW+x4C3jGV7abXvfQr9mBtVdOS3Cf334cZ1T7LXjYR\r\nO26IxR0clSOUHEkIexU52tRNWEVaq58YPAc=\r\n=Tl7s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/ponyfill.js", "types": "types/ponyfill.d.ts", "module": "dist/ponyfill.mjs", "engines": {"node": ">= 14"}, "exports": {".": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./dist/*": "./dist/*", "./package": "./package.json", "./types/*": "./types/*", "./polyfill": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.js"}, "./package.json": "./package.json", "./polyfill/es5": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.es5.js"}}, "gitHead": "3d687503d1f939094ebe1404c43f2e7dd0f4df36", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundler", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:unit": "node --experimental-import-meta-resolve ./node_modules/jasmine/bin/jasmine.js --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local", "build:bundle": "rollup -c", "test:bundler": "npm run test:bundler:rollup && npm run test:bundler:webpack", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium", "test:bundler:rollup": "cd test/rollup && npm ci && npm test", "test:bundler:webpack": "cd test/webpack && npm ci && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tslib": "^2.3.1", "eslint": "^8.13.0", "rollup": "^2.70.1", "jasmine": "^4.0.2", "minimist": "^1.2.6", "micromatch": "^4.0.5", "playwright": "^1.20.2", "typescript": "^4.7.0-beta", "wpt-runner": "^4.1.0", "@types/node": "^14.18.12", "@types/jasmine": "^4.0.2", "abort-controller": "^3.0.0", "recursive-readdir": "^2.2.2", "@rollup/plugin-strip": "^2.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.4", "@rollup/plugin-replace": "^4.0.0", "@microsoft/api-extractor": "^7.21.2", "@rollup/plugin-typescript": "^8.3.1", "@typescript-eslint/parser": "^5.19.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^5.19.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_4.0.0-beta.2_1649716630401_0.3693867922909877", "host": "s3://npm-registry-packages"}}, "4.0.0-beta.3": {"name": "web-streams-polyfill", "version": "4.0.0-beta.3", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@4.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "2898486b74f5156095e473efe989dcf185047a38", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.3.tgz", "fileCount": 15, "integrity": "sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==", "signatures": [{"sig": "MEQCIHLYMZKqqtBu5SzMGygfj4b25sdzuqWJT6oLjrJB8Uh6AiA+llfxkPXTlU7jy+aMxdK3Ma+mU1F4hRIr9+wVQTC8Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijTd0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrB2RAAoUXW/l06KgbNYIQgIFA3d6MxqmLwzpVzHu65ve8+17kD45lZ\r\nY0F616CeakovEu2lSQj9jj4gzE7wweywna9hF02QAY41jTF6ol8qBU9Q3TIT\r\nL1A9zjj2R+XSbj3kzg8vH3Xjs+RqRZRRMgr+rERGVAwRxovaVwZob/PDSz/L\r\nNP81B9Q7uUSKu6w0vFjQY1AXvA1K8Eb/AxY18QubpMXbNkB2Cca5Rvds5/kP\r\nIwzSQ/3T1VjOogJGXySUkpqQN+rUUOduK6L4vsBXfW01QIws7gBcCjLzg6OL\r\nsRq1WyE6MJDiKy0y3Pm1xDmjHz19IjfIpFXf8ukucGzLMb524a4um64fjKlB\r\nq6OY4zWwbO+dXdfZbq9ZEtp9Mgj9tArkc2895FFil69/wSP7cunJSBxAB5ek\r\noka3E7XT523akq7f200FLMlPZxhkwsH3qThwrzGqZLfe3s17agrpDN18I65a\r\nb4Uh3tHTh+d6GGpJGIlA+vFSQ5+KlvdZRlauifxrcCzkSAuLits/ZOgilfvb\r\nTTZ3AOt28FI73ki5WoFca02gJkXIHykhK+ae6zRj/RUHxqzCx9TJW8150TPg\r\nzL3OrWJdPxq2LaHMea9MuwEKG/c775pM3/9Ol/gKR/mEbimKvGBZ4ZewGz8U\r\n4iY05UkTxcbyRdBfb6gjJj1SUuL187Gf6xA=\r\n=mJAz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/ponyfill.js", "types": "types/ponyfill.d.ts", "module": "dist/ponyfill.mjs", "engines": {"node": ">= 14"}, "exports": {".": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./dist/*": "./dist/*", "./package": "./package.json", "./types/*": "./types/*", "./polyfill": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.js"}, "./package.json": "./package.json", "./polyfill/es5": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.es5.js"}}, "gitHead": "4444d642c39a03533bf62a111869b40822ccac35", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundler", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:unit": "node --experimental-import-meta-resolve ./node_modules/jasmine/bin/jasmine.js --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local", "build:bundle": "rollup -c", "test:bundler": "npm run test:bundler:rollup && npm run test:bundler:webpack", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium", "test:bundler:rollup": "cd test/rollup && npm ci && npm test", "test:bundler:webpack": "cd test/webpack && npm ci && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tslib": "^2.3.1", "eslint": "^8.13.0", "rollup": "^2.70.1", "jasmine": "^4.0.2", "minimist": "^1.2.6", "micromatch": "^4.0.5", "playwright": "^1.20.2", "typescript": "^4.7.0-beta", "wpt-runner": "^4.1.0", "@types/node": "^14.18.12", "@types/jasmine": "^4.0.2", "abort-controller": "^3.0.0", "recursive-readdir": "^2.2.2", "@rollup/plugin-strip": "^2.1.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-inject": "^4.0.4", "@rollup/plugin-replace": "^4.0.0", "@microsoft/api-extractor": "^7.21.2", "@rollup/plugin-typescript": "^8.3.1", "@typescript-eslint/parser": "^5.19.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^5.19.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_4.0.0-beta.3_1653421940638_0.06233098248151969", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "web-streams-polyfill", "version": "3.3.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "d16f2dc29e1f23179771697c05b24ff3d157cb3c", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.0.tgz", "fileCount": 41, "integrity": "sha512-qGPA+g7LsFEF3dXQDJdZUSUBEuCONtE303GrFblnE+5BGTIim+h8CcOmYzylo/4in2GcdpirP/fBkM3/J6kWoQ==", "signatures": [{"sig": "MEYCIQDfAJuAFOE0Oql7mej4RGGCJB0rCh5bsYkeU5TpDiXOcgIhAMrOg/eiT6eGoXmWdJmBn/1vNkA+utLlegW7BiWZ/2De", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8998835}, "main": "dist/polyfill", "types": "types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 18"}, "gitHead": "78409d3cf3533af6c4be3258de9ba991aa163515", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run --local", "build:bundle": "rollup -c", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.6.2", "eslint": "^8.56.0", "rollup": "^4.9.2", "jasmine": "^5.1.0", "minimist": "^1.2.5", "micromatch": "^4.0.5", "playwright": "^1.14.1", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/node": "^18.19.4", "recursive-readdir": "^2.2.2", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-replace": "^5.0.5", "@microsoft/api-extractor": "^7.39.1", "@rollup/plugin-typescript": "^11.1.5", "@typescript-eslint/parser": "^6.17.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^6.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.3.0_1704381822106_0.4831674733849718", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "web-streams-polyfill", "version": "3.3.1", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "4d16e0804fd25c2a211f6cd722cf45b0b1cded36", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.1.tgz", "fileCount": 41, "integrity": "sha512-hhvyQPaWKuZpyr19naOWHktLWtbkpOaR048vXDNR9EgYb2Al7rqCd7RBFx68eYZHgkIlQy9UtO01psg/ONg4cQ==", "signatures": [{"sig": "MEUCIB5FpSgNzvAu4eh+6Szjw3hyT2ueZkBnND57sRVJFho9AiEAom+0XlIaZ2W5xLLrPmQRabDTA0jdQHfDHK2McDh9mPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9032974}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "de42d2a95e823f00fc3da21e8ff20bacbb6f34a6", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "build:bundle": "rollup -c", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "postbuild:types": "downlevel-dts ./dist/types/ts3.6/ ./dist/types/ --to=3.5 && node ./build/downlevel-dts.js", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "broken publish, upgrade to v3.3.2", "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "18.18.0", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.6.2", "eslint": "^8.56.0", "rollup": "^4.9.2", "jasmine": "^5.1.0", "minimist": "^1.2.5", "ts-morph": "^10.0.2", "micromatch": "^4.0.5", "playwright": "^1.14.1", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/node": "^18.19.4", "downlevel-dts": "^0.11.0", "recursive-readdir": "^2.2.2", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-replace": "^5.0.5", "@microsoft/api-extractor": "^7.39.1", "@rollup/plugin-typescript": "^11.1.5", "@typescript-eslint/parser": "^6.17.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^6.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.3.1_1704390005058_0.39184024980433363", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "web-streams-polyfill", "version": "3.3.2", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "32e26522e05128203a7de59519be3c648004343b", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.2.tgz", "fileCount": 43, "integrity": "sha512-3pRGuxRF5gpuZc0W+EpwQRmCD7gRqcDOMt688KmdlDAgAyaB1XlN0zq2njfDNm44XVdIouE7pZ6GzbdyH47uIQ==", "signatures": [{"sig": "MEUCIQCntMoSbFOPufGMy8o1PPuyHdON2/vw7Cp42Fri58v3aQIgaK9zAhEP7HWuNBfHb7qb2iGkxcNU+a3h4BJs0tv7ZK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035432}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "49c215e6106799816e9d3de0649c7a20981a6ab3", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "build:bundle": "rollup -c", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "postbuild:types": "downlevel-dts ./dist/types/ts3.6/ ./dist/types/ --to=3.5 && node ./build/downlevel-dts.js", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "18.18.0", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.6.2", "eslint": "^8.56.0", "rollup": "^4.9.2", "jasmine": "^5.1.0", "minimist": "^1.2.5", "ts-morph": "^10.0.2", "micromatch": "^4.0.5", "playwright": "^1.14.1", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/node": "^18.19.4", "downlevel-dts": "^0.11.0", "recursive-readdir": "^2.2.2", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-replace": "^5.0.5", "@microsoft/api-extractor": "^7.39.1", "@rollup/plugin-typescript": "^11.1.5", "@typescript-eslint/parser": "^6.17.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^6.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.3.2_1704390682467_0.9624001986509192", "host": "s3://npm-registry-packages"}}, "3.3.3": {"name": "web-streams-polyfill", "version": "3.3.3", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@3.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "2073b91a2fdb1fbfbd401e7de0ac9f8214cecb4b", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "fileCount": 43, "integrity": "sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==", "signatures": [{"sig": "MEQCIC04mxSFnuq+iqYgHE4XYRl8o4ZBDG+PAWJ5ZIeD1IbPAiBzOBdQ89z82wvtMvE9ZSLXn6SKW3jfstUpSaiAzkiW2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/web-streams-polyfill@3.3.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 9036205}, "main": "dist/polyfill", "types": "dist/types/polyfill.d.ts", "module": "dist/polyfill.mjs", "browser": "dist/polyfill.min.js", "engines": {"node": ">= 8"}, "gitHead": "ef5c9e9094341b721e8724eb9589a711733d0edb", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:unit": "jasmine --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "build:bundle": "rollup -c", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "postbuild:types": "downlevel-dts ./dist/types/ts3.6/ ./dist/types/ --to=3.5 && node ./build/downlevel-dts.js", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "20.11.0", "typesVersions": {">=3.6": {"dist/types/*": ["dist/types/ts3.6/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.6.2", "eslint": "^8.56.0", "rollup": "^4.9.2", "jasmine": "^5.1.0", "minimist": "^1.2.5", "ts-morph": "^10.0.2", "micromatch": "^4.0.5", "playwright": "^1.14.1", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/node": "^18.19.4", "downlevel-dts": "^0.11.0", "recursive-readdir": "^2.2.2", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-replace": "^5.0.5", "@microsoft/api-extractor": "^7.39.1", "@rollup/plugin-typescript": "^11.1.5", "@typescript-eslint/parser": "^6.17.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^6.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_3.3.3_1708118147627_0.37609771191882335", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "web-streams-polyfill", "version": "4.0.0", "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "web-streams-polyfill@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "dist": {"shasum": "74cedf168339ee6e709532f76c49313a8c7acdac", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.0.0.tgz", "fileCount": 12, "integrity": "sha512-0zJXHRAYEjM2tUfZ2DiSOHAa2aw1tisnnhU3ufD57R8iefL+DcdJyRBRyJpG+NUimDgbTI/lH+gAE1PAvV3Cgw==", "signatures": [{"sig": "MEUCIFjh29qDVRWMXKoEyHWlD8+IwwDVW/oN2RT+0HU8RADAAiEAjQK2XxmavEHWR1HepsJEe8vl92tc2Y3LlEehSnTU31k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/web-streams-polyfill@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 441002}, "main": "dist/ponyfill.js", "types": "types/ponyfill.d.ts", "module": "dist/ponyfill.mjs", "engines": {"node": ">= 8"}, "exports": {".": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./dist/*": "./dist/*", "./package": "./package.json", "./types/*": "./types/*", "./polyfill": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.js"}, "./package.json": "./package.json", "./polyfill/es5": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.es5.js"}}, "gitHead": "9eeeb1355f46f1ffd116799d236a34987d1f4e45", "scripts": {"lint": "eslint \"src/**/*.ts\"", "test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundlers", "build": "npm run build:bundle && npm run build:types", "prepare": "npm run build", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:unit": "node --experimental-import-meta-resolve node_modules/jasmine/bin/jasmine.js --config=test/unit/jasmine.json", "test:types": "tsc -p ./test/types/tsconfig.json", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "build:bundle": "rollup -c", "test:bundlers": "npm run test:bundler:rollup && npm run test:bundler:webpack", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.js", "test:wpt:firefox": "node ./test/wpt/browser/run.js --browser firefox", "test:wpt:chromium": "node ./test/wpt/browser/run.js --browser chromium", "test:bundler:rollup": "cd test/rollup && npm install && npm test", "test:bundler:webpack": "cd test/webpack && npm install && npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Web Streams, based on the WHATWG spec reference implementation", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "devDependencies": {"tslib": "^2.6.2", "eslint": "^8.57.0", "rollup": "^4.12.0", "jasmine": "^5.1.0", "minimist": "^1.2.5", "micromatch": "^4.0.5", "playwright": "^1.42.0", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/node": "^20.11.21", "recursive-readdir": "^2.2.2", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-replace": "^5.0.5", "@microsoft/api-extractor": "^7.41.1", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^7.1.0", "@ungap/promise-all-settled": "^1.1.2", "@typescript-eslint/eslint-plugin": "^7.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/web-streams-polyfill_4.0.0_1709157183947_0.217138679026474", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "web-streams-polyfill", "version": "4.1.0", "description": "Web Streams, based on the WHATWG spec reference implementation", "main": "dist/ponyfill.js", "module": "dist/ponyfill.mjs", "types": "types/ponyfill.d.ts", "exports": {".": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.mjs", "require": "./dist/ponyfill.js"}, "./es5": {"types": "./types/ponyfill.d.ts", "import": "./dist/ponyfill.es5.mjs", "require": "./dist/ponyfill.es5.js"}, "./polyfill": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.js"}, "./polyfill/es5": {"types": "./types/polyfill.d.ts", "default": "./dist/polyfill.es5.js"}, "./dist/*": "./dist/*", "./types/*": "./types/*", "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"test": "npm run test:types && npm run test:unit && npm run test:wpt && npm run test:bundlers", "test:wpt": "npm run test:wpt:node && npm run test:wpt:chromium && npm run test:wpt:firefox", "test:wpt:node": "node --expose_gc ./test/wpt/node/run.mjs", "test:wpt:chromium": "node ./test/wpt/browser/run.mjs --browser chromium", "test:wpt:firefox": "node ./test/wpt/browser/run.mjs --browser firefox", "test:bundlers": "npm run test:bundler:rollup && npm run test:bundler:webpack", "test:bundler:rollup": "cd test/rollup && npm install && npm test", "test:bundler:webpack": "cd test/webpack && npm install && npm test", "test:types": "tsc -p ./test/types/tsconfig.json", "test:unit": "jasmine --config=test/unit/jasmine.json", "lint": "eslint \"*.mjs\" \"src/**/*.ts\"", "build": "npm run build:bundle && npm run build:types", "build:bundle": "rollup -c", "build:types": "tsc --project . --emitDeclarationOnly --declarationDir ./lib && api-extractor run", "accept:types": "npm run build:types -- --local", "prepare": "npm run build"}, "engines": {"node": ">= 8"}, "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "keywords": ["streams", "whatwg", "polyfill"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "devDependencies": {"@eslint/js": "^9.17.0", "@microsoft/api-extractor": "^7.48.1", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "@stylistic/eslint-plugin": "^2.12.1", "@types/node": "^20.17.11", "eslint": "^9.17.0", "globals": "^15.14.0", "jasmine": "^5.5.0", "micromatch": "^4.0.8", "minimist": "^1.2.8", "playwright": "^1.49.1", "rollup": "^4.29.2", "st": "^3.0.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.19.0", "wpt-runner": "^6.0.0"}, "_id": "web-streams-polyfill@4.1.0", "gitHead": "ea1b0b7dcc6558192226ab6e2256611b138b1b9d", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-A7Jxrg7+eV+eZR/CIdESDnRGFb6/bcKukGvJBB5snI6cw3is1c2qamkYstC1bY1p08TyMRlN9eTMkxmnKJBPBw==", "shasum": "3ba095d0eb3ef6377cd126e8354b2cdba286e0d3", "tarball": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.1.0.tgz", "fileCount": 12, "unpackedSize": 442126, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/web-streams-polyfill@4.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVjeDAmIHYM06I+G+reIXbuoaVvOrJIujVyEkFE/SHXAIhAJIV1TFVdorXBuqbSG/utgzmYH7wbnOLg76PJbP9gII5"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/web-streams-polyfill_4.1.0_1736109861424_0.2548391284379421"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-03-02T18:29:50.194Z", "modified": "2025-01-05T20:44:22.011Z", "1.0.0": "2016-03-02T18:29:50.194Z", "1.0.1": "2016-03-03T22:17:52.632Z", "1.1.0": "2016-04-03T06:26:58.870Z", "1.1.1": "2016-04-03T06:35:16.278Z", "1.2.0": "2016-07-16T07:53:34.453Z", "1.2.1": "2016-07-16T08:32:16.074Z", "1.2.2": "2016-07-16T08:52:07.238Z", "1.3.0": "2016-08-07T08:45:04.848Z", "1.3.1": "2016-12-14T07:53:14.473Z", "1.3.2": "2016-12-14T14:24:30.374Z", "2.0.0": "2019-03-10T14:23:06.906Z", "2.0.1": "2019-03-16T12:20:24.835Z", "2.0.2": "2019-03-17T16:52:29.614Z", "2.0.3": "2019-04-04T22:02:37.198Z", "2.0.4": "2019-08-01T18:24:23.106Z", "2.0.5": "2019-10-08T12:43:19.570Z", "2.0.6": "2019-11-08T12:21:41.985Z", "2.1.0": "2020-02-23T11:54:11.636Z", "2.1.1": "2020-04-11T08:44:54.533Z", "3.0.0": "2020-07-20T19:19:03.695Z", "3.0.1": "2020-11-12T00:08:03.392Z", "3.0.2": "2021-02-09T23:46:12.139Z", "3.0.3": "2021-04-09T17:12:48.510Z", "3.1.0": "2021-07-21T21:56:44.376Z", "3.1.1": "2021-09-05T22:21:14.167Z", "4.0.0-beta.1": "2021-09-06T20:14:22.133Z", "3.2.0": "2021-11-06T15:05:35.960Z", "3.2.1": "2022-04-07T20:29:00.636Z", "4.0.0-beta.2": "2022-04-11T22:37:10.568Z", "4.0.0-beta.3": "2022-05-24T19:52:20.773Z", "3.3.0": "2024-01-04T15:23:42.365Z", "3.3.1": "2024-01-04T17:40:05.287Z", "3.3.2": "2024-01-04T17:51:22.720Z", "3.3.3": "2024-02-16T21:15:47.924Z", "4.0.0": "2024-02-28T21:53:04.107Z", "4.1.0": "2025-01-05T20:44:21.625Z"}, "bugs": {"url": "https://github.com/MattiasBuelens/web-streams-polyfill/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/MattiasBuelens/web-streams-polyfill#readme", "keywords": ["streams", "whatwg", "polyfill"], "repository": {"type": "git", "url": "git+https://github.com/MattiasBuelens/web-streams-polyfill.git"}, "description": "Web Streams, based on the WHATWG spec reference implementation", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "whiterabbit1983", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# web-streams-polyfill\n\nWeb Streams, based on the WHATWG spec reference implementation.  \n\n[![build status](https://api.travis-ci.com/MattiasBuelens/web-streams-polyfill.svg?branch=master)](https://travis-ci.com/MattiasBuelens/web-streams-polyfill)\n[![npm version](https://img.shields.io/npm/v/web-streams-polyfill.svg)](https://www.npmjs.com/package/web-streams-polyfill)\n[![license](https://img.shields.io/npm/l/web-streams-polyfill.svg)](https://github.com/MattiasBuelens/web-streams-polyfill/blob/master/LICENSE)\n\n## Links\n\n - [Official spec][spec]\n - [Reference implementation][ref-impl]\n\n## Usage\n\nThis library comes in multiple variants:\n* `web-streams-polyfill`: a [ponyfill] that provides the stream implementations \n  without replacing any globals, targeting ES2015+ environments.\n  Recommended for use in Node 6+ applications, or in web libraries supporting modern browsers.\n* `web-streams-polyfill/es5`: a ponyfill targeting ES5+ environments.\n  Recommended for use in legacy Node applications, or in web libraries supporting older browsers.\n* `web-streams-polyfill/polyfill`: a polyfill that replaces the native stream implementations,\n  targeting ES2015+ environments.\n  Recommended for use in web apps supporting modern browsers through a `<script>` tag.\n* `web-streams-polyfill/polyfill/es5`: a polyfill targeting ES5+ environments.\n  Recommended for use in web apps supporting older browsers through a `<script>` tag.\n\nEach variant also includes TypeScript type definitions, compatible with the DOM type definitions for streams included in TypeScript.\nThese type definitions require TypeScript version 4.7 or higher.\n\nIn version 4, the list of variants was reworked to have more modern defaults and to reduce the download size of the package.\nSee the [migration guide][migrating] for more information.\n\nUsage as a polyfill:\n```html\n<!-- option 1: hosted by unpkg CDN -->\n<script src=\"https://unpkg.com/web-streams-polyfill/dist/polyfill.js\"></script>\n<!-- option 2: self hosted -->\n<script src=\"/path/to/web-streams-polyfill/dist/polyfill.js\"></script>\n<script>\nvar readable = new ReadableStream();\n</script>\n```\nUsage as a Node module:\n```js\nvar streams = require(\"web-streams-polyfill\");\nvar readable = new streams.ReadableStream();\n```\nUsage as a ponyfill from within a ES2015 module:\n```js\nimport { ReadableStream } from \"web-streams-polyfill\";\nconst readable = new ReadableStream();\n```\nUsage as a polyfill from within an ES2015 module:\n```js\nimport \"web-streams-polyfill/polyfill\";\nconst readable = new ReadableStream();\n```\n\n## Compatibility\n\nThe `polyfill` and `ponyfill` variants work in any ES2015-compatible environment.\n\nThe `polyfill/es5` and `ponyfill/es5` variants work in any ES5-compatible environment that has a global `Promise`.\nIf you need to support older browsers or Node versions that do not have a native `Promise` implementation\n(check the [support table][promise-support]), you must first include a `Promise` polyfill\n(e.g. [promise-polyfill][promise-polyfill]).\n\n[Async iterable support for `ReadableStream`][rs-asynciterator] is available in all variants, but requires an ES2018-compatible environment or a polyfill for `Symbol.asyncIterator`.\n\n[`WritableStreamDefaultController.signal`][ws-controller-signal] is available in all variants, but requires a global `AbortController` constructor. If necessary, consider using a polyfill such as [abortcontroller-polyfill].\n\n[Reading with a BYOB reader][mdn-byob-read] is available in all variants, but requires `ArrayBuffer.prototype.transfer()` or `structuredClone()` to exist in order to correctly transfer the given view's buffer. If not available, then the buffer won't be transferred during the read.\n\n### Tooling compatibility\n\nThis package uses [subpath exports](https://nodejs.org/api/packages.html#subpath-exports) for its variants. As such, you need Node 12 or higher in order to `import` or `require()` such a variant.\n\nWhen using TypeScript, make sure your [`moduleResolution`](https://www.typescriptlang.org/tsconfig#moduleResolution) is set to `\"node16\"`, `\"nodenext\"` or `\"bundler\"`.\n\n## Compliance\n\nThe polyfill implements [version `fa4891a` (3 Dec 2024)][spec-snapshot] of the streams specification.\n\nThe polyfill is tested against the same [web platform tests][wpt] that are used by browsers to test their native implementations.\nThe polyfill aims to pass all tests, although it allows some exceptions for practical reasons:\n* The default (ES2015) variant passes all of the tests, except for the [test for the prototype of `ReadableStream`'s async iterator][wpt-async-iterator-prototype].\n  Retrieving the correct `%AsyncIteratorPrototype%` requires using an async generator (`async function* () {}`), which is invalid syntax before ES2018.\n  Instead, the polyfill [creates its own version][stub-async-iterator-prototype] which is functionally equivalent to the real prototype.\n* The ES5 variant passes the same tests as the ES2015 variant, except for various tests about specific characteristics of the constructors, properties and methods.\n  These test failures do not affect the run-time behavior of the polyfill.\n  For example:\n  * The `name` property of down-leveled constructors is incorrect.\n  * The `length` property of down-leveled constructors and methods with optional arguments is incorrect.\n  * Not all properties and methods are correctly marked as non-enumerable.\n  * Down-leveled class methods are not correctly marked as non-constructable.\n\n## Contributors\n\nThanks to these people for their work on [the original polyfill][creatorrr-polyfill]:\n\n - Diwank Singh Tomer ([creatorrr](https://github.com/creatorrr))\n - Anders Riutta ([ariutta](https://github.com/ariutta))\n\n[spec]: https://streams.spec.whatwg.org\n[ref-impl]: https://github.com/whatwg/streams\n[ponyfill]: https://github.com/sindresorhus/ponyfill\n[migrating]: https://github.com/MattiasBuelens/web-streams-polyfill/blob/master/MIGRATING.md\n[promise-support]: https://kangax.github.io/compat-table/es6/#test-Promise\n[promise-polyfill]: https://www.npmjs.com/package/promise-polyfill\n[rs-asynciterator]: https://streams.spec.whatwg.org/#rs-asynciterator\n[ws-controller-signal]: https://streams.spec.whatwg.org/#ws-default-controller-signal\n[abortcontroller-polyfill]: https://www.npmjs.com/package/abortcontroller-polyfill\n[mdn-byob-read]: https://developer.mozilla.org/en-US/docs/Web/API/ReadableStreamBYOBReader/read\n[spec-snapshot]: https://streams.spec.whatwg.org/commit-snapshots/fa4891a35ff05281ff8ed66f8ad447644ea7cec3/\n[wpt]: https://github.com/web-platform-tests/wpt/tree/7ef95a1c3f1c178e455b21569eddb31af7c3691f/streams\n[wpt-async-iterator-prototype]: https://github.com/web-platform-tests/wpt/blob/7ef95a1c3f1c178e455b21569eddb31af7c3691f/streams/readable-streams/async-iterator.any.js#L24\n[stub-async-iterator-prototype]: https://github.com/MattiasBuelens/web-streams-polyfill/blob/v4.0.0/src/lib/readable-stream/async-iterator.ts#L143-L147\n[creatorrr-polyfill]: https://github.com/creatorrr/web-streams-polyfill\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true, "psychollama": true}}