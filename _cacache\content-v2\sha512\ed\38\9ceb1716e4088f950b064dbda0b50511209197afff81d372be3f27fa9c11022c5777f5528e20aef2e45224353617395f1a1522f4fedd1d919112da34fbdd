{"_id": "get-tsconfig", "_rev": "44-c913afcdd560ea4caed9c1755debe4e0", "name": "get-tsconfig", "dist-tags": {"latest": "4.10.1"}, "versions": {"1.0.1": {"name": "get-tsconfig", "version": "1.0.1", "keywords": ["typescript", "tsconfig"], "license": "MIT", "_id": "get-tsconfig@1.0.1", "maintainers": [{"name": "jayrylan", "email": "<EMAIL>"}], "homepage": "https://github.com/typeslick/get-tsconfig", "bugs": {"url": "https://github.com/typeslick/get-tsconfig/issues"}, "dist": {"shasum": "373d5587a71584b66d602b8514cb3b27a8b0cffb", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-RINgl9zhxzTPAX4TI9ypdsjXzj37vtDcNU79UeOVGHEIeddO+VUIU5zqHtVHKf3JiYSxTm1GXMbU9FUY0ngpCA==", "signatures": [{"sig": "MEUCIQCwHEMvaI8mr5Se2C7m2xc5goa13E83GE5C6eOGYnEX/QIgWHJ+wzjec3jVLB9j/W3+zkFkBagy+/EBtxxusXPX9cU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcb14/CRA9TVsSAnZWagAAH5oP/3NVJKoS3xjIPHVQFQNE\nPwB9HFKF4TL/AlKjYMNgJJZ5LRA5Lo95RlaVDV8BlS1QvBsgm5bbj09/yfo6\nQUEKkwnBKdPr8s2pyFxW4v3JoTBJOzG939FygBXvCK6xNSSfEKdO8QPxFmGM\nejNzE8ty5PIKp+eZEBRgeJpqKAqjyJeIIb/nngV9hqf1haf6SsW6xYaVdrWq\nN1efH0rmmSP/766XQcCsBFZsIgm5UMkx6BGxpwGA5sYfUXuNmVCmWMdWA4Vr\nh44EC7WohpHutF9iA6lR626E5cWxd3Oen80VMhU++amJ2KVC2n5gTA/4zqYM\nqi4ij5Eo9oXMNnqd74I0q/m107UauvMClcKf02CI7rJ182WhsI4xlLo5e+qe\ndx+/5+HMqrF0rVyV/arPNBhezyHQGEeg+hhmNWW6KRG4W1qLyv1zJSNQF9Lf\n+P0dySPxPnXrFb4B49evtGY0dx6SLnwORiXizX41O049pDpimIatM6FbDYgc\naCsYgPmf21LcOYwB2k241ALmkXdcMk36x4RlBnWLvI/GbiJwajnfsOcRMvUE\ncuWNzvJO/cvKp5hqTpm5sSQVMPCeULwupX/S36NsJL9+T4+nx35kkORhNEfC\nUrvKcqmOv8Yrw+CM/kOIMq084yi+LZsVixqKRCxh/sIyE0sH3CIwWRF9hOGd\n9V7w\r\n=aCt+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist-node/index.js", "pika": true, "types": "dist-types/index.d.ts", "esnext": "dist-src/index.js", "module": "dist-src/index.js", "_npmUser": {"name": "jayrylan", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/typeslick/get-tsconfig.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Utilities for finding and parsing TypeScript configuration files.", "directories": {}, "sideEffects": false, "_nodeVersion": "11.8.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^24.1.0", "weak": "^1.0.1", "cspell": "^3.1.3", "tslint": "^5.11.0", "core-js": "^2.6.0", "typedoc": "^0.14.2", "prettier": "^1.15.3", "@babel/cli": "^7.2.0", "@pika/pack": "^0.3.2", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.1.0", "typescript": "^3.3.3", "@babel/core": "^7.2.2", "@types/jest": "^24.0.6", "@types/node": "^11.9.4", "tslint-slick": "^5.0.0", "tsconfig-slick": "^3.0.2", "@types/fs-extra": "^5.0.4", "@types/exit-hook": "^1.1.0", "@types/jest-diff": "^20.0.1", "semantic-release": "^15.13.0", "@types/babel__core": "^7.0.4", "babel-preset-slick": "^7.0.4", "jest-serializer-path": "^0.1.15", "@babel/runtime-corejs2": "^7.2.0", "@pika/plugin-build-web": "^0.3.11", "@pika/plugin-build-node": "^0.3.11", "typedoc-plugin-markdown": "^1.1.26", "@microsoft/api-extractor": "^7.0.17", "@pika/plugin-build-types": "^0.3.11", "@types/write-file-atomic": "^2.1.1", "typescript-tslint-plugin": "^0.3.1", "@pika/plugin-standard-pkg": "^0.3.11", "@loomble/cspell-dictionary": "*"}, "peerDependencies": {"typescript": "*"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_1.0.1_1550802494747_0.7705865120923983", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "get-tsconfig", "version": "2.0.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@2.0.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "d64b2cbd35013a6c0e4d99f9e7d2051498f95106", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-fDtWGqDNbD6tOvfKt8SJ028s1hH3D4/xB8OzzUH2iqFfuHwEdQMVzRwBYjHFLe2qM+k8/0uAibuwt7kvlwuSGQ==", "signatures": [{"sig": "MEUCIQCLfl8SH/QE+PpNfh2NhGgBEEHf7C6SXa0qsywOzyQpfgIgXmGGoaTvPIN8sFE83CS7dfLRp/Yj+X8E9I0stG4DbNg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3y2qCRA9TVsSAnZWagAA0O4P/1uE6J89LuUVftsxV+S6\nwyW3OpTScXUDQJrXO3ZpmtkkTdKbgBuUu21y+nc7df4ygA2fvW9rdmEa9Vpw\nTD6svbrwaS+cKIaa7u1a9LtVE8ZCkeVg/UmdfTK+J4R9RFVj+Zfa+BxKAXo1\n9wgIwJp3NCyAqgRg3dKvTmmWCpZf3bZoxGaKaAAEfOA9mIUdbGRqgK5UWX8Y\nF2efS7nm0dK6eCy93NhTawOF1XT6zPppn1/ZoatS4qhbmvswNl8oNcq02psF\naURR2xaRCBQBKLw6880xQ/Ky6YbvHDJ6vA8Gbry2svf142W77vCvYZ1uaFeG\nyynG3spuF8p+SOHwX6Bf/CndZ8LcEc24dZeFE4QfHemCC2JAoIDrgScyPfr0\n6qULq8+vr88hSaV6w7OFeAcHNfgUCU5HykkLBDZDzlhxrYIfj3ifgDS1wZVl\n/RYoVU9i3+/yu3QF5FQ+9ZlQhLv13SD10maR/mzaLTiQlZBqpHekSONMhBrN\nTwHjKkwncYYihgVSL9THOM/z6ohprSaj/EIDDwc17d7cwAbLQxMVrVa/UOY2\naRr0vPmckhgclTNsiOav/s5NnwBAkLyTGMrBTgtqYa/xXqAaziyNv4CGg/St\ntHD6aSEhtUOu6CWw99IN79vwJJsTQ8JWcsGAq25+P9dOgLgmP2RQiBrhJJ1y\naW2C\r\n=eKOx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"pre-commit": "npm run build && lint-staged && npm test"}}, "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "4b5171b6542284b1b2b99b5b8e3d9972a44eaba0", "scripts": {"lint": "eslint .", "test": "jest", "build": "tsup src/index.ts --clean --format cjs,esm --dts --minify"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Find and/or parse the tsconfig.json file from a file or directory path", "directories": {}, "lint-staged": {"*.{js,ts}": ["eslint", "jest --bail --findRelatedTests"]}, "_nodeVersion": "14.18.3", "dependencies": {"aggregate-error": "3.1.0"}, "eslintConfig": {"extends": "@pvtnbr/eslint-config", "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.4.7", "tsup": "^5.11.11", "husky": "^4.3.8", "eslint": "^8.6.0", "es-jest": "^1.5.0", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "@types/node": "^17.0.8", "lint-staged": "^12.1.7", "@pvtnbr/eslint-config": "^0.11.0"}, "peerDependencies": {"typescript": "^4.0.8"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_2.0.0_1642016170106_0.20961377153874805", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "get-tsconfig", "version": "3.0.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@3.0.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "193b0cb0f6a3be3c702ea9be704f407c064857b6", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-3.0.0.tgz", "fileCount": 6, "integrity": "sha512-6M+rJ2v+VULEBkV6g59960wtjdoevNgWWhx+UMi8u2xDiIHg8CtVmB0bpUyAY0znHCT00BvOPr4JoH6FGDx+zQ==", "signatures": [{"sig": "MEQCIBgD9rXQOR61Xa/Xroa8KyQESVjFQ/oQwxnV6lma21F1AiBZh/hgmHVXs8EYe/aswtmwjcaMnmEhw3J4Ew4UzKL5KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRd/vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjRA/+Nee+XJDXkfsohMFjeLR+HqNX+/4ga4RC+0LRKA4zRRgoAb7y\r\nNQoNPIkNZyfxXzkQQnhK+odIRKoRGzJo0kw92LLOV5N70MQfxggUOPQW/f+L\r\nuc/McRF3v0YGl8qLcGFLFIfmdicLdSOiVdUHnDG60kVnhdzpWrnGkTPJE60U\r\n+vIRIgNdLYtmzJGjfLPfUf11EUQvkQ6yMxcHAlmfkxt9O1g8W1jaH4HvSFk9\r\n0S2uJlyU5hAcc+bHFvLXTm2rJqTA3Jhvdukm1h5Ru/6ORND7S7GVRPuqbmkF\r\nCOygQyqk0AjcZFTkjf9nXTtN+0NmoGK4la/cYZTCLwM/33bUbRg9908brGOm\r\nQQtdHkPxUnbm+mGJare7f6yFVVqItVkQyfyccEiqQ1ApDF26MbkA4zyXQm8Q\r\no8Ug91B4U3p7qsVfk1f0QmeBhoO8IDvh0EUg4k+B54/WUDbKmfAYkKe6GELC\r\nrc4fpjL5IGSw6e9SwMz0O6CSeKno1uQ0+Hjbw1lLWJud0Bh/eYT7P4TF5GX1\r\nrtyFUNUXJuXIfPnLwvWMWM5iz+JWwl2AX9G+5K3Yx/Uhy/m09wIIynJHFLBV\r\n6ktUTAuBL3S/0LVMrI22wYgdxTeuR2gVw7NR3U99mb2PK1OZVWRN5lelyKUE\r\n5dF59td7+AF2V2y+UvCwl2NVaKp7QXGqkuY=\r\n=SyBh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"pre-commit": "npm run build && lint-staged && npm test"}}, "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "e58443a8ad12a28060a8a667297ddc14a31689d2", "scripts": {"lint": "eslint .", "test": "esno tests", "build": "pkgroll --minify"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "lint-staged": {"*.{js,ts}": ["eslint", "npm test"]}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"esno": "^0.14.1", "execa": "^6.1.0", "husky": "^4.3.8", "slash": "^4.0.0", "eslint": "^8.12.0", "manten": "^0.0.3", "es-jest": "^1.5.0", "pkgroll": "file:vendors/pkgroll-0.0.0.tgz", "type-fest": "github:privatenumber/type-fest#built/tsconfig-watch-options", "typescript": "^4.6.3", "@types/node": "^17.0.23", "lint-staged": "^12.3.7", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_3.0.0_1648746478793_0.8709627030076144", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "get-tsconfig", "version": "3.0.1", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@3.0.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "02cadb5abc5f0d53033c8b2f3005b84134ba22e9", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-3.0.1.tgz", "fileCount": 6, "integrity": "sha512-+m30eQjbcf3xMNdnacXH5IDAKUMbI7Mhbf3e1BHif1FzBlUhBzBlmOVc7kL4+kB035l8OCyBdI3dNXZ3of9HqA==", "signatures": [{"sig": "MEUCIBm6mDNgbbYfRa1cadvScRXkBp4gblxRhQv/HIglUrVEAiEAgh3ZHcA5BDIo8mzwam+HFqptbrGf68qdaEKyZ9NUNVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRiezACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQ0w//Qd9d3bPXGUgSqiNC7W868exLosQTyhJvuyrZh4qvcxuIDesQ\r\nA2U98E4f29ga9eXXV1kV+wzDtiecanKxLQ1mO57IvPB6XwhMnng0n4A7hlA3\r\nHuE2t780IKvfvAARkcp9L+klN5ySN84O993yilMHvuXUA7Z4ZyGoYL4wvXBy\r\nY12xDcZH3wtzQJFSynTihD/+gtFjViZFMIw6cjy7KMguluxE4X1qQ2eSfgkH\r\n3mAwp4T4q+jIYOdDpZhhATCUwUNX0FYtQsZbFr5O+MHFuUnes04jfo87/knY\r\n99Xd93UjUxfvFDNk1XSfzS1C6ckq6P/sL1uMUBZc3KEfoP8QM71Vrh+bsSFp\r\nl5iMU22w8Bsc8uXZ70fwY7N+MyMsdjs6XdUJekgZ0tFJw5kzHLJORy3Q56Zy\r\n5aUEzacHKjED6usDimhMuqDW3VXYgGut4bUtSvgHkgjsF5JdnYosUMBfaF3E\r\npeOzKjqrPX76R5vUD3Yx3dhXSONUl6c4VmiWQuKXzZR5GmC3XG4Z0OCWqWT8\r\nPhHrCR/tqW3yiho9+F6ZyatJTjKXD+qD2LqJVrfltEgGBp5u9xhZjQkRGgW5\r\nt4z38HpatwAQvfPRikDWz2i6ylKQRedlT5/Ftc0JLu6lbBMTP35NHsQCSysI\r\ne9iGVE9/pVxGq4uIGmo228HveCY1OJ7yiXY=\r\n=jWME\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"pre-commit": "npm run build && lint-staged && npm test"}}, "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "f246acccf762be5699189b2d09f1e00d32359b04", "scripts": {"lint": "eslint .", "test": "esno tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "lint-staged": {"*.{js,ts}": ["eslint", "npm test"]}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"esno": "^0.14.1", "execa": "^6.1.0", "husky": "^4.3.8", "slash": "^4.0.0", "eslint": "^8.12.0", "manten": "^0.0.3", "es-jest": "^1.5.0", "pkgroll": "^1.0.1", "type-fest": "^2.12.2", "typescript": "^4.6.3", "@types/node": "^17.0.23", "lint-staged": "^12.3.7", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.19.1"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_3.0.1_1648764851768_0.5791969349916533", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "get-tsconfig", "version": "3.1.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@3.1.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "4491699356861f53ffc68477f20c259146530a30", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-3.1.0.tgz", "fileCount": 6, "integrity": "sha512-/8GSkE76aYtvKYX+uNShbgxI0tRbksEbbjgPOpk0Ca8QqkVZDxTmh6OCQXZaqqOSdfXkEHkvLQ7iPvFd2fnwVQ==", "signatures": [{"sig": "MEQCIE5D3TSWc2YI6knrEFgyOD8g/Zfs8EkY2HFPfEDeLPxsAiAYu1QWlP54G9/V3sr9OB7kq9dxykE15QkTPyQf5vCHrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJin7mKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCjg//QwYgfnDFB0hheCpxJ6GDWFjWjA/DN15ElwlpS67zSwtIiyw3\r\nNK+MCWL17p5AGDp+GZ3oVDsrRKBHtdu+qVibCs7x6qRzQQogEKw5OtK2mIpH\r\nz0ewnlsH79+bvNufBCIlXRpQL9k5pD2gSjrGxrjEOskb+ezDvXG3TKb5IRn2\r\nY+QdwwImGVfcL8hq8cOXfwvE+j2LLiEeeFulU4DHNv6ac/7FT257wFDPMmtQ\r\n+kNSH4f+GSWqDq3ErHm+P3BmlyPf++p5X8zHhYeCGWaxdDy68BKfoGth/Vic\r\n9UvmfqMFEeyntgEJcSqmEm4QxdHgrtiawsCbwE0px7v8uLJJUb3KEuNNrt93\r\nEh2hUEVYAzYoeidqxXpMTelBOLqHh2cYNX9a7IqsM0dkR2r9bWl2gnY5FZjf\r\n1gnWxqMUXE/V4JIaJ6rZbguFcioGibOb/inNVCpj2pZiWfeVzb89ZMuHik48\r\nCXaqL8TeQFKJPXSqFF01haJJAhp3NxLyXP7YQRkhtaiFElSF/LDeHF3Avru4\r\n8USMHG4yNQMeWi2NiU3i00H4a77G2WxjwY0rcO8H4uvjogBlht3kGmce3Ots\r\ndjufvOa1xiMhgXFORtJFGfvCQqRwWYp2KQW1rY4hzAMiF4sTVR4rJokkvuKd\r\nHx0YeAIssH/JSlZX0vUoKEPO0pNP99LqLiY=\r\n=SUEK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"pre-commit": "npm run build && lint-staged && npm test"}}, "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "48c2df844fc1cf5fe41ba0e990f5ae077ec8bfb8", "scripts": {"lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "lint-staged": {"*.{js,ts}": ["eslint", "npm test"]}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.4.2", "execa": "^6.1.0", "husky": "^4.3.8", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "es-jest": "^1.5.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.0", "typescript": "^4.7.3", "@types/node": "^17.0.41", "lint-staged": "^13.0.0", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_3.1.0_1654634890241_0.7855808398485811", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "get-tsconfig", "version": "3.2.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@3.2.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "95d823dbddd22e139ed873b1ce41bccefc18001f", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-3.2.0.tgz", "fileCount": 10, "integrity": "sha512-PChG1QIKJKpzrgR/wRhX5TWMKqH1cIHViJYDk0cjXhpwXFHIGGJiL5TXu/frupyetjXXxeDInWbP4vhvhHSBkg==", "signatures": [{"sig": "MEYCIQDGk1Cs6857PzIm3VN/5drnSvT8wEvHZJXBKeBrdmsIFAIhAIKNpqGBnmqgYdBGd4poKQaPv9OnnMpjGnBWwXPZadWE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiot0fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonNg//V9WOeC+6cx6COfsKG76on8IfxxsHQobOqgroXxHBbYPWzAfs\r\nvl2Cg2yP8WsLQi5M65bQsOMuAzU8OepbBHVwn4dayEzK7LvckwZpXWkZG7Yw\r\nSU+7xbJgzeUNiA3S92vvKv9CsGQSS6TvvtCBk2CBYwEB/cI8VBLkzdIF66on\r\nvZwbP9KDIZd7yPhg7ePKsTwefG/q45WnABVvyMmIJgko6EPwU+wPsXIs1Lld\r\nWmjRoySpTuL2w+A/NkTQGX/f/RCunJX6CGkAyq3OoU4qxcOVl5a8QfwcT95A\r\npJOqzVMOKx9SemPnu+JL/gEUyjIZaJ1zAE9qxlm8yXkqWR/GJB+Xv2vXw0aD\r\nrVBFT/AQ8wvlUp5sxYjToHoM+ZbB8z5/rOQ8aZw/MT0ei5Y1kph/B6f/9do4\r\neJamTzIRddWJVD8xXPusNBPUOIS8Ykw0Y0wtLPaPkbmpe8Q/3t4i35ODzJvb\r\nmlDcc/MCweyll5XVurI/3t8EW8ZtDDFGmnj5wnfjVxiGYxCPZr8FOCAfDOmG\r\nQiRNFJCUJChgz3MUdutlcBGOpWRQpzBWVk53+mExPFKvROp6YZMFxSZ4BZY9\r\nJ64zLpJCT0NJR+KIoqzLP6IE7k8FTLxOu3syNGRjWp18k6lsJldyj/TlLM9H\r\nAfbLO3+70tMfvP/ixPgVfaK71Rd67U8bd/o=\r\n=i3EL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"pre-commit": "npm run build && lint-staged && npm test"}}, "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./paths": {"types": "./dist/paths/index.d.ts", "import": "./dist/paths/index.mjs", "require": "./dist/paths/index.js"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "b27dd1607735d58db0c27497ffa284230d92ca3d", "scripts": {"lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "lint-staged": {"*.{js,ts}": ["eslint", "npm test"]}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.4.2", "execa": "^6.1.0", "husky": "^4.3.8", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "es-jest": "^1.5.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.0", "typescript": "^4.7.3", "@types/node": "^17.0.41", "lint-staged": "^13.0.0", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_3.2.0_1654840607262_0.3454022797449652", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "get-tsconfig", "version": "4.0.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.0.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "ea24c94ba587cf54a0179d15d94dcfc0e6d1a550", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.0.0.tgz", "fileCount": 6, "integrity": "sha512-WvnskjM5QJbSjuZrJkG3CeIE/ywYjL9/UMYu5dpOa3iD6GjefbQq7J/nrZY4yo6QSYuYwmj7G7Xd9qzCjMMKUw==", "signatures": [{"sig": "MEYCIQCRW8Mcewp6inkTecNxtNQMuDxvruDB15HW/GL7tUx3GAIhAJnTaZpDrc/l5g1SsyyxF+wzrHJNud1HP9J4bdPij+G5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio3LZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvDg//Sf1NIqI3Hg1M+CLk6S4YiVdxLUjcXWwdHlYAZoKsXwwJI2Tt\r\nQxZBArRvyJnenqUar0OxVM3CG0HJRvPsR4x0aa4lUWF+QlCLEw7jpm1/Hbdv\r\nuq3A85GwOr2OCyz88QezXCdsMZ63M3AygDNhrMVcelds4l17ppYnMnsGKhJL\r\nguWaNFniqQxDZrwh6l1VLu2Ad3EOZ5LffqcChcYU83o5xX7NOb4IQ0ZsFSYK\r\nn9/I/PbpJ4IyrBsw62UD/MHtjgvhTB2D5Cuyt1fW7veQibZKdMS3J3ZnWTqw\r\nEiClHLtBbpkOvnUPlL4lWalBsLxEqp5Y7vXM1o8uopBtfWGgTzzMtyWe3hy4\r\nkZctAUpyA8A/i+v/GmHahc1UtfNutWefdw4BxYxdyXVSQPW+VdIKM7hmhZmd\r\n37CjhlyUY7Q3cWnpCOLMvFJjtixYJE+68+WB2rTDhgq60WN2pwjl/jZwyGg2\r\neZW6yL0GL9NLFJ6zpVvdE0nPFkepHt3th8RWjKotwiwFrAfAKB3bBpaJo3pw\r\nP9udvltj3wLhmriz8CgxTef4F3KfutHGaGhMJzI127l7e//uQ8whJ6DoAIOc\r\n/PNGbyKpre7ksoEdF1BzyRMz4Ewv0TnDX4l132yr/aOY6p47mZY1CoT5YdPy\r\n5lPm7Xuh6+txKnTUr7fqlfcLAkQKIWXWyS4=\r\n=3piJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"pre-commit": "npm run build && lint-staged && npm test"}}, "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "58a696ce9b775f1988b69e06410f40b48c975054", "scripts": {"lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "lint-staged": {"*.{js,ts}": ["eslint", "npm test"]}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.4.2", "execa": "^6.1.0", "husky": "^4.3.8", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "es-jest": "^1.5.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.0", "typescript": "^4.7.3", "@types/node": "^17.0.41", "lint-staged": "^13.0.0", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.0.0_1654878936831_0.745573142256297", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "get-tsconfig", "version": "4.0.1", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.0.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "5794bc6434324554d508142e856a583a9d8b86e8", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.0.1.tgz", "fileCount": 6, "integrity": "sha512-HgE1gLcqLfPYSV4BPgRx8GJpkNI/xM9WhUfAXQ3gUUdbaZMQdKMgbmfxafXIAc51wchQpcjqNIXqzZ2seGk7Tg==", "signatures": [{"sig": "MEUCIQCUDo+BwXATDyK8pNMqdwHZlKCpBSdvJa1R1g1IU6M9TwIgJVfGVO/7AMLonquUqLMs2+WNZbDspXwmaKZvpDqFPd4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJip4A4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWPxAAiLysND4hKkglkDW4CaPwMyrKc4S4RchviGN9d6q4EHgodPCf\r\njuPybRRld6/KVq6piytzb5Edu7You2Nxlkau3grtJJXgbl/DkJ5VtY0d0rMp\r\nVaNYW8VDbh2lH4vK1uGp/X3yITKNrgPZgrQmikCcBQ1N0k04l2B03xC2b05u\r\nllB1+Pyrm2vOUp4XlucZlM8WixPQ5dYuGwE0RBd4kDplpb5t0l6j1Twm3UcJ\r\nnhD9v/LzDptWfknJED2qbP9iaDig/LS9ilRemXvAoyOgAQoFwqoM7nPRZz5x\r\nr0DLSZM7kXgodP3uxrUyG25gNmHY7UweaXAwAkg8lusKIFYkYbvKk+3bg7D8\r\nhQGPBts8usGXfJHlmaZN0a4ZWW+H73L2dT38Z51hrZC5+4SmpIDKW+Q5zkZ2\r\nHQ+revSRL1CtSHHlnS/sMpCLVNlZEECda/1l1kykS8PMN23zkchky8Tr3d/w\r\nzlLU4rWEiMXVbtoVy1AfrsNgmZ8l2HAZPNV+J0WR+/ZgF/AVlymU6A20Zqd7\r\nOCH/eG0f7K5/1cV7T6YiKDM9zecvbh9t0Bq1u/emHP2kS4EmklQ2kLkSsHqn\r\nC0+SSEv2USDhNQoJwsrFXQQOQq2L4HoVJ9NrTiDhe8nUjX42ok4MQ314BNXA\r\nXURhatNP3rGi/W7IimfnVhv7jWdJv3D7wlg=\r\n=zxGt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "husky": {"hooks": {"pre-commit": "npm run build && lint-staged && npm test"}}, "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "ef45b6e779522eccd46a77c778d1ffa432cf603c", "scripts": {"lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "lint-staged": {"*.{js,ts}": ["eslint", "npm test"]}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.4.2", "execa": "^6.1.0", "husky": "^4.3.8", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "es-jest": "^1.5.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.0", "typescript": "^4.7.3", "@types/node": "^17.0.41", "lint-staged": "^13.0.0", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.0.1_1655144504576_0.21049844652378558", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "get-tsconfig", "version": "4.0.2", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.0.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "3011ec4d688d7e4c8a54b694b00f9efb010ab6ad", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.0.2.tgz", "fileCount": 6, "integrity": "sha512-mId66O9RChjqWtUrq793OhYTJGUM1InYWw9wLQ4APAazEbN/BAAAcR+/X7dPBNy6om1vdGFTP5RGHste86ZOqQ==", "signatures": [{"sig": "MEQCIB3Z9m6s/s1l734iAlaF9lB+HOTJvjRnXoeug7KdSKdcAiB+Tkx1quY25//tvVKr7ZSiFZhVgy1c0mM9VDTpePMlBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJip93vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0thAAjmTz1Nj9Fi9/7JOyK7JoWvnJ6AlkmICmrb8t1/DvXcXjJ3zH\r\nq1y73aZkRzXy/mdl85ZAcfWev6HqQBw/o/pWLsHVuaOeSn6mqVdrB2VRxJZ+\r\noB3JU/NQApVQfAXXu8iT7mHmLlcG/IN7XDClRbyFcT3bgsT5FmwJgPm/euoG\r\neOHiPgZYi27s3jK474Chno8jHqzhcDi1e/IbSdQjLs90l9Ri1ua7N5U9oKD3\r\nq6kOVeXbHR+Xo3c98rhlUbTPVff09YNkm/mghAchiuf3StJbv7XVKWKgyeev\r\nS2MEEnGNpMjoHubfGcabVOGVSHGKqaHXPFu6R4/bfnOPiDf5silm0rPSp4or\r\nfCE3fLMmPEo4cBKSZzOW9zUxHdkw6RnczznouXtQDc1RbN1fHs7MzgYFbQk+\r\nIBvKLxRtEkOWjnr5UcoXH51lTBA8+gaGqMtsOEFLJDXv5q8lKW2MUU5yeGNZ\r\ndEyYm+jkOM4E3QPs0t3tk0iOxj2KyOT9fPiuA4OqjtaMxNM0Bl9ceCyTGPdP\r\nUPHZOWQRgoylA5BaryX27dqM0BwkyDUZ1hUF8UsLBVDXy4OB8dU/HuRBxOw3\r\nfW8RmRv829FB0Den3/gmM29GqL/ZeP3e7FD7aBrYiLTuSNgIX74M5Vxt0Ucy\r\nmPL4KArhZ3UQPCrpvnc238c0+AyCVr6yC0w=\r\n=JOnJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "c0233575198127bbcbbf0ebf296dd25641cc054b", "scripts": {"lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.4.2", "execa": "^6.1.0", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "es-jest": "^1.5.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.0", "typescript": "^4.7.3", "@types/node": "^17.0.41", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.0.2_1655168495278_0.7435649532361981", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "get-tsconfig", "version": "4.0.3", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.0.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "0a14d9966b6fcdd011014e417c636f0c27042ab1", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.0.3.tgz", "fileCount": 6, "integrity": "sha512-wviJjx25Il6iUWra43qxBr+GiLzE1dMh50o+nPmzSU/M2qkMkShpE4J71d3fVFazY76CNeoUT6jz5M9q6Fr+7g==", "signatures": [{"sig": "MEUCIH6iSO4X3ZkUgCBaUwpER5MS5igi7TD7lCq4GpG4+P4tAiEAkgjXBU9d11nLxO4gkiMqz7Ao3l1nc6s5YgN9hnMDoDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq49UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDHQ/9GbXU4/6xM0Xajdo2LumSWzJcI396v6YaIboE2O34S+QBqCd6\r\n7zHvnCzjcRLs06Hz9aj5CeaO25NRnNbOcvK5Gvd+kHghexc93thj+m9NMSR1\r\n9YsnN9PiRNSPg+xv/yCria5CetgpmJzcN4CbxHL2sV9Z3444bP3Od2PHKgId\r\nY2nd01cicZKjiqNqPaFosg6S9jSdYdy/J4DCIo3M5yIwyVyYnsGMjwTzot++\r\nAfAxege3fusTDshg0oSDnDfMIgrLz+CLHZJ//xnH71JAEr62oLpC0U43Myfd\r\nP1BkBM5HFAIF16ciAPr1MpIyEOvzpTIpcufBD/pCpMKAyEWGygubiBhLZcPq\r\nV5yHgqWxxMbZf1x8rV2pdhPaV4f5HHxeJb/cFYnkEIEF/sEEW8useRLOtiAH\r\nqdK7PJdjj35uHu5c1Gi0cLPyl21xwNXGhVG8XKWGHhNhbLzcj4eGimpEMKah\r\nVZxsL/1/s7/N7Cc+t9Lc044H05Vafvz9iCEIVhxNPCKkYqaGSdcpnddvcEqy\r\nsgT5huPN7Eo3rT5MZ2fBZeoS0xWiOh/ik/tI2R2iPU4pajHLr9U1rCufco4c\r\n2vO9m+wxVllyPL17tDf0Yr2TIOZT2DiQNtoHB7A/cFM9PEqGfb7KSZCvBCsM\r\n4d0yApDC+COBpd34GTy31kPqZyXkPcFd020=\r\n=Djz1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "a107c720daa5045426eef70df2601daac6defd0f", "scripts": {"lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.4.2", "execa": "^6.1.0", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "es-jest": "^1.5.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.0", "typescript": "^4.7.3", "@types/node": "^17.0.41", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.0.3_1655410516060_0.7352651957545742", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "get-tsconfig", "version": "4.0.4", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.0.4", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "b5500a39bdc7d32814a14e510206f023a71ecb96", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.0.4.tgz", "fileCount": 6, "integrity": "sha512-HV3OGunzMsulS6nFzOYj4L/cjO8BoI8PlCUyR1g8On+hUmthxqaOmo/qyFRzOJ/IF/fLH0k+qz7NhMt9d3QnvQ==", "signatures": [{"sig": "MEUCIQCLILfDXb9fQAnp5t7ozJXY9Kib2EYkjqFXmjG+0Hj8pgIgdjB/J+CNu22tXLrr56KeQgG8DODKtUV2A5OI6Nww0qY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirJm8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+fA/+LEPcNV6u9E6fQgLMGrHj9Ou/wZjYS7sRc2LB8Ku1fgjd+2Q8\r\n8sXRQLljwMmRzXUi0/EKMzIR2Zu1Bw8d2+MkmdIeskqwkeYgXzbOxBcif7NQ\r\npobDAakvAJuiqRmDXXO4ijVVe4w5CUmK/eJpPnwO8Xb1gT8008GIqOxByMMS\r\ndFPxy5G+0SitYqXO3B3X5SrLWsm2Z0rrYLNkEQRTESPfMfzaxaGC81R5502j\r\nMC3vpOUMi1k+9kwVtt1Vn6qE6fTKlauoz4XE0t0J05fSeVmYtqkNCRPvhnwG\r\nbAeRkCO7Clk3ddkYxnq30hcVv6/PANTq2A0DkV0u2INVLNVmJbv+xyAVfix5\r\nObV05oV1uLU9La3dKMtjDqfG7JdrI9pWsIOJHFwTBbIGQtowpEMV4/0RgcYs\r\n7EQC7b28VuCNJMkYNUnsN3x7rBKDEBI+Ec1PXV7P6uQ+JL+HUxESfVOacZPp\r\nFiTYJEBNhbqosJ5WA92Sa7U/9hRDp0qrhY4uIH2GFzDrAdcFAOtgGUeyOLd0\r\nVuCy6bHabKEHIP5eYnHAVGzX59qdmBP6skkL9OpzhY82sXoq1V6EUA53lPS+\r\nXMRaYEAY+pSu2rJc5nLKwhKbh8Etd1t8fx68kQntA82Lm50a39IeTl0BAw1v\r\nyV6MexRdy+90dZnRDVUp2ZAPA8crN3ArCpc=\r\n=p+fA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "773374154fede32d2f310e1ec63396fde733ee66", "scripts": {"lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.4.2", "execa": "^6.1.0", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.1", "typescript": "^4.7.3", "@types/node": "^17.0.41", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.0.4_1655478716339_0.9784129555423218", "host": "s3://npm-registry-packages"}}, "4.0.5": {"name": "get-tsconfig", "version": "4.0.5", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.0.5", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "b9aa657c3c218828388386c87836d348f4702aeb", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.0.5.tgz", "fileCount": 6, "integrity": "sha512-UiOHySG2zoM0krlrfJOMQoY5UR1Z57/HjMUJdi7lJHCLKiES9zZsDXtU0BPo2GUI5EqJmNRnqU3FdMjDmf2XaA==", "signatures": [{"sig": "MEUCIQDGW66eAa4Hk62qhdyrmng0u/WIxyrErCAGsapcCrsZtwIgWIqzPkDt3KdichMI7IpCzYzk/RXq6hOln0ljlexQbsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisq0QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpegQ/8DzdvSWtx57jK0y9HyqOK3ZKA9+nHFcrMvd2EFU2dt/aZTGRm\r\nDSDtrTR8id6KN+Iuo5q4j1bC1EspO7Ek+mrXy20Ua161DUuUpYIaN+t7B+Fb\r\njzAJou1PF0wo024J2LWQkIKPBOjzLqJiCQrB5YiOMVHWY1bhmzty5+v9whqa\r\nKKI695+571/SZVMN0Vpu7sc/oWKSqX/jtAR0nZrdqkZpVlP8lRK+/1CY7rDZ\r\ncFtgUy0RfYxDIgNiN5jGSUVQQZtXl2G7LKlwl5FW1FHbNJrjslgyZasGzl86\r\nDFoNmL/j2dmETg7l70FWhq0Iz9p23gqbWeJqzQbJ4tOJRVPSWN3iDK35WsJ+\r\nrDp6ihSV2rhqS9jRBlS+GkmrCKJDUQOheFSe0U/eL5D3vbxrNCyV+u5yKWCF\r\nupFz5mOaDDdPfj/GluuLiMNvwPONzLj+sPqaXykAzsiKGVObnr+1Ty3Uy7HC\r\nvO5hQufoqc4lfx842GMXBe6L8qQYVJCawV8Z8e53rMbofmpH+sp3XeMtb9Jv\r\ngOYDGlgwJZxfrcBY8I2zt60xPIDpOdDa6YmqrszxwvFddX8k1lpfhIJ5umgC\r\nHAdRLb2BBIQ0rSoqzYVKnJpcBzaLQyK38lOR2Z+YUnWsB2kdKUX2rE91s0u5\r\n96IAFTljrM30RXKc2VXQCMMCO52EmG2+RXY=\r\n=4klH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "b003f2e5ffe4a928d931b2da0ffddc9258f86961", "scripts": {"lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.4.2", "execa": "^6.1.0", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.1", "typescript": "^4.7.3", "@types/node": "^17.0.41", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.0.5_1655876880152_0.010480863164711307", "host": "s3://npm-registry-packages"}}, "4.0.6": {"name": "get-tsconfig", "version": "4.0.6", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.0.6", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "31ee086022b1261fccf89cfc7460db0f3c02fa57", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.0.6.tgz", "fileCount": 6, "integrity": "sha512-yK9g+lk9PSaYS4RM9jnlmFSyymNDdLaIk42h6uOO6DOvqKEpL4KhUAcI1/7/sWe8+FWZe1/EjFXFfvv6T+cgPA==", "signatures": [{"sig": "MEQCICuVME+TWj9gxtkvn3GwOI0hEDA8i4pyR53FGS29hk5eAiAjV0ODOhBTfXn/6vz8ERRTOKHuHFaKYn784GpzEeG6gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJith+UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmohag//X60jnp0Y718UARzdzglfnZcbvPd7BxcnyEZjpimDRJDXTcqu\r\nzBrV9dn94p/mDE+dTIKftNdrBsHGBYMaqaOc7FeqbCT5XWJGdTIjGH6V7f4l\r\nambkl2bA4MaR3+04Hsr0ga2KiGLExzLFq/Je0lprGH5shp9r1+kFBC7F3ulX\r\n2uBLDZjRmaLYyk+dCagfrnc1aDDNxRaVcawiZr14dZldS9Zn1GVzxXw4mNlW\r\nTQvddYVaP5H5HtQdefz2Fj/lX0YsjbNnb2PqDhFg0ZC2Xunl9CNpaERBMG21\r\n0YCdh1Y5vDZ/zDN7HzizoXyIQNRbnPxcNiCbgNW4IypaI2LweN09l8LfMODj\r\nEYV7BTW9ZNVqLa2V1/T2/8BQU5Nf5nwvNGrndZIXStI7vtu4Y5iZxblU8HB8\r\n6a/NXZWlWNa9lOEVvUK9U81G3lW9dPVuO1iN80itf+RbfUWpvH30R1cawajX\r\n30XgEvIW8sA/hf3SZwMkQkzr8OkIWOGprFO66dt5YfVsL+q2QjH377Kv0KxF\r\nQ2Qhrux8GtHNpvaMUuqxBmKWWenLNUyUQoWXzGUN1SA5LWM477RfR7Wi9qOG\r\n/ERklsORMeW1rAQ5cC/P3aMs8/yQ5Lih2XyM+GCnsYbSsDuC6pSQGzZ6KDX0\r\nWxC+ZHSB1b1bcucBx1uPdSNkQbtmvpr3PKs=\r\n=Uy2E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "96a5c10bed2750794ac720ebf7a1dd7d5169a06e", "scripts": {"lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.4.2", "execa": "^6.1.0", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.1", "typescript": "^4.7.3", "@types/node": "^17.0.41", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.0.6_1656102804223_0.7572815091018616", "host": "s3://npm-registry-packages"}}, "4.0.7": {"name": "get-tsconfig", "version": "4.0.7", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.0.7", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "fa6314dfdd43f693605379e9f4717332d5f93e46", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.0.7.tgz", "fileCount": 6, "integrity": "sha512-hI0ikvRti9TmxszXtfRaVxSU0yQ2HJK03MU14dG/exX/RrELbMTYJW0xOzGR6c2FsddZ3eZHZxDotEIPOw4D+A==", "signatures": [{"sig": "MEQCIF/uZdLQPhlXNPU2rhr7BItCP8p7demZCgFb/Tvw5JMDAiB3Tj8E+BQkU6xN7AnnC6ay9geO9NDz836vcLoVI+M3ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuNjeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpICQ/+P2zDYLf8669fhQPhW/SX64+PGuufH6Bwef2VShQ3SzhIrVUu\r\nAURGL5OIFcGiAvceCmB0OyMzEDAYB+hQSf27BU/lgXwYyU5B5BdxonIJevDh\r\nkRQagAW5vp5lYqEnJeB57rBBW4aZR/4u3/IKIOUdyzNIzkQJ/7vPzvmLe8Pw\r\n3uVU2+srTHjKuUfLN6Ggwa91cHxrX5l+ofDyr7cwnowEVjW22PYGDFua2Ez4\r\ninuHp6nrXsQ+zacFMrZr06ePhnBZum2Z2UthEdW4Qyfna4Lk0iduRAc8QyhX\r\nmrUwEA0gdBKZV3+CjEwHyaup0epY1az8gbNSDfk0mMLiWu/UzghYJiL4fRYh\r\nGJtS1PJiHyOmMko8epud7uvY30WoyQw1j1fftwO7usgqDYvup/rQgpIx/vQz\r\nwiG6h18ijp6lVAaDfuC5HIUBoZj/z2cNTH7K9oizmZ9UBWrrYYDe3yiG/rqq\r\n4uFTPddnHNJS4fzxx9AP1LvzN/+mVFeM37CgjdYUKATiXaVTpwHxz1XGyTg6\r\nG1HJNac7QgpviY6yPx9u7pkdVckBx6qLGiUrIIflEX7MzuAdps80zJQXZlEQ\r\nntT2e/rN02OokMEyAU0237kMOtv7xckAAHLjNE0gPp+Jct9PDOatKa7LezDj\r\nWfyHecfgxVTzqCwm0jEW0Gj4qP4iOZSsLbY=\r\n=MDMJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "af7584d3be3057d438b12d37d67724e867de597f", "scripts": {"lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.4.2", "execa": "^6.1.0", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.1", "typescript": "^4.7.3", "@types/node": "^17.0.41", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.0.7_1656281310319_0.6673931269046462", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "get-tsconfig", "version": "4.1.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.1.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "325fd5a58ee94596037263ce8b7db0ce8ac7b925", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.1.0.tgz", "fileCount": 6, "integrity": "sha512-bhshxJhpfmeQ8x4fAvDqJV2VfGp5TfHdLpmBpNZZhMoVyfIrOippBW4mayC3DT9Sxuhcyl56Efw61qL28hG4EQ==", "signatures": [{"sig": "MEUCIQCOTyjsSC+EVusZkORmntH9UTk8IN6j+bG3r90SNP/6GgIgSI8HGuOJWZ7QERNyWqT+UXxBbSYgsOX2AAO5rKV38cw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiu1sRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtjBAAlRZj5JXlMvhz0DJSjKy9Cw7+GaQkPqXGx6GattJiY1Fzure1\r\n93Z9doF0d6e90xT0MIWRL4z7OHX2/RcdOfthjcW9pyAOYMAFef/7+73zHpgU\r\npN76l9f+amIrEHecF0OgY0UTjChrnKlT6WQWFcPHrWIbhmj0JZybX0ZAM93S\r\n7esKJa+qDmrVYACAZITAIyzsftQjt+4ViCUlAlizxhuW0RlYB+ksNcOXNoMp\r\nFZ4XSjOrlnSty5I3rHWg5VFPVpRwgYvU+u1k3rwm/WgI0afPODHh3Gb7xuiG\r\nKp5QO2uNN+022hCvcv7M6LSvfQo5z9CW0zoQAFsPaP2LTLWpn5TgSket4Y92\r\nSPFbwQllGbDctp4SX1hE+Gx4cPCEajSBsCEdR+SUO1enEonobD7OASVK4N7e\r\nZcB4uZFmIcUmWEL+fhsLESBcn4M/mKrt5satsLCXcnok0VxDUJUoKyXk5ozf\r\nme20ajYIihM4hlcJMqVWZnkcYzzizC13/sc+79BODv+rn7Yvpmu/uD9PCNiH\r\nDhuSbvROICOMYF4EhOVZZTNcH5DN91Hcj56DHKfdaVBsg1qAJdQiKLhOC8Uf\r\nD3GqYKXhXOrcc9niG1tTk8WtADxiKduFD8+9B37HZlBoaVXyOx7rWyOj8H/a\r\nWezaf5w1O5MuKmomYYv4sGvmYRw3g4Kn6qc=\r\n=n/DQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "6a0d98944180a091bc9ecd2f01db195a61ad1c9b", "imports": {"#get-tsconfig": {"default": "./dist/index.mjs", "development": "./src/index.ts"}}, "scripts": {"dev": "tsx watch --conditions=development tests", "lint": "eslint .", "test": "tsx tests", "build": "pkgroll --minify --target node12.20", "pretest": "npm run build"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "16.14.2", "eslintConfig": {"extends": "@pvtnbr/eslint-config", "overrides": [{"files": "tests/**/*.ts", "rules": {"@typescript-eslint/no-shadow": ["error", {"allow": ["test", "describe"]}]}}], "ignorePatterns": ["tests/fixtures"]}, "_hasShrinkwrap": false, "devDependencies": {"tsx": "^3.6.0", "execa": "^6.1.0", "slash": "^4.0.0", "eslint": "^8.17.0", "manten": "^0.1.0", "pkgroll": "^1.3.1", "type-fest": "^2.13.1", "typescript": "^4.7.3", "@types/node": "^17.0.41", "jsonc-parser": "^3.0.0", "@pvtnbr/eslint-config": "^0.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.1.0_1656445713601_0.9997257108566671", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "get-tsconfig", "version": "4.2.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.2.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "ff368dd7104dab47bf923404eb93838245c66543", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.2.0.tgz", "fileCount": 6, "integrity": "sha512-X8u8fREiYOE6S8hLbq99PeykTDoLVnxvF4DjWKJmz9xy2nNRdUcV8ZN9tniJFeKyTU3qnC9lL8n4Chd6LmVKHg==", "signatures": [{"sig": "MEUCIHHeREkCmZ7iz21ZDSPAcpEEBIelVqmHlZrCcKv2Z3FwAiEAzeKbDRM5jD9Vhg6g3/raLERqqt78iORFIdmactdIkso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0IwKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq13w//fBY8j1RF08hLLJBybsDqJvSmh4D0G9KMMAYMj1p7ylbtFs0I\r\n7zM6Y5xtWi3rl63a/3b9zMk9vhJ8p+GnTOsgbr2jpJ4j3hEj+XdOem9sJwKo\r\nfpSlthog15VcXgbOiSxsF+lw0YSWmsGafmR9Q4TuVV6SfOf2dZ2aCfqkRRO2\r\n0YDO+FZcqWSVyvHGT14f7Qk073mby2ClJen+RuOjQbnnTdNj2wf7j7dPPqC1\r\nAZZnifoQtacYtqaD9i+EnGS5tB4ER+1XlilzYdxftjOXspXXDiEPM2Xd4nyk\r\nVMNy5ZryO7VtHfzynEUDRyBGgdnpxXEavSNhCbCsxSRlGbpDfKWq7QtfF6rz\r\nUYNmi68yhKXPkqJ099i1UM9je0qdWoWMK5NYhiSs5rksblfBg4o1JqDSotyN\r\nvkc/kWs31QMPF+rteFFTTa5Rd3xzAV62AqeQzOKeFkmZaLEGxzLVFBiIoNVb\r\nrB/KT+lIIB2lHsH3xyx9LA+Ma8TrojDygdekfdMKjgxXWqxH0N6/A8IR1bCl\r\nb9aXw3gPAhrA/65f18tX/3tkkVUR+g+jT1RdZ35CDJ0yO4gMGjhb27WeOhrU\r\ng0vz9sHVwHTeOZ1eVkpKs91sY8D//FCwy3ylKjMWdIQ8B/Jguf41ZK70PcXo\r\nYytozZeqg2o6Y30sBHs29GpUIF+Q8AuBlt4=\r\n=Ae1n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.mjs", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "57c74dd693a43887068bd6f332fc79de85e4e4b0", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.2.0_1657834506173_0.4638066525961304", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "get-tsconfig", "version": "4.3.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.3.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "4c26fae115d1050e836aea65d6fe56b507ee249b", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.3.0.tgz", "fileCount": 7, "integrity": "sha512-YCcF28IqSay3fqpIu5y3Krg/utCBHBeoflkZyHj/QcqI2nrLPC3ZegS9CmIo+hJb8K7aiGsuUl7PwWVjNG2HQQ==", "signatures": [{"sig": "MEYCIQDJRhwDW4MJRbaS2FXy72w0MmxXw1Nq8ZvE9Bkz4sMgrAIhAITwW6zsyLGZ8RQoJ2mAlNmzbng/7zA8TUpekSPPUwvb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsbRNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4xQ//QXDMAHawklAi3pSqeAvrE7zYdiZQ4j/HWBHRJ7VJNeFMoIl8\r\neA/oVQdTpHWyRdokUC8e7+XP1CThzzvMgazpktTFXeLWw0S3DMf49iWIPHyj\r\nqRBQJTI8CaIh7RDfaANMTkqYik7IFZ6pMHPuZ0V68Uh40nJdk63TMnX/9IOP\r\nCf0dWjxvWYa/E07kOsLG4toatu99FFmNGt1dQmqxr2DGKDFW+xNDQFukJLOU\r\nXj72fnwdaEXLuzpoamJifdLjfpM65Y9qBvOkG5NIYkM32pbAhqj/0tY47nsO\r\nCiIuJqZrFKqKiiDvhKxuwgZ8yqQ4mIDrr+Xfp+R/st9ureH31xi/+uXIOQIh\r\n4H+SrqypY/MKVb0y8YR32238CDVS9suDadmCSt+pQCQOPsPbqx1j5FOf5hr3\r\ncqEahcvOQ14zrZ5zYKxLKpn1FN5cURv9zlkfyM/J1oXvrJVXIdx1q+J5b/8L\r\nW1pp3mf4/jI74i3jWdu6YEjQ1EhZ0APaxwih+mfEi8EuhxM4FJLvLZMiynJJ\r\nW8pM5Nudlb6xUzaicrI5pmCrszwCLrrK3HwsFmgNMSjkb4Oyeo1WZb/nCj6l\r\nmKqgv1EOZnUY228x7DI3eBfV/1SviiqRpv9W2sk07oQrYvzKyeug8jpn7Cjn\r\nZurAmwznnnimSWxq0A2l9pGDXPmd93cgeB8=\r\n=uHm/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "294a2a820e5711c9f3c20b04b316f1bc61807078", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.3.0_1672590413154_0.4506898874015979", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "get-tsconfig", "version": "4.4.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.4.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "64eee64596668a81b8fce18403f94f245ee0d4e5", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.4.0.tgz", "fileCount": 7, "integrity": "sha512-0Gdjo/9+FzsYhXCEFueo2aY1z1tpXrxWZzP7k8ul9qt1U5o8rYJwTJYmaeHdrVosYIVYkOy2iwCJ9FdpocJhPQ==", "signatures": [{"sig": "MEYCIQDbRFb2Jy5k3L8u2wJ2Pl0KNoGWSkCkZfpBBYR6+Xrp3QIhALQrrszl+C5VAQRRETQW/rafpddlE9GroHlW5UK7VsI5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4E5cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvTw//XhC/cByYgpO0/sGjMQ/24arM7inv46ALa5jWN6GapJSoqLad\r\ncUnFwDdCZCTh9mrwxyCj2jHVJaGOIcdNmojWmHtmGfPwUrCudvs4ua24Wu/+\r\nw1KNGrQ5nQcjo6p1uVf2NqmeYFq4+mkCr8rQkpA6twjtCbk9nK0t5lRq1CAF\r\nh5F7S9JASmun5JRo8JXT3xkEayzDLBF6qTfJVxRsO6gjiE8ao4uX06zwMVl5\r\nSVqAGkOpRvpbKY/0lteLIphqm/EXjbBQ1iBzu7wcJ27vHsIP+9XUHssc/Soo\r\nplcLxpC0MG76NneeaPKHx3CIstZBccX8sXKilZ+7Km/UqXuJS0U0hp2UW7C3\r\nrnvQfGmS8ocrrf1yjrEmc3feMRLllztnwqjPihizgFSjYNw9KZmiis+Dkn+Q\r\nsToJKYjzxurJM5equR1eftpNHewTJRVLXgKKR3bgtU68RI9nClsSU+IIezrg\r\nkZiIpRudSRaxObvbEiQboUu5gNZAAAbfJfDQmRT961y1TrZC4czxUfP7i65h\r\ngQPKbhPGGCWEDh4QVxu9F0XCjGP0BtGsCnFPPpXWRobWbdQqMETTLYO5rZUC\r\n2xoQMBA5J7/sSSbGIWeLdwy5Beop4Fn/+F3rZ4D7xtee9lnwiqxv3YmzDMpx\r\nKjrsB/cCnWS9TXrMZZS//x1zIauUqTZLnhQ=\r\n=v7aA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "dc975387ca22b67a0dc96c09ad4176d41a46efc0", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.4.0_1675644508472_0.9038167934058021", "host": "s3://npm-registry-packages"}}, "4.5.0": {"name": "get-tsconfig", "version": "4.5.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.5.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "6d52d1c7b299bd3ee9cd7638561653399ac77b0f", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.5.0.tgz", "fileCount": 7, "integrity": "sha512-<PERSON>jhiaIWCJ1sAU4pIQ5i5OfOuHHxVo1oYeNsWTON7jxYkod8pHocXeh+SSbmu5OZZZK73B6cbJ2XADzXehLyovQ==", "signatures": [{"sig": "MEQCIHGPN/5eDRkrZrBob/gxerqeImu77VOq+jCagoYXXwkNAiADdVT0LpUs/kjIv0aJJGAT3utuMNladxGPQnXVNPn6tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIOm+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLVQ//cTNEVZ6eQRVRCqW9eF5dEX7ePlLu7u59nttvOeKr2dfieHbn\r\nb80E2y2Zk3JpJOeHOLRWwOulwM/+Iq5xtYzxJ6iw/Hvv6NC4FP5f/xwXe1tL\r\nKZvHQvEp0EZp9A2Jm38iR/HBh6sITkoRgce8PDBKjRlgcNsK7E5F/ue5yA4/\r\nGPUjB4EAuYOTCHCXDcyAF67xBEb8j6ox37SZxiKXbHF8P6+7X6kDyG54bgZJ\r\nnuxmeA3MvNQIHYPzl/BhY+F8uhUAHWB0kzp3GHQCAOfG1KZczZhAkK0TepG2\r\n0CX1d84Zp1p8cfAKbsU7aNRYa+r6VTiMXK4exEaXiApZqHqd4a94tL1XENDo\r\nfgqcAf2Aaka5T/9fMdCleifXPuAzjQpyc9PYDd9BOIlgZvJ7liRUvzf+Y+2H\r\nlLHZotUceuoknUm/efgZIj9cbh4vvMPbXKFzhxZJSDLqBARBrnQbArJMl/I8\r\nBsR6vogYGl6k5Ddd0VyQVKh3739WZlzJQzid6olwfENnqjYvtA6pAtgyw9PO\r\nXVRVMqk09G6PJcEpsHetZW7G+p86dTuxbQHoAHjLf/NLNMXENsXjxhvdD9Oo\r\nScNJ3F/9n+oGeq5n5MSbh4VFQ9IEV/AjlhXbD7HBHWPNfQDAFpAfpiobc5VK\r\nWafMk3gSH9hKfs1bVEi/Sr8nQfOwB6/VJWI=\r\n=wMS/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "002798158c8467487d2b47b7d59632a01f09e8bf", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.5.0_1679878590393_0.4633501575593688", "host": "s3://npm-registry-packages"}}, "4.6.0": {"name": "get-tsconfig", "version": "4.6.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.6.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "e977690993a42f3e320e932427502a40f7af6d05", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.6.0.tgz", "fileCount": 7, "integrity": "sha512-lgbo68hHTQnFddybKbbs/RDRJnJT5YyGy2kQzVwbq+g67X73i+5MVTval34QxGkOe9X5Ujf1UYpCaphLyltjEg==", "signatures": [{"sig": "MEYCIQDc1T+HFJ9ENEfri7xJB9QuTWLTA8lIxwl8+/wDLQJF1AIhAKPeBftzWi40GF1bKQTAPEAl7RL16rIf6SzwLX9gc2Q1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96890}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "ea1b356b8abbaef37c167669db89a47113fd6f3c", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "18.12.1", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.6.0_1685351451717_0.32099519795223186", "host": "s3://npm-registry-packages"}}, "4.6.1": {"name": "get-tsconfig", "version": "4.6.1", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.6.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "e7ca69b2bf9df4c7df457c336797a31bb78e63fc", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.6.1.tgz", "fileCount": 7, "integrity": "sha512-03KhXlcmM2U7gjZlQmsJBJQhf4uUjmpnfXvVeM6YZE+IcKqhdp/1mViZ+C1MaaIfjlfpZpSpDR5sWC6TR03DmA==", "signatures": [{"sig": "MEYCIQDDib14pgk5lrxHmNkPilaUCWgjQIAwcqAF0DgsGuoMVwIhAKv7Dc6edZkky5xf8lQua3vd62oAixsU9DHYUYamT/0Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97028}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "136acf4a9a9f72e842e7ba90c7334729c26e853a", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "18.12.1", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.6.1_1687857180273_0.04125565608679227", "host": "s3://npm-registry-packages"}}, "4.6.2": {"name": "get-tsconfig", "version": "4.6.2", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.6.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "831879a5e6c2aa24fe79b60340e2233a1e0f472e", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.6.2.tgz", "fileCount": 7, "integrity": "sha512-E5XrT4CbbXcXWy+1jChlZmrmCwd5KGx502kDCXJJ7y898TtWW9FwoG5HfOLVRKmlmDGkWN2HM9Ho+/Y8F0sJDg==", "signatures": [{"sig": "MEYCIQCSk/mA/+AyjOiXKAT4m1Mnq9PUJABZjzq9Na1ervvNpgIhAOC4Bq7ZAj/2fXLfUUemb4N81KiZVUSJaeiLUjIjzVAS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97034}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "8acfb83551c81dffa222f247b604e14276d4edbb", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "18.12.1", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.6.2_1687859237964_0.9061514918116726", "host": "s3://npm-registry-packages"}}, "4.7.0": {"name": "get-tsconfig", "version": "4.7.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.7.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "06ce112a1463e93196aa90320c35df5039147e34", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.7.0.tgz", "fileCount": 7, "integrity": "sha512-pmjiZ7xtB8URYm74PlGJozDNyhvsVLUcpBa8DZBG3bWHwaHa9bPiRpiSfovw+fjhwONSCWKRyk+JQHEGZmMrzw==", "signatures": [{"sig": "MEUCIQCzwpXWAvjJVDl5olJYPTaoGMVeDq/GC8r+hKdMmO2RvQIgZVrrChAZ1vBzWSbQmCqEEbTGKXNNJjmOyDaH4OkMFRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101107}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "c834d2322f560754b2a857fe2a94ad619908991c", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "18.12.1", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.7.0_1691653957252_0.6546303448012134", "host": "s3://npm-registry-packages"}}, "4.7.1": {"name": "get-tsconfig", "version": "4.7.1", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.7.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "3df7bb5d4f5f83486c33e5253c7a0f8375edb798", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.7.1.tgz", "fileCount": 7, "integrity": "sha512-sLtd6Bcwbi9IrAow/raCOTE9pmhvo5ksQo5v2lApUGJMzja64MUYhBp0G6X1S+f7IrBPn1HP+XkS2w2meoGcjg==", "signatures": [{"sig": "MEUCIEv/AMYCQLcD2W+yclz7P4ZX1iUF++cdEqXQU0tWC3aQAiEAuOC9pPTOeVa7l9s4BU5wLf1aDAor2eFB3R2odMepVWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101373}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "785457b7941b64be2281e2e2d9595d5c439992d6", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "18.18.0", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.7.1_1695294291143_0.9092038508013973", "host": "s3://npm-registry-packages"}}, "4.7.2": {"name": "get-tsconfig", "version": "4.7.2", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.7.2", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "0dcd6fb330391d46332f4c6c1bf89a6514c2ddce", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.7.2.tgz", "fileCount": 7, "integrity": "sha512-wuMsz4leaj5hbGgg4IvDU0bqJagpftG5l5cXIAvo8uZrqn0NJqwtfupTN00VnkQJPcIRrxYrm1Ue24btpCha2A==", "signatures": [{"sig": "MEYCIQDRAob94/8+jY9YaMteF++Q9UpyjNJrVws40iZT7eMiXgIhAIoPUnnvtgo/eBFUrWRf3OeRkcqJAVUVlgX9l0J/R+uX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101455}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "4b5f839bb1a244a1cabfa9bfc47dc8bbaa1f3937", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "18.18.0", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.7.2_1695368409907_0.8906858439744147", "host": "s3://npm-registry-packages"}}, "4.7.3": {"name": "get-tsconfig", "version": "4.7.3", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.7.3", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "0498163d98f7b58484dd4906999c0c9d5f103f83", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.7.3.tgz", "fileCount": 7, "integrity": "sha512-ZvkrzoUA0PQZM6fy6+/Hce561s+faD1rsNwhnO5FelNjyy7EMGJ3Rz1AQ8GYDWjhRs/7dBLOEJvhK8MiEJOAFg==", "signatures": [{"sig": "MEUCIBp3K84PgeXStJet7L9Wmwca68nwBg8qqp969TjbliblAiEA6AbYtWsyfxcT9qlOZdBBXljiFXKBdSCVI3LA3Om8aS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101130}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "d1c46c04f200a8239f3e5734c42011de1ea11380", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.7.3_1709788394605_0.04817030528792299", "host": "s3://npm-registry-packages"}}, "4.7.4": {"name": "get-tsconfig", "version": "4.7.4", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.7.4", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "228e1a3e37125aeb4467e9b992b92c4533093bd2", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.7.4.tgz", "fileCount": 7, "integrity": "sha512-ofbkKj+0pjXjhejr007J/fLf+sW+8H7K5GCm+msC8q3IpvgjobpyPqSRFemNyIMxklC0zeJpi7VDFna19FacvQ==", "signatures": [{"sig": "MEUCIQCs/5uDloI2eP6/nHiXlos8CKO2WJg/os8wToyvUBemEwIgBOUK++D05ugf8pkmHijYIY1iLd67zrQPw2fQ6OwzO4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101027}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "b009299ce0303ee5c76fe403ebe413caa4b61da3", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.7.4_1714972284624_0.5906049165214364", "host": "s3://npm-registry-packages"}}, "4.7.5": {"name": "get-tsconfig", "version": "4.7.5", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.7.5", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "5e012498579e9a6947511ed0cd403272c7acbbaf", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.7.5.tgz", "fileCount": 7, "integrity": "sha512-ZCuZCnlqNzjb4QprAzXKdpp/gh6KTxSJuw3IBsPnV/7fV4NxC9ckB+vPTt8w7fJA0TaSD7c55BR47JD6MEDyDw==", "signatures": [{"sig": "MEUCIBNGBnAZsPIyupTO5oY5oLYmojYknF3Ak1XcUHyKNzyBAiEAppiUJSOSqWIDEK7+oyMwb1TH2U1hTg4EaJLtOUhO+/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101168}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "0a3270e67ddf9a10b907eb5115f13a56857261ff", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.7.5_1715269515076_0.30141521329597865", "host": "s3://npm-registry-packages"}}, "4.7.6": {"name": "get-tsconfig", "version": "4.7.6", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.7.6", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "118fd5b7b9bae234cc7705a00cd771d7eb65d62a", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.7.6.tgz", "fileCount": 7, "integrity": "sha512-ZAqrLlu18NbDdRaHq+AKXzAmqIUPswPWKUchfytdAjiRFnCe5ojG2bstg6mRiZabkKfCoL/e98pbBELIV/YCeA==", "signatures": [{"sig": "MEUCIBoxkHUH8MvBS8s69eHJvP+uOLxt1kfGu/qUerH+DCm/AiEAlzZSXuru57hYgpoajqo0DAPi8CaAkcauiclYZ74DQBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103180}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "9e78ec52d450d58743439358dd88e2066109743f", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.7.6_1721355453907_0.2374429974079384", "host": "s3://npm-registry-packages"}}, "4.8.0": {"name": "get-tsconfig", "version": "4.8.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.8.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "125dc13a316f61650a12b20c97c11b8fd996fedd", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.8.0.tgz", "fileCount": 7, "integrity": "sha512-Pgba6TExTZ0FJAn1qkJAjIeKoDJ3CsI2ChuLohJnZl/tTU8MVrq3b+2t5UOPfRa4RMsorClBjJALkJUMjG1PAw==", "signatures": [{"sig": "MEUCIQC/hGMdQlAvI2dYNKpL4CrpveZGhdAZhE03DrROKVVjEAIgWXD4O0w7JCyxENTASJU8TjpX7eyacVplIoFn8leGids=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104940}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "eba85380401a64b6cc3ff8e639fa2391da41c2d2", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.8.0_1724923877518_0.4758819825010012", "host": "s3://npm-registry-packages"}}, "4.8.1": {"name": "get-tsconfig", "version": "4.8.1", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.8.1", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "8995eb391ae6e1638d251118c7b56de7eb425471", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.8.1.tgz", "fileCount": 7, "integrity": "sha512-k9PN+cFBmaLWtVz29SkUoqU5O0slLuHJXt/2P+tMVFT+phsSGXGkp9t3rQIqdz0e+06EHNGs3oM6ZX1s2zHxRg==", "signatures": [{"sig": "MEUCIQDcKj3n9Qj8qSZwyCi5WgWa4zUEvWD8/Ur4od58xTwEygIgW7E7n6PRAHGdS7LCxWeHBr12JkBFHSKP+p2Wy7rUcyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105468}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "7e58ea3067aa8aea7f06c708706d13af60cda8dd", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.8.1_1726126269289_0.021283875274779707", "host": "s3://npm-registry-packages"}}, "4.9.0": {"name": "get-tsconfig", "version": "4.9.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.9.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "704ae2ce2a94935921675dd19c05508b713a405d", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.9.0.tgz", "fileCount": 7, "integrity": "sha512-52n24W52sIueosRe0XZ8Ex5Yle+WbhfCKnV/gWXpbVR8FXNTfqdKEKUSypKso66VRHTvvcQxL44UTZbJRlCTnw==", "signatures": [{"sig": "MEUCIQDbn1F8h8ZkHVA/m8FK9+QfqtQ4o2E2dvHdlXUmCnJauwIgYYD5icH+Mn8SUa64504HgoX1TVFUr9KOh9JIOtbxFPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110347}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "b671a94e106432b43ae6f52e2c2014e72e156eef", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.9.0_1737343686349_0.08077875535877799", "host": "s3://npm-registry-packages-npm-production"}}, "4.10.0": {"name": "get-tsconfig", "version": "4.10.0", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "get-tsconfig@4.10.0", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "dist": {"shasum": "403a682b373a823612475a4c2928c7326fc0f6bb", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.10.0.tgz", "fileCount": 7, "integrity": "sha512-kGzZ3LWWQcGIAmg6iWvXn0ei6WDtV26wzHRMwDSzmAbcXrTEXxHy6IehI6/4eT6VRKyMP1eF1VqwrVUmE/LR7A==", "signatures": [{"sig": "MEYCIQCGmPKlVVT0EjWBRdh0In5LdW5xlBmBaCZctt6N76jt0gIhAJjksIfEfQYQa/RiJqBiwRRFcNzJCtEt0pehydqyNvBX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115818}, "main": "./dist/index.cjs", "type": "module", "types": "./dist/index.d.cts", "module": "./dist/index.mjs", "exports": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "gitHead": "865dd9a7866d4fc418daf11b55da4b8183e28664", "imports": {"#get-tsconfig": {"types": "./src/index.ts", "default": "./dist/index.mjs", "development": "./src/index.ts"}}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/privatenumber/get-tsconfig.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Find and parse the tsconfig.json file from a directory path", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/get-tsconfig_4.10.0_1737454790005_0.9571091617415679", "host": "s3://npm-registry-packages-npm-production"}}, "4.10.1": {"name": "get-tsconfig", "version": "4.10.1", "description": "Find and parse the tsconfig.json file from a directory path", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/privatenumber/get-tsconfig.git"}, "funding": "https://github.com/privatenumber/get-tsconfig?sponsor=1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.cts", "exports": {"require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "imports": {"#get-tsconfig": {"types": "./src/index.ts", "development": "./src/index.ts", "default": "./dist/index.mjs"}}, "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "_id": "get-tsconfig@4.10.1", "gitHead": "8564f8821efa26cc53d2d60b2f63c013969dbe49", "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "_nodeVersion": "20.12.2", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ==", "shasum": "d34c1c01f47d65a606c37aa7a177bc3e56ab4b2e", "tarball": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.10.1.tgz", "fileCount": 7, "unpackedSize": 115564, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHdpj5ysjzvohMsvoUFtiJp3M22n8qj4z8j9XoXRDVqUAiEAo2Il/OUzCaIrlX3dSnkJ+pi8NWrgkwWVXrr/3ZiOHuw="}]}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/get-tsconfig_4.10.1_1747766226657_0.8668920733782148"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-02-22T02:25:24.290Z", "modified": "2025-05-20T18:37:07.213Z", "1.0.0": "2019-02-22T02:25:24.466Z", "1.0.1": "2019-02-22T02:28:14.937Z", "2.0.0": "2022-01-12T19:36:10.266Z", "3.0.0": "2022-03-31T17:07:59.196Z", "3.0.1": "2022-03-31T22:14:11.945Z", "3.1.0": "2022-06-07T20:48:10.373Z", "3.2.0": "2022-06-10T05:56:47.431Z", "4.0.0": "2022-06-10T16:35:37.052Z", "4.0.1": "2022-06-13T18:21:44.749Z", "4.0.2": "2022-06-14T01:01:35.512Z", "4.0.3": "2022-06-16T20:15:16.244Z", "4.0.4": "2022-06-17T15:11:56.512Z", "4.0.5": "2022-06-22T05:48:00.349Z", "4.0.6": "2022-06-24T20:33:24.424Z", "4.0.7": "2022-06-26T22:08:30.505Z", "4.1.0": "2022-06-28T19:48:33.769Z", "4.2.0": "2022-07-14T21:35:06.345Z", "4.3.0": "2023-01-01T16:26:53.350Z", "4.4.0": "2023-02-06T00:48:28.829Z", "4.5.0": "2023-03-27T00:56:30.583Z", "4.6.0": "2023-05-29T09:10:51.907Z", "4.6.1": "2023-06-27T09:13:00.466Z", "4.6.2": "2023-06-27T09:47:18.148Z", "4.7.0": "2023-08-10T07:52:37.446Z", "4.7.1": "2023-09-21T11:04:51.289Z", "4.7.2": "2023-09-22T07:40:10.156Z", "4.7.3": "2024-03-07T05:13:14.740Z", "4.7.4": "2024-05-06T05:11:24.854Z", "4.7.5": "2024-05-09T15:45:15.221Z", "4.7.6": "2024-07-19T02:17:34.078Z", "4.8.0": "2024-08-29T09:31:17.707Z", "4.8.1": "2024-09-12T07:31:09.475Z", "4.9.0": "2025-01-20T03:28:06.601Z", "4.10.0": "2025-01-21T10:19:50.213Z", "4.10.1": "2025-05-20T18:37:07.025Z"}, "bugs": {"url": "https://github.com/privatenumber/get-tsconfig/issues"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/privatenumber/get-tsconfig#readme", "keywords": ["get-tsconfig", "get", "typescript", "tsconfig", "tsconfig.json"], "repository": {"type": "git", "url": "git+https://github.com/privatenumber/get-tsconfig.git"}, "description": "Find and parse the tsconfig.json file from a directory path", "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "<p align=\"center\">\n\t<img width=\"160\" src=\".github/logo.webp\">\n</p>\n<h1 align=\"center\">\n\t<sup>get-tsconfig</sup>\n\t<br>\n\t<a href=\"https://npm.im/get-tsconfig\"><img src=\"https://badgen.net/npm/v/get-tsconfig\"></a> <a href=\"https://npm.im/get-tsconfig\"><img src=\"https://badgen.net/npm/dm/get-tsconfig\"></a>\n</h1>\n\nFind and parse `tsconfig.json` files.\n\n### Features\n- Zero dependency (not even TypeScript)\n- Tested against TypeScript for correctness\n- Supports comments & dangling commas in `tsconfig.json`\n- Resolves [`extends`](https://www.typescriptlang.org/tsconfig/#extends)\n- Fully typed `tsconfig.json`\n- Validates and throws parsing errors\n- Tiny! `7 kB` Minified + Gzipped\n\n<br>\n\n<p align=\"center\">\n\t<a href=\"https://github.com/sponsors/privatenumber/sponsorships?tier_id=398771\"><img width=\"412\" src=\"https://raw.githubusercontent.com/privatenumber/sponsors/master/banners/assets/donate.webp\"></a>\n\t<a href=\"https://github.com/sponsors/privatenumber/sponsorships?tier_id=397608\"><img width=\"412\" src=\"https://raw.githubusercontent.com/privatenumber/sponsors/master/banners/assets/sponsor.webp\"></a>\n</p>\n<p align=\"center\"><sup><i>Already a sponsor?</i> Join the discussion in the <a href=\"https://github.com/pvtnbr/get-tsconfig\">Development repo</a>!</sup></p>\n\n## Install\n\n```bash\nnpm install get-tsconfig\n```\n\n## Why?\nFor TypeScript related tooling to correctly parse `tsconfig.json` file without depending on TypeScript.\n\n## API\n\n### getTsconfig(searchPath?, configName?, cache?)\n\nSearches for a tsconfig file (defaults to `tsconfig.json`) in the `searchPath` and parses it. (If you already know the tsconfig path, use [`parseTsconfig`](#parsetsconfigtsconfigpath-cache) instead). Returns `null` if a config file cannot be found, or an object containing the path and parsed TSConfig object if found.\n\nReturns:\n\n```ts\ntype TsconfigResult = {\n\n    /**\n     * The path to the tsconfig.json file\n     */\n    path: string\n\n    /**\n     * The resolved tsconfig.json file\n     */\n    config: TsConfigJsonResolved\n}\n```\n\n#### searchPath\nType: `string`\n\nDefault: `process.cwd()`\n\nAccepts a path to a file or directory to search up for a `tsconfig.json` file.\n\n#### configName\nType: `string`\n\nDefault: `tsconfig.json`\n\nThe file name of the TypeScript config file.\n\n#### cache\nType: `Map<string, any>`\n\nDefault: `new Map()`\n\nOptional cache for fs operations.\n\n#### Example\n\n```ts\nimport { getTsconfig } from 'get-tsconfig'\n\n// Searches for tsconfig.json starting in the current directory\nconsole.log(getTsconfig())\n\n// Find tsconfig.json from a TypeScript file path\nconsole.log(getTsconfig('./path/to/index.ts'))\n\n// Find tsconfig.json from a directory file path\nconsole.log(getTsconfig('./path/to/directory'))\n\n// Explicitly pass in tsconfig.json path\nconsole.log(getTsconfig('./path/to/tsconfig.json'))\n\n// Search for jsconfig.json - https://code.visualstudio.com/docs/languages/jsconfig\nconsole.log(getTsconfig('.', 'jsconfig.json'))\n```\n\n---\n\n### parseTsconfig(tsconfigPath, cache?)\n\nParse the tsconfig file provided. Used internally by `getTsconfig`. Returns the parsed tsconfig as `TsConfigJsonResolved`.\n\n#### tsconfigPath\nType: `string`\n\nRequired path to the tsconfig file.\n\n#### cache\nType: `Map<string, any>`\n\nDefault: `new Map()`\n\nOptional cache for fs operations.\n\n#### Example\n\n```ts\nimport { parseTsconfig } from 'get-tsconfig'\n\n// Must pass in a path to an existing tsconfig.json file\nconsole.log(parseTsconfig('./path/to/tsconfig.custom.json'))\n```\n\n---\n\n### createFileMatcher(tsconfig: TsconfigResult, caseSensitivePaths?: boolean)\n\nGiven a `tsconfig.json` file, it returns a file-matcher function that determines whether it should apply to a file path.\n\n```ts\ntype FileMatcher = (filePath: string) => TsconfigResult['config'] | undefined\n```\n\n#### tsconfig\nType: `TsconfigResult`\n\nPass in the return value from `getTsconfig`, or a `TsconfigResult` object.\n\n#### caseSensitivePaths\nType: `boolean`\n\nBy default, it uses [`is-fs-case-sensitive`](https://github.com/privatenumber/is-fs-case-sensitive) to detect whether the file-system is case-sensitive.\n\nPass in `true` to make it case-sensitive.\n\n#### Example\n\nFor example, if it's called with a `tsconfig.json` file that has `include`/`exclude`/`files` defined, the file-matcher will return the config for files that match `include`/`files`, and return `undefined` for files that don't match or match `exclude`.\n\n```ts\nconst tsconfig = getTsconfig()\nconst fileMatcher = tsconfig && createFileMatcher(tsconfig)\n\n/*\n * Returns tsconfig.json if it matches the file,\n * undefined if not\n */\nconst configForFile = fileMatcher?.('/path/to/file.ts')\nconst distCode = compileTypescript({\n    code: sourceCode,\n    tsconfig: configForFile\n})\n```\n\n---\n\n### createPathsMatcher(tsconfig: TsconfigResult)\n\nGiven a tsconfig with [`compilerOptions.paths`](https://www.typescriptlang.org/tsconfig#paths) defined, it returns a matcher function.\n\nThe matcher function accepts an [import specifier (the path to resolve)](https://nodejs.org/api/esm.html#terminology), checks it against `compilerOptions.paths`, and returns an array of possible paths to check:\n```ts\nfunction pathsMatcher(specifier: string): string[]\n```\n\nThis function only returns possible paths and doesn't actually do any resolution. This helps increase compatibility wtih file/build systems which usually have their own resolvers.\n\n#### Example\n\n```ts\nimport { getTsconfig, createPathsMatcher } from 'get-tsconfig'\n\nconst tsconfig = getTsconfig()\nconst pathsMatcher = createPathsMatcher(tsconfig)\n\nconst exampleResolver = (request: string) => {\n    if (pathsMatcher) {\n        const tryPaths = pathsMatcher(request)\n\n        // Check if paths in `tryPaths` exist\n    }\n}\n```\n\n## FAQ\n\n### How can I use TypeScript to parse `tsconfig.json`?\nThis package is a re-implementation of TypeScript's `tsconfig.json` parser.\n\nHowever, if you already have TypeScript as a dependency, you can simply use it's API:\n\n```ts\nimport {\n    sys as tsSys,\n    findConfigFile,\n    readConfigFile,\n    parseJsonConfigFileContent\n} from 'typescript'\n\n// Find tsconfig.json file\nconst tsconfigPath = findConfigFile(process.cwd(), tsSys.fileExists, 'tsconfig.json')\n\n// Read tsconfig.json file\nconst tsconfigFile = readConfigFile(tsconfigPath, tsSys.readFile)\n\n// Resolve extends\nconst parsedTsconfig = parseJsonConfigFileContent(\n    tsconfigFile.config,\n    tsSys,\n    path.dirname(tsconfigPath)\n)\n```\n\n## Sponsors\n<p align=\"center\">\n\t<a href=\"https://github.com/sponsors/privatenumber\">\n\t\t<img src=\"https://cdn.jsdelivr.net/gh/privatenumber/sponsors/sponsorkit/sponsors.svg\">\n\t</a>\n</p>\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}