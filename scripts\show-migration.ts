import * as fs from 'fs';
import * as path from 'path';

/**
 * 显示最新的迁移文件内容，方便复制到 Supabase Dashboard
 */
function showLatestMigration() {
  console.log('📋 显示最新的数据库迁移文件内容\n');

  const migrationsDir = path.join(process.cwd(), 'supabase', 'migrations');
  
  if (!fs.existsSync(migrationsDir)) {
    console.error('❌ 找不到 supabase/migrations 目录');
    console.log('请先运行: npx supabase init');
    return;
  }

  // 获取所有迁移文件
  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();

  if (migrationFiles.length === 0) {
    console.log('📝 没有找到迁移文件');
    return;
  }

  // 获取最新的迁移文件
  const latestMigration = migrationFiles[migrationFiles.length - 1];
  const migrationPath = path.join(migrationsDir, latestMigration);
  
  console.log(`📄 最新迁移文件: ${latestMigration}`);
  console.log(`📍 文件路径: ${migrationPath}\n`);

  // 读取并显示文件内容
  const content = fs.readFileSync(migrationPath, 'utf8');
  
  console.log('🔽 SQL 内容 (复制以下内容到 Supabase Dashboard):');
  console.log('=' .repeat(60));
  console.log(content);
  console.log('=' .repeat(60));
  
  console.log('\n📝 使用说明:');
  console.log('1. 复制上面的 SQL 内容');
  console.log('2. 打开 Supabase Dashboard: https://supabase.com/dashboard');
  console.log('3. 选择你的项目');
  console.log('4. 点击 "SQL Editor"');
  console.log('5. 粘贴并执行 SQL');
  console.log('6. 运行 npm run db:check 验证结果');
}

// 显示所有迁移文件
function showAllMigrations() {
  console.log('📋 所有迁移文件:\n');

  const migrationsDir = path.join(process.cwd(), 'supabase', 'migrations');
  
  if (!fs.existsSync(migrationsDir)) {
    console.error('❌ 找不到 supabase/migrations 目录');
    return;
  }

  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();

  if (migrationFiles.length === 0) {
    console.log('📝 没有找到迁移文件');
    return;
  }

  migrationFiles.forEach((file, index) => {
    const isLatest = index === migrationFiles.length - 1;
    const status = isLatest ? '🆕 (最新)' : '✅';
    console.log(`${status} ${file}`);
  });

  console.log(`\n📊 总计: ${migrationFiles.length} 个迁移文件`);
}

// 检查命令行参数
const args = process.argv.slice(2);
const command = args[0];

if (command === 'all') {
  showAllMigrations();
} else {
  showLatestMigration();
}

export { showLatestMigration, showAllMigrations };
