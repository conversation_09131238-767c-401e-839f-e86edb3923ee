{"_id": "npm-normalize-package-bin", "_rev": "34-86727cf7c806f0b5060fc42af920ce1b", "name": "npm-normalize-package-bin", "dist-tags": {"latest": "4.0.0"}, "versions": {"1.0.0": {"name": "npm-normalize-package-bin", "version": "1.0.0", "author": {"url": "https://izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npm-normalize-package-bin@1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npm-normalize-package-bin#readme", "bugs": {"url": "https://github.com/npm/npm-normalize-package-bin/issues"}, "tap": {"check-coverage": true}, "dist": {"shasum": "c05ea346d29087efb34181adae00c667950aa163", "tarball": "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-1.0.0.tgz", "fileCount": 11, "integrity": "sha512-q5spmGV/CMb/glA+ziZT3ZNbZKnua8SRQLWxQK5cpQik+l4YxgElAw72z6ZrwMrnJtzoEs259Vl3neEfxKQmDw==", "signatures": [{"sig": "MEUCIBkYMvUOEYLqxxaVyrLNb+5ICWgUVmwn5GLyi64jyxoAAiEA1mGrTLczIsg00EQ5h+qc+WnGz4/NSTWElRUxekIRaCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7tIFCRA9TVsSAnZWagAA3TIP/0o5y9R/dEVVf1yDnqAn\nL7TjhRPsQypZoDU252XBloTcFpvUF9Dt/MFpkqr7A1KHpxYx47J9JqUb9z/L\nqSzWig87q8DNszd0EuX+trNzJl5ahhoD9wda6D9iiyvdgsJSPyQhhnIvgxn+\nv3RU+LM5vuiF2TMei9negfkNvJfl1Hw39Awbx+1qLXogytE4xBcfxcfioNTf\nOdWHep+rAW1aYL8t+JwEjgA9p10iXf0omavW6M/iQqfY48a8lY7fljWd2VE9\nJ0fjhXR6kQX4ppGipzMBZL7p8jd6t1tJTE/KbelbQMelZ71Br2aHgH8C0GZV\nMH8k6Z+RY03A5K1c9NhJFDrgvBhB0PrN4IOEZhttinj6tvp8OVj0UV+Quv+y\n67vFxNwf2PVvdeFf1OMFAnLUTmVv3H9L2/j+i/r6xCZPpQ/VKv6bCGV9S7gg\n0H4iQ2dWAmApjP37K+SMqdRLvI2SgO3ddqTKM4eDp610rF5jjzqEVouMRGl3\nA6cZkBY+6aF4RKH7PfXyE1BkhUoJcRLtx20I+cZM6SWu3rhvMFtWBfoUAwHi\nAggp1mzYqLGfpZpGZ3ApagjnNqLhdyWKCvtA7Tf2AVbzjkgfPWnsJ4CvCiKr\n0yn/UFhTozYHkF5SOb0eP/a5KrhMZpZxzVOAiHehjuK5SnC71bQSD0TDsGIg\nO9BV\r\n=2K9j\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "f2b2b3467d6f91384598898b52b5d9490035fbad", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/npm-normalize-package-bin.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "Turn any flavor of allowable package.json bin into a normalized object", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.2"}, "_npmOperationalInternal": {"tmp": "tmp/npm-normalize-package-bin_1.0.0_1575932420747_0.056471445051378355", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "npm-normalize-package-bin", "version": "1.0.1", "author": {"url": "https://izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "npm-normalize-package-bin@1.0.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npm-normalize-package-bin#readme", "bugs": {"url": "https://github.com/npm/npm-normalize-package-bin/issues"}, "tap": {"check-coverage": true}, "dist": {"shasum": "6e79a41f23fd235c0623218228da7d9c23b8f6e2", "tarball": "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-1.0.1.tgz", "fileCount": 11, "integrity": "sha512-EPfafl6JL5/rU+ot6P3gRSCpPDW5VmIzX959Ob1+ySFUuuYHWHekXpwdUZcKP5C+DS4GEtdJluwBjnsNDl+fSA==", "signatures": [{"sig": "MEQCIEBEaMWlLwVcLRLgX5jkdxFqb/oQYD/kfWw0KjryoR5LAiBAlgbyp2cqh/pDjKhPTcuHQsM2F6tjqvUSTKickfv3MQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7tvRCRA9TVsSAnZWagAAGdMP/1QUD4tHDsTrb4dv9wPQ\nDymYtZouU2YYYEqDOM0SsscC1zQQZ7bFRKju/yaselb1Iv3/AodqGz9uXIg9\nA8cZlomH7j8HXl9w63DHcltjhXg3Q3evo65PtLBm57Y9i6R5FzVsLy3ogqY1\n5AB86yZBd/QujCJSAhgrHDy/gz4SA/mb5xoLa42wMBsXX3+ZsCCxXG3BOhdc\nIWaNQi3Fw5o3G2omEfs5jDEAQ4x6kSMakS9obPBzbY5BVBr22lTxDf2xV5ku\njjsBC9zB0dVcR4xQrW9EM9CArPz/DoyAY8B02ZffcBmH4bwzSkpsW9CWMcpR\nHOfD0VIs2SsmOPlzacoMMMb07z+cE9iJE0jG1mTB8r60hU7ncPme3Vh5SLtR\nBw1Mp2hXntIUNYZpEgKZe3/aJw5pYXg5ZXr4RvLX2sPkRKE0rlbHwf+xEkyu\n+5xDBvXCghwS9ZRPnhjDRfD9J0vLgb4BSRtqDW771G21h9FoJ0o7EAL3bn3C\n/1WBmZRjYO0WOxAZGwnTVoPGrk5bMlq4doN1TRQK+djlV+D/35zCYptvFkLi\nwdGQ1Rlyd8d6+hrkD1iTRnfVPTU/38bWLyqHNO0uxOQLhMAJA0MKKozIgexQ\nMtzqCGnZ4S8ytJcCggfoboeLdagIkNqBREAIfzj9iXkfXJLzcDSHcErVQo2W\nUmMe\r\n=56ia\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "812e4af8749ee8acaa78e4d9e9ce3983897227ea", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/npm-normalize-package-bin.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "Turn any flavor of allowable package.json bin into a normalized object", "directories": {}, "_nodeVersion": "12.12.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.2"}, "_npmOperationalInternal": {"tmp": "tmp/npm-normalize-package-bin_1.0.1_1575934928937_0.7548793808830665", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "npm-normalize-package-bin", "version": "2.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "npm-normalize-package-bin@2.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npm-normalize-package-bin#readme", "bugs": {"url": "https://github.com/npm/npm-normalize-package-bin/issues"}, "dist": {"shasum": "9447a1adaaf89d8ad0abe24c6c84ad614a675fff", "tarball": "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-awzfKUO7v0FscrSpRoogyNm0sajikhBWpU0QMrW09AMi9n1PoKU6WaIqUzuJSQnpciZZmJ/jMZ2Egfmb/9LiWQ==", "signatures": [{"sig": "MEYCIQD+1CvbArG7F5AvTsnF1r5rBcsCiuIvFFykvv1+jRVynwIhAM/3Cntav+kmZMWtG+2h2nU4XShT63mwBrUhApavsah+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBAqDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrdmw/8DwQetVCZGm+ud5ymNVsv2WiI/apdBSp97dVxHWru3ENOM99Q\r\nLpTSn38rZFPJ/9mFhKt/Kj42EEXDLaDcpu3NLn4gVWZENiiK1GADNYK/Vsaz\r\nskaq9134s9YSlijMXk1GrGFyaq7Gh8FVLKHU351sUN7wUXTzNQz+kiwwqYMH\r\nCYenczHhdxFsPiMIfVoSGyJJMqdXp7lOvO8cavOG4RMKSpTwd47HwbH5hJMd\r\nvGr0igesvKlukWkTb0TQhVrHHSFchIK/SqDRSIChrqtCpdCRDGydPS6ncWvs\r\nz61zN/uUbJBgpHVyE8jiZ/6xNxYUcE4/KmOtnpFUqQ5X2qauVjmdwoKArc2t\r\n0gT+sLh1ucsjCNKJbB2hxPrsr4XcdJCZiBbcnxViYrPUEKbCY+x8Nhgj+uIy\r\n+cYdGUEnaCDaRrDs8B3HZdFZe6/UcWF77/IZMewZ92lZk5RhLemVze6vDqzp\r\n6a64fhYZWNif/t/mApd5cX/8K+Vdfd7FQIMoMg+fLYe3YiZ3jFaGveuozi4t\r\n1JzqEn4FenN4wNVcnuiUFkjk/51Oo+5oi0ifmFBKhnUtyKXIvwBBH0j1ZChs\r\numTdM50oTPg6HykInxUnwEVkGmKrBVNmPly3g5sks1B/Wv2D9vG/THAL6psF\r\nIYqeRLkgJ1EQTclEG2p0mzehfXDOpQq/pPU=\r\n=ddy2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "3a177d6b0d0063855612510c81ff9e2191517cde", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/npm-normalize-package-bin.git", "type": "git"}, "_npmVersion": "8.18.0", "description": "Turn any flavor of allowable package.json bin into a normalized object", "directories": {}, "templateOSS": {"version": "3.5.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.7.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/npm-normalize-package-bin_2.0.0_1661209218971_0.8074207735827665", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "npm-normalize-package-bin", "version": "3.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "npm-normalize-package-bin@3.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npm-normalize-package-bin#readme", "bugs": {"url": "https://github.com/npm/npm-normalize-package-bin/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "6097436adb4ef09e2628b59a7882576fe53ce485", "tarball": "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-g+DPQSkusnk7HYXr75NtzkIP4+N81i3RPsGFidF3DzHd9MT9wWngmqoeg/fnHFz5MNdtG4w03s+QnhewSLTT2Q==", "signatures": [{"sig": "MEQCIAhzM69+X31xwewwXz2emgnA+FG0ztzfxLINpisac412AiBgJyEqhNZiMMK140+F4qPZByvpjvVooUknqJIQCLeDtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSPI/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCCw/+LwW+PK6jUFoWg2IioBTTOuneqX6/6CTSnpYjaBtUfIcMlLtm\r\nG3R9y0zgk2mpC6Cpkv1OB8o2ztnEG05OgiYFiKzLjpoHPS2AiVidV1fNmpQX\r\n0noiVuEvaqrR+HPA1nhSPzVT7PbqhGET+vnHWQra7fxZLY4C7X3BGG9i/6a8\r\n6POU9qWrq+U5szvvhvUpRY4IMlJonrvMjFAtE1Obm/CtfSco4D+MGzOhBRN1\r\nz6doaLTJ6rdU5ihmB51Zlb8mBvOB8CB6KSnsqqcUXy50lY7wE2+30sq6i58T\r\nCNJP8vABTru+87h5k6nu+3fNAA0tlSp+cEPFMjCWZi1yjt8fd+m4W9H6xqa6\r\nRtIkSzgcuXzKVyBnuN87mTWHCxMiGMLsuE35xSA7lukUxlrdg7fyKzHYgnS4\r\nGYsh63hYObwMgOB2ByOJKnuMLbq1xX5fi5oqvwOt5P1dyyC9Erf1heIxS6/B\r\nXtFu4u8SKIQSiEgetC0TGgVcB26avPTOcbiXfWGReVBzPdSlPCzmKHyRoEkY\r\nlnkutMAJc8cBDTM3Ra9SfWatRgjXkuHd6uRMRdhLPseI6pKZtymxg8BHIPsS\r\nliIRL3LiUxOhZRWf3XeTmI6p0g+yogSSFDzwLm8jQ3RN5fLbgAYZkAFo9VaK\r\necpTmNON3W77jyVpsXcEafVQdm5pyylroeg=\r\n=Hm7d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "f3db0cf9c67c5446b84d13052da0437f212e3f87", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/npm-normalize-package-bin.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Turn any flavor of allowable package.json bin into a normalized object", "directories": {}, "templateOSS": {"version": "4.5.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.10.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/npm-normalize-package-bin_3.0.0_1665724991246_0.4581128291666525", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "npm-normalize-package-bin", "version": "3.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "npm-normalize-package-bin@3.0.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npm-normalize-package-bin#readme", "bugs": {"url": "https://github.com/npm/npm-normalize-package-bin/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "25447e32a9a7de1f51362c61a559233b89947832", "tarball": "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-dMxCf+zZ+3zeQZXKxmyuCKlIDPGuv8EF940xbkC4kQVDTtqoh6rJFO+JTKSA6/Rwi0getWmtuy4Itup0AMcaDQ==", "signatures": [{"sig": "MEQCH3B9f714kCf9NhmBVaTlPC0pYwu3gHgnAYpDtgyyCVECIQCMktqP8W1hJlRmZGyf9OzXyYAYmfLjL+Bss0qjIjwqJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/npm-normalize-package-bin@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 3579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUXeQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmri9BAAgfzaZmh2s/WA7uLPaPv0eqfMAgCHY3ftwGFOhde27mFdGqE6\r\n3frdcE+uIJGJp5R+KJD25vPPD7WCs7bo2jReCgsjErfQeKpEtBfvqxvhq6h2\r\nGybIM2+AwL5FFG4mQo0xBh/okKSD9iWkzPCkdDw60lpZSVEiAO1M5OZJpfnT\r\nL58Yy0L3QT2/0rZBweKCjCqc0+Kl6aJ4OR6Jir9c/m6zSyaUGvk9kGCYeQpD\r\n0N7Ms64AZlTfXmjnbPYTyZWsb5rY4n3xb5uXF6STLeKxmK7LkTTz1+kNZg6D\r\nFW2mLfGD2KC+eWvhfl0J+sw99MidfSr+MImK4SNRAwPa7V0gdl4VoQYNg9L4\r\nVc7g0kIZ4v3B+W7UExcDKybzRJlxaVLHryA3uPY9o7pdTI0FG18MzCsJfhsT\r\nkKZ9Y/0ILgWHgxb+RTPa05aPbldpftjhu6Hzqe8B1YDkbpJ2fkLpNdYTbhTn\r\nYgqvNBH4MioVdZ7bMF8RYF1h11Pe8c57mrnGUrepRdhSwSL6sMPftF5ieoh6\r\nvdckWubupUPPNruZOpahzrlVYJw9kimRia9QrI03R1SVIFLrNpuWQ1rN8gJV\r\nk0e43rYvYmdXFf2gg6uYjGTwuy6XKcpBSI07GRssq/TMGiknxkJoV0seOcUd\r\n85c80cnSHlanbO8IYPIYSs/+cdlVF2MgD/4=\r\n=79fh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "8d11759818e4224c053be0810326918980c90a6d", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/npm-normalize-package-bin.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "Turn any flavor of allowable package.json bin into a normalized object", "directories": {}, "templateOSS": {"publish": "true", "version": "4.14.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/npm-normalize-package-bin_3.0.1_1683060624197_0.6113286668602453", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "npm-normalize-package-bin", "version": "4.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "npm-normalize-package-bin@4.0.0", "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/npm-normalize-package-bin#readme", "bugs": {"url": "https://github.com/npm/npm-normalize-package-bin/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "df79e70cd0a113b77c02d1fe243c96b8e618acb1", "tarball": "https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-TZKxPvItzai9kN9H/TkmCtx/ZN/hvr3vUycjlfmH0ootY9yFBzNOpiXAdIn1Iteqsvk4lQn6B5PTrt+n6h8k/w==", "signatures": [{"sig": "MEQCIBheboNvkKscyqapzTzy7a92JNSoVPO9M5jDkMDXFuf/AiBQ56LQZE32ZSrHfwdIv13qZdkvGDrKJ9FHmwB/9SpB0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/npm-normalize-package-bin@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 3626}, "main": "lib/index.js", "engines": {"node": "^18.17.0 || >=20.5.0"}, "gitHead": "4594c6855a937f2d1296fe5b61adf318a9bf529b", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/npm-normalize-package-bin.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Turn any flavor of allowable package.json bin into a normalized object", "directories": {}, "templateOSS": {"publish": "true", "version": "4.23.3", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.8.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/npm-normalize-package-bin_4.0.0_1725495569017_0.7050804973997362", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2019-12-09T23:00:20.720Z", "modified": "2025-05-14T20:04:08.005Z", "1.0.0": "2019-12-09T23:00:20.938Z", "1.0.1": "2019-12-09T23:42:09.030Z", "2.0.0": "2022-08-22T23:00:19.157Z", "3.0.0": "2022-10-14T05:23:11.458Z", "3.0.1": "2023-05-02T20:50:24.353Z", "4.0.0": "2024-09-05T00:19:29.155Z"}, "bugs": {"url": "https://github.com/npm/npm-normalize-package-bin/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/npm-normalize-package-bin#readme", "repository": {"url": "git+https://github.com/npm/npm-normalize-package-bin.git", "type": "git"}, "description": "Turn any flavor of allowable package.json bin into a normalized object", "maintainers": [{"email": "<EMAIL>", "name": "gar"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "owlstronaut"}], "readme": "# npm-normalize-package-bin\n\nTurn any flavor of allowable package.json bin into a normalized object.\n\n## API\n\n```js\nconst normalize = require('npm-normalize-package-bin')\nconst pkg = {name: 'foo', bin: 'bar'}\nconsole.log(normalize(pkg)) // {name:'foo', bin:{foo: 'bar'}}\n```\n\nAlso strips out weird dots and slashes to prevent accidental and/or\nmalicious bad behavior when the package is installed.\n", "readmeFilename": "README.md"}