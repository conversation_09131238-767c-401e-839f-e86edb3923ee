{"_id": "create-vite", "_rev": "84-37f8a275a9d6f5fdf862ba7faf3612a2", "name": "create-vite", "dist-tags": {"beta": "5.0.0-beta.1", "latest": "7.0.0"}, "versions": {"0.0.0": {"name": "create-vite", "version": "0.0.0", "keywords": ["placeholder", "zce"], "author": {"url": "https://zce.me", "name": "zce", "email": "<EMAIL>"}, "license": "MIT", "_id": "create-vite@0.0.0", "maintainers": [{"name": "zce", "email": "<EMAIL>"}], "dist": {"shasum": "6b99531b75f918332086ac60f4ed4e55d244e7bb", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-0.0.0.tgz", "fileCount": 2, "integrity": "sha512-whX7cVDkYaqnkagzXsJkzkiD+KGYsnGdC1e0vePldvKyymYVallOiPlkllK/X/cdH430HPn9BKOvZiK6fa730A==", "signatures": [{"sig": "MEYCIQCUBe1Tw9g3kT1NHluNlL8hYjBuMCgwTgryIUkv8tRiYAIhAKJN2x3h0QPbjzAKvAXy6WvsZ9zVBY+BxKMSQc9okMsO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBcm5CRA9TVsSAnZWagAA4UYP/Rm8iSAc2sZALELL3qlb\nL8iSkjkI3sZfLsDKheeNVazlEGsy6nadjJSSH5MuPok0JbSInMKY3lYhqMnZ\n4105h1C7rG63Rt8Am0n2fyGj/3Rgh+2kDkBWvdQs0SYp4DQe1VgeCt4bmBZh\n9xpZ4OOtCEYs1x8gcFlc5I+Dkb7XREkfpvk7jEsyO/p1tXLj3hKT/5AwJnDe\nSMgTlMgq9JKdmeJ/cDDJ5W976x/Yc905fJOEixbr8snFtM54+hWW13qULhsu\nUT6Paq7HNqSLsBfF/SqZ9fxKiBh8TSeIJOnMuYIvRDhmBnjUjLOzKQVwDarz\nzf391d3BumkpHnV5t9/i8KNnLXDhj4tydIkqxva4Hwo5hplSOhe/U0qA1wrU\n7edRYEqu1eckhsC/b1F3o80wEgMYPHPv9IrtIk3kC/s5WphMn0Jdo5fLDicu\nAfiEXt6FDZiQhPLLtmTcBWye2GLG9CZ3UholKvCJCmFRxTUaYOoY3aHiQqcr\nAQ0RHPPJZ1BDtvBubhhFPEwQn3qjhViLOxDM3lT29Bm6kvJjOtUmqvXsgrvM\nsJOEq9Ffd+EiNy1EQ1zZRwcF3P41HS9Dlt8Ri+4n+TIybRaeJn+gP4+3iZS4\nypkeNFrVPpip+iSgv4gN2kP1oDbB5xYlOEpJjurw/ZTpRhdM+sQYtTTGSLoc\nqfN+\r\n=efpw\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "zce", "email": "<EMAIL>"}, "_npmVersion": "6.14.5", "description": "Awesome node module", "directories": {}, "_nodeVersion": "12.18.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_0.0.0_1594214840890_0.07332224675193344", "host": "s3://npm-registry-packages"}}, "0.0.0-alpha.0": {"name": "create-vite", "version": "0.0.0-alpha.0", "_id": "create-vite@0.0.0-alpha.0", "maintainers": [{"name": "brillout", "email": "<EMAIL>"}], "dist": {"shasum": "7c069bc1addfbddd21609ec3255616b8b6e92d9f", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-0.0.0-alpha.0.tgz", "fileCount": 2, "integrity": "sha512-vnlC+AfLjdq44Cz1HqrJoguhHj5/RGaeniqQgMWBVTiNgaoVKSt7c/EhjrXlY9a5bn74Ou0yGFOYF0IYIJnS1w==", "signatures": [{"sig": "MEQCIBYi6yjl8YmpASE6rz93DHgKkiRLFu/MAUIxnBE/lPZwAiAa+MxKvU905fWUaKAp6ekJSJIfwV5iBqESBFOLf5E+jA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmRLaCRA9TVsSAnZWagAA+IEP/iAwCJovwSPBesXqyaF6\ne0XoLXTN/7rCNFzo5n2QVj3PbQxmKZKitLnOy4NXAHg/84dTQapEnR5KVutq\nbmAoOID7j6T6s5qVJT57TnnZ5pSvdwE+6vXKSZjrB2bgtU+xvxNhM9TztDNx\nfLN2vNOtqUk20TYPM42xjt7vvCABChRUbIOF8KkcR8u6eTtpAVyihNgQ/tDE\nsXC/Z1ol8vZwXkojhZZAlAuIW3MJxwMq14/qLxT02Irks9uhqdlJVAT4dxeJ\nQjbpKjeSlYaogDstkDyAp/jfF3EDz3To1nWvZODHpKeh7ki1E2BnEyq2/vUT\nyfLdoOAJPrqnXePA1neRMsfevUnuK5e93zYHxXU5x3LwPsOu7SULo2MJbDpD\nGIGF2My3QrwLchk1TlQiqSqrw8iCH4se+t3CdpRI7Uztyf6yqaWv0OEEjlwE\n5paOUzqKIcgUImQE/N34n0EsYQW3rLppO2N7C3h3zAV1mINOzpPCfsshZOa5\nKVkpuD1EmpKtLpelJWLqezyEYKXKEdrhKBs9JU/ShqtYgf3d7ZSQ4qACy219\nn4jQpuEF88phokWcTkPM+c2VgFoqdg7H7XBpqQt1djKFEmb6VV9Pz4mWmBjl\n9Beu7aG46STgQnc6hAtp+QOwP5saK19It4VAEbm/Ek8fHeMd98qOzfZbkOBq\nDKWW\r\n=bbOG\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "cdb0c3fa72ee52f194cebe30e4e97e8c3b7017ba", "_npmUser": {"name": "brillout", "email": "<EMAIL>"}, "_npmVersion": "7.7.6", "description": "This is work in progress, stay tuned..", "directories": {}, "_nodeVersion": "15.14.0", "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_0.0.0-alpha.0_1620644569657_0.44220180808420073", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "create-vite", "version": "2.5.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.5.0", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "cda74191bb68aeddf54683debe676931e6c3445f", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.5.0.tgz", "fileCount": 163, "integrity": "sha512-ffQl3garkDsmqxGZS0RfUOJlRl2DnpIv/ee2WHzWN+nZN75kndLFdP/qTRi8WCIY8xVYK4/Dk5Zgkm7h/zlguA==", "signatures": [{"sig": "MEUCIQD9RaMCxjt/IzzH6aYtfGW7a84ccx2qZR9BIIMIki/sNAIgC472aTJqb/WBnMNheToiBED3a4CrtH7BOKWa/4V/0tc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6/H6CRA9TVsSAnZWagAAFvcP/jlMmC84G6kCnQaLR3P8\n96KiMvA7qY374RecVqfe4TmcRZoVNzFGhMUSAHw5uEmRoRqvOTLsnyW2aEaY\n0XLgl9hZ0TqUC1TkOvTC2ClsKruhiDvsz22UIm6oJhTy/tMgHTS/HLN3TQZt\nGn64hLFFPnfEFIXN64Cf7bpWGoLcq6lkS9x1UivzNNdFRA6c/ZcC3rXSmuvL\nZr2C82WjHGnSuGLjk0BiNB+qLptDL5+jFziQLylQeFycLTLEMWOzOVstYB6b\nAQg85vG1dMem+lI9sKVbWDmmS0NESBPt+XSTmgi2OoAnv1M5sua/PA0YUjPz\ngx4ms4lredD2YqlUGkQU+q+v33piQ79gDLhFsUDTSDWgywq41owe7M3ZD781\nsq1HcH/IjHu7YcFy5K7bYrftrLnSAjyCSuheJAReKhpU0qPKd91w0Md2pTpK\n00wwbaP9enQ6/XfyC8qq16241UfXTHk8k8CCPzhEFRqd03YrJCNzVxlZ0awH\nzkXctxG7zvIy62+evJLpX5CKNelsQjXhYpv/qIZpTdTaAxIJGrdfB2A7d8vh\nPMaT4oPcgfbt/dp9FVF1nvAQgGxJZeenGpJD8XB8H4ZUheFfM39iFWUhnLll\nvW7frUtPX9zRkG5DFgssKKnw1h1nw91LH/u9omV2zPM01r4YNE3Sek2XZYW1\nFMFs\r\n=/H2z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.5.0_1626075642051_0.3865964520267031", "host": "s3://npm-registry-packages"}}, "2.5.1": {"name": "create-vite", "version": "2.5.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.5.1", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "2cdff304e5d6b71be1090fab01192c9b6fde4a41", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.5.1.tgz", "fileCount": 163, "integrity": "sha512-o36dzZvN7EDNSoH9Ir9cn8RHOXnD9Pt/krHRmOsEnZMvlYEZ821ruhkKOYzlwZPYf6mCRIzIHdRsNsFXL8+EIQ==", "signatures": [{"sig": "MEUCIDKgGV8tHlVWMR+1jeTZkwXKWwkDspGZRe0P8ytud1YzAiEA276GiHczgY7CgwhxDMB6kWLO6f4yx0eGYZr/UMMV0Qg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7DTfCRA9TVsSAnZWagAA6IUQAJ4zFNdl9Pfzc6z2ryAE\n8mKrK2r4ms09/q+BhyadRPzWsEJMs1+3ezt0r58/JwFAjisEkgngx2kQamIq\nTKPq1VkgwRZIbVGHiMOKWjK5lKOCWgIeZNzDbH5nb2h172pSw90ZMnnjS4MA\nrU8UsOQ+RNdflS4nzs4Jh5SD9D1/zlxQrVUXqO7AoJX2EeTZNIVlCcK6YZsn\n8Tv9GdnXQJ7b/DlPHOXUA6dFcVZFIqDhXG6sFW0AJ051sm78zXBOQX0SQV/L\ngag3qo07gHMQ+2vNZFBo1lgfGQG+kq5x/H2hMd6LZgmxp19nR5ucV7KXXnth\nWjiDiEdc06WhIBMuxemienU9S+T3IA2ins5oWYfvf4QMTjv6JGL9xk9UxYGX\nuHe69dlHuxec4CDgnUvRH875b5y97DKEVhY1LFZF+GGZLcx4wrxp4KylewKm\nEEBZFrGstX5F9SGGMMcRt5kN4svxE6g28aQRZx5p9hDLSiaPXov472zXMMxB\n9pXBZLwoaKIfTE0mG1BcP+qRurPA0pJrrli+vWbjdsQJ38imcDk0sWjfrU80\nLEVwPIgOwvn3ncgyKe2ZttELcVQl+zOkk8NTTZTEzIe3BdaZsSL7VhmtwZVm\nXOhYnR0B7M+97rV48Bk9f6EYA+xRdSa9uhGQMFePl1JAbb1nLgoWlEQuHqyO\nV27A\r\n=D9Qf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.5.1_1626092766959_0.2949662672589455", "host": "s3://npm-registry-packages"}}, "2.5.2": {"name": "create-vite", "version": "2.5.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.5.2", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "b533c3b45b04188ee579d7d5defaa0d6c785d9f7", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.5.2.tgz", "fileCount": 163, "integrity": "sha512-N8cnULLW7IZsArBpxj58b3wW2IrnTTrGNkMfnqFkZzga/LYWNupCYyf86YqiDYGDskYYh7k4iy9IQMebyMr9Eg==", "signatures": [{"sig": "MEUCIFA7SzJhsJWnnaFebFD/MwGuKqsO70in7EQAuCSRxNVsAiEA2G6nH1zstM613QzHDzQdGATYg4JOP2GmAqISv6nXAVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9rldCRA9TVsSAnZWagAABWAQAI1CVHQj1OEwJs9L/aSU\nOJsDpTY1jTTHrf4lrYrsntUCvhOaASGoOymw34f5KwgQwOc7uVDigeNc+wYh\nKlqlGi1nxmEcywx6WRn07mkyMt5mUNEdM0/Y5lSWOutRGRwmqITKKURw4wS8\nZfVdt49EcbDdcczv0wTZmNz6z3lHmMFCuLFxUszdj9ukRqcSkENRztdMs7J7\nhwhyj4Ius097teTVL4axXTkW59YzW923VxNfftYsEtANtF2bo2L2QZXW656a\nfeKtV8GMoBYnqlVj9ePjPFPlXUp/CcnUprd/iQsjEC8DgL+NwqYHV0l6y6ez\nKmEDdQe5TabBT0bbopulZKFwGu1vA+0ZAVeHWlI0MuGIrOEiplChyIM9r9J2\nbiBctsJ6vN32CH7aX5De0LngDUdBkHUDzie47Ztia4s/QKjnZhmK8mxfd1nR\n2vSOBzVyixyR1Lcu4ZU792fqNakEjZlrfOdB9a4+TkHeFaioqdrO8KRCgYuA\nM4zRXUk38QI9wkXKlWqrjDBTTa+ufZpSGAA3pp5ia6x6Uqp/ZKes7CTUoqqK\nO4tW8TmufhpDkZNCWiVK2LGZRNrFclp2OHm8WCOWFjrjsK2OVYTFGk7s0O+M\nqD4DMa4dJxWT0evaAlMhCpiotLmu8i0M/Qm/bv6aX7EnIYTV3Sgig3FRCz82\nL2hp\r\n=ahT9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.5.2_1626782045635_0.5478030826116977", "host": "s3://npm-registry-packages"}}, "2.5.3": {"name": "create-vite", "version": "2.5.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.5.3", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "5acec86edb10559abda178c36c8de75b56cf6ee6", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.5.3.tgz", "fileCount": 163, "integrity": "sha512-NDfxUa5xF5oSuR1ZA/OOIjsS7DNwUWYuR5cBmaCgtWT9MhVjym5789eB3wJBk+C6IDe8MgU2npifrTMaZXABUQ==", "signatures": [{"sig": "MEUCIQCsDmzOFk0MrQJLyUG5kcKBOEWXNtlip9ioV+lFnQ4VfwIgFnnkm8gQn3yMup93zTEl23IYvFUBOE30AoARmJZgPvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/9v+CRA9TVsSAnZWagAAy2oP/jbrGiU5CEtast7qHX8J\n5Gan29OYIajgdgDYaVD+VEEdXRw0cdIomJThyhvMzcFyBPLz3u5Vz2iq457g\nz5zvaTXSbzk6W664KE/Z/q+QVRB0K1E9T5as3E0KGvfxfCxZuEraEjVZzR81\n3cJUFyyBYQtaiTDSI0KXB+aRXzCHemChUbzuRgh9Pjzoc74DC8vajJf4pkVq\nFbci7KuyF8Dg389FVPztXAO14wkLMbkJuputY+A65Oh4mt2B4xN9sT2UavO8\n08xS1eQjyV5bvpR/XfsPqaWSCyELd2qpnvLJi8aHupC95mbSE7mbcS+mX/Kb\nxvAg8Nu55dVs6nGeIEF1x+JNb7yaMl4gEo0NOCdq2SQPFufCUxpJWHP+pie8\nyu6/Rn2Ea26hHlxm4zske1kPV6cbUPuISeD5MfRul1Y9lSd3p6Kh+rItVlOP\nrWhpy4kQVDIAvNsif62w+kcobFhIy/2/UWvflS7YiLBnQyT1ed7OBwZfDuIk\n45YEjjSBrU8wCyP9HlhxkHkh+NVL5dg/iPuEecellfi9SM8p7GdmqzDX7Eas\nOQs50EBZEo9+yz7BlGY5feOERaMHgqqilAlc+Indvn+ghnPFk57um6+vE2ka\nNAZEoEbx3/ptN/VIXwphxh+ZTMzRs8DIKviNRU1ZEZPW8is/n3DVv4VRBHtW\nayHM\r\n=aU2H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.5.3_1627380734242_0.09218691776890742", "host": "s3://npm-registry-packages"}}, "2.5.4": {"name": "create-vite", "version": "2.5.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.5.4", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "b6ea95286fe3faf34bc7902a8b4b9ac12c930ee6", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.5.4.tgz", "fileCount": 163, "integrity": "sha512-tu87l40pYiNZsvSVSZ2cWq0gdAxo+f2daaqN5GlAecDGCwRfJ7JA9eAxLNFt6Q0qeoySzZRDGJLIby7KxtA4bA==", "signatures": [{"sig": "MEYCIQDae9O4LBVvejZC6cZw7qhZUxqAm3/c4/q/4e/iS5LowAIhAPAisUKeQRr9X11cHDy1Il2oCr+GF0kbx6GN30N+1qH9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCShLCRA9TVsSAnZWagAAxkAQAJyypor7ibuX4NhXsdL/\nSjh0jYMkIi45WdIDK6+vFk8FzwCOFb7TixDzMZzXlivYpvH/SFtTN6b+ukAQ\nqFBbCSteZENHL43F+gtOMbzNizr9iJiB9GxkdnMHfPInQimScU91sBjCohGa\nqALVRTWxHnE7KH8wrd46BTE3bA2cd5Py0yFz03Jva5giX8MYYGJH3J/Ju64J\n/wJ+3oVyVAkk58l6RXvMg9LUzzXulpiudAa70iqrMViTSDKxA5p0ls6NImA4\nV6uUFI5O5ZDflqCM2y6y9xMC/KXy35YLJuxWIhqR4bfMaaleyl7Y5IosrilV\n4tYOClx+q5fNa/SYgWnef2l/lMiciUFNnsHBET8dWySejFvSf/wRyR0AV/g7\nrpxwu44X6ZT/gbaCshf4UfCHNS85G87w2I2lKVjD5GvHewvG2hlZSXOvykCn\n++Q5C1dLZlVEwDY/L3ACV+RzpWzAI0blkwsgxE7yDq1dbFs4lP6jJsexKwpz\nd6FKPgfa5VPZ7MdKz5JJqNKyC2Gmv21tbQ0O+o3syaMVUe3UyZ8L+GruHwT1\nR+/QJYZRAiFMUMmVlUqRb/wchNcs7ASoJizvI0TJOboxXsG89OC6TaLmP/eY\ntEq0itXz0G2blBE5YPJ4oNybTEVeoznUJl3vS2TekSajNc7at4BryBGqP3b9\nCXac\r\n=boXK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.5.4_1627990091122_0.14276079138576425", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "create-vite", "version": "2.6.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.6.0", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "3b516152104dba1d2c1dd34f1e5880857ba956d1", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.6.0.tgz", "fileCount": 162, "integrity": "sha512-7uFdBnRotxwJAkwzYerJP4cDoe63/60++i0ZQ67qT8cTVxXUi3Y97PcaBRzP+5wD9WGDWEQFu6IiYUu0gKfePQ==", "signatures": [{"sig": "MEUCIQCSrCd49wE/mLebKddxfRnW885GDpgvgluoH3M8NTAmcAIgbrCNMfKINvMd6b7IbKBauQYiiMnQNcTe8CKqUXDwVx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124614, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJs6kCRA9TVsSAnZWagAABvUQAIrOtZYtJwbeQRARIyry\nhFsvUaeR63mOaP3aWCITnGyAzaTN5FiiwIOEoKgbm4UppZs2TRylV2Ecjip0\n1Uu0y+xCNqWL7KuXzWI6vh4xb5+8u5rdnZCP/rrNY4go4T1IPrvnDzTpqfot\nc3ZJKvBvJEvQnQjWQ8SxyL9sFSBrVXlqxkQqH5J+9TTP/LTRKDLrUwxry59Z\nJAlTeGPF7q9lJbf75ubz1avrAHbr3RQ8es+Dd+Q4/XhI0595YYc0uJW01nH4\nOiu7/BRt/3EShnTUpQL3t6bE3YkL+SdiGPZ81FAhOvOlUKtYJC9S/SuZEtZI\nHsX+7FFnvgPIYz/xLVzISoOrr4Rx8xauZp6xvTlo5/p/y40AI6nkOb3NQu4r\nRuo1NheuPXcQ2ZzKItjjUFlxBqbDBEU014CLpJX/k2LDOFiiKf3l/6nHSNL/\nW12lSeLSPTAJ62+7C9Cn/hiraek8r/g4fCeAIdrRq+zwwzc0Vn7lO18ukfu1\nfXO3uMMAu9bUldCctZiP9t5jE2Vt0ADOmy9b9Wiih2bNo1HvrEwoIQXqzOAz\nGe4M2dIFehxVW1HnIIpDfPAcsjqAT9w5JTKGG5xzxzH58Vrgq9mHwIgVbg6k\n0ksrekFz4VVVX8TtGeyrOm/u2KrwBxkdGaIP2RvrESwWgTytVe75VlKJsVIa\nc3wg\r\n=Y0Pi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.6.0_1629933219888_0.8047355564380216", "host": "s3://npm-registry-packages"}}, "2.6.1": {"name": "create-vite", "version": "2.6.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.6.1", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "b0ae4c70556619d36e2394889c34efccb544b4af", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.6.1.tgz", "fileCount": 162, "integrity": "sha512-KQ2NBqSPvHPasxhW9l+kmu9cmGBBkf2WKnCIJddamjl16ZGEl7QYA9YFKWKXEDPfibUDJZgBjLwhz/pxRkPSbg==", "signatures": [{"sig": "MEYCIQDpSc9dWAMi4v71avK1OvHbPIeUG6KJWyiMNRYmnq7uiQIhALABqTSZxnVCT/HIUvSDuslAcM+SZCJ+JAUgYGGK7twT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLhdECRA9TVsSAnZWagAA398P/24vbp4ufra0PJ+8UThv\nIFm3GPRrbJW15l+oRuoGfv/Dh+kyDXkKerOcRiPv9DXpHeC53dHRXsglINuS\nTTItqd9/aC1+YCMeZJFvaPuH0Ak0fkhDiz+IGyn/sa9Ze5z4Wl+wqClC/shh\nNWsH3yu/Cll1Qa1DpdL/4yLw9xnoAQQ7z6VAf57mtbDc6RhEkFmRTqPCuwGp\nVYpTyx/JfzRh9+NOgYizn5+ks+OBRz/vi7DFE6sDAR4t8tUyrA7Tda6eo7ok\nYVKpuOKLsnQYqFpmGqm9kmjhKhReAY9VD/12x3JNAEZJZHYmnChSU1A5ETSh\n/6K8S26EtNfM+kiVt0+91kNm1Qly+VCVLl7Ml7EO/tqw4i5nX8jszfSQxfGU\nngH1b8Aso9TmUDwH5POFhprTR2nlbfWbtiblIktUSA0d+8FyoEaRfEfo/9xS\n6YJe2W6BHVUgXfg9E4sk0Ybntg6p7bwQcwwD1Ytkg8ejpERDGX1o5LKtqxD6\nW1PE8SJDmaa1OmIDGn5jSEREqhE/mVYpC+Pwq84UAIV9AH6N6lcMqx5PHECl\nVfABVPP7al20RLIOhxyTcc26mdhNFHovdz7srLc4fWlL/RpCgQGv5jY/yXXS\n/NleESYMmjLm3mXd/Kcab41e+RyxuWEuPMhH2rpdJ39aSxbteEWVXdAKGPzf\nkP5l\r\n=XAWM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.6.1_1630410564575_0.5786264006747355", "host": "s3://npm-registry-packages"}}, "2.6.2": {"name": "create-vite", "version": "2.6.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.6.2", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "b5333eb4f087767a2eacda94f2cc7bea19009993", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.6.2.tgz", "fileCount": 167, "integrity": "sha512-nPJCfDKrMtqEwxChewwU1GhgVjsetV5fuZaLtkbomDdZHOP70yuSkefLCqK4vfYXxfD9hTIryU4dSg03Ji0E8w==", "signatures": [{"sig": "MEUCIGRm9eG19wk/Hh/8VsL9SYC+kzEcIsLKtz2l5QWfEFgOAiEAndNFd5kHVbkl2C0AH46yWizC7/Uc1kqa12vkgR5i4l8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhN1OxCRA9TVsSAnZWagAAfbwP/0rloTAm+adryqZUF46K\nstvj3u02HUZGw6bRQw0YnC/xKJ3IfavGDWwXnuoGyGK2h062FnnDDHjkwply\nSCDAS+9PNHtCORaHT0VKBjeZGGs/4VkXAbGZG30pTc4GMu5UCzSRg6eYdZ/S\ny2DLi2t+W5POVhXRN6ah25ROKQVDpRHW1DpZOoXQvQ5Lk7i91PKXs9I0VJhA\nNJriAFddnNbddic9WO+HkKnjdsDL9bPnTn7WKMA7rJrnE3mEYh63EO2O1NU7\nujjZbmTAgw9M4EBrhtZDO/yqObx5bl2DjcgLQz4EluOpe8AMK6G+BeVNe6IT\n+U9SyzN35lRP0yHd2+jHEEEkSSx5D3yB+Sst8bKK8R0QkBp9MrvVNCjql2G5\nZJeFqh277gCNvw8tNCWc7KDgHIhq4D5Yj0tMe5eOZTfrLrIT+2YTb0zH46yJ\noZYrTgwanSif/19amA8K/FsjVYikLn5/XzFRmoN31UM7gLnFpWmIGYP5yaZ6\nZ+Xt4px2wkcif3wByF6lnB3nCkd1yVB/7tyAlwimQ2SQL5Uf2rmHn67GWYLr\nTKf4BObar3SyM5TlKDB4UHC4/10yHcVRKc+hX66Ttz8R81CyCuNBZd5ftKam\njek4sF5can+BSNw5laRiukYTky5WJtP+h8GgMrAI0vEHdHzkHx5GyLH6rGfz\nrdfS\r\n=TqYA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.6.2_1631015857043_0.4253127115774211", "host": "s3://npm-registry-packages"}}, "2.6.3": {"name": "create-vite", "version": "2.6.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.6.3", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "fa794f0d7295b95f881cd659439ba779c01db3c1", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.6.3.tgz", "fileCount": 167, "integrity": "sha512-5+e9tSX4GgB9y/oV7aZtg6WbEmr3N/mKyLgBepsUi3jSGP9heH3QJEfrwcOKQcxjMYHdRbmjdPFXW9CjLXtv+w==", "signatures": [{"sig": "MEUCIQC9rmuqvp/7YNm6EoADinGCJdRfB+ddo2G/KcYB9rnHgAIgDP/KsFi2MGPMXVBsI9GL3t5Yf4e9QxTSFYsIYQWQQDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126690}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.6.3_1632252447084_0.9591787452372598", "host": "s3://npm-registry-packages"}}, "2.6.4": {"name": "create-vite", "version": "2.6.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.6.4", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "092eaf4d4ddbb8e1e3eb06d86c814fd16162984f", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.6.4.tgz", "fileCount": 167, "integrity": "sha512-4Wh7GOCKkHSreRps5/pE7aJIASVyyVnZRwdWrMEwHX4+/3My/fyp48Rt5Rfl5WnE+5/wxIgDest8pkv+ldmX5Q==", "signatures": [{"sig": "MEUCID4BhTIGkNBRQcHMzxENB6aKA0jGGTGwwBUl/2K39RAaAiEAt4gt1ifh28qZsWXo/bdAX2u5uRTcepU4A8WcuY4BeAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126725}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.6.4_1632326551051_0.6006822575921367", "host": "s3://npm-registry-packages"}}, "2.6.5": {"name": "create-vite", "version": "2.6.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.6.5", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "17dd6b21ff7fe61ab9efc7e4ace57448038fe248", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.6.5.tgz", "fileCount": 167, "integrity": "sha512-nbtptke60h4CgwKLxNoqn+oa85NuJAlzdgBfFBS/rd9YqMPBV9wchnkTG1MI1oVRgSSr8XyS1Y7uz0bg7STDOQ==", "signatures": [{"sig": "MEYCIQCXguRhG1FeNX51PLvOof4vO7IX7fE5pXhXZlEMlrw0bQIhAI8UZbirEgFaTeaYH3ONsqEx45G9w6XX2EpF8bDiMcKn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126948}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.6.5_1632922330715_0.10633754114979155", "host": "s3://npm-registry-packages"}}, "2.6.6": {"name": "create-vite", "version": "2.6.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.6.6", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "97ee39490b9753a72eaa9fb85125a9968e0d50a2", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.6.6.tgz", "fileCount": 167, "integrity": "sha512-vQSlQuFFEDG+NaLiVn7um94MU244F5P8C4a8NavG2j1QiUedQg1ptQbk+wdjQaRW7ZjOv3N6Kp++6hvhWXRj7g==", "signatures": [{"sig": "MEQCIETC56TVN5j3n0R+29a5mS7BpBO1VHxaAAGNr8wmTjFCAiBDLw/sdiDMVHD2NYSO4PY0cIKXBWNvpZ8XbxP0zY0XQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127053}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.js --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.1", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.6.6_1633608282741_0.4139785100182791", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "create-vite", "version": "2.7.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.7.0", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "769d3a9745a4a4b81284325e77132faeba57c922", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.7.0.tgz", "fileCount": 167, "integrity": "sha512-BVLJkViB0+KGxiq+1pcBTTyu+/zGDYdmIDcWJ1aO89535WVLXJ/ZEmTy4HtOIzNTEA1acF338RcyxBcWnowyZg==", "signatures": [{"sig": "MEUCIQC8XxEB3VFLszfmsEyTuMkk2ZNP6es1mHS5DJ1wu1zDlAIgNclEvyahGMasfMEBS7psOOHvSuunr/U+Td/FdU0SHB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryR/CRA9TVsSAnZWagAAQtAP/iNqCRleOuaznevU98Yf\nNf6SSLL6FTezETQycgbv6ixPbhqZZvWtg8FTr644Mc038rWDYvpDYSbv4zRI\n+tcuPCi9a/zjmXvHOO2+9WWmnQ5+0ulTrbM4nCiVWCBEVYmTzqbYusnhOvI9\nMBfnt50arZTTXIDiR09YMxhlTEQtvRUCtVI9RC79SaifLo7j0aW2DWKmqGuT\n19BCzzCf3Wp72PSG5vG0aHQ3d6xWIsBu1zUBxipdOwHWODOYElxkltTgmQ9I\nDxsqLSPTQFFMj2nlUmYn2eD6W7WYRCq6eyYqhAid2Di+og3ixHhhKECjljlu\n9NWTNqtee6Rj9nR/oHMAjGQygKbaY0fwHppUqP229Zgbj3uxkqhD5BgNNoff\n6RcsMYVtoIQ4eufcmpc4Cg6zR5zPhc8yWiEofkB9RSB84U0iJv8jJ4C7s3TK\ndvr7jnvgz5Qzd0+FwUgalIucOUw+YtaEssrxnYhONOHfXkuKfN8vGkDd9nwL\nQkSMT5IYTvmEGCkprJug0lOxuYC5VpntVmC+F7Nt5BqcNSglIrg0ZMJ/DU9J\nHAy5Ncq36e5EvJ9BaXRpOH+NHglXoT7Iz9zPct3Yrfi4fbTMw537cC4ZCuNb\n/1+mbBXaxXV7LB3P58mqn3dNGLHJU6r81LwXSn8yNiJ6CcdF/IUwTqs1FfW5\nLya/\r\n=jLqX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.cjs --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.7.0_1638868095644_0.890099028115906", "host": "s3://npm-registry-packages"}}, "2.7.1": {"name": "create-vite", "version": "2.7.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.7.1", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "4bf4189a3a156f4e6a9342e93f73d81a2047a2b3", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.7.1.tgz", "fileCount": 167, "integrity": "sha512-oAMu8EO9yIiOv77JaxgrIVd4PaljTv2Vi+4gsyS5z2sz8tmmYL9P6ZL4ppwAw9jXj2WrZCXgJg7Nn9aphQ2t8A==", "signatures": [{"sig": "MEUCIQC+FQ1y3ZtGn/6tKCwQxLcfAhlsRbL26oS/7sxeoE3M2wIgWPR3sYfZ4iaYzDskzjCLjD6F25ug3In8xjVGRfNG2Og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127492, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtazQCRA9TVsSAnZWagAAmVkP/2YABex0X8NQm3AL2bq9\n9/e2lH526g5SA/s++di6dbCB1AHYDqGBiS3U/+6lsi9cdQ+hsCEzck2hTpT1\n+fG4sKTy5qfvNhlxzxCF/ayCJq9Jj12HPUk5NsSFqwXLUi4xWLOiNRSebUDI\n1ywfMvo6jaJ80HUQ0li5M8efIm6rgc+qAa25Q1eZ7V74+hOwLCq8QgDJlXAi\nVGQd+YtpjxpahVAu76EnyNY30zIuwTXhAtG63ksg8kJJv59YJ+TK9W/7qEDo\n569lLPt42bmT/x9lT5wjXwFYfMkh2s9ipezZakKnwJQFI1ehaRGy/x0SxWOt\nx+XMgPurYyeW2dULNmOI+K5oZnYgsiCFZZaOrgytHS+2llTMEJ0BtVK0ivMO\nUMVv4fzjmgUR8D+84w+aYuc2xfuwvq8ppPlP+IVEd0hWD4jcyHDRX/Tpwkhe\nKG2+AT4RSSuJWHMo8kV5eCmhZBOAPqNJTqycQnl41nDX8sb0lXGJRRwg0c7E\njX68opjvpd4S3G149j8wuxCpbCDhpIRJUcJwG6myTDnB6FFEJ21z63Am2s3q\n0hN5s6hq0jYBlfknXzOQ3sfNx8XIFmAJ8uZN+3EBifc0Hcb63DWsWx7pKfmQ\np9rDQKY/AbUpt7NjzW9MfT99NCV39X6JRQ9glIpIvAf8qNrI2ckd/42XB0RW\nlecv\r\n=7oU5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.cjs --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "yyx990803", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.7.1_1639296208715_0.9695455986245745", "host": "s3://npm-registry-packages"}}, "2.7.2": {"name": "create-vite", "version": "2.7.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.7.2", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "f77a759cae3f125f676b8e3ad52fe4bdccab5101", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.7.2.tgz", "fileCount": 167, "integrity": "sha512-4Eint1oL6GcZWGkgLbSmbzY1UNTkqQyjD9T4aM1YQsltKQ5ZNWDy+rvgYRPV9dR0XD6eLPs8TUUoge1xQCd4BA==", "signatures": [{"sig": "MEUCIEQ9TkkG/0yhOtQ9Ro85SiYSW1cuvNZUTw5Obz1gJh4QAiEAhdLk1t8CPS0APCWW3GA3LyRNdBin0+TOw7jqBlpHcMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtyOwCRA9TVsSAnZWagAAPy4P/AiF6QgB4ETSPH2//cnd\n9qL7h8AplBe2PwUua7v5tP+NO+gR73HUwPMejDqS9T7zZay9Q4uKgRzfuceS\nOnxoaq1QSXi+bg1hsiGWc/XToO/+GUchjSd/JRrBhsUVpX3/77cZyRMux3bp\nvHnDrQG8N8B1BHdbm87w2w7uy1EHvnQW8vcxxr94G+DahcGoMPHgp73AbnPf\nc8tg+ztmQ43DnrZjocfBkI4mldgGUl32mWSQgzkXgZ5LX+nqiOjtTOjAyC6A\noIQJHjynzEMpNFMQEoDpMbkDhtuFO2/zR1umPF+RidRgSBWdHQEUz27+HMJd\nAIhjcsuHOzaykbC+ihW6438nuccba3KM4E9235DwjCPpup6Y1ksacYZUOR6H\nEUVsowx8qkDmZEQD2Po9XR1h9hQBX0AZkeb7Gjcn/Ud+d7Rv1QVwpfm2uwQ8\nNv1tUk7psHwzwxEQtEF7JxzFr1YvAeI9WSuuFCXUHuzyayllBGnrhV4KZyoF\nORqkHyzXzCrM/XEtKm93UctjsI/XIq1ZMz7Zp2CzwEKaHNAXAzn1tHgnekXf\nRrznyjOp2urnJAKoEVyrQMV5PHtH59te71NDxFIqhQSRTtX0HwqPLUwNcaGB\n90Q314a7YqdIyhnRi2OgFjIXBlRpOUuVPn4YUQiVFseZi6shNIj/1Wd555bz\nv9Xh\r\n=TtYj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "node updateVersions && node ../../scripts/release.cjs --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.0", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.7.2_1639392176423_0.6441947652814604", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "create-vite", "version": "2.8.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.8.0", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "27b7e89895f59440690b9d793dc18c98502ed92c", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.8.0.tgz", "fileCount": 172, "integrity": "sha512-ckzs8s75NimP9su5WqA2+oZLGFYNxbahjdPYxYeFulQ+m9reO+pAgHe6XCfLl5sAvJ2g/i27Y43Fuh1ewUvXwQ==", "signatures": [{"sig": "MEUCIQD5/J4mT8LVmZ1CPEz1kz5FFdnHjI8K29fYd7nswsD+MAIgFUfNQjTS1C3prA3BsIMgE7BmtfszEn16YPJ2Pcccql8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiA1m/CRA9TVsSAnZWagAA7acQAKKplrLdb9kKo8px9Uim\nCH+3+qjR0WUYV2ita/Vj/Ml0kbSH195ULYEqFYqpkqxhjPeIA6wqo1B0AYT2\n/qWo82iFyo277U2TywO4HKDpNxD3uCKizUQt0IJ7R6KzmrwL5d+RWqWUXdbC\n9txiVKopSUAJJFO5NzBbgqeMnagZ2z102+0wwJDGKXGkhcy886r8YW0JJ4lx\nc7KpE6gkKtgUEJSIOOwuDfLuCA+ovDY8dJqgkMu5djstmkduD9K0aSFMs31D\nDm0mSeRV2CPzV7xPt/8FNTVbVuqZvycNKtFlhGtdnyh6HMJO2pTlmZyG+WfN\nZ9VkGryzAUy/s+W4pA3yRw1ClE7hokH+4ayoqxw3y+A4dNOf3F/EH6SFJ1EQ\n8D6K/i4OhN67o3pkCy1u23tbt1Mdje43DUCpTKyPxtsWbuv8kFk5V4LJec8n\nr7vozfeOSklnNvXUBXSlczamiD+aBARAUeIZX5zIEiADX05j+j/ZpANi9woa\ng6LCq12hczGEK9ARRsFHXiEnCeFbEh31fOByBJU35iFXKeNi/o02KoLKTYHT\n06E8QgCEww/fVdsT4PbtqInrJu98YlAMBLywem+tccLmUuDf+MiIU63VK4lL\nSOobAuN2hu6AoBmXjtTGXlQG5JPAbWaxtAp5Ogb0is2mRAtGjLBMaMY9Tzq5\nAie9\r\n=pXwh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "scripts": {"release": "ts-node updateVersions && ts-node ../../scripts/release.ts --skipBuild", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s --commit-path . --lerna-package create-vite"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2019-present, <PERSON><PERSON> (<PERSON>) You and Vite contributors\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.5"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.8.0_1644386751460_0.9385857135768423", "host": "s3://npm-registry-packages"}}, "2.9.0": {"name": "create-vite", "version": "2.9.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.9.0", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "2f96185adec337a9ece6d407cc85bebf03fc88c9", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.9.0.tgz", "fileCount": 131, "integrity": "sha512-o5ERuufhMM01Jh8ggw5844o+E63Ks1gKADAMAsRo5AgJnKl7170Mucce8WT+uxjLjwMOGbjJla+4z3RAn//xtQ==", "signatures": [{"sig": "MEUCIQDby7pWWbdUO9M/qI+9hmOS7u0oFFgTGAgOkozVuN2WxQIgUuan+kZq9Lr05OEy03GdGGcIUa9R2+pR1we7bevj9lI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRFhDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrULg//UwQJlUQCprhw3uehCwcQKZvqYtwefj9gr5NQXvdP7oOPqeBS\r\nBHg6Ak4q5mlJqNTNl12EBzh9UeneRFx5RirfhYAGG/YMtAH0m2kkZPdS2gun\r\nk56OwRnbyKFPyuZbeoGseM28y3VqCQnvwA5QzTmOrpfc63SBg3cu2m69jAnm\r\ncoef/zMior1HB83AeXvHpQLg+/5BmFW+gOTAutAKDVqS2b51k1NUZh8j4P7d\r\nEZ0ezznFEEu/+hgTBdQJzwxTlFHFN+BV9H9vkWcWxIorHNMjzC2noH9pBV0w\r\ni+3d9h4SZvGjdIbvgaL34MVMq1WK+PGAN+DkyaDLpRkIiYIYLh1yVp4BnJl6\r\nTxm4DuRCKu4T6ceXbz9uTRjJKhuOq6PrLH+MZsAu2JEvL7pPEcgpAfLV6hh9\r\nT+6A5A1tJQQlUzwJIEuLXDDFR8/vGJtAB5jbFb3/7K1l981OcoxONAZGKLla\r\nwlysHeg14Qun9QgPF5aO3q9Y7yicJiDZAnjgGTp0Qr+Kleo5r/UphBRNukAn\r\nMoTiQQaxs0ap4qMw7rUISeBG/LwIT5BxRHmAql+wVHJqmR446K4K/fCRV22I\r\n5F4O1G5DDUV1OIw5oJJxHcgZyG3y2XknwS+NOOGT1c/U3n8XsLUCgE2pchy4\r\narfmkQWkcCzIKyg48VYL8In44Zzf1+xsjYE=\r\n=NPDd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "gitHead": "690f2d1dfdc958128e6932350d9ba1a4b22c8ba2", "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.3.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "17.2.0", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.9.0_1648646211459_0.03395749465673381", "host": "s3://npm-registry-packages"}}, "2.9.1": {"name": "create-vite", "version": "2.9.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.9.1", "maintainers": [{"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "83edf0e6cb0fca371459f8b66965a57217667d8a", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.9.1.tgz", "fileCount": 131, "integrity": "sha512-urOJX33NC2k2ZYck2kFGuRlup0srvhuWtC3G7B/hEYPcowJpV+FgEN2KeI6OlaJJ//Wrcr6EXldyt1m0ugFkTA==", "signatures": [{"sig": "MEYCIQDWLfJgIja8pCcJZIBaZtdO4J1kQPXGmRZiVHYUpo5FRAIhAN75f0OmT8PaFLyG34kVIOTAOzRqR8vfXrCGdPdfJJty", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVtbIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpurQ/+IxdG8izt0SRWIViif/Io0+1dh8KHvmrFeQw9Et72D65vpK8D\r\nl1SuEz7hSG8/HPfvmmvY9DwN9B8gxlnNwcWP5rL18EyeFp9RA8J3Q88hDmSW\r\nL44tzjFmxhgw613cVkd3OwmaqtF950+sGmrBODx7+i/Mvg0yuq2Jysid3wvw\r\nLbEa1HE74G18bbCVaelXlN6a+rHnC6WcR6B/8IqDpYnnKCZnh0o73xMRAsnB\r\nqjeCwHvvPUpVaFKe9BtB1+jpcPcs+N6CF3H4qk5Q76fAAmKSqwnNg02COWZ5\r\n2xEu/sj0EUgXFG5EMRpQy+keLprk5oTd/jHZdXYgV2YO8O3Xi3Zhx0kKOctV\r\nMvo2hzunBkRq4d6B6KrzM/KW+yYJBxc07uf9Do516aJH7hnv4ZYg7a8FgkiV\r\nZXbG05QW6addyajwr/74nTKhxfep2aZ47eFdS3W8HTWNxEpUNDV1RNvjfN1/\r\niMVIuRu0sBQqIWB3ZLL8d93nm3AA6pDNEttw8h0s9yv+zhD31bQBrvZwIgpn\r\nASEA/Mc4tfvQbqnf2vCGp6Nnlf9xzmt5Fbiq9YK47BXSGhuyFcQyCcH8ss9/\r\nssX5XAywsBz4n96OkRvOIu0sYKkpzQ0mAmOhHclwvMlSeEOJSAppNJZm0uv0\r\nlNP5KoT18gx7MHtqysyU/E5loIDOs+d13ko=\r\n=Z7My\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "gitHead": "adacaba1cf03256cf18a3494b4d7b053605d4a46", "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.5.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.9.1_1649858248057_0.5184014239703321", "host": "s3://npm-registry-packages"}}, "2.9.2": {"name": "create-vite", "version": "2.9.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.9.2", "maintainers": [{"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "b7f6e344b5271de2a2785cb1fb7020efacf12250", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.9.2.tgz", "fileCount": 131, "integrity": "sha512-50UqOLnArYcU2W8Avgk8ibUPDAs8vm255GjqPHj3+7/gs70FknajKqf1t9kWkeXpiVtlTcMMyppPG04tPkNcbA==", "signatures": [{"sig": "MEUCIAlQ6umzq6rvE6Y9YkCiNey0DujwZ6v/IRRV7/4kW5ZzAiEA0f6xsxfNuYzPWY/9kNroKJcXrEDbDvOaHsiYVQX+Gwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXnHVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxxRAAnrXBZieQ2Djc+LOBwERzHLIZiJtsQ3I9Bv2OfUc+DoKxQv5k\r\nYynB2PdDDQDOC+bMVLMIJ+0ksSyGgE/j1ayeVeBX9XSe6SsqgNquWBrEVNrM\r\ngNf6ba6OO76uwvaOZRTnyeXKkIyAuT3encRH6KuOyMkaGTWDWWNoJ4tx4Ini\r\nhHl2w8nBHFgvHxT1QCMDulWEkkzo6fdjFCmeqmb9rybj9D9AhQgO77cy7adb\r\njGi38j2jWLSMSPCNhZJpIVPSU2C1Wc3azpW0OM1fwJHVcvPkmBV1hUDlcVOE\r\nEGiCPSPIHIvqgDvVYHQwrnb0D4i6E2xLcZbfqqg28G4AYCsVgAIfQNbdgtYr\r\nACTsKyGC+rXRtq4ZuaWQrhpkFrCemqOUsCgPKYPhkTKG7cuAh2GPP1mxDd0i\r\nqKWb/PG4MZfR+oUh4agde3s4/elolL44MKPDwtwkS5BiUxorF0ac02ZkRPKI\r\nEol3fwlYOsc2+Z6mwPIhZouYIHOrm5/84Bnt3omlF9TP91SWlVVP2O5+AapW\r\nOkZurL0rSmp9XegHKESEmxiENX7xhK+O49VexTR6U32+x9hngMWtqTd5iIXN\r\nJDdm0zlD8qW77FU/o0g0dCBrVQcUPvE8TzvTjVqEStqwsqYiTXy5hWOwyhUQ\r\nRPvDUnvdacYTRLdVAMHKQYkXy1FQpdkqvXw=\r\n=pLbO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "gitHead": "7f96b2633309d5f02b6f44d7350baad8b7522be7", "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.5.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.9.2_1650356693295_0.20369581784210933", "host": "s3://npm-registry-packages"}}, "2.9.3": {"name": "create-vite", "version": "2.9.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.9.3", "maintainers": [{"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "6551daf2c1f94577654a1aa3ff5286cdea7a7bcf", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.9.3.tgz", "fileCount": 131, "integrity": "sha512-3cy/nR6oyr+WrS/44xbqw7lPzECHNpsRsnKOJ6bcDTWBFsS7okeep1bpp8i9UakDfd/VHtl8R5WMKbTUNwBBjg==", "signatures": [{"sig": "MEUCIHkvTEfPEA5GmKAYSgmYwBlR9oiOF2K/73PFVDN3YdujAiEAlc2TpACzunOjH+YuVV1uFGToy3z7gIAimNOyHCe9LQM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicDIRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrb1w/+NJPYnKUaxFcxKbJd1PdzPmmp+z7q/lLmb59V0uqv1QoD/kXc\r\nQYvmRIE27DOr6LNpfMaJAo72QwENG1hdvOoWYny7KLtyvOAMjigIUi/Ntc8c\r\n7Zfx/awrZYCCJJWtn1Id5Yi5onaJgXzL3oAKNb9oyV1F0bRtC1pvG0IGKu3p\r\n0W5fgN9AREllW95Nh/UA3xraAa60RBJaXn3HuQngAz1O5einZRaYgA3zNk/l\r\n2RQ/KeEifEtFkJYdORvcI9Pov2ugdn+C3AlU4gkGPXgQwU0G2SSTDUw6NK2N\r\nhB1cb7U3tbrWIi1Hfo4aRFET6sHq0jPD4Qb7hycYr4EMBTBfd5nUdmUDirXI\r\n6NLtIikIq46EIVSc4WRdcnFGDuFADnJsafFO207FGtwts8YapeGEh9g9SNef\r\nf/NpKjsq3I6XtE1Wiwr7MYGaOngvui5Oy2YT+sY4bQXzd105qQqWPAgb7fkW\r\n7hn5Kw2aA9odsx10Dfq7SvbLpe8JqSZOCjJfC7nnOHkvQ5VYt3ZmBsnCx5cA\r\nZU0sFs+fb/x4xv2evK/A3k+KvSGog+fUzcl7lAJRNjwfnzK9hOTjJcqSFqZ1\r\nNRIFhPhdc38pEarjy8rbOS9kZu0lnaUX072a57lNtA47vUHjv7IGZbGFg3D1\r\nYOVmEP53eMQcFBzURQwiDuZWDaLloeoRYGM=\r\n=J07w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "gitHead": "1db7c4939a4c589854bb7a3a85002a8f3c42a50c", "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.5.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.9.3_1651520017739_0.5685240430641652", "host": "s3://npm-registry-packages"}}, "2.9.4": {"name": "create-vite", "version": "2.9.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.9.4", "maintainers": [{"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "0936d38b704b8a06c28471a58c29c31704dbfccd", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.9.4.tgz", "fileCount": 131, "integrity": "sha512-26ZjGlGH0ZDyt9rxJQice5YnD3wgXr3DyEt0bSznd2eVSzdgi/UgGSHQHU4U4SMKwAn0Vu9XmhA5MTboGuum5g==", "signatures": [{"sig": "MEQCIHct1Vv/FDS1jBXjRg7PZuIDPXrg0Pk0K8r8XRFOgtggAiBI26snNxWJYuhMsIuMoznGOFXWsS0+ZvdlOmrGnu8yLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie37hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohuA//YhZL9uqXQzarbKRO/yedOaD6kN0E7SbLj4aEYTptmINCgDta\r\nB8YMibhmjSUbldxk5g6AVrstBNNyzJjAxH7HmhmWzq7iq+BqpFt/DulUYtNp\r\nU4WJO2oy+4+uUuODO1NA5VbMogCTptldUKlQhFthG4SdbTaYVcQI64IUunj9\r\nrQep+Q2l7rkRvXNYJwpCBrEurako0RcCheiN0K7WCgKgR2xz4IbWobgrGsL6\r\n71W7TrkSPYX/Ryg0q5ynRfG3Q7Kgki9VRBsJqXydXnftuFCTaP3arUXgDIps\r\neZE6/lmh3bXrSFxcnFKwADGREq8PrsBvJLgSLAPBSH82sCHXBweqTHJVZW3C\r\nCXEhn5s81BbAd1F6uJviKdjjQrGBrfgagOY1sYjoXwB30y91uvmbZ3Xcsz99\r\nBztUAoRTYLV7BSt86Xw5ouxlZTqtsXvoO5ZOJnSsCFDDq0dhy9QkCiUygaHK\r\n+w6cxUsMT3IiEtURVj3shFE7U2qtwOMRVMOQS0O/1PD29TR1L0j02ooavmRa\r\ngs3VWlYiHuan7lbtJbRkjdF/adk9dD5kQZM1+HGPhKNAcw6f09EK7f4O7g59\r\noa2LrR5z5JqScBfqS7oAS/dKsiyOoUpXc053/Ud0k7td0O0CKcJ58aTNaMcL\r\nHaA8gAnc99bmGZrF99BbJclUtZG9Rp8NFBg=\r\n=pzMm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "gitHead": "1c2aef013eb01cad6cb24d9bd56d5c0618004d24", "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.5.5", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.9.4_1652260576981_0.07507978943382443", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "create-vite", "version": "3.0.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@3.0.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "035535acfb4fed124e4982e8f333808de760aebe", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-3.0.0.tgz", "fileCount": 146, "integrity": "sha512-G/sigmlGD6bKtLHt4cezI8kDwEThdbJKpyhFIrIVtvVwuCoAjtk79kP2RNjxjsOpM5BxQD5/R/5NKcymxBEEMw==", "signatures": [{"sig": "MEUCIQDRnSrdGF4FhYKucJGgkF2XBCuqk89um7ZeuFEuSrPXwgIgQp/hZSe8KUwRmSVFAXI6TngUyxpysm1L4hPaogM0s8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizsrNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNcg//Q8ksgzPqlIcl4PJXYlzM7gFb3rzPLBLN6DerOj1iePo9yM9C\r\nu6RRiLkQhjXGqCJAQeaL/Jpy1Xve8ZaURLMkgEEoznmBp6Dhe0m1c1DrQaHV\r\nP1TKoBKA1SGnzNjK2t21l1ipnnF/IKFxM2+/kgOBJk79nYl9/sAsZDbN1JQc\r\nBicOIxcrxIj/7Bp9OeLFRIzXFJBVKidaAg/lh0l3vaWfEKjVcV6vZS45I0oh\r\n/k4KvJJ6Sxj/7tvKztmJR6ucLkj8einTw1poAqBwclBN8nhanCWh4Y+X2TKs\r\nZE0Xo5q8ACDtYNFCkDF9nCdc5lxXeGyzCXoF6//F5q8f7TQ6YNb9M4sWKcsP\r\nE0nWNKA5psDtXGKmQXqGcqRrlfhudZZ8ifDB6hhTBu9PxOkI3I0KAI5C6Y2t\r\nI6qMfRGvjwUULEUiPhxiVHlcXy+Brp4DYRUntTVs5Y0y8CVvvkkaL+52N80l\r\njPWTHasxKbAXigKVZy8rGSlbHi5EWlQvtBXj27h/v8h3Xa7mTEJFuFM+MlYe\r\n2COMtG0WehpSJ2UlwxDiddAeP939lI7zpsBVJDyBTcsH7LDDXic3whiO8UG4\r\n9r3D5Sn6uzUmvWn2ruE0MdG2msZT8luJAJrXAOVq5sGZdODdwg8oqLFdZU8X\r\nT0yrmuzmJz/9S3mwWrYR6DBFqeMmK/D22Bk=\r\n=XT2y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": ">=14.18.0"}, "gitHead": "7065005b057e1f97d31576903e65eda5b82a3fd2", "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.11.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_3.0.0_1657719501013_0.37295716943012835", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "create-vite", "version": "3.0.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@3.0.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "8182ec2e18fdb852ea05e6dbdc1e70104dc04ee1", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-3.0.1.tgz", "fileCount": 145, "integrity": "sha512-vZw6zOp1FtCGz9x9thfSBeqhwbE7qieDV7DD6VjZvUDLUOI8auOVm7TkfaBcT4c9u7erhq9kjPnGbBTtRcJjKg==", "signatures": [{"sig": "MEUCIEPUieuIajsuDDouH0FciOROcrK6x8PkvGdYvVTRuUeqAiEAj6rfcNfH2ol+YRVVmMZD16+39SJ2aV3KUhYUB4eBfCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9WSXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwihAAmuLKbDpjaNlEhexZIPSVjZkSwfJ+6EGDherpC6eBXbnVR86W\r\n/OFDP31Iz3GcEwDDOH+7ZSkYqq+ZVIUCEkOnKKuiumtsjC57iTJNJQjQWnWX\r\nizTsvZg3iw1WYlJBL7XMtjZVDU5gVMPirLqnBeAEOLW835HNDLjz5TlcmgWY\r\nvIRulq8zPTnLgHdqCTK6sUuro81qrnesAiTDBT2nDnaSu90gENYQxLMpsXo8\r\nocShC7ClHolWHrx3/smJsb18VY+AGFBL2KymHLz6faC53b6wMT6SP0RD/jRC\r\ncA/dqFab9IYQi5DQaf8UzJylbX0S54MB4nOg23JRgtMqQHT541Dcg8jS3Kwc\r\nrnbp7HftGd0WmBzmHSpFAl5hkmdkbJOAs9HIhLY5qDax7UDO4pUmjajpEzEm\r\n7J+fyyqwcR+93MwUG/st3fNUKmIHfZV6HhJJMM3WI9c6adr56ndqzejydBUR\r\n94DNDWjzi/rvMsUPtxcEcdcdEEGq8+gqay30F0jePTzS7BS+Uks+rrH4VC07\r\nb9Wyw08bFQ2xNaaDzceyJR433alQqTZgvaIO29z+ObBtp4Q9cxJ49LIASohd\r\nn6L5tqnC+OXiG/a9nXuSK8HISWEHYgOJPaWlWzac+61WAmyUA0wPWjPWsVwm\r\niQD3GgIIPgs8aCnuqBxdLPToxuX+BBM38M0=\r\n=cy5g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "301442bfef8343c0a937aafd16a4e84c2a4d5e06", "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.11.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_3.0.1_1660249239435_0.9316007034478895", "host": "s3://npm-registry-packages"}}, "2.9.5": {"name": "create-vite", "version": "2.9.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@2.9.5", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "55743272060dcc6d7b74daeb2e04b37e071729d0", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-2.9.5.tgz", "fileCount": 131, "integrity": "sha512-XdthyVUsyNrPO/h8zXj1wzZ5tr2eb5DXwdsIQpNTARfwtGfsqrkEeA5wTd30GNlJL2xRQyn+su3f7hQQSG3cDg==", "signatures": [{"sig": "MEUCIAKJE0UXbgyvmKicrpqNDNo5m8giGEbnuPA1vXxkSIoPAiEA7m0vTdautbPbtY8RrGJW/+YIz4p3O96pC4ebMssxAj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115500, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9k/pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqByRAAi053/cIogEGxVzviK4sLduxQzqBByVUKwB1cizm20gnca3fK\r\nDmgYGwSwnWjXA6dHSv/OB52L26SoXD/qKlvg73IwV18QSfq9U5r2S14hrWKp\r\nO84MdG3b2j7+xRIHmg4WjHq550ndZl+e9i6NBg0NI+wKBCsY9PdA/HPn1M6J\r\nEx5vwGclIkTGmZfo2wCa/z6GNuF1pQv8q0nGefkqInhmEGbylGieLksHqb6w\r\nLv77GsJlAu7g2kPwBiDodghho69Ujjq38hm/2NcI1dESGh5GxGF/a/TOEi9W\r\nsvoCl9/WBWMYNqrTXpzHQcM2IVzYfazUi9JDdvXvOsWuWm7UZtmr2wrgv1S7\r\nszWzzo1FuyHuzkgtWxJPD/2sCbkEtab4HawPka6IdUDIsKptRAXlu5y2LOEX\r\nvD8lm5xQqKFOqJLs/Tj6i3vHwtFHLLF1H0YWHRe+3brx34r2Be10dap2hO1o\r\nixyrSaV1eBVpy0HHHJhUHh44TmwE1dAgW1C03qarS4Lu/lOHJH7ByoC+OxY5\r\nb+kmQRSS+rRY+UhHIZxR1h2DUuCCxBpWl9vYWv5WDRLJSlSGbK/meKPdITRx\r\nl7j9LHjrlHLyFJ38oyds0OAHiNd07BpQoQZ3z34HGaUoPQsYp+nybCYGcJhG\r\nh5ou+0F8XkGsS0DmPS2MYYyTXZgMEqp8eFg=\r\n=DBGE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=12.0.0"}, "gitHead": "4f00f5835618a772a7ba599ba5e5f2a1fc10ef11", "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.11.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_2.9.5_1660309480524_0.9115897591241677", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "create-vite", "version": "3.0.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@3.0.2", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "5a0fed9910f879b6d159391803ba6a5101b8e074", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-3.0.2.tgz", "fileCount": 145, "integrity": "sha512-WP+HAiS5OYqmW/aqUR3Woo+lwCRGqlFzu3r3wmd6OiBf6Vjt/Xtj+QUMxbuJ77qroHurOVAVgER8LB7ADBT3AA==", "signatures": [{"sig": "MEUCIEHAHNB926ESaMTqt1R5T5Cn4nEYK0VtHnI5+WPEReDUAiEAwP7/MJCc0XOyH3Ripd85EdoeoAn5SfaCDnkhHZCFua0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9ml0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJiA/7BP9ik4+WvcT4faB3jHsf348/xmZRKemwwthGU2BRiOiiD8dW\r\nZJBkn9eLghw5rGGw4Ps7cSnN+it5hBbOTmjpT/DNc87rFPPCGfh65z5EXLY9\r\nTCSLnvBcJdHU6v6Ii70gyy7YOeV1FBthA3nghR9F3ot2VfJxQgAZ4d1FyuBb\r\n7Kh+xpPHcB27Kn5wSX7N6pu4havUpHo/5qCOsPmi1VbawhymJL4ICCYvo9sm\r\n+wrKmy7PGBczwS8kIVviCLleUQAmzMUDI5zrBDMZq8CcsPgFf8L0HQogalLO\r\nyPXzphrClbwATvIbriw93Ms8hEQukVZfABTO0bOV+hvOACvqQGJJQHlvHDfc\r\n6b7KIlU5OqlJJ4zp+0tk27Tt1L86fagWo2/4uziVZzctbos7f6bHdnh0qa3y\r\nJMOxWBvm8mxkMhUBTjnOyY63X293BpeMjQIUbr4QObG17skL9kApqR7KvOw9\r\nm64tPFv2E8lW3cQzQCdWHgg+2LlLvaLFwhBApLGwQWEdU9lyfAhFTtSPzR1Z\r\n5C6NONv1dQvJZwzpWVsRsU086R7c/vqVdcf5bJvjbhJpdRCjbux4JvbA/Uuc\r\nX5QYoswgyHf3CnhYsc9+zN7TKSde/FuH8w/cB/Un3BBcxVMNfiDg9kjfGQYU\r\nLzqw+IaZ0KL+LNC2p8iqfEIcX4cX+8UK0E8=\r\n=YzpG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "c261fe62838575147236ff46b4eee927302de0b1", "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.11.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.6"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/create-vite_3.0.2_1660316020141_0.4870333570227914", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "create-vite", "version": "3.1.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@3.1.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "467f7bb5e56deeac89265e49cf4d64aa4e801d88", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-3.1.0.tgz", "fileCount": 146, "integrity": "sha512-nmuYUAem3FDpwLR7/3CzERigDSdLUgq0YxucFggKoG+FbKmXU7p6ujPKe4XEPNxNb7288i9oqatlhKvm30dnTQ==", "signatures": [{"sig": "MEYCIQCY7cqY5+o4N/mW+ahwN0WC3SuQAXROitBdHpfgt/sLdgIhAOGXZBojw/Bfu3xH35BK6kwnzsdLpKJQP4RSK/y6S2uP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFcghACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpT0BAAjEd4oejmcAKkUq6jrQAXiu1BGDaj8lfFaTt4TJfhdHUK3ZHt\r\nUKs8p3aepg9HwjanezzD6JPufqmW9omEn0hxCTy3snyxzcfxrruYAV80ZSNj\r\njm6sdmWcViCg0mCiihKRzLr7UqkuPvk9kGWEXl9+Dyaw4k8U0p2yob+8wZi4\r\nawBSak3mc2F3098ZpEDoHs76AluurbvGNwDy5f82AxiV/XlBx+CJopv4vUJ9\r\ndZSunqpm7qcgCyTr+XQmzIJsW13DhZdGNYB27Hy6sv+ee7LdTe0hu/LjsDxD\r\nHOQFkkAMt5W1Hyx3G0FhWyI7c/K4ALy6HD1sk+MvHVusZo6DNb4pcT/rJstC\r\n5qGjrHcKL6l4E2vRIPDaTdujZKFJt1SwxalVljJ4rj19iNZ+L8h8eci+Rdr4\r\nDT+kHFzOI+LJ2/Zw3wJydQpj9HmIDOlCTMA8EzgCWVKOaAtpOWv1oXMNou+v\r\nBb4Whm16wgYbUeSQ/uID4SsldPZutMXlH+EAIN4OeNAdAMzoDL5FdpRRoQxK\r\n1pAgKTXsx2NeUfFnfgwuqDOmnmlPoRCEcHRQ4qltT0elEJgW7nUNhJiueswf\r\n8/vOY1B11jxUkUUa0hLR+34eS/89wdf+R5TwAIeqqZLEcWSDqFeYt5frFgPx\r\nnJTSrP0WoxAcngjSvVgsrv9pMxQ70DxIEJ8=\r\n=3rIs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "3a843d96c9ca0137803aadc6f866401550106295", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.15.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.5.1", "minimist": "^1.2.6", "cross-spawn": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_3.1.0_1662371872919_0.13659190145727473", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "create-vite", "version": "3.2.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@3.2.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "d0ea3951ae3b8f65f3c512135dbc96bfa636be4c", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-3.2.0.tgz", "fileCount": 145, "integrity": "sha512-hOjpR2J5WkVI7wCoVJn0+2M7z7Rblix4QPpoV2chxSpOlDy0Urc+XumhR+ImNvXcfjW2yU776xWDnJjOFnYqeQ==", "signatures": [{"sig": "MEYCIQCKjCdvxZJ+TFFe1wYqXAF8uGV95ZwDwWtY255PPcIrbgIhANKettDGBJr3zsnGZM/soQnlFqVMx2AL9ckxcR2sJsZB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWTR/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqW2A//VhD/nFVUqOzhagYrRTZomugku5MCenisgFgZ4itZGlQPgeVI\r\nQZ8XQjWSHU8PeUPZAo7jqgb9bk1k8JTUEQ1M6qae++gMFCRkul/zxNBn4j+f\r\npuCOAsPRJ19jffw0KClKFrL0HqXp3K6dusqS1Od3x9D/LfMkKbKtiwF6zIrN\r\nqrbySWnOIEILnwoT+G7ODpwWPUazGcxw0OfZ52UJ7uhMOczuOX+PBeCKlb4h\r\nQc2twxcLWECXO+LCGHPrJqNlzF0K9q7X8gQz7p9WYdMbjOyWF/zNrATzoC9b\r\nonBA28Y+4WmInnlywb8O9JCiOP67SV+V+9OCKIv/Ty+A6yoSFIDeLIq9PWE5\r\nexdLD4Ud05bYGLZrJsqZl0L6OgAGLRo174LV2uelwYn8OF2WJhK6PLj9WqqT\r\n8S1I99f7D+E5vPJNP/m8XWMXJoC3YNtAeYE7loTS+KS642LGjnlY8y3qpykk\r\n/hhDpmkd7IJ3jxyLUi/1jho27Ure/iI4X5Nr7IivVzv+JQF4lvMB3oq1oHAn\r\n4XRnPAENTfizaJb5pAOEOfoXBbId0Va8KHgNCCeJztts6iYqZfb+k5ClbzOM\r\nmtRoq2gb2Fm6strYrwZNet0a+vWlhBs2q841sFDmi/LSutqC1wj4ZnO8CtpG\r\n83VyLlS5cyvEnP6MSajbGdD9t/vocul0f3U=\r\n=wJkb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "a285da800733545d9b320e2817985d8c3bace802", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.15.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.17.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.6.0", "minimist": "^1.2.7", "cross-spawn": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_3.2.0_1666790527052_0.2160426437595213", "host": "s3://npm-registry-packages"}}, "3.2.1": {"name": "create-vite", "version": "3.2.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@3.2.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "3e23dc09f1e7a3aa9b0c2f4922d1ff398b453046", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-3.2.1.tgz", "fileCount": 145, "integrity": "sha512-/VGNmHzr1NhOIrd4XM4wqsdLc7C2zdtihOo9sZPKu/bY9XqWbwTG5IxevJ/z8P9w4RrqpwaHY09WH0ZPBUPCXQ==", "signatures": [{"sig": "MEUCIQDFx8iQrEIT3XkgtnV8w7Rl044Xl4aKcr1MsiOHIpXsiAIgLrcF2SSMh1YsUUSFcmN2SQbILd8uJEXfIoijFHr0HHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaMKqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQZw/8C39kfFdGwcbtZo863CBtiyNkRwPf0oy8DPFAGqjunm9aYoGa\r\nvQQo6/HDrO8rLoIWxnuKffkUPwaOTKtRlIEKyXX7FlXG8IeanFW13BGHdzbQ\r\nSS2rULEbY948Cl98w1+w8+o0hZIMUiO2f5jBsHZh938AU2vBB9JwwBsztqDZ\r\n7SfW75XCuAtPRJqLGv23MHsyfo//Rtz1RaKaUXZqE92FtjVd2z0/Ujo0SK/p\r\nVRcYsDIu4MFqBCDw2qEPkmcD+lekidW3NBoWPWKy6epeDSgvPTFqLBhBo4i+\r\nxSkLF/+phG1EG4aKXyf2kgM23PfL3W+dbDc5gzN0zT2AbRQ3sGL5TxGq8mFe\r\nb//eoSNMS9nkEDw5jtmhWXmzfYeo2VDd5VUj3cYmhxvLn/TmCNJ2qv8ZNZ22\r\nmqhyMENTCVMg+7RUwsJxn6z68Bz8xFvkbN/oNZiELo9XJy7FJCZoIKJAwUUr\r\nD+tQAqQUk4S9t38puJ/wjPxpj0k289N/gCd6a4CZ4lyDXW46FWUOBOcgOZp6\r\nCa8AzJ1ws4+yp5XoZ/iQU93vP9K7W/4KdsnJZePRoPmnmxZJDEYqV404J0wF\r\nBiSmZQIxe8VBeRICZKGngImGfwVjEt4eZ1Sm6GRNHeL4dWNzMOI9CwMZDiyu\r\nwhVfKYTwxS58sLzmvOP5eos1lJZKjK1x8cI=\r\n=p7Na\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "d9f6dc0ecfc0f6a1c394c8645f474cb32a9e447a", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.18.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.6.0", "minimist": "^1.2.7", "cross-spawn": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_3.2.1_1667809961874_0.9083889614036886", "host": "s3://npm-registry-packages"}}, "4.0.0-beta.0": {"name": "create-vite", "version": "4.0.0-beta.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.0.0-beta.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "c5a793204091469eabfde1f90a6a6f2f886e310f", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.0.0-beta.0.tgz", "fileCount": 145, "integrity": "sha512-TkCEAbMxWoR6l+zNv43fjgubK5jI2BLqyXCCKeJtxsrRGIncSBRPXNZbqQgSz5UOHz6ukATLDBzVUn2GuMBRgg==", "signatures": [{"sig": "MEQCIFxE2kGV8Eyjb99M/IL/G9aTxxvovG3SjY+yNm8XFTj/AiAytwB2WE+qNWjMktZbRft/8Af9oCKrCrkfgnW9xOjayg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkPHpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvVg/+M6eSGM+qSQmcAMP9gnHNgjZoLrqXTJOm5+5B3uKQddLVn6UH\r\nn6uRcrI1JygazojNAYrT3F+2ZbnDxIK81JP5Q2KP2O2agvTS94VUT6o6AA+z\r\nBcmOexcmNitBpe7710Bm2kx5VyiWidkZkth4UTLrJJ3b3kP5wzFVA9xeMmIL\r\nLRwzcqiGVWuB2TbdbTeUQJ+aDOMA9nGFRNcJS+Kn5Hvo0GU1IofY6CVD4D8X\r\ndLuStwQqf6lqatTlueP1Z/bBbZ4L1I+nn3MrBMdhOS/b099T/NXe69/TZFf2\r\nhznsMkDKrXMbbj0yZwgU4M/Go61TUmB/Gkvwp7iCmIXieDNiQZUWpmy9SUkd\r\n8/rsOn0hTaQORaRVAKcvB5ituBXj8mhSVvLYmh6E3s7j5g+EJbEZ+Dt3fbFA\r\ncn8A31C5GAbRUhT2QtN+knmLiaf8Sd200D6iKGZbkHCFQXFRX/w43PLHNh3q\r\n0+fXr+p6osr/weeEXHnhXWyBNC117dzDxJCdReIqW8V++egQYcvD29/GFMiW\r\ntLZfGd3pr/ojkO+aDnQcw8qh6KZocqYytGC+S0J6o0wIDiiUHgSGF+vp+leU\r\nYRJ/chFDnSnWbqxbSM5LELkPOo/Nw4SbFIUCOyDram+ZKBtdV2yQwKND4biO\r\nuKWlhLpW7cW2W+XqLbf9fpJZ1H6UkIhnd0g=\r\n=rIOa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "4efb188c7c0dec58d6560090ab7ad2ac6363b3f3", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.6.0", "minimist": "^1.2.7", "cross-spawn": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.0.0-beta.0_1670443497261_0.46321161440975755", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "create-vite", "version": "4.0.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.0.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "e6303fa92c513cc232aabaf2d318dda1a1860a77", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.0.0.tgz", "fileCount": 145, "integrity": "sha512-U3YwGDXbFBQd+kUbkxGVeoYotoGpkNxl1TG6nBotG+rWdub7w8YJgK7pSnaOorJieFqMe/XvdDxzUKHLQnVv/g==", "signatures": [{"sig": "MEYCIQD2rfn/ZYGmdVtwoStWsrZFJBvqC++6wgqXZJaik5ZAfgIhAKFo3OnZ4iOGKyoxPLUORGCjqD0J33R5xwRgSa8Pe8D+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkzdVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgTg//ZFLkMA3u6iZzhMe1t5DC9EyF9nP9bWXQiv0icjPl7qxC2s5E\r\n2lHqWUssEzH40TTbUdW26dUjKVOy1UuIj2w31QnZcde/CkIh/hS4FEiro7/q\r\nqb4dUe+YdmpkPQS5WD55yPVwXibT2W+cha2kXrijuRdA8EO7/nFrZKFnXOLp\r\ns0Ij2zpFsDB9edeovnpna2etYLOMXU5gX4mhaDzR8u6PJwicUvXGrt8RNYqd\r\nwlER0Ss950Tyg+LVUl0DgqKjz+ve4dTrQu5IP+fi28n+qL+8I3pT0Kp6ncfS\r\nprc61lSBRsXdfdSVJTRdS3ojBpOdz9zzMK9yXsy6RwsoPyeGk8m72fcQxh1j\r\netn5iy9Q3+8PfcLLPSvlM4Usn3fJfM3y1S9EC3Of9gFcHG2aR3u2sltmlD9g\r\n7Y7UWLmwTUTDf+iQSrfrXpN/5yInGO14gvoR/X1p1nW1KZOmQvPozFCtN3FP\r\nEKqXpEtTsGdZZ/OkK/9GgQyaqfR3qkXuIbHolRAf1EHk0dQf26uVOgpY8wtk\r\njZU+2A54a1rzfW9nCBf1hPAWymzLNzb2WLqVZyhvBr4pe+FmJLvg9GaAe/ws\r\nAlWu3j+Cspe+KiA+XZgb/oiJ2fCm1YxI5e9s/6fLK0d2578b2o09eeW1Imf/\r\nOEtOJ26914ndk677f9xjddEB+cNzYQQ9+Hs=\r\n=0lb7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "d435db4f7a3cc924c0fdb451dc23860c7d70ce06", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.6.0", "minimist": "^1.2.7", "cross-spawn": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.0.0_1670592341021_0.6673175272118486", "host": "s3://npm-registry-packages"}}, "4.1.0-beta.0": {"name": "create-vite", "version": "4.1.0-beta.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.1.0-beta.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "698ee790e0fb6e576e63d470388db1eb5f252be9", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.1.0-beta.0.tgz", "fileCount": 146, "integrity": "sha512-YPxSYqp8HIG1E8YnGAXGBCJsr5Nx2vfsxR8nVKYr9d2pcXPc7zsnr32k0qCNG3TE+3TQyS1ChvjDoOOhKYn5tQ==", "signatures": [{"sig": "MEUCIEVpzmyZyEr3CnWgIqVBqT9CFZWLb5pc/zfi4d6KC6bmAiEAtICCLZ/MMg7ZPWFVqKme0MxgFYIHdyfW3scOdJ1TqBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0l2YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrenw//acOXxweI97T0VwkPI89qff1sPl5ruTe2BKlipN7o02lVKfp8\r\nhgOHyv71tzjKdRjgiusGpMNYADa84kIeFs3Lm1KCIKTQfBq+fQycqA+oYgzr\r\nM3C+xYYJp7VMAzOsea2/wB92VGCxrHZ/5DFiLt98s8ZWk39Dn6agJA+ujR50\r\ntlkRTd/CO0bdKVhwM8ydRlb3G8LAV6dqU48yq3suQo4ZvDPDkVUvTaiF79Mv\r\njS/dmCarXrp3lahToRoh5YFNrhr3lODHi+PFCYNuM1NpDcUoAqd5oLj1ALUQ\r\n6rQGbOJcwIRNkKqmP0Yz8XLVv0aP3LVtB/UdbhtenMfgwcgerGL8mskWcf/1\r\nGWS7ZLnMauBf+q7bsiFPivuZbNg1kzzegpf7KDllAE31h5W2FUcB6Ea5r511\r\nORR9v/rN6WsXMD49EIPJ6KvE7VsyhmNkY/nRFn9ROQwCvq6IaH/irgmg1K3C\r\nKCnw5oA3uY//JNVCq0Xs5I5aqwHeltoumsGMFf0iUtqjJl4vaXK+PdG05kTr\r\n1omIXruIYQCmPyWlIK+rC1htDF/z560yNlttLz6uGL9rJ2Q+xBbRVF3EPKbf\r\n7FbtIXMTTQ8lv0r0YevxESccp7uA8fadHRdUerZsXN/nt1VVOMJ8pAcEkNpN\r\nPFpNPVWpbNaJFfkdoG6CoHNImpa4v8vySls=\r\n=/ZbJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "d8581d8fdc9a90c43acb7f14376ac2fe46c0004e", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.3", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.6.0", "minimist": "^1.2.7", "cross-spawn": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.1.0-beta.0_1674730904337_0.08989716885962551", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "create-vite", "version": "4.1.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.1.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "107931dd181c8caa32ae32152367ac76c6d9d7ae", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.1.0.tgz", "fileCount": 146, "integrity": "sha512-IQAkwLJcp/6fQUnB2CBxTnsT5xfaHDUTCUi/LLWZTrzfrhVvtoKv9eHuXoG+XzyOyNgqUPJi9aNzwTXqSHw8Vg==", "signatures": [{"sig": "MEYCIQDaU037uCWqyQdOZjEhTXzBwrBvq/zF7WfRiJKmqdiVqAIhAO/VC5niMtTZJfKMZNpyUUp0kyxJTNxE9uVmWV1IV6Nq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj26W9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4xA//WcLD1RfudsUsv5xtTcoQdq5hU6pA01gRAubaxztOwOLU0hrx\r\nV6ZWq7TpWgXASwqxg6UQ8WRBxYmh+TJs+0Agvcp7Gre0VVx8nUsokWMHxbxu\r\nrG+3xR/O/S4Z5gfvZhScPsNpI5FSKdA+Q7s/RDeCKV3OVuNnvc9AcBuwOmLS\r\nyiNFF4soXh0PojE+fR6AGPnEuxEnLW/WUxdXZKrfgZooNm7JMk43vEBm/7IL\r\nkwklxOzVkb7VNFmT2mYh2JmFUyUjJeSvv5f/KUGn9/giwm1J7JoqZ3jpUrxL\r\njGT2nUdIGUjYah4J7yOV8wpEumlWxKKPpgwY/e8nAiFJ/4o4sKpI4c/7/kB5\r\nyQ02gBTHAsrdlJA0VMtSu5ldrN39nmZLQhYrTI3tR8qWd4M9Bi+dHhKCtKGh\r\njfKWulxmUSLSAZ9OZPjSDy6/9ZJwCZf0Jmw3tiZN79kKIlMD+iBKr66jI27F\r\n8qvw/tps7g/vyfF/Q+NAlWbvhVV2QS84TUo7KGSNkBW/JYI69Y0bGnrvdvhJ\r\n70lL8g16p21GXau/LFOE4iNCvTmGneimgblWhQE8n1rqe7IfVN/1hu/SxQmi\r\niF/vOZMVGsnsXZJbYDH1th9QSkLoFfJjoTD685NRseJf8Lrlv+NGESzJ89Kx\r\nY3m+mHXSxP878Dg2y9inkG6Tp/S1Ocn9dAI=\r\n=+gQf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "c53bfeb7227e430b092dc7dbbd887e2d703c725a", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.3", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.19.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.6.0", "minimist": "^1.2.7", "cross-spawn": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.1.0_1675339197408_0.2805472442689556", "host": "s3://npm-registry-packages"}}, "4.2.0-beta.0": {"name": "create-vite", "version": "4.2.0-beta.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.2.0-beta.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "60cd539d19fbc9db3c8f9b3ce08f95cc3643f285", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.2.0-beta.0.tgz", "fileCount": 146, "integrity": "sha512-hLPGXuGH/eSFoPV2wVZtvvqN5zxG5TED7cc8mo7Oj5Pc/3nx2cLIxhnW3azhUQ/okEW0grBE4fJsxbFP6XM0rw==", "signatures": [{"sig": "MEQCIEe19dcxh05Y3zelN8yH+wTfy4FzoSHPT1sGE6l3Gdc3AiBYRvqZmc+X4CpJy0X0eWWToNnC/QrwqLKKvQGlTlu66A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzwkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqbrw/8DZ6+CIvbrf6Xb7xEbj0JqwufwW15kLoiIthPvKl0G/gtLqdw\r\nxLrfrYxeL5Tv4nQAgDu53ehprt53UqQgNtw+iJtcg80RsTUdrNBnSYLo/0SS\r\nkj2lsvrDzKre402ZOP9gkHc3cfx2ydo9pdEy7l80guVwDAwWJsmvHCdXo8+O\r\n9DDGwJ3Z/ZjYHzQheRUIHq2K81Kv4eVVgi/yzXhtFHAcTpJh5t8CuYKI2xx8\r\nXY8jaePIHvRWn9t3uROt8bKhGkbKMhKOJ4xh77jS//3vzuiDatVsiaVg6mgA\r\n0Rdfe7u0Oc/EdyCFvNojX9nUjgXITng23WnHFN1teNYf4vkTVjCwemosply9\r\nUBnYG9ZYnRRcriytMwBot6JHGhpKzWEb8ni2D4hht4tqMQxU2eGnINLgyoAT\r\nhBLf1KFpZOqrckNJ3hZ5rlN2CpRx/4L2KFyrRaGkowRvu2UOfFM0Fu/2vAJY\r\nJplK7RAxE25C3KE+ZneOhVZFcnOezH3KdnG41zsvPkb20svRgDlTKjWKKd/R\r\ndWYHzyYUxCmpmvEamkYRpisaJDJ+lmZmvvfd6gMCpftBdcfwn+iIx66lkXSN\r\nXa83R+44yVq/Cbj4I4o1/+7hzXMFHhNsDOPPKU/8rlEafM1MdJR100M6HpfC\r\n/Lk8LD8Acfhhk12Rn9qjek8vWo985wl1D5M=\r\n=RBpc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "07579ed529ad257af4904942dfc38990761503e9", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.3", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.7.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.2.0-beta.0_1678195747833_0.899179512897545", "host": "s3://npm-registry-packages"}}, "4.2.0-beta.1": {"name": "create-vite", "version": "4.2.0-beta.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.2.0-beta.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "acd46788a70748a95b5df96cd3e3f83448f1a9aa", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.2.0-beta.1.tgz", "fileCount": 146, "integrity": "sha512-p6LNqS38toz75Uk7sytBvXKCghpCeIz6KNWDDr66OvAcgzTSRWVDII8Pwyh9mKX325lGJqZ2q6IQH0yVNu0//w==", "signatures": [{"sig": "MEUCIE03RR0T1ucLy9E+CODouYJLjBpJrIYoOkaDPxD0mQS2AiEA4FnV2cMutvLMpK/+Do1uuG5ukxIXX+oc4UkQjmhQNPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBz8dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrytA/8DVUM5bjrfOj2gL+hfPsOJT461dsQ5HGslrOuNPMrOWKmvve8\r\nWSVVgsAQI1TPKTIJX7+yEfckHIXdDiAh3Wx6N7aCgnHUHEZ0MFfrnLe5EH66\r\nouKeSRLP0Z0C+63QkIB4HjcgtdS190wtyjYTvsUHq82QNtQ22ZHZn5W4qZxw\r\n2uN+luFnircH3FFKJu1TKf9o1R0j1bghG4jM2dh28+6sXWGAue7kliV9ZhO4\r\n3+X4M7ZbvjN9FlWvBzoh2Ta+8J8ome3mb8ewtXhOU8p5f6+AXOgQrBrsW4zZ\r\nWZt7h9YTszqQqGi6Ld+Z9w6rf1M/ijr88RNv4UESmzNROYPcC+KhJPjNdXcf\r\nUXAQwpqbLxvizZPhKZujLmqWRQkdh/y91h5zhzcVZ2DOPMd4TpZyKwX5XxaU\r\nTu2mdf7wLI/y7sMe3ZrY6OBPIevuvTqgaLq5dgz8EIK8vLfp2lk/jqB9zKvb\r\nzzzdslomEleSIXr/0PzcTeQlNEe7i3CRr9EcHsSBneTvX1t2fCMgSbnGZa87\r\nb5AoeePSSaMHvtChtn/tRooLN4nj2v1SCa0isoyNbgSkekrSZfNlY6sDlMrR\r\nGdkR1xhAS0/qTTRLd0Vb6v1LakMSsrW9mgFHxo5KAM+doZ3umHM67KA1Z5rQ\r\nk9m8uDzEg0abltojGNI88E7rPfHyDxAe8pU=\r\n=+PdE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "057ff9573ef787b2770742d560daf46cf7188f0e", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.3", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.19.1", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.7.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.2.0-beta.1_1678196508814_0.55483060297538", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "create-vite", "version": "4.2.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.2.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "77277ad82979879214a2dad4eba58e34bbaf31b9", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.2.0.tgz", "fileCount": 146, "integrity": "sha512-nQ/24h33RFu+SRzYAcxVnkE/+SZsQTaaN5uTrnigetFUQkDy3n8Gf2m8lqgMprR/LBq8C7zEhYNdKQiulmdcNg==", "signatures": [{"sig": "MEQCIBFDuZ2Z/0RnT83ElPXe+CsuWZLBWq1mjKGL02ZxCTWsAiA1gv03RzXIscU0XkOrINUe4G3wQCUT55ZPPbcS5zHIvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEt6wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptrBAAgHVLrzibbGDl6y7L7hUsA35I51q3XgAqifEyzEtL2ODMfOek\r\n1DprjZ/QAZSTmlBTKB1BZO/Hx/bQ9Qvnvz56Enh5SDS0THSeo/luONat3RlA\r\n7fSDlrH0NI79sGtzAcQYtSBZZR+Npgon7aHrCK0YjWGR7x+l2umIazAtN3QY\r\nXiEmnWvFQhND0wCFRhmEBLX+X9u8VDUvZwvhmUi4zCfEH/+LJgZ1N1lUCC2x\r\nHS2sxpJG0kkgKgbLNeRHoCfVZEdbC9fXCPfaJ3WtzsQaEO32bqvMewtwlP4T\r\nW2i6X0Mk8wyT8gOgRu487zujmPp4w3RNARD9ISALIKZgKjRqkrl5RzYJjtmt\r\nh+XExPj2sdmy9/xCaqWwTL6vqdaNfSQp/tErnwaXKgHtO3S4NH7FuGli1zwv\r\nSAN/cPiqxbi57yYjhVWNkAFtvXumThWl9Gwweq6MeSVay8EA9arEiZgnRaN9\r\nSXuz6A03CU9CkOpDiiQ61eq+c5GHNZrbN6KW1dHCY4/VTKz+LtwD9JiVpKSH\r\ntX0YPnFil76UsBgVz4gFH1Ho7MQq68ra4E3wGWtF8QSPRpZBBe7AL420CuzS\r\ntuAofl/Bz7k7pJ6iC+bg6zLqhJEhgKv28KCuDq4z7BKGNhTtwEy3IE+O/QSI\r\ncjkOjlqwWIC/LLVPwMspRj+7+7+BYcXAeT8=\r\n=Qq6y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "f0d697b5ed07549470971f9ad8a046333a75ed3c", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.3", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.19.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.7.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.2.0_1678958256210_0.45876708304369074", "host": "s3://npm-registry-packages"}}, "4.3.0-beta.0": {"name": "create-vite", "version": "4.3.0-beta.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.3.0-beta.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "a1b5d7cb22e9a41519c5c3242908bc44d16a41b0", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.3.0-beta.0.tgz", "fileCount": 143, "integrity": "sha512-Cnhy4FsfdW3gOoZ44ul87zAGJ4XmIcWDupe2Klbdueu5NixRSIvFfNSx2+1mjFK6V0b5OfUw+QC05ULpIvVIqg==", "signatures": [{"sig": "MEYCIQCp2HjY8YE9dVPzMZLeprywy/UTOHOID1+2L4k4B9mi8AIhAM4PbtECDVY+3Oob8GT62bVtZzyqq2PR84IPGqciSCFD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLu4GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqh/g//XrkQi+p9cKn3sO3p0S7HjBocd66S1XJcXNCLnb5nswpbB5iU\r\nBmB+SVZycdnYjmA7Zo+p5nzaR54Eg7zSFm4XnEI+D4TZC8wF/tsr7ylRDBHn\r\niTYQaD3dLSbqCPcygnTDYwTG26eshUOsHPkPsXoZg2BRP27ykDeLubbfO/1G\r\n6E0k5GI+sv7A7pZ2R87w0XWwPJuRHH2Eg6/1y92lzEUcAisbNjqYdNmjncMI\r\nrTaLIXnDra0yw4xIfPZbh+le4k4k+xZ7W7KbsAOlWz6XBR0QXbN7JaWWyN0u\r\nhUAHhOyJjQ1ZtWRYPA4qI4E3oQ25nLluXrunrk6dZMKQm9QEgdma89oXrQrh\r\nX5m9if5GWPteeLYE3e8pwrYc+ZGL7idoD8TC3Uxn9fGFzLKjKdDkh+TxLIQG\r\n2qqJs7mnL8X4kGJk0UMVy84Gu60w3qLG67ClwEshPIKS5SGoDerOBYhQvde4\r\nV8WKLBpsoW4bosVVbttpnP+MzIXEiidhSSwEnUuQOIA6pYoGTrO0638HwhfB\r\nr1xwES4P7vdEoyDtfls9l2Irj3vJQg26NPoFJ0QlhhBg/cTpe0eYRSPU/o8j\r\nftm0YjHDr1uf1uYeeEEaZYyfLRsPB0U6/tPiTdUu8VbWugmg0uKzy4ysvB/C\r\nEYxMqxF9fI7mCEgsRc7eAJ8F8DLDOrkcEaA=\r\n=3bUa\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "2fdec3ca220913fc0dfba88181d597089435fa2c", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.4", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.7.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.4", "@types/minimist": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.3.0-beta.0_1680797190522_0.050562962379384624", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "create-vite", "version": "4.3.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.3.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "b95cd0b5eb85a4fa5fa52cdd74cee829747fd409", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.3.0.tgz", "fileCount": 145, "integrity": "sha512-60aJ8oXgbvNGbMZBFz09jGKDHfAL6v34UygM3puNCiOSI3vjLU2U/rgNBG9CW4aN0N+5GMhSzQ6VVRMY/wgiug==", "signatures": [{"sig": "MEQCIEBuTPt/ztB9eUV+Hxpwbcfn4dY2TREcu4nyWV5h7IEWAiAfxN2zu8inuRnlGULIqQtSwIVTPTfRVSYPuhG60lHubQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQR0iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkLg//T6948Bm1jja4HiIA1blUChyEhvpFUwgtyQZz7j/p+ubyTN49\r\nsKKDi8mUdHtl7KkBE3iqvesvjC7jcOoOBbaH5D8+y50y+fa5SOMXjt9TDyp9\r\njXI/qBaJJj/z3tow03x8zk5QPTLCdVIUXPCGv5Yr3JE9aUJIqMJ7+c9kj6AO\r\n+UTC2KNQBxGvj7/Qo3vxpnOxLmvdnb1uUprMmaH8PGPzWYKiFKVPbI8yVRYu\r\n8ogXv9BSIQ7pIZZh3J4+8gX9n5ymKnOTtQa/JlPr79NP80B9GHwt4gqFy3zr\r\nm+5B7/jZt16SoVLaxeaw7dbHDVKKn4fJJTkmNiVf3q5gylJ+uiBGfgZxvjL3\r\n/kHoIjg00SgNl4TiFUaSsKLu1+3t9QKtszbgRwBaCeuqC8qRrclYYmTYBJjX\r\nXXw7x45e9uD4tDZrZIiO4DlcdtyGdo5dhzTtZp52BcwIwOar69qpi7lsGoAN\r\njNL5m8ST8JCYkpbEe7+xytLVVtwnNwNLtiFTzsInN9epFqEcc7rZcKLPt3fq\r\nTTUTs0PNW3CLrHUcmfC0qICIxZnrEYwDXs0cWroyGYygXq/nL2O1ZulefZbQ\r\nPtcsyN6LoAHVnuuwNJnBfj7grLsT+43nvgDwJxRXdN3vBCuu0WaJSSJWxlV/\r\nvXbyrLBBfKVHMEhJbLmEiG9koAXK0uNTOK0=\r\n=ACzs\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "658f4d03cd142dca35253bcb44bc610090eadaeb", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.4", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.7.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.4", "@types/minimist": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.3.0_1681988898059_0.27521011277478524", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "create-vite", "version": "4.3.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.3.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "422666f64bfa32f77bcce0474861a6b147348b1f", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.3.1.tgz", "fileCount": 145, "integrity": "sha512-bQ2dSjnk+aLNJKGMeec1bU5PB5YRmdpZ6HaqbmfOXiiyfh3TaiYPUIRX+pqCVaaR8J5Rgfv9bjjCXRzRzqmxNw==", "signatures": [{"sig": "MEYCIQD8Fl0GBTPW27YcRQGvlZ+HP2t4bCw4gD9fDR8VZyrz/AIhANglBbQcp9kcYMsxA5EyEmtQMmjpx4161WVlA0rXtP2k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR9p2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxjQ/9H61qWmWT1GlOkRtsv0GvVDsTB+8IUZZ9DX8jYp68TNKs/pYU\r\nS8WDo7BJzBaVSGEYmBa61D3D8ebWNGEJLIwAzYJ1vBfnOK2yzIdvuByNLvon\r\nnjqTGk+31Y6RtaHjVu8trhoc5/SWMwdSAYfWmZovHUA6Ai6VwWV398Uzcu/d\r\nXEwoYy0KLIhTObekOK7jPXsvsg/UE9dXBJmimZB7g82YcJC3oyE3EIvEucnv\r\n9j8uLGy+/t6QloKkyq3/5upz70meavuWluwWhePw0BGrT7EuD4+FgPiDatbX\r\nj2vmr5UmonOXam6hkvIbsGiJ+oyW1KHwsE4/IC9g0MGsoA1oEv861/aE1tBG\r\nZnYk/iWDGAuaWXyxyjzuowGrG11ZTGbpDofZ7P2qofQkctO7TiGaf+oWJBjo\r\nKbwvKt2UfVmXZ81Jm07PNfytIzhDL1uQ/PwMhCTSjY6AO3aOW6qQNberZI5v\r\nQZOhY8nWjdTWW8uP0h9PJ/EXQnCIE7iL1akGbV/BShmOhoBr4NnlZe6pLpRY\r\n281PCJ8DtaZQ2JvapM77QabjAKHxh3+4eHeTX4S2ubkxJb0AFXgtd+w3ktcb\r\nChA8v9QIh1hFHpubae+h8TJ7LP4blfB1tKYpNyDphJlQhPfZmJ1KjZUUP/21\r\nemWEROe+PLHGyi0H7rC/4g4CGH/wxHT5Cdo=\r\n=u5qU\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "061e48b0a35d94b9f976c28e4d92154a340000bd", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.4", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.7.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.4", "@types/minimist": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.3.1_1682430582691_0.06953996472134549", "host": "s3://npm-registry-packages"}}, "4.3.2": {"name": "create-vite", "version": "4.3.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.3.2", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "c4abb0a29611e51af6abc2fdc3479d1cfc8ce489", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.3.2.tgz", "fileCount": 145, "integrity": "sha512-06SE3okEJD9r792cY1ZvtNVZgiVx8+mrx4Vfph8+GTVjwStAc4noSEvBLy9CJaeAVhpKBWBbc3Wzl1gUoScVvA==", "signatures": [{"sig": "MEQCIAq5+W2LTUtNwD47kva1t0f2viDqvw9b6NlQHAFC0qpQAiB4xN6sIDVrxPJyiuOjWfqdcdB1s4gRNxBMSpVHFNoSYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173450}, "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "gitHead": "5c3fa057f10b1adea4e28f58f69bf6b636eac4aa", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.4", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.7.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.4", "@types/minimist": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.3.2_1685352552681_0.9902519085567603", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "create-vite", "version": "4.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.4.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "d7dde9c4252ec948e444f217ed222f0745bcd3d8", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.4.0.tgz", "fileCount": 196, "integrity": "sha512-xheOoE/5pCF06rKTPLozLQbzUGQ0n+rutlhwStHTS4ruGgUYTtMCajrfGCCHSd0otKOn20aIU7mpK14Sgw/s3g==", "signatures": [{"sig": "MEUCIQDSri1bshg0aYZydHSv563GNpU6eCWJtU3DshUVzL4MwgIgbCWTup9c+nHisGzl6iUoDOCo0jr/E9gEROgIf/VipIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206152}, "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "gitHead": "d47108d5167736d2a1d97ae9cab72fa2a72cf701", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.4", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.4", "@types/minimist": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.4.0_1688635302095_0.5602733278073198", "host": "s3://npm-registry-packages"}}, "4.4.1": {"name": "create-vite", "version": "4.4.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@4.4.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "7d29a6ec53491914d3b176a7facfecba1b2659fd", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-4.4.1.tgz", "fileCount": 198, "integrity": "sha512-VbE5lc4aO7gterlwTDPxfnEhp/e0xAJui7NydmQiLpKq2yBLTQGcfMXeZeyoIH+E/Di2MQpE4o0K3lZNjlFL+A==", "signatures": [{"sig": "MEUCIQCjs55/rMtDptJ2gIXHl9UvpxoNFg+9zuAzlK0H6GbGGAIgbEaz6dgZglE8xkXNDIqeTqOQyCSEva1uGO9kpeohWaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207666}, "type": "module", "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "gitHead": "d991d7d43e657d66f9aca7fb72f7be79b9e08699", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit", "prepublishOnly": "npm run build"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "8.19.4", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "16.20.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.4", "@types/minimist": "^1.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_4.4.1_1689863642431_0.6559446771554815", "host": "s3://npm-registry-packages"}}, "5.0.0-beta.0": {"name": "create-vite", "version": "5.0.0-beta.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.0.0-beta.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "abc575879a5ca3b6b65ef08e75e1f3f96f0c8d5d", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.0.0-beta.0.tgz", "fileCount": 198, "integrity": "sha512-XHH+Bbp3Sm1PnN+3jOpGmLAm5KABtlC5bOE1WXhl3gwnw4iiA7IQs67tbO0FF1LAdwN9kV4gx8o9WbDn0ujnaA==", "signatures": [{"sig": "MEYCIQCdMn4zIiyKLzkOtfMYW+qOpA3ncDzdOQWMUAv6np8IUwIhANSNR+Uo2TW6t8r6WsdMAFMT9PT4HPUwozqLxuv5oaEL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207525}, "type": "module", "_from": "file:create-vite-5.0.0-beta.0.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/fm/9t92n8sd3kq850phpw310w0m0000gn/T/33261f351a4b959778414b8d96ff442d/create-vite-5.0.0-beta.0.tgz", "_integrity": "sha512-XHH+Bbp3Sm1PnN+3jOpGmLAm5KABtlC5bOE1WXhl3gwnw4iiA7IQs67tbO0FF1LAdwN9kV4gx8o9WbDn0ujnaA==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "9.5.1", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.5", "@types/minimist": "^1.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.0.0-beta.0_1696429252916_0.3740673356529236", "host": "s3://npm-registry-packages"}}, "5.0.0-beta.1": {"name": "create-vite", "version": "5.0.0-beta.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.0.0-beta.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "7d68deb3dcbb55476b5325957b0141d1e1e22d9e", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.0.0-beta.1.tgz", "fileCount": 198, "integrity": "sha512-GklgDSvOb8XrXRtCK2dkG0muj5oG2Yi7CP99VdA0P6fZ9lKiv/0h9QLyiKbElYM2JR68T5xpaboFbQl+1mRZLg==", "signatures": [{"sig": "MEQCIB/OBjCH09jJi5uZhugjzmNts9lTrsArpVvN5GAR9RMiAiASqYC49mmPpK3RWnxECd6M8192yzUCRatsuXHN8Oft8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207562}, "type": "module", "_from": "file:create-vite-5.0.0-beta.1.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "patak", "email": "<EMAIL>"}, "_resolved": "/private/var/folders/fm/9t92n8sd3kq850phpw310w0m0000gn/T/f71691743aaf95f15dc9825a533a1e49/create-vite-5.0.0-beta.1.tgz", "_integrity": "sha512-GklgDSvOb8XrXRtCK2dkG0muj5oG2Yi7CP99VdA0P6fZ9lKiv/0h9QLyiKbElYM2JR68T5xpaboFbQl+1mRZLg==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "9.5.1", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.6", "@types/minimist": "^1.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.0.0-beta.1_1697711996030_0.17620816664514027", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "create-vite", "version": "5.0.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.0.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "f67a5048d1da15891904b8ed6e3fdce42c635bb0", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.0.0.tgz", "fileCount": 198, "integrity": "sha512-whEVgshjPziiihkjvrhZVdxZ+KLrDi2OoGHjjPjavmkUfM1OJrwH1AtTsIDzL6tPcuWiNE9kO6GnTROzMGtIMw==", "signatures": [{"sig": "MEYCIQDYwPBdkcF6lqMplGIViNMB88JarBTxERuNWgxkAb0piQIhALH+ifQauaei5Rfsmnlh4YaQndnayRbJT7Nf5hw3w6/Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207312}, "type": "module", "_from": "file:create-vite-5.0.0.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/43ccb32d9fa74ece7e64c6293bd3c2d7/create-vite-5.0.0.tgz", "_integrity": "sha512-whEVgshjPziiihkjvrhZVdxZ+KLrDi2OoGHjjPjavmkUfM1OJrwH1AtTsIDzL6tPcuWiNE9kO6GnTROzMGtIMw==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "9.8.1", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.8", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.0.0_1700146786589_0.22286833728297428", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "create-vite", "version": "5.1.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.1.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "47b1bbc5fa03047de99ccc5debf4bbc7091c9a12", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.1.0.tgz", "fileCount": 198, "integrity": "sha512-sKEnpzdFcG3pPdYBdCFBeONg1+nBLkwUtH4wbva+MeU2bJXJTB+A7I+zAxc4Hauw8W38x9qbogYu49nzzbK7mg==", "signatures": [{"sig": "MEQCIDjK8/yBnEzSWXrfuqN6uF3at9RxDDfaqCxDHXvR5wnIAiAJr4njMTmBcEzPRPY4Zp83LoaJ7QcnnTYr1IfxACBclw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207481}, "type": "module", "_from": "file:create-vite-5.1.0.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/ad816e8272d20f979bc12ea218c992e6/create-vite-5.1.0.tgz", "_integrity": "sha512-sKEnpzdFcG3pPdYBdCFBeONg1+nBLkwUtH4wbva+MeU2bJXJTB+A7I+zAxc4Hauw8W38x9qbogYu49nzzbK7mg==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.2.3", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.1.0_1702375922176_0.43917433209202583", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "create-vite", "version": "5.2.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.2.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "9902529d7c14069222096ce06c4129637cc05f87", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.2.0.tgz", "fileCount": 198, "integrity": "sha512-gBWi01VTZSC317tBDr/gQY37wqvOoTg1q8hcTYzELpN6kvBk3OMiWWd94b0LmuR9FVPn4hSUZedm2n8UvfD2vA==", "signatures": [{"sig": "MEYCIQD+ibyq+caSbG0XYEJCfu341qzl9Vz+OFBPXv04F44z5AIhAJPxae/Bcg9pUq+tXlrvBfIMg7o/t9L2CU65gHvgXVCF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207681}, "type": "module", "_from": "file:create-vite-5.2.0.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/d6537f567855eb5712fe872c1942f7c7/create-vite-5.2.0.tgz", "_integrity": "sha512-gBWi01VTZSC317tBDr/gQY37wqvOoTg1q8hcTYzELpN6kvBk3OMiWWd94b0LmuR9FVPn4hSUZedm2n8UvfD2vA==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.2.3", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.2.0_1707388573770_0.33066858351619066", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "create-vite", "version": "5.2.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.2.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "a99e8d8f14ddffb8c3655187b82e88b45cbf6cca", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.2.1.tgz", "fileCount": 198, "integrity": "sha512-9/s/V7uMMrkTOE+DaH4cJFzjpcWHU1Y2p8zUMnidIe4SLyLfK0CPrDQ36ay3mDfuCZH4AcxqOOdL56alCrs1Pw==", "signatures": [{"sig": "MEUCIFSu0UGtnYmLbntZFSxETFBDhW/n9f92wHtzQmZT80YWAiEAthPmyJV9CmzGAXlzLm7N8sflOpZJo/+qczFi1HPAsNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207840}, "type": "module", "_from": "file:create-vite-5.2.1.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/8408fba756155cddddd96b758b164a9d/create-vite-5.2.1.tgz", "_integrity": "sha512-9/s/V7uMMrkTOE+DaH4cJFzjpcWHU1Y2p8zUMnidIe4SLyLfK0CPrDQ36ay3mDfuCZH4AcxqOOdL56alCrs1Pw==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.2.4", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.2.1_1708514162150_0.9060480158925124", "host": "s3://npm-registry-packages"}}, "5.2.2": {"name": "create-vite", "version": "5.2.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.2.2", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "9bb488f49a82ac8b1eaaad00d27ac34db76796e1", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.2.2.tgz", "fileCount": 198, "integrity": "sha512-xywU1m7GyRt4PYvM3zzhCdNyM/iEsojtli1DdI56uTaQo5GZf5xPD+U7j9C2z/bgpPtrZJLRUAi2S1eAs31ELA==", "signatures": [{"sig": "MEQCIADT+LUCcBrCQ6ZLUAAgG73P+76M3V9lkEpaOAiN5KYTAiB+Jf5n7BB9fB+rFlkJ3GCydIfgIUFmMcvWvxLjck5YEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 207841}, "type": "module", "_from": "file:create-vite-5.2.2.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/0f96f93cd4af54b69fa36dd3620647fc/create-vite-5.2.2.tgz", "_integrity": "sha512-xywU1m7GyRt4PYvM3zzhCdNyM/iEsojtli1DdI56uTaQo5GZf5xPD+U7j9C2z/bgpPtrZJLRUAi2S1eAs31ELA==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.2.4", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.2.2_1710167246872_0.8319871228489806", "host": "s3://npm-registry-packages"}}, "5.2.3": {"name": "create-vite", "version": "5.2.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.2.3", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "225d38cf93adebfaeb24b2a9037c29eaa85fb2e3", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.2.3.tgz", "fileCount": 198, "integrity": "sha512-fTIAW0foFjj0+oNzG0zs7VXMdQ/C9NcaLeoB/kl91HQukFKCXYMPzmllJAWX2lJTgunRRz4K14HBiXfyuX9xGg==", "signatures": [{"sig": "MEUCIGrUG0cscn+Zll7T5jMBP5VYA3YVALmP8HwjvBjy1TwtAiEA9uiX8ZIaVkWeD7Og+5WjktpLu8vxdCLjhdOKxch2X+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206748}, "type": "module", "_from": "file:create-vite-5.2.3.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/5ab9357ead51712644182ef160c6d94a/create-vite-5.2.3.tgz", "_integrity": "sha512-fTIAW0foFjj0+oNzG0zs7VXMdQ/C9NcaLeoB/kl91HQukFKCXYMPzmllJAWX2lJTgunRRz4K14HBiXfyuX9xGg==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.2.4", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.2.3_1710927589205_0.14627695755878878", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "create-vite", "version": "5.3.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.3.0", "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "260af83e7ca4dad77b5b74f6f8b5c3e897e3b454", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.3.0.tgz", "fileCount": 203, "integrity": "sha512-+ppU5+/JOI4L1K4Llr6+g96dqSB7pJBcwgWBgvdJviklLyxoAGVuaWoMQuRwMUUJ6NmEKtAvHBUzJ45m6Kxabg==", "signatures": [{"sig": "MEUCIEsND7TKTUdR6HP3phkWBLQp9B693oK/i623yFdjgMjpAiEA8++qUTGER0CJR2jFB53eSdt/8qOPpZVb78Bi5emtJBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 208969}, "type": "module", "_from": "file:create-vite-5.3.0.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/560bf7ceabfbab3fad9e1b49265d6a3a/create-vite-5.3.0.tgz", "_integrity": "sha512-+ppU5+/JOI4L1K4Llr6+g96dqSB7pJBcwgWBgvdJviklLyxoAGVuaWoMQuRwMUUJ6NmEKtAvHBUzJ45m6Kxabg==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.7.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.14.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.3.0_1718952582091_0.9874696523696735", "host": "s3://npm-registry-packages"}}, "5.4.0": {"name": "create-vite", "version": "5.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.4.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "783c4144e1e2cd6ffa8904b99ddf67a47d4f3bc9", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.4.0.tgz", "fileCount": 203, "integrity": "sha512-w/nY7fAQPbOCbbVNcyCGFTZvsxhlTTKMweEPekejI8FL8aGGsShI6trTQMypcsGEGYQUfnoMA9SA1EQhzZmK8A==", "signatures": [{"sig": "MEUCIQCAWQq7LXLySQJxobasrL+nDACVC4lCbHykYLQAObJf3AIgCRy5rvAekkVxF0owTGI/BRiSK1Hv1cQmdHMN+OIIbME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209673}, "type": "module", "_from": "file:create-vite-5.4.0.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/207d1dee3b63c1d9fd1ba1233faeb388/create-vite-5.4.0.tgz", "_integrity": "sha512-w/nY7fAQPbOCbbVNcyCGFTZvsxhlTTKMweEPekejI8FL8aGGsShI6trTQMypcsGEGYQUfnoMA9SA1EQhzZmK8A==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.7.0", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.15.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.4.0_1721121306891_0.9155615094634397", "host": "s3://npm-registry-packages"}}, "5.5.0": {"name": "create-vite", "version": "5.5.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.5.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "e6b9de27b86a89863fe248d3a4b40d48038d595a", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.5.0.tgz", "fileCount": 203, "integrity": "sha512-nOFT9IApMfSDN2C6XLC3iL5FV2HyL+ir3aI9cHelwOKLG6M1MHv+vMd08t4M4pcWnvqoQJ5bzsFDhNNrLtKEbg==", "signatures": [{"sig": "MEYCIQDOK9OZXOaBTCtrUHUlt3cNyv8J9fCUhkf4SsFei3W7rQIhAMqWTjInHWGplZDSzsM3Jp5W72B0r12GZPjcsxR1t8s2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210588}, "type": "module", "_from": "file:create-vite-5.5.0.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/9e2566c1d69817c35069b1ffe8ea9eb5/create-vite-5.5.0.tgz", "_integrity": "sha512-nOFT9IApMfSDN2C6XLC3iL5FV2HyL+ir3aI9cHelwOKLG6M1MHv+vMd08t4M4pcWnvqoQJ5bzsFDhNNrLtKEbg==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.1", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.5.0_1723044321046_0.7361332740872957", "host": "s3://npm-registry-packages"}}, "5.5.1": {"name": "create-vite", "version": "5.5.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.5.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "88878328eedbb00859bf115a1822359ec9bbec10", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.5.1.tgz", "fileCount": 203, "integrity": "sha512-+mHQ/4JYyDyY/7TmYIh1FYuGZdUQTrMl8JCyhiioVapAwpTrUyGMIP8qrcFfXUuYAk+DuiAbI/6xOhM4MS7rQQ==", "signatures": [{"sig": "MEUCIQDBs9/Zy5PRa8o6IYAmFjkXWrYQihu+Jko0hMBPKHY8DgIgXuNbIwN9noX81GWDbxPw4LXMrirrPqOlX70AljJkBq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210542}, "type": "module", "_from": "file:create-vite-5.5.1.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/ed3acfd896979ce9bb8240790cfb81cc/create-vite-5.5.1.tgz", "_integrity": "sha512-+mHQ/4JYyDyY/7TmYIh1FYuGZdUQTrMl8JCyhiioVapAwpTrUyGMIP8qrcFfXUuYAk+DuiAbI/6xOhM4MS7rQQ==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.1", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.5.1_1723047796204_0.8282550116700718", "host": "s3://npm-registry-packages"}}, "5.5.2": {"name": "create-vite", "version": "5.5.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.5.2", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "7e5119e338f0240986885fb5e1854e57afefa6c1", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.5.2.tgz", "fileCount": 203, "integrity": "sha512-glwkD5XS2NzNldnYfdyMHZjG15D0LkUvpzbPvwyLtNxiWjpfMvbS430saoBl7B1J3ldJpbtov7dkQVJN0jkn9Q==", "signatures": [{"sig": "MEUCIDA/IihQ9pdgI8LYAQHczSPe4onUpzYsUzFyuTUYMD3xAiEA1X+dRBhuQ/GEWmveuLcobch5zRes95Vx/H7Pc6/7B2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210589}, "type": "module", "_from": "file:create-vite-5.5.2.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/7b54b8490204fb11e548649d3fb7934d/create-vite-5.5.2.tgz", "_integrity": "sha512-glwkD5XS2NzNldnYfdyMHZjG15D0LkUvpzbPvwyLtNxiWjpfMvbS430saoBl7B1J3ldJpbtov7dkQVJN0jkn9Q==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.1", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.16.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "kolorist": "^1.8.0", "minimist": "^1.2.8", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.5.2_1723729405990_0.33595912072695455", "host": "s3://npm-registry-packages"}}, "5.5.3": {"name": "create-vite", "version": "5.5.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.5.3", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "e8e807eededb40fbdb255caabdb17c1099c261cb", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.5.3.tgz", "fileCount": 203, "integrity": "sha512-7ooDS8IsvxMd8axuA70makifVe9Oo5Pa/oTh8ykClKSYbpSbIAZF/pkitv3yWe8OKcJGyLWuta/touAsr4qang==", "signatures": [{"sig": "MEUCIQCaYT4j64R8g1Z09a0oW+7jJXyFj9twyefKglU+eqD9cQIgVepDjVyOI99q3VG/xVPy1AhgsCX2C52bNRC/L1f1uXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 211698}, "type": "module", "_from": "file:create-vite-5.5.3.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/08d82256444c92959133430f6e8004e3/create-vite-5.5.3.tgz", "_integrity": "sha512-7ooDS8IsvxMd8axuA70makifVe9Oo5Pa/oTh8ykClKSYbpSbIAZF/pkitv3yWe8OKcJGyLWuta/touAsr4qang==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.17.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "minimist": "^1.2.8", "picocolors": "^1.1.0", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.5.3_1728027252800_0.9625033803138674", "host": "s3://npm-registry-packages"}}, "5.5.4": {"name": "create-vite", "version": "5.5.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.5.4", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "c0228577afce22a152f9bd4a922008758f29e300", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.5.4.tgz", "fileCount": 203, "integrity": "sha512-r6tVDLWJpjCKoonv8F1LUAP6ksTS6J2raqBbsdnvth05ihJT74jBhl25PiEcrAQvuVqureOsYRCfI6Qwa+Lj4Q==", "signatures": [{"sig": "MEUCIQDEkAA1feAMvrGiu+d7ec2haI4qHUWeOej1bbwJWYFLWgIgTD9ny4dDUAq9NyzbCEUH1UWMCqNBCFdudN3h9TO0tjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 215725}, "type": "module", "_from": "file:create-vite-5.5.4.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/c387b8472be169160b4b5fc0a7e8d9f2/create-vite-5.5.4.tgz", "_integrity": "sha512-r6tVDLWJpjCKoonv8F1LUAP6ksTS6J2raqBbsdnvth05ihJT74jBhl25PiEcrAQvuVqureOsYRCfI6Qwa+Lj4Q==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.18.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^2.0.0", "minimist": "^1.2.8", "picocolors": "^1.1.1", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.5.4_1729668041000_0.7000054999420626", "host": "s3://npm-registry-packages"}}, "5.5.5": {"name": "create-vite", "version": "5.5.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@5.5.5", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "75cc5fe2c2c0a8a701c6b2e3a110cc2f419f2747", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-5.5.5.tgz", "fileCount": 203, "integrity": "sha512-xQaaMBBP7iTs9GrWj48y6WIMqn1l+83ao/BW1T32KkEzzi6kTEEAp0UI9EW/waCy7R4dGN6bRR86YQfPHmUsuQ==", "signatures": [{"sig": "MEUCIBOW704Y8U0I2zc9PSKpuCvjXw1Egxew2IWfqtAgHB6tAiEAzZ37m/sO8RyRgVLq9eWPvaX4jLt1FNZyCInuHDwsLvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218387}, "type": "module", "_from": "file:create-vite-5.5.5.tgz", "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/73dffd45afb1ecda1059f8e6f6198df1/create-vite-5.5.5.tgz", "_integrity": "sha512-xQaaMBBP7iTs9GrWj48y6WIMqn1l+83ao/BW1T32KkEzzi6kTEEAp0UI9EW/waCy7R4dGN6bRR86YQfPHmUsuQ==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.18.0", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^3.0.0-rc.11", "minimist": "^1.2.8", "picocolors": "^1.1.1", "cross-spawn": "^7.0.3", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_5.5.5_1730282190604_0.20718566020156226", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "create-vite", "version": "6.0.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.0.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "3b1daa95745be4f1b83ee3c856a0d88a43421e9b", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.0.0.tgz", "fileCount": 203, "integrity": "sha512-AuzUHDI9+g7eyj/gfuw2SAJMMM8EwkyyuJ7tneUYsfW9fPcuUeLWzUxCBYee+l9ssJ+6PmzXyt6Nr+2J74bSGw==", "signatures": [{"sig": "MEUCIGsOGnISVD3Af9tU4AE5S8riGZrN4BBvCcT2f0QvSV01AiEA2FaWf5KYgXIL6m6U6/WbY9Ede8Fbv36EG7nT4ISyM7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218396}, "type": "module", "_from": "file:create-vite-6.0.0.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/cc6aab9c4dd833341fe72541ffff1d19/create-vite-6.0.0.tgz", "_integrity": "sha512-AuzUHDI9+g7eyj/gfuw2SAJMMM8EwkyyuJ7tneUYsfW9fPcuUeLWzUxCBYee+l9ssJ+6PmzXyt6Nr+2J74bSGw==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^3.0.0-rc.11", "minimist": "^1.2.8", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.0.0_1732630871845_0.27794286707624694", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "create-vite", "version": "6.0.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.0.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "750d2b99073ca6e8b9439d6dfe9663c0f132a87b", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.0.1.tgz", "fileCount": 203, "integrity": "sha512-OO8rNsJ4M9kgdX5P/88JUPaSmOZBPr7K+3vU533GL5/i4ntIDfSJLpi3w4Kpu3mTeBQi7whMzXuEe9dRiIrrKA==", "signatures": [{"sig": "MEQCIB5XeYIfE4p+o+yISAGp4mrDJrpVoMPH3AiUHgpXw33BAiAogPjxpmEgOOjIkEQhOkXLIf5nP/3PRUlTjlAlALkNTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218420}, "type": "module", "_from": "file:create-vite-6.0.1.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/be52a143b3bc7ddfc0d3ffcd83226ba8/create-vite-6.0.1.tgz", "_integrity": "sha512-OO8rNsJ4M9kgdX5P/88JUPaSmOZBPr7K+3vU533GL5/i4ntIDfSJLpi3w4Kpu3mTeBQi7whMzXuEe9dRiIrrKA==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^3.0.0-rc.11", "minimist": "^1.2.8", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.0.1_1732683547049_0.9703678444040815", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "create-vite", "version": "6.1.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.1.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "2938aedaebe40a7fc3710bb8cfd932fedf4a4150", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.1.0.tgz", "fileCount": 204, "integrity": "sha512-PEwkB4QDnYI79Yq/1/y5ygaeR/GlVan+gumGc5a5S0Z8YOx6Ig18FG88zYN3pFDRUqlA7T218ZktPgD7fz8SnA==", "signatures": [{"sig": "MEYCIQDL7LYY+p1UKB/A6xMU81TuBpz6HwlXlX2VJ00JCovbaAIhAM2R79kL/ecYjFMAYdeOmR5C2TJtQBG+t0OzzFp+y2S5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218451}, "type": "module", "_from": "file:create-vite-6.1.0.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/6cee368964ba4df59a8530c24eb7ad52/create-vite-6.1.0.tgz", "_integrity": "sha512-PEwkB4QDnYI79Yq/1/y5ygaeR/GlVan+gumGc5a5S0Z8YOx6Ig18FG88zYN3pFDRUqlA7T218ZktPgD7fz8SnA==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^3.0.1", "minimist": "^1.2.8", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.1.0_1734602421475_0.9713088569585699", "host": "s3://npm-registry-packages-npm-production"}}, "6.1.1": {"name": "create-vite", "version": "6.1.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.1.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "49156a4f0c75866ca41123a809bd180434587871", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.1.1.tgz", "fileCount": 204, "integrity": "sha512-oHcCcikmJqWVwUCf3OhwRwnTHY062koziG7niDvMPBEXnie/ME5b22v/Z9PuyWhnM0Yy4iTXk0y9ZRMivNXhvQ==", "signatures": [{"sig": "MEUCIHyOy5ofu7cVarzvQRe/0oescqqxtvxVrQvqFzHhymUJAiEAvx3eapE11i/e2YpC8kmYFV7iHU6J9HqdhcjzNF5GrHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218431}, "type": "module", "_from": "file:create-vite-6.1.1.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/24afc7a344637bf4f0eb836ba2bdc74c/create-vite-6.1.1.tgz", "_integrity": "sha512-oHcCcikmJqWVwUCf3OhwRwnTHY062koziG7niDvMPBEXnie/ME5b22v/Z9PuyWhnM0Yy4iTXk0y9ZRMivNXhvQ==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.18.1", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^3.0.1", "minimist": "^1.2.8", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.1.1_1735005113277_0.7976909525943785", "host": "s3://npm-registry-packages-npm-production"}}, "6.2.0": {"name": "create-vite", "version": "6.2.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.2.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "67b123f62dd31149b3423b65f08a5aa9586db8da", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.2.0.tgz", "fileCount": 204, "integrity": "sha512-4Y1kNZcr/X/JiMipV6tAc8l+4HAHlxomS57jJq2KdZJZV2csP6ne2/I2BQqFopvizq0lZT80e/MqmdP/KBxrIA==", "signatures": [{"sig": "MEQCICQi31fjWUcX7owmDmZnQUvKxL/V7wvPbCxyBLZR2EfrAiA3RL05MNNmORtuLp5nJg6EMi0CEQjZvPp2T0dGFywbqg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 218575}, "type": "module", "_from": "file:create-vite-6.2.0.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/3fbd1b5c6cb70bbfb19bb290ba128ea9/create-vite-6.2.0.tgz", "_integrity": "sha512-4Y1kNZcr/X/JiMipV6tAc8l+4HAHlxomS57jJq2KdZJZV2csP6ne2/I2BQqFopvizq0lZT80e/MqmdP/KBxrIA==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^3.3.1", "minimist": "^1.2.8", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.2.0_1738763374621_0.24449170636719697", "host": "s3://npm-registry-packages-npm-production"}}, "6.2.1": {"name": "create-vite", "version": "6.2.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.2.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "0f57309358c97780890971671057fbff34d92b21", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.2.1.tgz", "fileCount": 204, "integrity": "sha512-Bjq4QfIRyLV7K+tnzc7Vn4k6Z0qrjgMkXh1d5YHd96ItLoFeJOh1rUHLd3zi4q/GM9MVBAlerok/XF1ow0+VIA==", "signatures": [{"sig": "MEYCIQCTk7EoPNR6u6Ux4yGj8/eWwTVev/gYRvkG8SQw/5rgNQIhAJCJ/+D6FgI8pLc/fNZfHrSS/o5DwHoPphtkDeg2WCMU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 218480}, "type": "module", "_from": "file:create-vite-6.2.1.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/5fa220a11e7374f5d615b92efbd651d4/create-vite-6.2.1.tgz", "_integrity": "sha512-Bjq4QfIRyLV7K+tnzc7Vn4k6Z0qrjgMkXh1d5YHd96ItLoFeJOh1rUHLd3zi4q/GM9MVBAlerok/XF1ow0+VIA==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "devDependencies": {"prompts": "^2.4.2", "unbuild": "^3.3.1", "minimist": "^1.2.8", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@types/prompts": "^2.4.9", "@types/minimist": "^1.2.5"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.2.1_1740452994445_0.5753930742102813", "host": "s3://npm-registry-packages-npm-production"}}, "6.3.0": {"name": "create-vite", "version": "6.3.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.3.0", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "7e35210937153bec17d198cebafbcb659035188b", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.3.0.tgz", "fileCount": 204, "integrity": "sha512-WjnSCipRrHSI34/2lRUvs898QBxLioUr+949brcL8pFOAlfp67nisvb6jonHXeM6rPCxmF+ymHGBAGM0e7YGvg==", "signatures": [{"sig": "MEQCIGtvToAMjbjyTnLKWCrPq4iI1irRLVskmp/n3AxF4AyUAiBboGKv6C6nCHgEZSm9/2zHNIWHZFUMiUzAxJqN5ENR4w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 210991}, "type": "module", "_from": "file:create-vite-6.3.0.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/a1a5faa2104fd3878e45c3ac0410d29e/create-vite-6.3.0.tgz", "_integrity": "sha512-WjnSCipRrHSI34/2lRUvs898QBxLioUr+949brcL8pFOAlfp67nisvb6jonHXeM6rPCxmF+ymHGBAGM0e7YGvg==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.18.2", "_hasShrinkwrap": false, "devDependencies": {"mri": "^1.2.0", "unbuild": "^3.3.1", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@clack/prompts": "^0.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.3.0_1740459728483_0.6054318480789722", "host": "s3://npm-registry-packages-npm-production"}}, "6.3.1": {"name": "create-vite", "version": "6.3.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.3.1", "maintainers": [{"name": "soda", "email": "<EMAIL>"}, {"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "4ed830c512c129ebde4e5750db2c447ccb7ab9ed", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.3.1.tgz", "fileCount": 204, "integrity": "sha512-kI3S6OgiBYnRlmhr4gNY76Fj62fMh+KukEd2PSAvMPdnY95rHhJCjCoUlt4BuHVNjfmRMbsFBBtO5dryFoSyUw==", "signatures": [{"sig": "MEQCIBSL0sKU55xKkOcCxuc4uHwNwJrcO9RIO1PIRzrTpmfDAiBICT0J5qD+08hg2Dyb+WU26kBwVwZV6VyPHR5Qgyz4OA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 211444}, "type": "module", "_from": "file:create-vite-6.3.1.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/b48a49d01e31fba0d5bb44340a4ee5f2/create-vite-6.3.1.tgz", "_integrity": "sha512-kI3S6OgiBYnRlmhr4gNY76Fj62fMh+KukEd2PSAvMPdnY95rHhJCjCoUlt4BuHVNjfmRMbsFBBtO5dryFoSyUw==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.18.3", "_hasShrinkwrap": false, "devDependencies": {"mri": "^1.2.0", "unbuild": "^3.3.1", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@clack/prompts": "^0.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.3.1_1740657821175_0.41526333242328395", "host": "s3://npm-registry-packages-npm-production"}}, "6.4.0": {"name": "create-vite", "version": "6.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.4.0", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "13594c9eacf297f875bfbf14e33f49dad273e540", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.4.0.tgz", "fileCount": 204, "integrity": "sha512-US7FmzAwXB0YpyAIymNXTvldzhcco2JvbffTgQ6Uts8IJKvvslIsnU+gDwciz0ZYQMycjjAaHuN4MVZJBZ/SgA==", "signatures": [{"sig": "MEYCIQDbMxnXQnOih/+HXpzYc9PKX2aJYuhDhQ7AP5CB5lxJRgIhAKHOKhCzpXM2AOMEAfQp3W8JVk4KTRQVq6rwFhXea63K", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/create-vite@6.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 211821}, "type": "module", "_from": "file:create-vite-6.4.0.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/0d8df925fe3c4df0711558d6cef0cfa5/create-vite-6.4.0.tgz", "_integrity": "sha512-US7FmzAwXB0YpyAIymNXTvldzhcco2JvbffTgQ6Uts8IJKvvslIsnU+gDwciz0ZYQMycjjAaHuN4MVZJBZ/SgA==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "devDependencies": {"mri": "^1.2.0", "unbuild": "^3.5.0", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@clack/prompts": "^0.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.4.0_1744790112073_0.2537556775292298", "host": "s3://npm-registry-packages-npm-production"}}, "6.4.1": {"name": "create-vite", "version": "6.4.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.4.1", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "cc5f5af6849ec0e3fd4179bbab35e9ce56f3deb8", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.4.1.tgz", "fileCount": 204, "integrity": "sha512-an7oLlpm4FFd8zkVk6ytazmfLwMVYYyoLeuzlCoGPBKCbS6d7obm6OyVXAKt58xCxYlhMzxM0z42HBmMGWME2g==", "signatures": [{"sig": "MEYCIQCk2CpzZymBfItW0Z1akmYKICRinn+fqiIy/h0HwF9nCAIhAJ0BMrWA4DjUkHxsUZJr0L7KZQzkynNHmjjovIZ2lK66", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/create-vite@6.4.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 211918}, "type": "module", "_from": "file:create-vite-6.4.1.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/b075a0a35de7fd3d4f1dce0cda7787fb/create-vite-6.4.1.tgz", "_integrity": "sha512-an7oLlpm4FFd8zkVk6ytazmfLwMVYYyoLeuzlCoGPBKCbS6d7obm6OyVXAKt58xCxYlhMzxM0z42HBmMGWME2g==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.19.0", "_hasShrinkwrap": false, "devDependencies": {"mri": "^1.2.0", "unbuild": "^3.5.0", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@clack/prompts": "^0.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.4.1_1744854874206_0.870434781529682", "host": "s3://npm-registry-packages-npm-production"}}, "6.5.0": {"name": "create-vite", "version": "6.5.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "create-vite@6.5.0", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bin": {"cva": "index.js", "create-vite": "index.js"}, "dist": {"shasum": "12c28bc0eb0a6a512588f7ae760e28c735db39fc", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-6.5.0.tgz", "fileCount": 204, "integrity": "sha512-2GsVyyXPMsXeIHR9m61ANZ0YJkUojUbgACdzIAlYoKgw/b5M5GJAfzuBD56uJ4q82NxrAekt0k4OTtH1s+rfcQ==", "signatures": [{"sig": "MEUCIQDu83aZwYjxra0JNPpr9rYiszcV31A91mdLX4FQx84B8QIgI0nBiORaH0Vy507BPJEC50CppmgZjaTEz/jhVkGsju8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/create-vite@6.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 212835}, "type": "module", "_from": "file:create-vite-6.5.0.tgz", "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": "https://github.com/vitejs/vite?sponsor=1", "scripts": {"dev": "unbuild --stub", "build": "unbuild", "typecheck": "tsc --noEmit"}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>"}, "_resolved": "/tmp/1bd370c6bac418e67b817244fdd201c9/create-vite-6.5.0.tgz", "_integrity": "sha512-2GsVyyXPMsXeIHR9m61ANZ0YJkUojUbgACdzIAlYoKgw/b5M5GJAfzuBD56uJ4q82NxrAekt0k4OTtH1s+rfcQ==", "repository": {"url": "git+https://github.com/vitejs/vite.git", "type": "git", "directory": "packages/create-vite"}, "_npmVersion": "10.8.2", "description": "## Scaffolding Your First Vite Project", "directories": {}, "_nodeVersion": "20.19.1", "_hasShrinkwrap": false, "devDependencies": {"mri": "^1.2.0", "unbuild": "^3.5.0", "picocolors": "^1.1.1", "cross-spawn": "^7.0.6", "@clack/prompts": "^0.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/create-vite_6.5.0_1746437850751_0.6871249847542942", "host": "s3://npm-registry-packages-npm-production"}}, "7.0.0": {"name": "create-vite", "version": "7.0.0", "type": "module", "license": "MIT", "author": {"name": "<PERSON>"}, "bin": {"create-vite": "index.js", "cva": "index.js"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/create-vite"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "funding": "https://github.com/vitejs/vite?sponsor=1", "devDependencies": {"@clack/prompts": "^0.11.0", "cross-spawn": "^7.0.6", "mri": "^1.2.0", "picocolors": "^1.1.1", "tsdown": "^0.12.8"}, "scripts": {"dev": "tsdown --watch", "build": "tsdown", "typecheck": "tsc"}, "_id": "create-vite@7.0.0", "description": "## Scaffolding Your First Vite Project", "_integrity": "sha512-ybKmTPtWP/w95c3ZC9QhxC2HjFvEcdeK26zlmeBkspjPytnFR7w/zqnj3CdayBY2Im/1qE764VgUpcwRwBCWaA==", "_resolved": "/tmp/f06603131e6ad108cf9344df1a9fcc3f/create-vite-7.0.0.tgz", "_from": "file:create-vite-7.0.0.tgz", "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-ybKmTPtWP/w95c3ZC9QhxC2HjFvEcdeK26zlmeBkspjPytnFR7w/zqnj3CdayBY2Im/1qE764VgUpcwRwBCWaA==", "shasum": "35b0e182c2eed2c4fef17f83d3cc9716584a3337", "tarball": "https://registry.npmjs.org/create-vite/-/create-vite-7.0.0.tgz", "fileCount": 204, "unpackedSize": 228561, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/create-vite@7.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCO+uTk36cmnndEhi20VqRtb076cTT7PKFXH5xjbk4uqwIgY/Iq2CBkiRV2S2PBO+7jhTmOOBiKvNPv/8b22eZmcts="}]}, "_npmUser": {"name": "vitebot", "email": "<EMAIL>", "actor": {"name": "vitebot", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/create-vite_7.0.0_1750761576931_0.3557835396309319"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-07-08T13:27:20.890Z", "modified": "2025-06-24T10:39:37.828Z", "0.0.0": "2020-07-08T13:27:21.025Z", "0.0.0-alpha.0": "2021-05-10T11:02:49.787Z", "2.5.0": "2021-07-12T07:40:42.160Z", "2.5.1": "2021-07-12T12:26:07.093Z", "2.5.2": "2021-07-20T11:54:05.768Z", "2.5.3": "2021-07-27T10:12:14.397Z", "2.5.4": "2021-08-03T11:28:11.480Z", "2.6.0": "2021-08-25T23:13:40.086Z", "2.6.1": "2021-08-31T11:49:24.739Z", "2.6.2": "2021-09-07T11:57:37.234Z", "2.6.3": "2021-09-21T19:27:27.264Z", "2.6.4": "2021-09-22T16:02:31.286Z", "2.6.5": "2021-09-29T13:32:10.896Z", "2.6.6": "2021-10-07T12:04:42.911Z", "2.7.0": "2021-12-07T09:08:15.853Z", "2.7.1": "2021-12-12T08:03:28.876Z", "2.7.2": "2021-12-13T10:42:56.623Z", "2.8.0": "2022-02-09T06:05:51.648Z", "2.9.0": "2022-03-30T13:16:51.622Z", "2.9.1": "2022-04-13T13:57:28.199Z", "2.9.2": "2022-04-19T08:24:53.494Z", "2.9.3": "2022-05-02T19:33:37.925Z", "2.9.4": "2022-05-11T09:16:17.118Z", "3.0.0": "2022-07-13T13:38:21.314Z", "3.0.1": "2022-08-11T20:20:39.647Z", "2.9.5": "2022-08-12T13:04:41.011Z", "3.0.2": "2022-08-12T14:53:40.315Z", "3.1.0": "2022-09-05T09:57:53.083Z", "3.2.0": "2022-10-26T13:22:07.277Z", "3.2.1": "2022-11-07T08:32:42.074Z", "4.0.0-beta.0": "2022-12-07T20:04:57.437Z", "4.0.0": "2022-12-09T13:25:41.247Z", "4.1.0-beta.0": "2023-01-26T11:01:44.590Z", "4.1.0": "2023-02-02T11:59:57.671Z", "4.2.0-beta.0": "2023-03-07T13:29:08.042Z", "4.2.0-beta.1": "2023-03-07T13:41:49.047Z", "4.2.0": "2023-03-16T09:17:36.384Z", "4.3.0-beta.0": "2023-04-06T16:06:30.789Z", "4.3.0": "2023-04-20T11:08:18.294Z", "4.3.1": "2023-04-25T13:49:42.895Z", "4.3.2": "2023-05-29T09:29:12.898Z", "4.4.0": "2023-07-06T09:21:42.376Z", "4.4.1": "2023-07-20T14:34:02.628Z", "5.0.0-beta.0": "2023-10-04T14:20:53.098Z", "5.0.0-beta.1": "2023-10-19T10:39:56.248Z", "5.0.0": "2023-11-16T14:59:46.809Z", "5.1.0": "2023-12-12T10:12:02.434Z", "5.2.0": "2024-02-08T10:36:13.886Z", "5.2.1": "2024-02-21T11:16:02.372Z", "5.2.2": "2024-03-11T14:27:27.035Z", "5.2.3": "2024-03-20T09:39:49.380Z", "5.3.0": "2024-06-21T06:49:42.317Z", "5.4.0": "2024-07-16T09:15:07.194Z", "5.5.0": "2024-08-07T15:25:21.305Z", "5.5.1": "2024-08-07T16:23:16.433Z", "5.5.2": "2024-08-15T13:43:26.189Z", "5.5.3": "2024-10-04T07:34:13.017Z", "5.5.4": "2024-10-23T07:20:41.276Z", "5.5.5": "2024-10-30T09:56:30.792Z", "6.0.0": "2024-11-26T14:21:12.090Z", "6.0.1": "2024-11-27T04:59:07.263Z", "6.1.0": "2024-12-19T10:00:21.685Z", "6.1.1": "2024-12-24T01:51:53.461Z", "6.2.0": "2025-02-05T13:49:34.863Z", "6.2.1": "2025-02-25T03:09:54.619Z", "6.3.0": "2025-02-25T05:02:08.659Z", "6.3.1": "2025-02-27T12:03:41.393Z", "6.4.0": "2025-04-16T07:55:12.290Z", "6.4.1": "2025-04-17T01:54:34.405Z", "6.5.0": "2025-05-05T09:37:30.939Z", "7.0.0": "2025-06-24T10:39:37.110Z"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/vitejs/vite/tree/main/packages/create-vite#readme", "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/create-vite"}, "description": "## Scaffolding Your First Vite Project", "maintainers": [{"name": "yyx990803", "email": "<EMAIL>"}, {"name": "patak", "email": "<EMAIL>"}, {"name": "ant<PERSON>", "email": "<EMAIL>"}, {"name": "vitebot", "email": "<EMAIL>"}], "readme": "# create-vite <a href=\"https://npmjs.com/package/create-vite\"><img src=\"https://img.shields.io/npm/v/create-vite\" alt=\"npm package\"></a>\n\n## Scaffolding Your First Vite Project\n\n> **Compatibility Note:**\n> Vite requires [Node.js](https://nodejs.org/en/) version 20.19+, 22.12+. However, some templates require a higher Node.js version to work, please upgrade if your package manager warns about it.\n\nWith NPM:\n\n```bash\nnpm create vite@latest\n```\n\nWith Yarn:\n\n```bash\nyarn create vite\n```\n\nWith PNPM:\n\n```bash\npnpm create vite\n```\n\nWith Bun:\n\n```bash\nbun create vite\n```\n\nThen follow the prompts!\n\nYou can also directly specify the project name and the template you want to use via additional command line options. For example, to scaffold a Vite + Vue project, run:\n\n```bash\n# npm 7+, extra double-dash is needed:\nnpm create vite@latest my-vue-app -- --template vue\n\n# yarn\nyarn create vite my-vue-app --template vue\n\n# pnpm\npnpm create vite my-vue-app --template vue\n\n# Bun\nbun create vite my-vue-app --template vue\n```\n\nCurrently supported template presets include:\n\n- `vanilla`\n- `vanilla-ts`\n- `vue`\n- `vue-ts`\n- `react`\n- `react-ts`\n- `react-swc`\n- `react-swc-ts`\n- `preact`\n- `preact-ts`\n- `lit`\n- `lit-ts`\n- `svelte`\n- `svelte-ts`\n- `solid`\n- `solid-ts`\n- `qwik`\n- `qwik-ts`\n\nYou can use `.` for the project name to scaffold in the current directory.\n\n## Community Templates\n\ncreate-vite is a tool to quickly start a project from a basic template for popular frameworks. Check out Awesome Vite for [community maintained templates](https://github.com/vitejs/awesome-vite#templates) that include other tools or target different frameworks. You can use a tool like [degit](https://github.com/Rich-Harris/degit) to scaffold your project with one of the templates.\n\n```bash\nnpx degit user/project my-project\ncd my-project\n\nnpm install\nnpm run dev\n```\n\nIf the project uses `main` as the default branch, suffix the project repo with `#main`\n\n```bash\nnpx degit user/project#main my-project\n```\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}