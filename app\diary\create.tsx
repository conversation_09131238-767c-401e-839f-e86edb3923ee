import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  SafeAreaView,
  Dimensions,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  ArrowLeft,
  Save,
  Camera,
  MapPin,
  Tag,
  X,
  Plus,
  Image as ImageIcon,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { router } from 'expo-router';
import { MoodOptions, WeatherOptions, CreateDiaryEntryInput } from '@/types/diary';
import { DiaryService } from '@/services/diaryService';
import * as ImagePicker from 'expo-image-picker';

const { width, height } = Dimensions.get('window');

function CreateDiaryContent() {
  const { isDark } = useTheme();
  const { user } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const [formData, setFormData] = useState<CreateDiaryEntryInput>({
    title: '',
    content: '',
    mood: 'happy',
    weather: 'sunny',
    location: '',
    tags: [],
    images: [],
    is_public: false,
  });
  const [newTag, setNewTag] = useState('');
  const [loading, setLoading] = useState(false);

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleSave = async () => {
    if (!user) return;
    
    if (!formData.title.trim() || !formData.content.trim()) {
      Alert.alert('提示', '请填写标题和内容');
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await DiaryService.createDiaryEntry(user.id, formData);
      
      if (error) {
        Alert.alert('错误', '保存失败，请重试');
      } else {
        Alert.alert('成功', '日记保存成功！', [
          { text: '确定', onPress: () => router.back() }
        ]);
      }
    } catch (error) {
      Alert.alert('错误', '保存失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleImagePicker = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('权限', '需要相册权限来选择图片');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      // 这里应该上传图片到服务器，现在先用本地URI
      const imageUri = result.assets[0].uri;
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, imageUri]
      }));
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleRemoveImage = (imageToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter(img => img !== imageToRemove)
    }));
  };

  const dynamicStyles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'space-between' as const,
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '700' as const,
      color: colors.text,
      flex: 1,
      textAlign: 'center' as const,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    saveButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      backgroundColor: colors.primary,
    },
    saveButtonText: {
      color: colors.background,
      fontWeight: '600' as const,
    },
    formContainer: {
      padding: 20,
    },
    inputGroup: {
      marginBottom: 20,
    },
    label: {
      fontSize: 16,
      fontWeight: '600' as const,
      color: colors.text,
      marginBottom: 8,
    },
    textInput: {
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 12,
      padding: 16,
      fontSize: 16,
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
    },
    textArea: {
      minHeight: 120,
      textAlignVertical: 'top' as const,
    },
    selectorContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 12,
    },
    selectorItem: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 20,
      backgroundColor: colors.surfaceSecondary,
      borderWidth: 1,
      borderColor: colors.border,
    },
    selectedItem: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    selectorText: {
      fontSize: 14,
      color: colors.text,
    },
    selectedText: {
      color: colors.background,
    },
    tagInputContainer: {
      flexDirection: 'row' as const,
      gap: 12,
    },
    tagInput: {
      flex: 1,
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 12,
      padding: 12,
      fontSize: 14,
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
    },
    addTagButton: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 12,
      backgroundColor: colors.primary,
      justifyContent: 'center' as const,
    },
    tagsContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 8,
      marginTop: 12,
    },
    tag: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      backgroundColor: colors.accent + '20',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      gap: 6,
    },
    tagText: {
      fontSize: 12,
      color: colors.accent,
      fontWeight: '600' as const,
    },
    imagesContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 12,
      marginTop: 12,
    },
    imageItem: {
      position: 'relative' as const,
    },
    image: {
      width: 80,
      height: 80,
      borderRadius: 12,
    },
    removeImageButton: {
      position: 'absolute' as const,
      top: -8,
      right: -8,
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: colors.error,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    addImageButton: {
      width: 80,
      height: 80,
      borderRadius: 12,
      backgroundColor: colors.surfaceSecondary,
      borderWidth: 2,
      borderColor: colors.border,
      borderStyle: 'dashed' as const,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
  };

  return (
    <View style={dynamicStyles.container}>
      <LinearGradient
        colors={[colors.backgroundSecondary, colors.backgroundTertiary]}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          {/* Header */}
          <Animated.View style={[dynamicStyles.header, { opacity: fadeAnim }]}>
            <TouchableOpacity
              style={dynamicStyles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={20} color={colors.text} />
            </TouchableOpacity>
            
            <Text style={dynamicStyles.headerTitle}>写日记</Text>
            
            <TouchableOpacity
              style={dynamicStyles.saveButton}
              onPress={handleSave}
              disabled={loading}
            >
              <Text style={dynamicStyles.saveButtonText}>
                {loading ? '保存中...' : '保存'}
              </Text>
            </TouchableOpacity>
          </Animated.View>

          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={dynamicStyles.formContainer}
            showsVerticalScrollIndicator={false}
          >
            <Animated.View style={{ opacity: fadeAnim }}>
              {/* 标题 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>标题</Text>
                <TextInput
                  style={dynamicStyles.textInput}
                  placeholder="给你的日记起个标题..."
                  placeholderTextColor={colors.textSecondary}
                  value={formData.title}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, title: text }))}
                />
              </View>

              {/* 内容 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>内容</Text>
                <TextInput
                  style={[dynamicStyles.textInput, dynamicStyles.textArea]}
                  placeholder="记录今天发生的事情..."
                  placeholderTextColor={colors.textSecondary}
                  value={formData.content}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, content: text }))}
                  multiline
                />
              </View>

              {/* 心情 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>心情</Text>
                <View style={dynamicStyles.selectorContainer}>
                  {Object.entries(MoodOptions).map(([key, mood]) => (
                    <TouchableOpacity
                      key={key}
                      style={[
                        dynamicStyles.selectorItem,
                        formData.mood === key && dynamicStyles.selectedItem,
                      ]}
                      onPress={() => setFormData(prev => ({ ...prev, mood: key as any }))}
                    >
                      <Text
                        style={[
                          dynamicStyles.selectorText,
                          formData.mood === key && dynamicStyles.selectedText,
                        ]}
                      >
                        {mood.emoji} {mood.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* 天气 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>天气</Text>
                <View style={dynamicStyles.selectorContainer}>
                  {Object.entries(WeatherOptions).map(([key, weather]) => (
                    <TouchableOpacity
                      key={key}
                      style={[
                        dynamicStyles.selectorItem,
                        formData.weather === key && dynamicStyles.selectedItem,
                      ]}
                      onPress={() => setFormData(prev => ({ ...prev, weather: key as any }))}
                    >
                      <Text
                        style={[
                          dynamicStyles.selectorText,
                          formData.weather === key && dynamicStyles.selectedText,
                        ]}
                      >
                        {weather.emoji} {weather.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* 地点 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>地点</Text>
                <TextInput
                  style={dynamicStyles.textInput}
                  placeholder="在哪里发生的？"
                  placeholderTextColor={colors.textSecondary}
                  value={formData.location}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, location: text }))}
                />
              </View>

              {/* 标签 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>标签</Text>
                <View style={dynamicStyles.tagInputContainer}>
                  <TextInput
                    style={dynamicStyles.tagInput}
                    placeholder="添加标签..."
                    placeholderTextColor={colors.textSecondary}
                    value={newTag}
                    onChangeText={setNewTag}
                    onSubmitEditing={handleAddTag}
                  />
                  <TouchableOpacity
                    style={dynamicStyles.addTagButton}
                    onPress={handleAddTag}
                  >
                    <Plus size={16} color={colors.background} />
                  </TouchableOpacity>
                </View>
                
                {formData.tags.length > 0 && (
                  <View style={dynamicStyles.tagsContainer}>
                    {formData.tags.map((tag, index) => (
                      <View key={index} style={dynamicStyles.tag}>
                        <Text style={dynamicStyles.tagText}>#{tag}</Text>
                        <TouchableOpacity onPress={() => handleRemoveTag(tag)}>
                          <X size={12} color={colors.accent} />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                )}
              </View>

              {/* 图片 */}
              <View style={dynamicStyles.inputGroup}>
                <Text style={dynamicStyles.label}>图片</Text>
                <View style={dynamicStyles.imagesContainer}>
                  {formData.images.map((image, index) => (
                    <View key={index} style={dynamicStyles.imageItem}>
                      <Image source={{ uri: image }} style={dynamicStyles.image} />
                      <TouchableOpacity
                        style={dynamicStyles.removeImageButton}
                        onPress={() => handleRemoveImage(image)}
                      >
                        <X size={12} color={colors.background} />
                      </TouchableOpacity>
                    </View>
                  ))}
                  <TouchableOpacity
                    style={dynamicStyles.addImageButton}
                    onPress={handleImagePicker}
                  >
                    <ImageIcon size={24} color={colors.textSecondary} />
                  </TouchableOpacity>
                </View>
              </View>
            </Animated.View>
          </ScrollView>
        </SafeAreaView>
      </LinearGradient>
    </View>
  );
}

export default function CreateDiaryScreen() {
  return (
    <ProtectedRoute>
      <CreateDiaryContent />
    </ProtectedRoute>
  );
}
