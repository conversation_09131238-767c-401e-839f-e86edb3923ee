import { supabase } from '@/lib/supabase-node';
/**
 * 检查数据库表结构并生成 SQL 修复语句
 */
async function checkDatabase() {
  console.log('🔍 检查数据库表结构...');

  try {
    // 检查 profiles 表
    await checkProfilesTable();
  } catch (error) {
    console.error('❌ 检查数据库失败:', error);
  }
}

async function checkProfilesTable() {
  console.log('\n📋 检查 profiles 表...');

  // 尝试获取一条记录来查看当前字段
  const { data, error } = await supabase.from('profiles').select('*').limit(1);

  if (error) {
    console.error('❌ 无法访问 profiles 表:', error.message);
    return;
  }

  const currentFields = data && data.length > 0 ? Object.keys(data[0]) : [];
  console.log('✅ 当前字段:', currentFields);

  // 定义期望的字段
  const expectedFields = [
    { name: 'id', type: 'UUID', required: true },
    { name: 'username', type: 'TEXT', required: true },
    { name: 'email', type: 'TEXT', required: true },
    { name: 'bio', type: 'TEXT', required: false },
    { name: 'location', type: 'TEXT', required: false },
    { name: 'avatar_url', type: 'TEXT', required: false },
    { name: 'created_at', type: 'TIMESTAMPTZ', required: true },
    { name: 'updated_at', type: 'TIMESTAMPTZ', required: true },
  ];

  console.log('\n📝 期望的字段:');
  expectedFields.forEach((field) => {
    const exists = currentFields.includes(field.name);
    const status = exists ? '✅' : '❌';
    const required = field.required ? '(必需)' : '(可选)';
    console.log(`   ${status} ${field.name} ${field.type} ${required}`);
  });

  // 生成缺失字段的 SQL
  const missingFields = expectedFields.filter(
    (field) => !currentFields.includes(field.name) && !field.required
  );

  if (missingFields.length > 0) {
    console.log('\n📝 需要添加的字段 SQL:');
    console.log('请在 Supabase Dashboard 的 SQL Editor 中执行以下语句:\n');

    missingFields.forEach((field) => {
      console.log(`-- 添加 ${field.name} 字段`);
      console.log(
        `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS ${field.name} ${field.type};`
      );

      // 添加注释
      const comments = {
        bio: '用户个人简介',
        location: '用户所在地',
        avatar_url: '用户头像URL',
      };

      if (comments[field.name as keyof typeof comments]) {
        console.log(
          `COMMENT ON COLUMN profiles.${field.name} IS '${
            comments[field.name as keyof typeof comments]
          }';`
        );
      }
      console.log('');
    });

    // 生成完整的 SQL 脚本
    console.log('📄 完整 SQL 脚本:');
    console.log('```sql');
    missingFields.forEach((field) => {
      console.log(
        `ALTER TABLE profiles ADD COLUMN IF NOT EXISTS ${field.name} ${field.type};`
      );
    });
    console.log('```\n');
  } else {
    console.log('\n✅ 所有字段都已存在！');
  }

  // 测试字段访问
  console.log('\n🧪 测试字段访问...');
  for (const field of expectedFields) {
    if (!field.required) {
      try {
        const { error: testError } = await supabase
          .from('profiles')
          .select(field.name)
          .limit(1);

        if (testError && testError.message.includes('does not exist')) {
          console.log(`❌ ${field.name}: 字段不存在`);
        } else if (testError) {
          console.log(`⚠️  ${field.name}: ${testError.message}`);
        } else {
          console.log(`✅ ${field.name}: 可访问`);
        }
      } catch (error) {
        console.log(`❌ ${field.name}: 测试失败`);
      }
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkDatabase();
}

export { checkDatabase };
