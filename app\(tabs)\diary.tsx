import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
  TextInput,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { BookOpen, Plus, Calendar, MapPin, Star, Leaf, Flower, Sparkles, Music, Crown, Clock, ChevronRight, CreditCard as Edit3, Camera, Save, X } from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

interface HerbEntry {
  id: string;
  name: string;
  scientificName: string;
  description: string;
  location: string;
  dateFound: string;
  properties: string[];
  rarity: 'common' | 'uncommon' | 'rare' | 'legendary';
  uses: string[];
  notes: string;
  image: string;
  illustration: string;
}

export default function HerbDiaryScreen() {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const [selectedEntry, setSelectedEntry] = useState<HerbEntry | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [newEntry, setNewEntry] = useState<Partial<HerbEntry>>({});

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const herbEntries: HerbEntry[] = [
    {
      id: '1',
      name: '香芹',
      scientificName: 'Petroselinum crispum',
      description: '一种常见但珍贵的草药，具有强大的治愈能力。叶片呈深绿色，散发着清新的香气。',
      location: '斯卡布罗集市东侧花园',
      dateFound: '2024年春季第3周',
      properties: ['治愈', '净化', '增强活力'],
      rarity: 'common',
      uses: ['制作治愈药剂', '烹饪调味', '驱邪仪式'],
      notes: '在晨露中采集效果最佳，避免在满月夜采摘。',
      image: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
      illustration: '🌿',
    },
    {
      id: '2',
      name: '百里香',
      scientificName: 'Thymus vulgaris',
      description: '神秘的紫色小花草药，传说中能够增强魔法抗性和精神力量。',
      location: '迷雾森林深处',
      dateFound: '2024年夏季第1周',
      properties: ['魔法抗性', '精神强化', '驱魔'],
      rarity: 'rare',
      uses: ['高级魔法药剂', '保护符制作', '冥想辅助'],
      notes: '只在特定的月相下才会显现真正的魔法属性。需要用银制工具采集。',
      image: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
      illustration: '🌸',
    },
    {
      id: '3',
      name: '月光草',
      scientificName: 'Lunaria mysticus',
      description: '极其稀有的银色草药，只在满月之夜才会发光。具有强大的魔法能量。',
      location: '古老废墟月光石旁',
      dateFound: '2024年秋季第2周',
      properties: ['魔法增幅', '时间感知', '预知能力'],
      rarity: 'legendary',
      uses: ['传说级药剂', '占卜仪式', '时空魔法'],
      notes: '极其珍贵，一生可能只能遇到一次。采集时需要特殊的月光仪式。',
      image: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
      illustration: '🌙',
    },
    {
      id: '4',
      name: '火焰花',
      scientificName: 'Ignis floralis',
      description: '炽热的红色花朵，触摸时会感到温暖。具有增强力量和勇气的特性。',
      location: '龙之巢穴入口',
      dateFound: '2024年夏季第4周',
      properties: ['力量增强', '勇气提升', '火焰抗性'],
      rarity: 'uncommon',
      uses: ['战士药剂', '勇气符咒', '火焰防护'],
      notes: '采集时需要耐火手套，最好在黄昏时分采摘。',
      image: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
      illustration: '🔥',
    },
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return '#4A7043';
      case 'uncommon': return '#D4A017';
      case 'rare': return '#8B4513';
      case 'legendary': return '#6B4E71';
      default: return '#B0B7A4';
    }
  };

  const getRarityText = (rarity: string) => {
    switch (rarity) {
      case 'common': return '常见';
      case 'uncommon': return '不常见';
      case 'rare': return '稀有';
      case 'legendary': return '传说';
      default: return '未知';
    }
  };

  const handleAddEntry = () => {
    setShowAddModal(true);
  };

  const handleSaveEntry = () => {
    console.log('Saving new entry:', newEntry);
    setShowAddModal(false);
    setNewEntry({});
  };

  return (
    <View style={styles.container}>
      <ImageBackground
        source={{ uri: 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' }}
        style={styles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['rgba(245, 232, 199, 0.95)', 'rgba(245, 232, 199, 0.9)', 'rgba(245, 232, 199, 0.85)']}
          style={styles.overlay}
        >
          <SafeAreaView style={styles.safeArea}>
            {/* Header with Bard */}
            <Animated.View 
              style={[
                styles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
                }
              ]}
            >
              <View style={styles.bardContainer}>
                <View style={styles.bardFrame}>
                  <Image
                    source={{ uri: 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                    style={styles.bardImage}
                    resizeMode="cover"
                  />
                  <View style={styles.campfireOverlay}>
                    <LinearGradient
                      colors={['rgba(212, 160, 23, 0.3)', 'rgba(212, 160, 23, 0.6)']}
                      style={styles.campfireGradient}
                    />
                  </View>
                  <View style={styles.sageWreath}>
                    <Text style={styles.wreathIcon}>🌿👑</Text>
                  </View>
                  <View style={styles.luteBadge}>
                    <Music size={16} color="#F5E8C7" />
                  </View>
                </View>
                <View style={styles.bardInfo}>
                  <Text style={styles.bardTitle}>草药吟游诗人</Text>
                  <Text style={styles.bardSubtitle}>记录自然的智慧</Text>
                </View>
              </View>

              <View style={styles.titleContainer}>
                <Text style={styles.mainTitle}>草药日记</Text>
                <View style={styles.titleUnderline} />
                <Text style={styles.subtitle}>探索与发现的珍贵记录</Text>
              </View>
            </Animated.View>

            {/* Campfire Market Elements */}
            <View style={styles.campfireElements}>
              <BlurView intensity={20} style={styles.campfireStall}>
                <Text style={styles.campfireIcon}>🔥</Text>
              </BlurView>
              <BlurView intensity={20} style={[styles.campfireStall, styles.stallRight]}>
                <Text style={styles.campfireIcon}>🏕️</Text>
              </BlurView>
            </View>

            <ScrollView 
              style={styles.scrollView} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.scrollContent}
            >
              {/* Add Entry Button */}
              <Animated.View 
                style={[
                  styles.addButtonContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={handleAddEntry}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={['#D4A017', '#E6B82A']}
                    style={styles.addButtonGradient}
                  >
                    <Plus size={20} color="#6B4E71" />
                    <Text style={styles.addButtonText}>添加记录</Text>
                  </LinearGradient>
                </TouchableOpacity>
              </Animated.View>

              {/* Herb Entries */}
              <Animated.View 
                style={[
                  styles.entriesContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                {herbEntries.map((entry, index) => (
                  <BlurView key={entry.id} intensity={30} style={[styles.entryCard, { borderColor: '#B0B7A4' }]}>
                    <TouchableOpacity 
                      style={styles.entryContent}
                      onPress={() => setSelectedEntry(entry)}
                      activeOpacity={0.8}
                    >
                      {/* Entry Header */}
                      <View style={styles.entryHeader}>
                        <View style={styles.entryLeft}>
                          <View style={styles.herbIllustration}>
                            <Text style={styles.illustrationIcon}>{entry.illustration}</Text>
                            <Image
                              source={{ uri: entry.image }}
                              style={styles.herbImage}
                              resizeMode="cover"
                            />
                          </View>
                          <View style={styles.entryInfo}>
                            <Text style={styles.herbName}>{entry.name}</Text>
                            <Text style={styles.scientificName}>{entry.scientificName}</Text>
                            <View style={styles.entryMetadata}>
                              <View style={[
                                styles.rarityBadge,
                                { backgroundColor: `${getRarityColor(entry.rarity)}20` }
                              ]}>
                                <Text style={[
                                  styles.rarityText,
                                  { color: getRarityColor(entry.rarity) }
                                ]}>
                                  {getRarityText(entry.rarity)}
                                </Text>
                              </View>
                              <View style={styles.dateContainer}>
                                <Calendar size={12} color="#8B7355" />
                                <Text style={styles.dateText}>{entry.dateFound}</Text>
                              </View>
                            </View>
                          </View>
                        </View>
                        <ChevronRight size={20} color="#8B7355" />
                      </View>

                      {/* Entry Description */}
                      <Text style={styles.entryDescription} numberOfLines={2}>
                        {entry.description}
                      </Text>

                      {/* Location */}
                      <View style={styles.locationContainer}>
                        <MapPin size={14} color="#4A7043" />
                        <Text style={styles.locationText}>{entry.location}</Text>
                      </View>

                      {/* Properties */}
                      <View style={styles.propertiesContainer}>
                        {entry.properties.slice(0, 3).map((property, idx) => (
                          <View key={idx} style={styles.propertyBadge}>
                            <Sparkles size={10} color="#6B4E71" />
                            <Text style={styles.propertyText}>{property}</Text>
                          </View>
                        ))}
                      </View>
                    </TouchableOpacity>
                  </BlurView>
                ))}
              </Animated.View>

              {/* Diary Stats */}
              <Animated.View 
                style={[
                  styles.statsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <BlurView intensity={30} style={styles.statsCard}>
                  <View style={styles.statsHeader}>
                    <BookOpen size={24} color="#D4A017" />
                    <Text style={styles.statsTitle}>日记统计</Text>
                  </View>
                  <View style={styles.statsGrid}>
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>{herbEntries.length}</Text>
                      <Text style={styles.statLabel}>总记录</Text>
                    </View>
                    <View style={styles.statDivider} />
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>
                        {herbEntries.filter(e => e.rarity === 'rare' || e.rarity === 'legendary').length}
                      </Text>
                      <Text style={styles.statLabel}>稀有发现</Text>
                    </View>
                    <View style={styles.statDivider} />
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>4</Text>
                      <Text style={styles.statLabel}>本月新增</Text>
                    </View>
                  </View>
                </BlurView>
              </Animated.View>
            </ScrollView>

            {/* Entry Detail Modal */}
            <Modal
              visible={selectedEntry !== null}
              animationType="slide"
              transparent={true}
              onRequestClose={() => setSelectedEntry(null)}
            >
              <View style={styles.modalOverlay}>
                <BlurView intensity={50} style={styles.modalContainer}>
                  <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
                    {selectedEntry && (
                      <>
                        <View style={styles.modalHeader}>
                          <View style={styles.modalTitleContainer}>
                            <Text style={styles.modalTitle}>{selectedEntry.name}</Text>
                            <Text style={styles.modalScientific}>{selectedEntry.scientificName}</Text>
                          </View>
                          <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => setSelectedEntry(null)}
                          >
                            <X size={24} color="#6B4E71" />
                          </TouchableOpacity>
                        </View>

                        <View style={styles.modalImageContainer}>
                          <Image
                            source={{ uri: selectedEntry.image }}
                            style={styles.modalImage}
                            resizeMode="cover"
                          />
                          <View style={styles.modalIllustration}>
                            <Text style={styles.modalIllustrationIcon}>{selectedEntry.illustration}</Text>
                          </View>
                        </View>

                        <Text style={styles.modalDescription}>{selectedEntry.description}</Text>

                        <View style={styles.modalSection}>
                          <Text style={styles.sectionTitle}>发现信息</Text>
                          <View style={styles.infoRow}>
                            <MapPin size={16} color="#4A7043" />
                            <Text style={styles.infoText}>{selectedEntry.location}</Text>
                          </View>
                          <View style={styles.infoRow}>
                            <Calendar size={16} color="#4A7043" />
                            <Text style={styles.infoText}>{selectedEntry.dateFound}</Text>
                          </View>
                        </View>

                        <View style={styles.modalSection}>
                          <Text style={styles.sectionTitle}>草药属性</Text>
                          <View style={styles.propertiesList}>
                            {selectedEntry.properties.map((property, idx) => (
                              <View key={idx} style={styles.propertyItem}>
                                <Star size={14} color="#D4A017" />
                                <Text style={styles.propertyItemText}>{property}</Text>
                              </View>
                            ))}
                          </View>
                        </View>

                        <View style={styles.modalSection}>
                          <Text style={styles.sectionTitle}>用途</Text>
                          <View style={styles.usesList}>
                            {selectedEntry.uses.map((use, idx) => (
                              <View key={idx} style={styles.useItem}>
                                <Leaf size={14} color="#4A7043" />
                                <Text style={styles.useItemText}>{use}</Text>
                              </View>
                            ))}
                          </View>
                        </View>

                        <View style={styles.modalSection}>
                          <Text style={styles.sectionTitle}>采集笔记</Text>
                          <Text style={styles.notesText}>{selectedEntry.notes}</Text>
                        </View>
                      </>
                    )}
                  </ScrollView>
                </BlurView>
              </View>
            </Modal>

            {/* Add Entry Modal */}
            <Modal
              visible={showAddModal}
              animationType="slide"
              transparent={true}
              onRequestClose={() => setShowAddModal(false)}
            >
              <View style={styles.modalOverlay}>
                <BlurView intensity={50} style={styles.modalContainer}>
                  <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
                    <View style={styles.modalHeader}>
                      <Text style={styles.modalTitle}>添加新记录</Text>
                      <TouchableOpacity
                        style={styles.closeButton}
                        onPress={() => setShowAddModal(false)}
                      >
                        <X size={24} color="#6B4E71" />
                      </TouchableOpacity>
                    </View>

                    <View style={styles.formContainer}>
                      <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>草药名称</Text>
                        <TextInput
                          style={styles.textInput}
                          placeholder="输入草药名称"
                          placeholderTextColor="#8B7355"
                          value={newEntry.name || ''}
                          onChangeText={(text) => setNewEntry({...newEntry, name: text})}
                        />
                      </View>

                      <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>学名</Text>
                        <TextInput
                          style={styles.textInput}
                          placeholder="输入学名"
                          placeholderTextColor="#8B7355"
                          value={newEntry.scientificName || ''}
                          onChangeText={(text) => setNewEntry({...newEntry, scientificName: text})}
                        />
                      </View>

                      <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>发现地点</Text>
                        <TextInput
                          style={styles.textInput}
                          placeholder="输入发现地点"
                          placeholderTextColor="#8B7355"
                          value={newEntry.location || ''}
                          onChangeText={(text) => setNewEntry({...newEntry, location: text})}
                        />
                      </View>

                      <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>描述</Text>
                        <TextInput
                          style={[styles.textInput, styles.textArea]}
                          placeholder="描述草药的外观和特征"
                          placeholderTextColor="#8B7355"
                          multiline
                          numberOfLines={4}
                          value={newEntry.description || ''}
                          onChangeText={(text) => setNewEntry({...newEntry, description: text})}
                        />
                      </View>

                      <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>采集笔记</Text>
                        <TextInput
                          style={[styles.textInput, styles.textArea]}
                          placeholder="记录采集经验和注意事项"
                          placeholderTextColor="#8B7355"
                          multiline
                          numberOfLines={3}
                          value={newEntry.notes || ''}
                          onChangeText={(text) => setNewEntry({...newEntry, notes: text})}
                        />
                      </View>

                      <TouchableOpacity
                        style={styles.saveButton}
                        onPress={handleSaveEntry}
                      >
                        <LinearGradient
                          colors={['#D4A017', '#E6B82A']}
                          style={styles.saveButtonGradient}
                        >
                          <Save size={20} color="#6B4E71" />
                          <Text style={styles.saveButtonText}>保存记录</Text>
                        </LinearGradient>
                      </TouchableOpacity>
                    </View>
                  </ScrollView>
                </BlurView>
              </View>
            </Modal>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: width,
    height: height,
  },
  overlay: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 15,
  },
  bardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  bardFrame: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 3,
    borderColor: '#D4A017',
    overflow: 'hidden',
    position: 'relative',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  bardImage: {
    width: '100%',
    height: '100%',
  },
  campfireOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  campfireGradient: {
    flex: 1,
  },
  sageWreath: {
    position: 'absolute',
    top: -8,
    left: -8,
    right: -8,
    alignItems: 'center',
  },
  wreathIcon: {
    fontSize: 16,
  },
  luteBadge: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    backgroundColor: '#8B4513',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#F5E8C7',
  },
  bardInfo: {
    flex: 1,
  },
  bardTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 2,
  },
  bardSubtitle: {
    fontSize: 14,
    color: '#8B7355',
  },
  titleContainer: {
    alignItems: 'center',
  },
  mainTitle: {
    fontSize: 28,
    color: '#4A7043',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  titleUnderline: {
    width: 80,
    height: 3,
    backgroundColor: '#4A7043',
    borderRadius: 2,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8B7355',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  campfireElements: {
    position: 'absolute',
    top: 180,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 30,
    pointerEvents: 'none',
  },
  campfireStall: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(212, 160, 23, 0.3)',
  },
  stallRight: {
    alignSelf: 'flex-end',
    marginTop: 30,
  },
  campfireIcon: {
    fontSize: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  addButtonContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  addButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  addButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  addButtonText: {
    color: '#6B4E71',
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 8,
  },
  entriesContainer: {
    paddingHorizontal: 20,
  },
  entryCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 2,
  },
  entryContent: {
    padding: 16,
  },
  entryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  entryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  herbIllustration: {
    position: 'relative',
    marginRight: 12,
  },
  illustrationIcon: {
    fontSize: 24,
    position: 'absolute',
    top: -8,
    right: -8,
    zIndex: 2,
    backgroundColor: 'rgba(245, 232, 199, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  herbImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: '#B0B7A4',
  },
  entryInfo: {
    flex: 1,
  },
  herbName: {
    fontSize: 16,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 2,
  },
  scientificName: {
    fontSize: 12,
    color: '#8B7355',
    fontStyle: 'italic',
    marginBottom: 6,
  },
  entryMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rarityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    marginRight: 8,
  },
  rarityText: {
    fontSize: 10,
    fontWeight: '600',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 10,
    color: '#8B7355',
    marginLeft: 4,
  },
  entryDescription: {
    fontSize: 14,
    color: '#8B7355',
    lineHeight: 20,
    marginBottom: 12,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  locationText: {
    fontSize: 12,
    color: '#4A7043',
    marginLeft: 6,
    fontWeight: '500',
  },
  propertiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  propertyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(107, 78, 113, 0.1)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    marginRight: 6,
    marginBottom: 4,
  },
  propertyText: {
    fontSize: 10,
    color: '#6B4E71',
    marginLeft: 2,
    fontWeight: '500',
  },
  statsContainer: {
    paddingHorizontal: 20,
    marginTop: 10,
  },
  statsCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B4E71',
    marginLeft: 8,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#8B7355',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(139, 115, 85, 0.3)',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.9,
    maxHeight: height * 0.8,
    borderRadius: 20,
    overflow: 'hidden',
  },
  modalContent: {
    padding: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitleContainer: {
    flex: 1,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 4,
  },
  modalScientific: {
    fontSize: 16,
    color: '#8B7355',
    fontStyle: 'italic',
  },
  closeButton: {
    padding: 8,
  },
  modalImageContainer: {
    position: 'relative',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: '#B0B7A4',
  },
  modalIllustration: {
    position: 'absolute',
    top: -10,
    right: width * 0.3,
    backgroundColor: 'rgba(245, 232, 199, 0.9)',
    borderRadius: 20,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  modalIllustrationIcon: {
    fontSize: 32,
  },
  modalDescription: {
    fontSize: 16,
    color: '#8B7355',
    lineHeight: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  modalSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B4E71',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#8B7355',
    marginLeft: 8,
  },
  propertiesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  propertyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(212, 160, 23, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  propertyItemText: {
    fontSize: 12,
    color: '#D4A017',
    marginLeft: 4,
    fontWeight: '500',
  },
  usesList: {
    marginLeft: 8,
  },
  useItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  useItemText: {
    fontSize: 14,
    color: '#4A7043',
    marginLeft: 8,
  },
  notesText: {
    fontSize: 14,
    color: '#8B7355',
    lineHeight: 20,
    fontStyle: 'italic',
  },
  formContainer: {
    marginTop: 10,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B4E71',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: 'rgba(245, 232, 199, 0.8)',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'rgba(176, 183, 164, 0.3)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#6B4E71',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  saveButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  saveButtonText: {
    color: '#6B4E71',
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 8,
  },
});