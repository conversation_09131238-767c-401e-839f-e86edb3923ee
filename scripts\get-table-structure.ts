import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';

// 手动加载 .env 文件
function loadEnv() {
  const envPath = path.join(process.cwd(), '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=');
          process.env[key] = value;
        }
      }
    }
  }
}

// 加载环境变量
loadEnv();

// 使用环境变量创建 Supabase 客户端
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ 缺少 Supabase 配置');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function getTableStructure() {
  console.log('🔍 获取 profiles 表的实际结构...');

  try {
    // 使用原生 SQL 查询获取表结构
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default
        FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        ORDER BY ordinal_position;
      `
    });

    if (error) {
      console.log('⚠️  无法使用 RPC，尝试直接查询...');
      
      // 尝试直接查询一条记录来获取字段
      const { data: sampleData, error: sampleError } = await supabase
        .from('profiles')
        .select('*')
        .limit(1);

      if (sampleError) {
        console.error('❌ 无法访问 profiles 表:', sampleError.message);
        return;
      }

      if (sampleData && sampleData.length > 0) {
        const fields = Object.keys(sampleData[0]);
        console.log('✅ 通过样本数据获取的字段:', fields);
        
        // 检查每个字段的类型
        const sample = sampleData[0];
        console.log('\n📊 字段详情:');
        fields.forEach(field => {
          const value = sample[field];
          const type = typeof value;
          console.log(`   ${field}: ${type} (值: ${value})`);
        });
      } else {
        console.log('📝 表为空，无法获取字段信息');
        
        // 尝试插入一条测试记录来触发字段检查
        console.log('🧪 尝试测试字段访问...');
        
        const testFields = [
          'id', 'username', 'email', 'bio', 'location', 'avatar_url', 
          'role', 'organization', 'organization_rank', 'reputation', 
          'gold', 'tasks_completed', 'shop_id', 'created_at', 'updated_at'
        ];
        
        for (const field of testFields) {
          try {
            const { error: fieldError } = await supabase
              .from('profiles')
              .select(field)
              .limit(1);
            
            if (fieldError && fieldError.message.includes('does not exist')) {
              console.log(`❌ ${field}: 不存在`);
            } else if (fieldError) {
              console.log(`⚠️  ${field}: ${fieldError.message}`);
            } else {
              console.log(`✅ ${field}: 存在`);
            }
          } catch (error) {
            console.log(`❌ ${field}: 测试失败`);
          }
        }
      }
    } else {
      console.log('✅ 表结构信息:', data);
    }

  } catch (error) {
    console.error('❌ 获取表结构失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  getTableStructure();
}

export { getTableStructure };
