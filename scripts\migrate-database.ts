import { createClient } from '@supabase/supabase-js';

// 从环境变量获取 Supabase 配置
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!; // 需要服务角色密钥

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ 缺少 Supabase 配置');
  console.log('请确保设置了以下环境变量:');
  console.log('- EXPO_PUBLIC_SUPABASE_URL');
  console.log('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function migrateDatabase() {
  console.log('🚀 开始数据库迁移...');

  try {
    // 检查 profiles 表是否存在
    console.log('📋 检查 profiles 表结构...');
    
    // 添加 bio 字段（如果不存在）
    console.log('➕ 添加 bio 字段...');
    const { error: bioError } = await supabase.rpc('exec_sql', {
      sql: `
        DO $$ 
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'profiles' AND column_name = 'bio'
          ) THEN
            ALTER TABLE profiles ADD COLUMN bio TEXT;
            COMMENT ON COLUMN profiles.bio IS '用户个人简介';
          END IF;
        END $$;
      `
    });

    if (bioError) {
      console.log('ℹ️  bio 字段可能已存在，尝试直接添加...');
      // 如果 RPC 不可用，尝试直接执行 SQL
      const { error: directBioError } = await supabase
        .from('profiles')
        .select('bio')
        .limit(1);
      
      if (directBioError && directBioError.message.includes('column "bio" does not exist')) {
        console.log('❌ bio 字段不存在，需要手动添加');
        console.log('请在 Supabase Dashboard 中执行以下 SQL:');
        console.log('ALTER TABLE profiles ADD COLUMN bio TEXT;');
      } else {
        console.log('✅ bio 字段已存在');
      }
    } else {
      console.log('✅ bio 字段添加成功');
    }

    // 添加 location 字段（如果不存在）
    console.log('➕ 添加 location 字段...');
    const { error: locationError } = await supabase.rpc('exec_sql', {
      sql: `
        DO $$ 
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'profiles' AND column_name = 'location'
          ) THEN
            ALTER TABLE profiles ADD COLUMN location TEXT;
            COMMENT ON COLUMN profiles.location IS '用户所在地';
          END IF;
        END $$;
      `
    });

    if (locationError) {
      console.log('ℹ️  location 字段可能已存在，尝试直接添加...');
      const { error: directLocationError } = await supabase
        .from('profiles')
        .select('location')
        .limit(1);
      
      if (directLocationError && directLocationError.message.includes('column "location" does not exist')) {
        console.log('❌ location 字段不存在，需要手动添加');
        console.log('请在 Supabase Dashboard 中执行以下 SQL:');
        console.log('ALTER TABLE profiles ADD COLUMN location TEXT;');
      } else {
        console.log('✅ location 字段已存在');
      }
    } else {
      console.log('✅ location 字段添加成功');
    }

    // 检查最终的表结构
    console.log('🔍 检查最终表结构...');
    const { data: columns, error: columnsError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);

    if (columnsError) {
      console.log('❌ 无法检查表结构:', columnsError.message);
    } else {
      console.log('✅ profiles 表结构检查完成');
      if (columns && columns.length > 0) {
        console.log('📊 当前字段:', Object.keys(columns[0]));
      }
    }

    console.log('🎉 数据库迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateDatabase();
}

export { migrateDatabase };
