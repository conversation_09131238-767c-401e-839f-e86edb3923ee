{"_id": "fetch-blob", "_rev": "30-98e0558b27a68f35fb1d4d542781d5c2", "name": "fetch-blob", "dist-tags": {"latest": "4.0.0", "RC": "3.0.0-rc.0"}, "versions": {"1.0.2": {"name": "fetch-blob", "version": "1.0.2", "description": "A Blob implementation on node.js, originally from node-fetch", "main": "index.js", "scripts": {"test": "ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/fetch-blob/issues"}, "homepage": "https://github.com/bitinn/fetch-blob#readme", "devDependencies": {"ava": "^1.4.1", "codecov": "^3.5.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^14.1.1"}, "gitHead": "1acea29a77e145bb4deaeda21d1b2a993398c801", "_id": "fetch-blob@1.0.2", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-yzQVWRNIomfiGsUUueunMpB+LWZA6liJvg/1+Sso9Q0RFw75eNnykwAsS1QozeDl+FCttgjX7LApmqbsimIweA==", "shasum": "f44979e9df2f7d424e1857d2d6584fe6db170e1c", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-1.0.2.tgz", "fileCount": 7, "unpackedSize": 8404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3SQfCRA9TVsSAnZWagAAWvoP/j2Nx3gVE5OmcanvKeed\nF9PG8/P7xsGP3SUkVPynQHWrLdEWhhmo+XoP7BaFG9H8hHSE+FEd4Q2GDiyc\nQ1ozNL5s3q4r+zc2u04oRbUpulC4AkFK1bS+NzY9OB89V/zq2P8MTsA8GBum\nr37HqSejjtIAcnDXDjHZvc+Hv9C9h51ameSJ2afTBLqhAlgkRUVkyj91MrLi\n177GKZRuDTBR5RHnvC/nYbxQ+lkelIdWQOzUtT5Nbk/EhCKl/5awSboo+6Fc\nDqkON4FoP5HlBoX0tpWeNRGm2KbkOGzhHa0wWmgKAJMMMCbADFdW4YcfWEgi\n9o01pkPAqVhp+pQleKvOWfZpvAguH/7CAZMyv0y4N3WtLm1KD7/apjFggAwk\nXm8E67ijFHNZPLQdfnfKHgse5DAsCAqNc/J6Xpsbff3GoFsI4338Mg6cUsvV\nP9mVxcOZog/D1pqlefLkx+HvaB62915YzSkCGzDqvvrfbKrHxGKlSyi3wW0v\ngJ/z9qC5Fuiz0SvSK5yc/y5c9jRAHY4bNdpK1hPltBlmQg7DWmnnmwR5abYy\n3xcktFfWL0Mzqr84UryD/5YXnubDXAKW+PJv3ubMtXnrCfkfdVzL04uRUwS5\nJmOk7gEJh52J3FgDBOsrZq1uyvGU1aQWD47ygpsvw5sZEMpg+UCJrPKHqXHW\nAXVJ\r\n=a+Xp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB00vXa2omd7y0HEoYSC3+bMKPN3G8uBpiaQQFBD24IRAiEAtJNfeY7ssv9Z3UpRFtG+CssmFRKkvxe4IcRiE0Jlkwk="}]}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.2_1557996575048_0.09011285146401549"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "fetch-blob", "version": "1.0.3", "description": "A Blob implementation on node.js, originally from node-fetch", "main": "blob.js", "scripts": {"test": "ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/fetch-blob/issues"}, "homepage": "https://github.com/bitinn/fetch-blob#readme", "devDependencies": {"ava": "^1.4.1", "codecov": "^3.5.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^14.1.1"}, "gitHead": "78ec7f45c0d54e4090423dbe56abd1d455223ba2", "_id": "fetch-blob@1.0.3", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-5MFud4mZ7GMniHz6q0gifn9EcGzFw1AQgia24L4XH8zabWyKLaPzNksWAzeH80jCuP2NEosLtBsTGZRnKhOn/A==", "shasum": "7d75e3886f1bdb28083e30298b169de575f674f2", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-1.0.3.tgz", "fileCount": 7, "unpackedSize": 8465, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3SarCRA9TVsSAnZWagAA3H0P/3aquaL3XMYCX8j5X7oh\n+M00aU5V6aV8ytE0e+O78VsjfPs86Rj4SRUxyoHzifm+PuPOEBYcDIYCYfY0\nDX+SvWmFSWS7ieOjQRkQE4QflvJxMsgZDz41F1Cw4/noGU7xMEIZwovrlcVu\nr5fR/98sniUJzTjZlqcbnCxiCROv9z7MUGvfUu4yQKkuvKTDC9YXeO+/dJNC\n4vBosVQV3gCwTcPYqRda3tLmGp1TaJylqA5Zx4ts186Mm8HrvQu72JjjON/3\n1tdmbLucuzR+M0QOYiAd6+xHx+fhEF0O09yXEHJee956Y6FPyf1GbKgAJmuy\nJbVgAGngO1TERghHTXJjBafR1JE7qRdkYPvkEpRnGlXp0gOlCkRKMmwZC4dh\nhnP+cL71QOpwGG6ad3I13L08FMuDWMevkgwTjpWJZ179tHvd38pNCR2Tbapb\nXMa6/uLlBzQMCskM75XV00fmUGDqflsII1Z7d2FuNLSTrUskFQIkSbINaCX3\n5G0grRn6wr9ZvoQLGm4J+67TFQE8gXAXuh65oEQF1U5JcKa6I1+tsR/xIUbu\nXta4sXntKhCqYw3hCJQf83Kr1YwD4iH6U+FT0TADZvJSfcnitrbrwHg4eulg\nwZZOu8UgtHrMcs2OgPUTriI17DDzMp9ra/eV+aTj8ZGCPRr/giGyR/qs1Chw\ngfFr\r\n=Skj9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHxBl83u/Z2Wt9/PgmVYDOT0Z+heHyDxue3hHguq3rieAiEAlOaFW+gPhrV95CZfi6pCs8JfgoLj5ji9aka+AL2bAs8="}]}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.3_1557997223440_0.1508320811348316"}, "_hasShrinkwrap": false}, "1.0.4": {"name": "fetch-blob", "version": "1.0.4", "description": "A Blob implementation on node.js, originally from node-fetch", "main": "blob.js", "scripts": {"test": "ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/fetch-blob/issues"}, "homepage": "https://github.com/bitinn/fetch-blob#readme", "devDependencies": {"ava": "^1.4.1", "codecov": "^3.5.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^14.1.1"}, "gitHead": "b197ea6198d349ca822f7385212aa728fe0695f5", "_id": "fetch-blob@1.0.4", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-4Nb07BSJ2OR/fWJ1nI9QqLJI4vSs3jtei1UP4oPWffeKpc+8aWPsWzBH6cpJXqQTqSqYinl7oro1f2hrIpnK3Q==", "shasum": "23242b3f873d71b7165c0cc1b3d2dbb89584d293", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-1.0.4.tgz", "fileCount": 7, "unpackedSize": 8571, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiKE6CRA9TVsSAnZWagAAuWYP/jNddLaef4C+f0hBwyz/\nnXoGPjRHXPWjFwy4DuI4tbRKCanHLMkkmmFHa8c6QSTLO9LfLOcS56Ou47vw\nBY7sByl1u5wjFIBruAmNh6+XNiuWRhliKm3wj9Gi/W5fIMzgmw/OAwIoJSII\nS+7wX6uwCFjYEGGbJhWxVnUTR0iNRTl1SblzRg0ewn09bWxPhPpSpIkGzMch\n0SeEm50jogFNNNYYWAfycyX6Zq4QfSD7YtlHkKCliggsgSLj8B0j3w5Iim9H\nhgk3oF3fp0ELDZrT4PiZDfYW0loCDUiwLpst5YLJU6VtGXCimoNfqmmPwCiC\n2GuUG229Qc4z+1BrMHCKvX4/xU7R7W9E3JZJGh6Ty/iw0T/9fJhTSAMMlvx2\nop8ELTLBQF5cdKAljmHuyT25PQJ21R888yDbgK5xHO6n/72Xe1sf+OkyMnfm\nj5FI5xRfSy+8R5hsfdjRAfRsOxlvhP1KgiH1SurzPs6ub20zP1tmtGsV0PH6\nDCFhPKRa4H3dS1HUwtBTBIBUvt/DJTqXi/n4OSX+bETyDRUln5nb1TU/Drrw\nUSyfGn45EhDv+imtfhp+aF5fEpmrjsi3hSHFruOHZHMVKMwvzHse+rE0D/Gq\nKEutYRQ+FJuyOblTeDHKUmc9/6jChojy0/ptCoLgvUVHIF9T/6KnqfWBwKu0\nzahw\r\n=AIeF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFKFYY0WmLNndRRoQ59M50fjxdHNpsNUKMq5iW98zeYaAiB+qae0nqrmJ5fUGK7ZhERrQLDllgJT7v0BlzhPTTZ4vw=="}]}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.4_1569235258189_0.34324797986165256"}, "_hasShrinkwrap": false}, "1.0.5": {"name": "fetch-blob", "version": "1.0.5", "description": "A Blob implementation on node.js, originally from node-fetch", "main": "blob.js", "scripts": {"test": "ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/bitinn/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/bitinn/fetch-blob/issues"}, "homepage": "https://github.com/bitinn/fetch-blob#readme", "devDependencies": {"ava": "^1.4.1", "codecov": "^3.6.1", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^14.1.1"}, "gitHead": "b09fdac53198023370ae6cd441d35b8201b8b74c", "_id": "fetch-blob@1.0.5", "_nodeVersion": "12.0.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-B<PERSON>ggzO037jmCrZmtgntzCD2ymEaWgw9OMJsfX7FOS1jXGqKW9FEhETJN8QK4KxzIJknRl3RQdyzz34of+NNTMQ==", "shasum": "5cc86a8740236c38d8073b153c8b51be5f3dc11e", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-1.0.5.tgz", "fileCount": 7, "unpackedSize": 8666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2VhsCRA9TVsSAnZWagAAqk8P+gLeXpDTHvpGuiKWRPiF\nc7D9PtLTnhMMSEahHk5CY5wt3gMgFGYy2Vqmr2D8UpibOnkXHc77DlmPKCS3\nJO4jK+2RifeEAMEBr2f29yPjdC1fZr218GFjquht6fZhfECYLbEyHFDjxOTB\nXHPLGEFuVcPtrJ8Nel7otMRieoKB+B5yK0CaK5DMaMtCDu+aZw6Dr+DVBbry\nMXdLD+4xLvimdgDtSQ6OhX1yYTwN27Hq8QR1yY+6Cau21Qd45q4dp3CM2xau\nRTYyqEVIBfDNKVPMNYuNV6qbMnqwCcIeZiZhVDMjnNy1hrZRhNnQrf8yYzG9\nv12v/sQYTGXrmqpPLWmoBYK5Ucr+IS4hUi5VvR6RxFlhp/BSojsOWLGldUcx\nNGYKbBj1OmpABK/dAjUJuFIsuvXT3yMKu02y/Fkub4K+2oBNoHCpphVO68tK\nbOE61YMaLAW/qoA+aohfBIDUbLW8nIwc1zw1j/Dqp7lX9y/2lZSKeh1w02mk\nRBmz3+LhQoNs1Q0z6kv7Y3obpiAOP0V2OFjdeJeOklAs8fdQ4em9mHd34ilf\nTQgdbDZVehMTsQ8K5vOBwra09exda4ZjCLwB2nInpM65leLdaZmAtk933lOK\nEtm8f4XTqonFXbkqEBgjcwsc5eQDpp9C0C1vjHWB9kBTZA7QVCWk/llp6llM\nxJjJ\r\n=ILs9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFQ/JUPGf27m6EDNxoR/T1aWwXttetthwib/ZbvZEg2rAiBCShiuAQQKZhtFZYNiGgHOie5apOIXD2JlzYbPH4GEmQ=="}]}, "maintainers": [{"name": "bitinn", "email": "<EMAIL>"}], "_npmUser": {"name": "bitinn", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.5_1574525036160_0.5380429482971754"}, "_hasShrinkwrap": false}, "1.0.6": {"name": "fetch-blob", "version": "1.0.6", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": ">=6"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^15.0.1", "xo": "^0.30.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "gitHead": "bec5a3dceb340ccbf3130f0000807395460fcc17", "_id": "fetch-blob@1.0.6", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-XTotUY7hVtqdbHE0Ilm/u/nnXRv1T8nepxhMHzB885O0EkVvI05UlZq7rHQSd6hVDCNAGx4HTjbJO60Onjfckw==", "shasum": "d1e7f02df9dfb9729307ece7e255209051e2de6a", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-1.0.6.tgz", "fileCount": 6, "unpackedSize": 6888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexkSJCRA9TVsSAnZWagAA9bAP/307oVWdt/GyW2RxUNOv\nIf/OKcW9Bn0ixHniMklQLuU+JatOWqKSfxXvpsPGszJDzQP0ROsdUIv9qXjd\nJHIj5WpogCDGxqhdCbr5XlWCSLaqohOWoxMR3riFWO2hhStRkQRYSF2ng4ub\n/AhTSZEvy1/yqXU8MfTQ4qFhIFRZyCdCafoL5BZ/aqcvUmDT5vk5CNxK5QcL\ntTIe4rHZvq5GQpLhCoubTcnjfJuff7qU7vPPE6YigZt+52YgM3XDHb/ehiF9\nwH5gZV/PI2sibB4bhPYyC7adYxkGgE87GBWLTZ1L9qa+/lYE28nNeutPBWzv\nNqXriH6RcnK/QIm2TE9L62WibT12ImX0xS+DrbRr+AxS/EC9VcZlEV86dOjl\nbaB4ia/RNlsZhDWXrEIlKN2KnbXk0HpFkPKgr4MmY4YQefcw+4edaw2S84Fz\nRQtrnXPUfVcBHmR7Zi9cybhkg2P09pHb1PAZY2fMtn9NfdFzswYkc3Mibth2\na8VbiNXT6ul0N+HH1YHUJGtNGbloqC/LWCoBol0tmU7Gp7nubrbYZjEP4LVw\nMnWxzHwmS3imIFWipNqZGwE2vRnMMjxBuqTI/UempePe52I5Bg8KhL4uo/I2\nlRCwyq1JG4bhH0Zaru9fSJZTbdgdWeCEE9yGu331h6JGB36fMTkhHfUoIvM6\n/h5o\r\n=Bfer\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4Bi5Eo9+0EOw0iNknant5YXYXeYakydwmCAFbnyv5uwIgPNNDoiCVfapwSmbC0CzObLab1fVOTzrznordFiDbuY4="}]}, "maintainers": [{"email": "xx<PERSON><PERSON>@pm.me", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bitinn"}, {"email": "<EMAIL>", "name": "endless"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.6_1590051977443_0.006245879081175687"}, "_hasShrinkwrap": false}, "1.0.7": {"name": "fetch-blob", "version": "1.0.7", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^15.1.0", "typescript": "^3.9.5", "xo": "^0.32.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "dependencies": {}, "gitHead": "62fc8eab22ec15277b63f61806e602bf0490724b", "_id": "fetch-blob@1.0.7", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-mJyUUpl2bOpjV5FdUNbVTMNYBLESg3q4mvkgNcNlxQqI+l9K0rT7OQLBxrn1w8U+/iof5hWcHBbeYwBm4jcBOA==", "shasum": "c2ceffcde92dac1613c26289cd006019859ec2aa", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-1.0.7.tgz", "fileCount": 6, "unpackedSize": 11015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4OrLCRA9TVsSAnZWagAAN38P/jfH/4ku1VgOUwrsGiap\nkUOdrjStOd4M7RZJNzrFoiLEqyUpqvUzWl6X1wHRGI/dDwSSN/rE/9HTLk5j\nw0uwllupmSiI2Gg/Z6P3lyS6fE6b5zH55pO0cwwVSXWyFQxSMv1Rvn3bqb7X\n2w3iVV3wicNCKb5o1E3mX0BYV0/8FTVl+2sXtTEASdY8NcObgIQJ0EXqC9kD\nje3Xe7YrkB7FkpbSnR8Euu9t0VMFoHqgXl5GvuBzwyyCbss/LgMwFZ/4kJ7c\nFuBCtgEcK2fspFzfFDNfjEfQyyBu3P3b64nT8vrudUx48QEv9G2AcU7LqGWN\nNhcjk8shEITAW2TIM9ZyjA07IbHuFVi5t9B6eH2xDFBA+fhW2sLLEtcGYO5j\nmtfjOhJeH/KF6oWlsgNQDonxBfiISYeLXcpX6zshRzmR5J3QVaaZFazzEGzY\nhgMxk32FXPGh/rKI8KMzzTsBz+FvmcjZbpdXzVD6QY7lgclR5md1Go6ZqCZv\nlf93xfNA+XCfovozUYmmPknjn9TRgg7ueLQGUzBx3C8OcudH/esxvANi/De4\nBgxqPFHby3lf1xHPpZe35fiwG9gt5o9dzI3tuJUUwW2k87o/6Mn0bFFRxnGa\n3gU7kaJnE/5XgC9LHGe0l8XWEGNJneM51L6K7UpFi0Uj4sqEkiPivWygxVUS\nn21m\r\n=txrV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHkTBJ3P2jz87aJgniK23aTodDYmZhmIsw4ZQDnvxcwAIgN5klhD2aoiFB5jsMZF0mhBwdwg85Go4WxdGdo8sIpZs="}]}, "maintainers": [{"email": "xx<PERSON><PERSON>@pm.me", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bitinn"}, {"email": "<EMAIL>", "name": "endless"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_1.0.7_1591798474835_0.882075840191062"}, "_hasShrinkwrap": false, "deprecated": "This release contains breaking changes, so it has been renamed to 2.0.0. Changelog: https://git.io/JfSDh"}, "2.0.0": {"name": "fetch-blob", "version": "2.0.0", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "nyc ava", "coverage": "nyc --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "nyc": "^15.1.0", "typescript": "^3.9.5", "xo": "^0.32.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "dependencies": {}, "gitHead": "38639ec4aa7c9330effb0b6a2e3addc32b68c96c", "_id": "fetch-blob@2.0.0", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-I1mmL/BDg8sDZOVmRkdpuDFNipxHMf0Djy4OaDvp6iP230FI/eJlt4p2ocFJZXte6M2DuMtS7Y9mk0Qe4Am9mw==", "shasum": "b2e459fe507858b3e568aef984585aef2ba3366b", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-2.0.0.tgz", "fileCount": 6, "unpackedSize": 11255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4TBkCRA9TVsSAnZWagAAfI4P/Aqv8vHbBBgIm3b8Dgcw\nQDa10xy3lgu8ydqI16RdLK2KZSAJPfR/ycLsIQvx3N+bD5/imRJmggQFWq+K\nT9NlBLr0sC563Andcx6p0tVIrO6QTSlWsicy460xCkoJFtCvBMIfqrAeaIbb\nusab3u2Om0Ob5deHfFH/pNe7fM1ehnf32uB1OWsv9ioBtjxW2jfq1TYnSFPk\nXGuK7rbCt0dFPeXxOqRi0Bq3derbGaWfTvjQ/AN4cFLUpcYBKMFZTG9HxBie\nhGWNf7EH7zdChrlVJtjyB7/McIJbvKbsJG6VxbkRy2i6YzQHqoXLcBNlrG/a\nK6waIXiSuiefIBbJtneDaD7YCyz7EtsoKaubxEUjtx6gKb4JIoUw7nd11H7k\nWA+j1PIDNxH2/3hG5Yn/+Ja7QdvMRlJNjALRsz9RIP9LJF1myGWs2ftcTeWY\nlRXDDHfhp4Gi5/M32DveS5vzzSOTcawIHyru/UrRvgHLwcPy0rDA8XMFL19v\n2Af7g0qL53ne2hOdaH21TtARRp5Z08lUJM1QfpngzI23r/vmu/HBwQpcwKIv\nLS+gFB/FEe+NJEQUiYgLecVzdNiRO/aOXaZ55DsMl3E+iMyZpHK3WZIDA/Xg\n1SK3JdvNvUNNHwNNNIty+HYhzPQdEtznB38alvIKFAmcOe0XAt5RL+EKMT+5\nh3DF\r\n=iAy7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICR3v3D6rwJvvAr6/ckljwN5lNjuTB09Y2OeOjpJbejEAiADDSagEw2EIMBC+kxAyTqoz7Q6U3aP/ZjM4SV4iBEbtw=="}]}, "maintainers": [{"email": "xx<PERSON><PERSON>@pm.me", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bitinn"}, {"email": "<EMAIL>", "name": "endless"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_2.0.0_1591816292196_0.1531083274071261"}, "_hasShrinkwrap": false, "deprecated": "Wrong Node.js engine version was specified in this release. Please upgrade to version 2.0.1 or higher."}, "2.0.1": {"name": "fetch-blob", "version": "2.0.1", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0 || >=12.3.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "c8": "^7.2.0", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "typescript": "^3.9.5", "xo": "^0.32.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "dependencies": {}, "gitHead": "99388a79bbdf4a2f774da891c54d4d1a0c1c0afc", "_id": "fetch-blob@2.0.1", "_nodeVersion": "14.3.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-1jFpa68M4EzObtFa7XOKZoN1unsaeJ6hGSbxaWaVO+TkHmVvnyzRu1ktZAFbUvTZ9NC/qMKGKJ79dK4MzuSBiw==", "shasum": "64b583cd2bd21685b17706818b388af08cb40b9b", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-2.0.1.tgz", "fileCount": 6, "unpackedSize": 11362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4kBQCRA9TVsSAnZWagAA4uUP/0eUzYnvN7QeSRFs6BEB\nGUC/pyGF4t2QKVpzNJklj0W1T7S3a1SUPKRem08Ya8YGoIFzkyYGTjJFGmOF\nA0xj/48pK9Hh5DmalZ52emPHie8Eg6yGA15rOyzT7Z66Edq4GT54iqjApJsh\nV/lG1VpVMlNv5dY79CrtqaEdX6i27TpF41aVcddJ2b8mshXZtda018Uxl5KS\nwkQhY6xIyqAJzXQs2iMdlAwV/h5cAlxsFlsF+jXpC9O84Q0av87PA2GQf+Hh\nI9U7P4/q0PERCG14qA37DEeKXDxBRmiBi58++fE8ZHSniGcoG3cdreHsqTo7\n9JoqXHRIXV26mZ2pnS2t8Dyn35/23hXG2RwqY4b7HkqMXJSW/lUs70O03Ikp\nqygXC72hcE2ZyXyZbot5DPEJ55h2yPUQDudjtFAesbQKaH6MzJcKfIJfex7o\nT/WD9HfqqJ1tT9rqI9QrgObB0XxQXohkzJtYGrYuKVScVeeTgj1hOHbbDVQV\nOzc8x+NUAKQ3d0hvcBBatbd2/AViiTl/lQ6Fgmc6nkakCVEqwJj8VFjvANgT\n7ZsfV0HmqRnFYdFrRxAHgE2QSPKcuR+TlsRK4qBg2c/Cf5XSQzvDfUIDu2vM\nsT/ljkPvQTQaaUQ5GoIEkwc3xgj0dhqI31+toBAwQ3D6I9KOvMqkU39+KnOd\n7Wez\r\n=yhdZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDCzY5HYq0136iDiRuAU6AhDQwYRA2MOHwdERd/QyBgsAiAdG8bZ52x7tKT2I6fPh/CVNFwzonfx3J1JyQ7PMTjifA=="}]}, "maintainers": [{"email": "xx<PERSON><PERSON>@pm.me", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bitinn"}, {"email": "<EMAIL>", "name": "endless"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_2.0.1_1591885904508_0.1855559850446531"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "fetch-blob", "version": "2.1.0", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0 || >=12.3.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "c8": "^7.2.0", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "typescript": "^3.9.5", "xo": "^0.32.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "peerDependencies": {"domexception": "^2.0.1"}, "gitHead": "5b2bab5365a5540ae109211d5d7c352485e73a14", "_id": "fetch-blob@2.1.0", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-K/q9efIbuv/x6CqQkaEaCOFYhRLjZcnocxyFVAb7+IoqQVVpWILIGCbRbrHp9sTGesl4LJl14vPIZRbRW2z6KA==", "shasum": "29d6f91e0d8ac7ae691a3923f34832365506c1d8", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-2.1.0.tgz", "fileCount": 7, "unpackedSize": 13312, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIEoxCRA9TVsSAnZWagAA5CEQAI1DKhwnUKvG+diMSpvB\nrIVlrhglI+//Bspo5gypwBI5Go1+030t14SlSeTi1EN54s87fKkl7ppsXKlp\nWJ2HtPYVvJJycZ5gOfQEgU+eA02BhLcIGaUz6i14Zg/8trlZiOC4nlTckvcB\nQC9PLmfcPDOSaunPDmaoD3MxuFvCbHGosY8nf7KenSPzMgT3GizMkoE9Gsl1\nvwKEO6SLh2UCk/mSq60DNToZbDtVBhreVFop/Jbv9m7XGIqI5KgEXf01rIwJ\nOo1cFroo5CxaY6yl7NU/4e/beCJQgETOJMoTPimyQvwIJHzH89v0c0tiGkb1\nuKeUr2pCLXaeTAyFSOccQfgSlWRYwXYqc1aOtaC95nYUmHW4zyl2duX9MElA\nlOsmKRlnDaP3f6ou+vDGH8WRtjw13IL4VJ2lId+t1VJXQb5EHPwzX1SqEW8N\n7PnRnXJeZOVs2GTvZHZrg2kXv3sEyMbvbYZjSbQJNCtH82phEGvot0sOlocE\n8xfV9PBQMzdw6ZZFYuhaPHH9cVQFJPtFseYpbcdua7UF8A+B5q0Dra0QWc9J\nEiPedb0RDziEADdC0eNNXupC+ZEWzvL6bfnvkJ3w4aXy8Dgr9EgJSuMT82yH\n/xtdpjOLjL4xEBcfpBDKFZAso0Go8T5YYJ9iij8M9EmRu7mUDPkV1KJTZo4G\n2JoT\r\n=yr/O\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG9uy/lC4Ry+Z56x2ePDS5FW7PNmESoXTCqH0v2WaEnmAiBcJ4O46aHCU+/He8/u1G6+YTiyWuXIp948Lj+rHBgj0A=="}]}, "maintainers": [{"email": "xx<PERSON><PERSON>@pm.me", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bitinn"}, {"email": "<EMAIL>", "name": "endless"}], "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_2.1.0_1595951664615_0.18159729006565173"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "fetch-blob", "version": "2.1.1", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0 || >=12.3.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.8.2", "c8": "^7.2.0", "codecov": "^3.7.0", "get-stream": "^5.1.0", "node-fetch": "^2.6.0", "typescript": "^3.9.5", "xo": "^0.32.0"}, "xo": {"overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "peerDependencies": {"domexception": "^2.0.1"}, "gitHead": "365330b9e155ffe8720eedab84883c531ae45355", "_id": "fetch-blob@2.1.1", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-Uf+gxPCe1hTOFXwkxYyckn8iUSk6CFXGy5VENZKifovUTZC9eUODWSBhOBS7zICGrAetKzdwLMr85KhIcePMAQ==", "shasum": "a54ab0d5ed7ccdb0691db77b6674308b23fb2237", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-2.1.1.tgz", "fileCount": 7, "unpackedSize": 13384, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIE6qCRA9TVsSAnZWagAA3LkQAJHgnZCxQJ/v2LQSTRTQ\n1tnOyg/haDpXbvk7bWBpRzaCzEljnprjyKrPNW1DcvMjHthQqApjhK0cn1Nw\nweEn49tiEWx0frnlrqrT0DEUzWyrgJNqqYaLhCFHCRdHGRm3/A2/Xxv9St//\nPW64bJ1mtPxmLaKjz3H6H5bE5kLj93S/8Bub31AO96QMhj4EQe6H6k74qryi\n3mVqYgo+6jNhAas62EoKwV5q/lMN15brjM9W6hVNH/OmJH4Br5WeENtxQu4Y\nzMDL92+VPzE9DMwYWGUAiJ6Okv1ZcRPFuJ8wguOiTI6XZ61aML+KGcxZdWLS\n4YBTFNDziX3FcpFP0fGowBWv6vDfLbLTgs7L737mT4+0cRqfUsCw3mmcwMS7\nGPxrgwwUxnt1840iT/Pcc5pQSDQd6I2FxTSk2JQUbAevpL71lMwnZHBkb+sd\nfKrNvA3W5Vv9+tBOMJSs39dzmWIZLfdg4EgzZpsR5SA7ZILxjEu01sOw5k8Q\ntqrcWWIsXKrDT3OEq3fb+W6Jxa5+CxEwjSpKXfllYHWPWXOxI93rwUhRoz+l\nee2KULOWWhT9wVlwSipl+9ZnkWW7pug5YEQmljj3GhQwwcqfGM5MfD1X+mRR\n9PYUBf7EhADoTJ/g7kOjp28Tq6sR+ltukCMwPcSjeeDhLOG0bPM5YWfN7mCI\nW3Ae\r\n=ld9I\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDrIQqmopl91fzNp2HTGsJMrkXpv+lfCjHqmVx47xiswIgGKy2V+JjtQvojvMBJCgrFDFt0jRVWCSQ+eAvWfw5O3Y="}]}, "maintainers": [{"email": "xx<PERSON><PERSON>@pm.me", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bitinn"}, {"email": "<EMAIL>", "name": "endless"}], "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_2.1.1_1595952809760_0.5215171375097736"}, "_hasShrinkwrap": false}, "2.1.2": {"name": "fetch-blob", "version": "2.1.2", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "scripts": {"lint": "xo", "test": "xo && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": "^10.17.0 || >=12.3.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/import-index": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "peerDependenciesMeta": {"domexception": {"optional": true}}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.1", "codecov": "^3.8.1", "domexception": "^2.0.1", "get-stream": "^6.0.1", "node-fetch": "^2.6.1", "typescript": "^4.2.4", "xo": "^0.38.2"}, "gitHead": "5584886f0f34402bbb53811ef7b4c63ddcddad2d", "_id": "fetch-blob@2.1.2", "_nodeVersion": "15.8.0", "_npmVersion": "7.5.1", "dist": {"integrity": "sha512-YKqtUDwqLyfyMnmbw8XD6Q8j9i/HggKtPEI+pZ1+8bvheBu78biSmNaXWusx1TauGqtUUGx/cBb1mKdq2rLYow==", "shasum": "a7805db1361bd44c1ef62bb57fb5fe8ea173ef3c", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-2.1.2.tgz", "fileCount": 8, "unpackedSize": 14101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfvipCRA9TVsSAnZWagAAHtYP+QCyqWh8ghyUc6lVog2B\nA0Rl1EDFILT47X6Kfn+VMWYuSSNYYuuYMWj8R0Xit05i0anYL+PQcqxqTPMd\n69mKVJMSgzCs1+z+nJv6crqcr0HSUiWzR3Bf2USQBAbTcm3Fh1Y1Qe4MEMSB\nXlP+AAI41w/C27yxmL4yBDL17VJlYVd1YEy0gggSmlW/NHutkNV+bE/xcxM+\nUaW4M0DLtkV4pzFvG8Rkvl9qwKhcYJu8+QkFhxk5fF5qvEZm/1H1fAMGkOJc\nEJL9frOV9lfcON3cfDaGHXCM293b//KulqibMD35D0sCjt9azchfMiM8IVKT\nMx+uqzrtO/tjE7vtAySxxJxWmPPqxLYn+oDMYk1IfMZFI+AS3QuYbx51QdXJ\nKnEcQiO4Gxq9DYIL6+4uqfIkJSgnAp1CQ1gmyrBoYBpeyxAU5r8Arqpqjbae\n1I4y9CzdWy0s7G3MFH9WOoVcQGqdk2Z0sfAIge93ldI6HvqjAH9VozdBuKCo\nBlGqx8F7TECt49MujzOty7NbFgspKprYlk4qvRSlyLZlRWaMloYoyxALPHyf\n/0i7DJ4kKmG5lcpv1DYTJVmIwj8Jtj99nN+3cABNz42AXaxloeGRE8kuR8fa\ncKD2uxG9Yq3XsWfxOA3kjGtjQ0tyMyIxQEkBopWtpflIdwkQwuVpiB2KV9eq\nWU6r\r\n=9NIy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAcb5IOVhxx44xQV16B/dKVRORCR2Nckxk+TZdkKay8EAiAWI9U+srZRlxqrc/1fBHDjOXwzgVz2A2EklZQ9k4+DEA=="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_2.1.2_1618933928915_0.5847854579968081"}, "_hasShrinkwrap": false}, "3.0.0-rc.0": {"name": "fetch-blob", "version": "3.0.0-rc.0", "description": "A Blob implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "node-fetch"], "engines": {"node": ">=14.0.0"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/import-index": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "peerDependenciesMeta": {"domexception": {"optional": true}}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.1", "codecov": "^3.8.1", "domexception": "^2.0.1", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.2.4", "xo": "^0.38.2"}, "gitHead": "a0c0abe1a180b24c2aa2ca48e8e8480a9f6dc20d", "_id": "fetch-blob@3.0.0-rc.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-x5MJQ9puLDQBc65PNOEozYOaNJIVqTJpJwoUs8x3SXWr9cJzrY2nsiUoTpv3Ow3TXIqauwCbF3std23oltPEsw==", "shasum": "a7207d9f02d5ee84fb59f1544a3714e07f8ec0d2", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.0.0-rc.0.tgz", "fileCount": 8, "unpackedSize": 20716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglQ09CRA9TVsSAnZWagAAYgQP/iisV1i5MDj9Vlxdi3ua\nl2NT30WHj+80nfZZhkDKlnA0AE1wGaS7a8zFbNd3aPziFhGmlIw+k/jhVzrz\nURSxxBhHFVAynt7eYm4cs4+hql1slykL+7cX4DT7rntQ7zuz3wT+/wGoeEUK\nXf2KA3J1qBQT9vNXuKPYlQCmnHmOkvwUE9B4CuIbkzgnMP03kSNIJ9Dju39x\nRNRuMCrxkGgE2MWgP6/smjjp0bdyRWCQsfdNtCgOOTzkZeHTCJ02lWW6+iwo\niCV2NGPvWvrwv34FaBkze0JMAhciqulfsJs2YzEf8Nh5+VWfUB6C3GfzCeRV\nOSqLEatwZw+q+1UP6c1LIKtHbkRJkb69OqGVoQFOHWvDfRyAXggzlcJy5QDM\nFBbSG9h01pEJK+bnTuqaplrtz+tQjJImMMn/fVEJ+C+1U5Ymg+u5HbvbQRfh\nVXLOs0sCgvywG+XVMi89ts1IehWSzVq5dZKPRzQIrFXFxLyToT/nP1W0ooda\nEWWwhOBtZl+5KlnxcyNZKdYMhkUcYR5AYj0O/5WLh5tEZyIXvjPXhv6CveUB\n7qxyIavDJMDV07qHhnKavnOP+H2yGFIN2kKsMwPhrBf3RPHIvGiavVXBVgUy\naX8X5eftlCD37fiFe6FuEmFQVc7LrHxOb3Pv4WGKBj2PBNGnQYwpXbqzPJO0\n/hwH\r\n=1nkR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGDMdu2ER3cxreZLm8uZV4LP6MEK7SYiGjr5evkn9ol/AiEAwFgMgoSSMEreNuAhEUlbJ/5IBBBVSNo6jyp9Vtv+IiY="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.0.0-rc.0_1620380989338_0.6724846358760896"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "fetch-blob", "version": "3.0.0", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": ">=14.0.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/prefer-node-protocol": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-spread": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2", "xo": "^0.40.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "gitHead": "b5fa0bd79fa2ffbd2442ef985325c1de15d9f929", "_id": "fetch-blob@3.0.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-zkWSie/M6VryeUOZD9Feeel5PtaB2P7mXTC5fEoRpngVi96e69jP/PA1LagYsBlC2G4tleqWqxoRwC2ttMEy3Q==", "shasum": "0ab11d20f63aae7615e8ce314e8bf8bac70480db", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.0.0.tgz", "fileCount": 10, "unpackedSize": 24984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsgcmCRA9TVsSAnZWagAAWy4P/0HFXVj5YfaZ6s3kniC/\nCbX9QODX0Ny2Kdik6eXLOlS8Q+Z6pA6UFesRRiezU+yMHBxZDD62swj0RkFB\n0HwBAIJIVCw55+FqjfsiBTAM7ZJJDc/ZRLrnK6DGlaE3ftTDZLfoS9W5twi/\n2IwLGGd9UO3BWLS8s7/armsTgDmFE83+OfJMNbMZScAcP+mHAokDhADlMBTV\np0drSQNmW3wUfEL9YkG+1afEbkhQrEHkxst3jle+ikIsGNqZ3O50lFaLWzzV\n24JGLG4+qBT6gaGzfnMVCf5FjZfMNbhNjEl4au4HNg2ZGkKxsE7bOTk8fn4j\nvSWW724EUB5Elo5TG3Tuk/l61U6WabVoW+3fFMX4sV63c7riWSXvdqti8v77\niVATgI+1DIKIMCoahveQnuxB2DCm32jIAb+yN0QkYqMD6XMRmcfKwrpvRlzX\nv5PiyIIMHMH1LWU3n4YEmTCfE1HBhSvJx7cYbw9POazuyN2vJKnZ4LrzUKRV\n5FiJpRKhQRjQQwIwUhqlnKtvixdTEo7WhK3JhTWxNUl5e0yEsKzILeieify3\n3qv9HVjHlWRHxnV+pMvwFC2jSaEhUb87qEniFZkBwSz5YuniMl57deWMd6qV\ngWvV8a1cDNvOJJGYVplGI2lFlbWRKH/qErOIfMvwva57uK3emi4tG9zPStl+\n+N3p\r\n=yUJI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1K9XD9KGyDAYTwTyaj6fDpfBxY+EOQy2HwFlbAPGCQAIgCT33ZH4txv8hAM+9OewF8NF/QvX0vgxCsloC6W1pYmk="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.0.0_1622279973572_0.40039732622470137"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "fetch-blob", "version": "3.0.1", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/prefer-node-protocol": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-spread": "off", "unicorn/prefer-number-properties": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2", "xo": "^0.40.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "gitHead": "5ba554efd2df7eb674d860f96b7cbd35781d00b1", "_id": "fetch-blob@3.0.1", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-uq8YWG2SVATaJtkP6YukvWHw++EpNsoT8vuBxFk57vcUXVQaS9jkdH6RKWVrbgO0YUNy3dPrXO+7HnSgTcJipg==", "shasum": "c67d9b2d9e6d7202209b234ba14e13de451c5db9", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.0.1.tgz", "fileCount": 10, "unpackedSize": 25039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgzb5mCRA9TVsSAnZWagAAQMAP/1X6bfUKKRecAjsNEy45\nkLQiTEOIkqlkTjdU/CdJtk05IiCDjQ+dbqx7/DQlcUuKLJFiMbDgBRl4cpdm\nyk82LT1D34EBWZZNNFZEU2E606OB/airF5VMMUXWZMY1PM6rsIzE3JzEZ70m\nZhj5UG5aBvyox0w0tqcT4nXZrtljR8FaUWbi1lgT/PlJjG5oz7u4RERDs+ox\nkem6wD0Tcbn05BH1eEABQa1WzHsMZT3pJlMduM1IILEe6j+PFcPPJomHPFZ7\nl0m75MJKYoP8T18rcKEg5VLN12zeDTtQxUG3ZwceBYTt1llz7PWQ99xrurQt\n4v94PbDbDaWd9dYWbxYqlE/z4kur7tQ2LrLTOdixyTnaO7AA6c2hN/4xI5hL\nyQmwjElQTcG6xBUjfiYGKo1Q4QhHU/8oBS7ukGQLJLrE7J5piQbfHASWgUsL\ndTRbesyfDUq0YXQpgwI/W+NZS2ytsvGTjcEsM5C7f8HU8iQWJJg6ej5poXm5\nYhogvBaIMyiVCodih0qvsH0vkbcw7zi4F0kSnJlTo2+rzMZ6DvG8USkAtgbv\nkV4BxJiKlr0tRWF6StYEy82mRBUi12XpcvpNnPrVhBBHa+brW1UYGWcK20yU\n3O5Yq2YhSnCWUo8qd2O0wq1gz/ce09/Kiv+qYzx2osS6/uuOAKNZP58ru+Yz\njVs6\r\n=5eia\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHntS1OpERjhOLKVrrAPW1JbR+Q+0MZSFmFREPu+TwKAAiEA1VHo143zUb1eOa4S8IrpOa1OubtnAcyoFyFtHkexMlg="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.0.1_1624096357881_0.07193428206370212"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "fetch-blob", "version": "3.1.0", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/prefer-node-protocol": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-spread": "off", "unicorn/prefer-number-properties": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2", "xo": "^0.40.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"web-streams-polyfill": "^3.0.3"}, "gitHead": "52f296c2839b9fb09cd460d453534f312914d301", "_id": "fetch-blob@3.1.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-+M3dfFQfT5urXVV9rtNMHGenkVF1xejn3gEM+9IMpXJ70YYQTTmvwwZP5bAzUlA2Sk8IwPO1e0D7QLu3cdvF7g==", "shasum": "f8ceca3638734047e79641d55b47f8d9bef61177", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.1.0.tgz", "fileCount": 10, "unpackedSize": 23279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg8LsQCRA9TVsSAnZWagAABogP/ioaMXcl9PLps0VOX5oW\nLRBLq3Z653wNbGrTVAB7yc0YaGdGHdn7s2xMWh3x6GU4tcQQ3/tprG22W0la\nEbk3Z8fazDtH7sTb5XtpeZfUmPhr1yyqtRMnZQD7umJF+jMwbLjaSAe6c+7+\nfn2SRHEdQWbITxhNJYj7laCI7hJypz9jji4R9jhJ4mwB522oj3N4eSkKjZaj\nXklqQR8PEwpKGah4tRC6fOjE8Es6dJnj7RCIISdj15VN5FcEXgRAv7PlAyuP\njFX1Yo5JCrysaqZIMJbgisc6ZLKra2Cacf+1a6O7xSkv7tX0Sb0pz0e/BVvK\n0YaRGuX2qR8xCHNvwuZr8AUQnbZyG6uh/9AilJS6CzVqZxzrI76Z168uXCrN\n/YLA4A3U2QJfG2n5/YfwvkmLpCMOx1eCNXx9RdmaEt0XQ+0Cj3lmkJ2DM44k\nGERa9WlQiCOt8S70QZ6463ChumWpc+67FWiQk2M2wV7AKybNtLlHpro20PN+\nwLrVGONWiu15wqUGQ1RakUvQaqwtVSqCrbd5KRCjCBuQtMdp8u2NUNWeZ4/m\nQaERDXLgfH/WsY9l1ArU6ijI7y5SYomUNi9UeOsQXRZ+mR6i/I8GyE3/mJM1\nQs3Wsl35Ki1Ex8hEhp5KCpdPqBLv5l9EbRp+mycsV0YW1tKPN/rjFEZTmmPA\nGMkp\r\n=bfi1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCilZvxNRRCt4ysYrUEApnlzB5IhYPKgiltgBFvHidigIhANoGzNd1bnBqq+KgC0wzJXwfrUgEjpQaDwGxHPLOV2oJ"}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.0_1626389264128_0.8726396966471455"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "fetch-blob", "version": "3.1.1", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/prefer-node-protocol": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-spread": "off", "unicorn/prefer-number-properties": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2", "xo": "^0.40.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"web-streams-polyfill": "^3.0.3"}, "gitHead": "52f296c2839b9fb09cd460d453534f312914d301", "_id": "fetch-blob@3.1.1", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-Ny2ibV7UwiZnG4W5f7OS8epXMTcicQ5XcLWcmrhSiK1y6ZTk+DXIrBuIQBWs9sZafwuZqkF9wdGrEg20aeyCZQ==", "shasum": "30ad5a50ba27400005b016cee5a970d1adbf1617", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.1.1.tgz", "fileCount": 11, "unpackedSize": 24160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg8LzNCRA9TVsSAnZWagAAeYUP/1P2zEPvIeV0SN2OHB9D\ni+xDJhUUAVznrXanrTRPxvB8BvdNRjWk7juziRLz3xzz+vNaArCxqMzglfZs\nuplYAOf5OokVoZwVmzDyO2B6OKRU7bGgz8mhsy20GYe6PNRmntUsRAQ45sU/\nPYkKgjTqtqMJFHSCyKXpCN1REFZMyUb/UW7SSkx115xXUyLsQwHLluujZ+lS\n3Zi6qIMKhGMvuySzbqzPg98Y/z5BnAeMtUfvBdYN0cfrflFbHSfWg2w7gYxX\nWZxCZGQ/jcvM+KWxhvjMvIcloW8igTToGv0UJTVVItziATcx+jVXXXLNQXPx\ndGfUegv0WDxSTiaYVyYZTPX6cY0lC1H9wTbktquuYwnwoX7J/SHQK2DtEMSv\nAYAI8CFw9W7peWybBT+TS5mJ3nHnE2Iz6qpmICfYS20CLXCIfXGaMdL15VvV\ntPnp56PwrMuYAMwYW87Qffdg0XhhZ3p559zEOyRdNIX3qWNyXZyNE0YxgRhp\nZPaDinHShbB17VjoQibCvK9TGbfKNfE7EQ7UAEOC61yihPlCKarQtXY6ZLsQ\n/w/5MxT1FXshPXR+HzoWyHZMIvhFFTFS7FeqUhhEYMKfao80dRI+tVnJLUeW\ndzpL0+tCP0rSBPnN5clMJz795wGMw8Pue8d56GzjgEdovg+MCHXns0155fYU\npj/d\r\n=qLtP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCewiaDINAbbguwIuCSNWds1R+z4t55UwhU1+F8kyY2XgIgP84i4AzpTVfjF+Ynpenn3x8DHRFFSrepq2O1X6xQfsY="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.1_1626389709381_0.5683454289261796"}, "_hasShrinkwrap": false}, "3.1.2": {"name": "fetch-blob", "version": "3.1.2", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"lint": "xo test.js", "test": "npm run lint && ava", "report": "c8 --reporter json --reporter text ava", "coverage": "c8 --reporter json --reporter text ava && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "xo": {"rules": {"unicorn/prefer-node-protocol": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-spread": "off", "unicorn/prefer-number-properties": "off", "import/extensions": ["error", "always", {"ignorePackages": true}]}, "overrides": [{"files": "test.js", "rules": {"node/no-unsupported-features/es-syntax": 0, "node/no-unsupported-features/node-builtins": 0}}]}, "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2", "xo": "^0.40.1"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"web-streams-polyfill": "^3.0.3"}, "gitHead": "0b028432d0fd00841128a94817c2a81a9558463e", "_id": "fetch-blob@3.1.2", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-hunJbvy/6OLjCD0uuhLdp0mMPzP/yd2ssd1t2FCJsaA7wkWhpbp9xfuNVpv7Ll4jFhzp6T4LAupSiV9uOeg0VQ==", "shasum": "6bc438675f3851ecea51758ac91f6a1cd1bacabd", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.1.2.tgz", "fileCount": 11, "unpackedSize": 22612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg8VisCRA9TVsSAnZWagAARbwP/2AcYR8wO4o0lJlh0vOU\nFgkC0xaDUHmTTLtMqaqRdpO5e7tq7/rzPlnb5aycsHKC4WdC/AXZU9LZdSPH\nOY+eEe2jBAh2wqUVtm6ELy5XKXQKUDBxztCHcT1/RB3Y0OiSyQ3/ceBjdPm+\n/ezKPW98x0cEF3NtVWC3i99vrmf7Gn9vWp+qOYyosOsKHJLGTlXDECh0IMgQ\n64gZlhimzSNA6m+6MoYdxzD+FYOyEsPTJ9zWEWaW45+dlzjJ1Mc6WBprpTfZ\nukmZ6AwV7N46lukgLNtbnI/MeucJqDKcZpW11Lx/4phNoy5hshWIGGEUU5AU\nQtDd3IomWevK4g4y/mbsubl/5DT0YoDSUoRf/HJIjRgH5vBppJPMrDdbAI0b\ntmFobu3y39oq1xVNaAcL3VYe642h1hHTtFk6Ewq8wpqgTmhANskep3nw6GWP\nb3Ds83t70xsFPF4x0fDpGhLkGyG2tZ8ilT2AR46SwwYrSdtZC1cZRhq/KRfN\nVXecjRIgwqwJRplHfqoP/0VCPBP95ilbJ6I3mdC7GYjTRsUWIpN34vEdnOat\nSDjvMSNpKRy+D80ZGOD2Pvt9m5jX/+0+vb003MVp408q9qguZR3VurD2Wymz\nbHvUAKCFy9yX5gAl+J9VQwimDsvwVEcJgU9+ezWY2BjOB8NINwBGMNO4m0yA\nfL53\r\n=pclR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB9EzgbE6sy1mmYqMjkOke/fp2DqBmeWNTCxJmVkwxAwAiEAyAbKKvd392Uc50Rqzqh16uboQUP7XuboTcJz3mg419Q="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "xx<PERSON><PERSON>@pm.me"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.2_1626429611917_0.45141052844757557"}, "_hasShrinkwrap": false}, "3.1.3": {"name": "fetch-blob", "version": "3.1.3", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test": "ava test.js", "report": "c8 --reporter json --reporter text ava test.js", "coverage": "c8 --reporter json --reporter text ava test.js && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"ava": "^3.15.0", "c8": "^7.7.2", "codecov": "^3.8.2", "node-fetch": "^3.0.0-beta.9", "typescript": "^4.3.2"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"web-streams-polyfill": "^3.0.3"}, "types": "./index.d.ts", "gitHead": "8ab587d34080de94140b54f07168451e7d0b655e", "_id": "fetch-blob@3.1.3", "_nodeVersion": "17.0.1", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ax1Y5I9w+9+JiM+wdHkhBoxew+zG4AJ2SvAD1v1szpddUIiPERVGBxrMcB2ZqW0Y3PP8bOWYv2zqQq1Jp2kqUQ==", "shasum": "a7dca4855e39d3e3c5a1da62d4ee335c37d26012", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.1.3.tgz", "fileCount": 10, "unpackedSize": 21679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2q8PCRA9TVsSAnZWagAAvw8P/1dvURvPVzn7TYtI9Yqv\nssj05vt6pYZjc+Y4RETvQAO1JxmXTzLwPPfwm7B/JrggXKaW96j3cPDbB4UN\n0ShNIorWj6fg/Zt6mN20QcTw7znH/EvWtmYLC+89l0TbTx6G8ldnCA4wXWI/\n3NrlDOm2coTep3jhvXWJBoCuAdYFJPdJrYxvZgF3/uWBwkWJs3H4Fj7hSmRV\nxBGjVhZAEAr2TQ3FFnXKER+Thz7kK0YBu6GqJ27hA4mvQUCHRhKfXvaiAPOl\nlvu+JNJDgn6F7k3xIeUEosXnPEYWZAfX2T3zYxY/3xhX+ouA+wq8GIMo94g1\nDUmgP64aFZ+uj7oz05Syo/Gn2OnNrcLa+kBo/KG2PeGqKseDGYXEx0+opN0p\nFRb/u6daI62BzNd8iRNltrAuOTV1oSRybSGqMIhEmJGpUbo+6SF+bnpd+isV\nTiwy3rTo+zUwdv7W2d87QjWLwhhG+Hu6XCPqd0XR7XPVxTZ0vNenPMgUD51K\npvqgrPvK7dHe6y/XBEX5ZzNFSaErp3zL0u1Ud+R6O7LD2uOPwksEV6D+tAEB\n6yn1xQsRJaNEt5jz8j58JSY3lSW1MDz1aT2WgrczACJ1onYFA6j77VuELtBi\nlAgDQtRtkwCdWWdpAct+cV63Un1wuhR9Ru8LXDoESfWqlfVNsP/0MsiYk3IZ\nCrLr\r\n=LGyG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1VKims7evo28l5ynnI2i4j14mzyD0sVmuUHl3NqWOiwIgEnROsfiG1hnnBO1QTuTk3eE67Tn8YaIvEptUKpK/jO4="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.3_1635596396065_0.5964602541736606"}, "_hasShrinkwrap": false}, "3.1.4": {"name": "fetch-blob", "version": "3.1.4", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"test": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "report": "c8 --reporter json --reporter text npm run test", "coverage": "npm run report && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"@types/node": "^17.0.9", "c8": "^7.11.0", "typescript": "^4.5.4"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "types": "./index.d.ts", "gitHead": "dc295883669fbc41d39d502ffbae36f1c12f580a", "_id": "fetch-blob@3.1.4", "_nodeVersion": "17.3.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-Eq5Xv5+VlSrYWEqKrusxY1C3Hm/hjeAsCGVG3ft7pZahlUAChpGZT/Ms1WmSLnEAisEXszjzu/s+ce6HZB2VHA==", "shasum": "e8c6567f80ad7fc22fd302e7dcb72bafde9c1717", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.1.4.tgz", "fileCount": 10, "unpackedSize": 21755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6V7DCRA9TVsSAnZWagAAHSoP/i5mi90oNuCNbL/ONLpu\n7rAIrHZC8Vx4CazPB4FRyCy/Jg5G3+kgTn8FNmnmXHC20aJSsQWgw/KtRDpk\nDka6t++BBQedZaosXhRCqgVB4Yz/9NMQ6be8c0E8xsmGf3AkEvse+9c1YDtV\nlQpJT7hEpvKITSB2pg/k2AW3wXVqTIAIGOEXkY7jBE8pR+HI9lSt6JHEjqOG\n0ZgChb+48jL9a2R84dtKT5AD7MGdA61jNrk7g5vShRV2wJu9lQEGVNQy6NE0\nYjjQ6814FHi6ejGLwJHSh10qox0KHwqrQkbGM48tigkXbZHosbfhkpaE2vil\nFKWgC43mVYOqQvZL844zju8supUx0z3hbtc4A3L+Aj6+6thhlI3zigXPq00g\nyTvhdNz3GHnDIi7EUKTEW+D6dXRnS+vuLdwGxTV7fGMawq/akJYHkM4LVESd\nyOZozcZ9wBVcp1mueBGKVaMXKZYUUM6F8p6G3K8/U1if4RE91MMsw+I+0l2s\n0FHI35rz8m7kkddbPbvmSS3hY5K4Iq8/oxkzMSLOeIsQxmX8duUe3wbBcc49\ngQ+GE08Gjj4TbPhkkGBC5SZldViWORS0kQPAUdEB/hlXxAHiLSF68mWNducU\nUf6zK5ep/feX7QAkHxwhvfF5PGSo76QcAG82J7ihj1f152x+rXTjW/9M7alb\ne+DT\r\n=H1sO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqahMkZsGlpEzSz+GDq+p0C7NcPBu12C86EqSXTito4gIgfROojZP309/dmmOmXfByhKQRZBk0wpfMFlxPmAWlWvE="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.4_1642684098839_0.5539772210551002"}, "_hasShrinkwrap": false}, "3.1.5": {"name": "fetch-blob", "version": "3.1.5", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"test": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "report": "c8 --reporter json --reporter text npm run test", "coverage": "npm run report && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"@types/node": "^17.0.9", "c8": "^7.11.0", "typescript": "^4.5.4"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "types": "./index.d.ts", "gitHead": "d4632ad83e399f2b259a2a57b3f01be8bcbe7d1d", "_id": "fetch-blob@3.1.5", "_nodeVersion": "17.4.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-N64ZpKqoLejlrwkIAnb9iLSA3Vx/kjgzpcDhygcqJ2KKjky8nCgUQ+dzXtbrLaWZGZNmNfQTsiQ0weZ1svglHg==", "shasum": "0077bf5f3fcdbd9d75a0b5362f77dbb743489863", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.1.5.tgz", "fileCount": 10, "unpackedSize": 22063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMRgUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8+w/9ErDOi3NPC0SQHjd7hVlN46Ny/i7OPR9BUZgzgSsY90jKlA1u\r\nCqXlYRbxlEWomrUc6imhq7/Nmlyg20DAFwCBAE5LZhzZQkXrH8Zopd2zGHpd\r\nEos3ny3Tf7GOcJvecyYkh+2AqYeGxgwBQG8pM0VXQCtxR3BvqJ95352ZPD7m\r\nPK7gYGtJQU87lL872Kz8A+NQRmSNs93/pXfvc2vh/W8acGEqs1lWH/++smZB\r\nJ7LQCza+rJ/qUoQ9yesgsD70aTbBj9aGjZTpbA/jmnk4EopCBYR93/vzUHMZ\r\nY4Dbq5vTV6E286K8MAvKjSv2pcrN7+pGxoIv8KODRQgvS+oo6tTDUdszi1GR\r\nBPO3fBvZFIB3vvnLrf4ciDs/RTL5B0mOmAqQA4Mdi/blGBuWifhHAYpSt84a\r\nsf4d1VIzalCnCS3NXjApZkLHO8KBoLc8CQj8FXLksTT4zEk6HwaJak5ut3CU\r\nWYIu7XNiWh7X1Lt2Lvvwqi4n0qu1U05Hns1mDxzDxm8n/PH4I+6BYan1VTgz\r\nB0oudN/IkmnAIEBvw9XYl0xr6RVM4of54DMXrJAOATjzn1Frp21mkYPzNhxo\r\ndP9/p/wqEsxJ/rg/W7OQzSWKjtEm/5hs/YyI0w/miWbfMTskmzA3bjHESpKq\r\nH+aJ2ZYAbLMStBOTVe3z1ELBfq2/Z8DAPYU=\r\n=BIAx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCn/FZ6M0r96/WtHQ0Eb1AzkILuPj0+uoILdqEaZyVZEwIgcmH2kZ133Ujr20NQjcqsjc7/fbIkf16+HamXBsZMIkA="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.1.5_1647384596212_0.9138752697477188"}, "_hasShrinkwrap": false}, "3.2.0": {"name": "fetch-blob", "version": "3.2.0", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"test": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "report": "c8 --reporter json --reporter text npm run test", "coverage": "npm run report && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": "^12.20 || >= 14.13"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"@types/node": "^17.0.9", "c8": "^7.11.0", "typescript": "^4.5.4"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "types": "./index.d.ts", "gitHead": "b8c8176ba48a2118c65c9d646089e7d9fa3cb8c2", "_id": "fetch-blob@3.2.0", "_nodeVersion": "18.3.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==", "shasum": "f09b8d4bbd45adc6f0c20b7e787e793e309dcce9", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "fileCount": 10, "unpackedSize": 21755, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDudNvQVfDmUfiZpLK4mm/ygNwN9RdxPCaScl7kDlYZ8gIgclnWfUyRd+wv36Iu5Id93p+dWAnQC2zJKZbJgrw9/xA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixco7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqghQ/+I2GOv0ckYVLM31o7fHYtNSAh3S8KPRA+aTP1yVxGzfwgHsaZ\r\nXeqq4UKbhA4oVacEYzAkL1YoxUx2Ys9+vRK1lzlbW1eyx6xurAct+XjerqmV\r\naNLtfSd6d6FUwSNtPvf1V3DV66fQbjDloOWjb0VRlUovADMkZ3YcLm+NL0Pk\r\nDhSbuMIEdymuecgmHw1ryAJYAzHi4gpjcAc8fqQ/PMabEIeM9+DMy4Jujh/X\r\nHJWXlo0O6sw6a4f4izTk6G8XDj1T+qR7Bzc7dOf1pezvFn3xTSANpDTt2PKU\r\nnnxGKUWY4wjoXCISZad1uBahFrhI8CM7yK+qS/N/x6EKdJMKM93qJI45IiEz\r\nLzYV+FXMBQYzyYMdtdWshVOCCyr3gew7qi0xGluQpj5WNx31qRs2gG8Mqux5\r\nME9cFJ4lRM6KZokdEazay2NA6kuo+Ps9BnofmPLbb4ZSQNR8z2c/du25YY3w\r\nbAyKenxEgfQwpZ3ZweAqO286OSzpPrKpKKDCEHBkZGUQSWoyPfWvUblF91b2\r\n3/Yx8u7RWREjzVUPzkfNSTGNdB+TrhW4Qk73fcunWcz3L14KuDQ9gyRT8Ykb\r\ndG9EFSsvp0uH8U9EDynxqC3A7Zh5t0fW5P9S3N0zYggc/RADiT3ATcIpKCPA\r\noV44Hl8Cqt3amuKma3aC8bVkXy09du9eoBI=\r\n=tXFl\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_3.2.0_1657129530976_0.10045095451226915"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "fetch-blob", "version": "4.0.0", "description": "Blob & File implementation in Node.js, originally from node-fetch.", "main": "index.js", "type": "module", "scripts": {"test": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "report": "c8 --reporter json --reporter text npm run test", "coverage": "npm run report && codecov -f coverage/coverage-final.json", "prepublishOnly": "tsc --declaration --emitDeclarationOnly --allowJs index.js from.js"}, "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "keywords": ["blob", "file", "node-fetch"], "engines": {"node": ">=16.7"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "license": "MIT", "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "homepage": "https://github.com/node-fetch/fetch-blob#readme", "devDependencies": {"@types/node": "^16.5.0", "c8": "^7.13.0", "typescript": "^5.0.4"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "dependencies": {"node-domexception": "^1.0.0"}, "types": "./index.d.ts", "gitHead": "fc7f8e2e5c48a0a1c0fd74179f894df7709112e1", "_id": "fetch-blob@4.0.0", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-nPmnhRmpNMjYWnp9EBMGs6z5lq9RXed5W1vuZcECrsDVQInM8AMQSooVb3X183Aole60adzjWbH9qlRFWzDDTA==", "shasum": "7175579ab41933d047293c8e71dd5a49f9620a52", "tarball": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-4.0.0.tgz", "fileCount": 9, "unpackedSize": 24185, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDu7VyuolLrMnyTSJoSJF9M5GIh9O9QFBPs3OI2eDqCEAiBrPHm5LvnYlfTBk/ETPmi8V0AArXb2oVjik2fiDloIdw=="}]}, "_npmUser": {"name": "endless", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fetch-blob_4.0.0_1684410445646_0.5070895321784545"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-05-16T08:49:35.047Z", "1.0.2": "2019-05-16T08:49:35.235Z", "modified": "2023-05-18T11:47:25.919Z", "1.0.3": "2019-05-16T09:00:23.548Z", "1.0.4": "2019-09-23T10:40:58.371Z", "1.0.5": "2019-11-23T16:03:56.264Z", "1.0.6": "2020-05-21T09:06:17.568Z", "1.0.7": "2020-06-10T14:14:35.034Z", "2.0.0": "2020-06-10T19:11:32.303Z", "2.0.1": "2020-06-11T14:31:44.611Z", "2.1.0": "2020-07-28T15:54:24.787Z", "2.1.1": "2020-07-28T16:13:29.891Z", "2.1.2": "2021-04-20T15:52:09.049Z", "3.0.0-rc.0": "2021-05-07T09:49:49.451Z", "3.0.0": "2021-05-29T09:19:33.770Z", "3.0.1": "2021-06-19T09:52:38.068Z", "3.1.0": "2021-07-15T22:47:44.332Z", "3.1.1": "2021-07-15T22:55:09.550Z", "3.1.2": "2021-07-16T10:00:12.061Z", "3.1.3": "2021-10-30T12:19:56.214Z", "3.1.4": "2022-01-20T13:08:19.018Z", "3.1.5": "2022-03-15T22:49:56.424Z", "3.2.0": "2022-07-06T17:45:31.179Z", "4.0.0": "2023-05-18T11:47:25.810Z"}, "maintainers": [{"name": "endless", "email": "<EMAIL>"}, {"name": "bitinn", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Blob & File implementation in Node.js, originally from node-fetch.", "homepage": "https://github.com/node-fetch/fetch-blob#readme", "keywords": ["blob", "file", "node-fetch"], "repository": {"type": "git", "url": "git+https://github.com/node-fetch/fetch-blob.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se"}, "bugs": {"url": "https://github.com/node-fetch/fetch-blob/issues"}, "license": "MIT", "readme": "# fetch-blob\n\n[![npm version][npm-image]][npm-url]\n[![build status][ci-image]][ci-url]\n[![coverage status][codecov-image]][codecov-url]\n[![install size][install-size-image]][install-size-url]\n\nA Blob implementation in Node.js, originally from [node-fetch](https://github.com/node-fetch/node-fetch).\n\nUse the built-in [`Blob`](https://nodejs.org/docs/latest-v18.x/api/buffer.html#class-blob) in Node.js 18 and later.\n\n## Installation\n\n```sh\nnpm install fetch-blob\n```\n\n<details>\n  <summary>Upgrading from 2x to 3x</summary>\n\n  Updating from 2 to 3 should be a breeze since there is not many changes to the blob specification.\n  The major cause of a major release is coding standards.\n    - internal WeakMaps was replaced with private fields\n    - internal Buffer.from was replaced with TextEncoder/Decoder\n    - internal buffers was replaced with Uint8Arrays\n    - CommonJS was replaced with ESM\n    - The node stream returned by calling `blob.stream()` was replaced with whatwg streams\n    - (Read \"Differences from other blobs\" for more info.)\n</details>\n\n<details>\n  <summary>Differences from other Blobs</summary>\n\n  - Unlike NodeJS `buffer.Blob` (Added in: v15.7.0) and browser native Blob this polyfilled version can't be sent via PostMessage\n  - This blob version is more arbitrary, it can be constructed with blob parts that isn't a instance of itself\n  it has to look and behave as a blob to be accepted as a blob part.\n    - The benefit of this is that you can create other types of blobs that don't contain any internal data that has to be read in other ways, such as the `BlobDataItem` created in `from.js` that wraps a file path into a blob-like item and read lazily (nodejs plans to [implement this][fs-blobs] as well)\n  - The `blob.stream()` is the most noticeable differences. It returns a WHATWG stream now. to keep it as a node stream you would have to do:\n\n  ```js\n    import {Readable} from 'stream'\n    const stream = Readable.from(blob.stream())\n  ```\n</details>\n\n## Usage\n\n```js\n// Ways to import\nimport { Blob } from 'fetch-blob'\nimport { File } from 'fetch-blob/file.js'\n\nconst { Blob } = await import('fetch-blob')\n\n\n// Ways to read the blob:\nconst blob = new Blob(['hello, world'])\n\nawait blob.text()\nawait blob.arrayBuffer()\nfor await (let chunk of  blob.stream()) { ... }\nblob.stream().getReader().read()\nblob.stream().getReader({mode: 'byob'}).read(view)\n```\n\n### Blob part backed up by filesystem\n\n`fetch-blob/from.js` comes packed with tools to convert any filepath into either a Blob or a File\nIt will not read the content into memory. It will only stat the file for last modified date and file size.\n\n```js\n// The default export is sync and use fs.stat to retrieve size & last modified as a blob\nimport {File, Blob, blobFrom, blobFromSync, fileFrom, fileFromSync} from 'fetch-blob/from.js'\n\nconst fsFile = fileFromSync('./2-GiB-file.bin', 'application/octet-stream')\nconst fsBlob = await blobFrom('./2-GiB-file.mp4')\n\n// Not a 4 GiB memory snapshot, just holds references\n// points to where data is located on the disk\nconst blob = new Blob([fsFile, fsBlob, 'memory', new Uint8Array(10)])\nconsole.log(blob.size) // ~4 GiB\n```\n\n`blobFrom|blobFromSync|fileFrom|fileFromSync(path, [mimetype])`\n\n### Creating a temporary file on the disk\n(requires [FinalizationRegistry] - node v14.6)\n\nWhen using both `createTemporaryBlob` and `createTemporaryFile`\nthen you will write data to the temporary folder in their respective OS.\nThe arguments can be anything that [fsPromises.writeFile] supports. NodeJS\nv14.17.0+ also supports writing (async)Iterable streams and passing in a\nAbortSignal, so both NodeJS stream and whatwg streams are supported. When the\nfile have been written it will return a Blob/File handle with a references to\nthis temporary location on the disk. When you no longer have a references to\nthis Blob/File anymore and it have been GC then it will automatically be deleted.\n\nThis files are also unlinked upon exiting the process.\n```js\nimport { createTemporaryBlob, createTemporaryFile } from 'fetch-blob/from.js'\n\nconst req = new Request('https://httpbin.org/image/png')\nconst res = await fetch(req)\nconst type = res.headers.get('content-type')\nconst signal = req.signal\nlet blob = await createTemporaryBlob(res.body, { type, signal })\n// const file = createTemporaryBlob(res.body, 'img.png', { type, signal })\nblob = undefined // loosing references will delete the file from disk\n```\n\n- `createTemporaryBlob(data, { type, signal })`\n- `createTemporaryFile(data, FileName, { type, signal, lastModified })`\n\n### Creating Blobs backed up by other async sources\nOur Blob & File class are more generic then any other polyfills in the way that it can accept any blob look-a-like item\nAn example of this is that our blob implementation can be constructed with parts coming from [BlobDataItem](https://github.com/node-fetch/fetch-blob/blob/8ef89adad40d255a3bbd55cf38b88597c1cd5480/from.js#L32) (aka a filepath) or from [buffer.Blob](https://nodejs.org/api/buffer.html#buffer_new_buffer_blob_sources_options), It dose not have to implement all the methods - just enough that it can be read/understood by our Blob implementation. The minium requirements is that it has `Symbol.toStringTag`, `size`, `slice()`, `stream()` methods (the stream method\ncan be as simple as being a sync or async iterator that yields Uint8Arrays. If you then wrap it in our Blob or File `new Blob([blobDataItem])` then you get all of the other methods that should be implemented in a blob or file (aka: text(), arrayBuffer() and type and a ReadableStream)\n\nAn example of this could be to create a file or blob like item coming from a remote HTTP request. Or from a DataBase\n\nSee the [MDN documentation](https://developer.mozilla.org/en-US/docs/Web/API/Blob) and [tests](https://github.com/node-fetch/fetch-blob/blob/master/test.js) for more details of how to use the Blob.\n\n[npm-image]: https://flat.badgen.net/npm/v/fetch-blob\n[npm-url]: https://www.npmjs.com/package/fetch-blob\n[ci-image]: https://github.com/node-fetch/fetch-blob/workflows/CI/badge.svg\n[ci-url]: https://github.com/node-fetch/fetch-blob/actions\n[codecov-image]: https://flat.badgen.net/codecov/c/github/node-fetch/fetch-blob/master\n[codecov-url]: https://codecov.io/gh/node-fetch/fetch-blob\n[install-size-image]: https://flat.badgen.net/packagephobia/install/fetch-blob\n[install-size-url]: https://packagephobia.now.sh/result?p=fetch-blob\n[fs-blobs]: https://github.com/nodejs/node/issues/37340\n[fsPromises.writeFile]: https://nodejs.org/dist/latest-v18.x/docs/api/fs.html#fspromiseswritefilefile-data-options\n[FinalizationRegistry]: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/FinalizationRegistry\n", "readmeFilename": "README.md"}