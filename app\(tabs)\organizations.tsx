import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  SafeAreaView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { 
  ArrowLeft, 
  Crown, 
  Sword, 
  Shield, 
  Star, 
  Users,
  TrendingUp,
  Award
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { router } from 'expo-router';
import { 
  Organization, 
  OrganizationNames,
  getOrganizationRanks,
  getRankInfo,
  DarkBrotherhoodRank,
  StormcloaksRank,
  ThievesSyndicateRank,
  WinterholdAcademyRank,
  ImperialLegionRank
} from '@/types/roles-organizations';

const { width, height } = Dimensions.get('window');

function OrganizationsContent() {
  const { isDark } = useTheme();
  const { user, profile, joinOrganization, leaveOrganization, upgradeRank } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const currentOrganization = (profile as any)?.organization || 'independent';
  const currentRank = (profile as any)?.organization_rank;

  const handleJoinOrganization = async (organization: Organization) => {
    if (currentOrganization !== 'independent') {
      Alert.alert(
        '确认离开',
        `你当前是 ${OrganizationNames[currentOrganization].zh} 的成员。加入新组织将自动离开当前组织。确定继续吗？`,
        [
          { text: '取消', style: 'cancel' },
          { text: '确定', onPress: () => performJoin(organization) }
        ]
      );
    } else {
      performJoin(organization);
    }
  };

  const performJoin = async (organization: Organization) => {
    setIsLoading(true);
    try {
      const { error } = await joinOrganization(organization);
      if (error) {
        Alert.alert('错误', '加入组织失败，请重试');
      } else {
        Alert.alert('成功', `欢迎加入 ${OrganizationNames[organization].zh}！`);
      }
    } catch (error) {
      Alert.alert('错误', '加入组织失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLeaveOrganization = async () => {
    Alert.alert(
      '确认离开',
      `确定要离开 ${OrganizationNames[currentOrganization].zh} 吗？`,
      [
        { text: '取消', style: 'cancel' },
        { text: '确定', onPress: performLeave }
      ]
    );
  };

  const performLeave = async () => {
    setIsLoading(true);
    try {
      const { error } = await leaveOrganization();
      if (error) {
        Alert.alert('错误', '离开组织失败，请重试');
      } else {
        Alert.alert('成功', '已成功离开组织');
      }
    } catch (error) {
      Alert.alert('错误', '离开组织失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const getOrganizationIcon = (org: Organization) => {
    switch (org) {
      case 'dark_brotherhood':
        return <Shield size={24} color={colors.error} />;
      case 'stormcloaks':
        return <Sword size={24} color={colors.info} />;
      case 'thieves_syndicate':
        return <Star size={24} color={colors.warning} />;
      case 'winterhold_academy':
        return <Award size={24} color={colors.accent} />;
      case 'imperial_legion':
        return <Crown size={24} color={colors.secondary} />;
      default:
        return <Users size={24} color={colors.textSecondary} />;
    }
  };

  const getOrganizationDescription = (org: Organization) => {
    switch (org) {
      case 'dark_brotherhood':
        return '神秘的刺客组织，隐藏在阴影中执行任务';
      case 'stormcloaks':
        return '精英剑术宗师集团，追求极致的武道境界';
      case 'thieves_syndicate':
        return '技艺高超的盗贼公会，掌控着地下交易网络';
      case 'winterhold_academy':
        return '古老的魔法学院，研究神秘的符文与法术';
      case 'imperial_legion':
        return '帝国的正规军队，维护市场的秩序与安全';
      default:
        return '保持独立，不受任何组织约束';
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <ImageBackground
        source={{
          uri: isDark
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg'
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg',
        }}
        style={{ flex: 1, width: width, height: height }}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary,
          ]}
          style={{ flex: 1 }}
        >
          <SafeAreaView style={{ flex: 1 }}>
            {/* Header */}
            <Animated.View
              style={[
                {
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 20,
                  paddingTop: 20,
                  paddingBottom: 15,
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              <TouchableOpacity
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: colors.surfaceSecondary,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 16,
                }}
                onPress={() => router.back()}
                activeOpacity={0.8}
              >
                <ArrowLeft size={20} color={colors.text} />
              </TouchableOpacity>

              <Text
                style={{
                  fontSize: 20,
                  fontWeight: '700',
                  color: colors.text,
                  flex: 1,
                }}
              >
                组织管理
              </Text>
            </Animated.View>

            <ScrollView
              style={{ flex: 1 }}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                paddingHorizontal: 20,
                paddingBottom: 100,
              }}
            >
              <Animated.View
                style={[
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                {/* 当前组织状态 */}
                <BlurView
                  intensity={30}
                  style={{
                    borderRadius: 16,
                    overflow: 'hidden',
                    borderWidth: 1,
                    borderColor: colors.border,
                    marginBottom: 20,
                  }}
                >
                  <View style={{ padding: 20 }}>
                    <Text
                      style={{
                        fontSize: 18,
                        fontWeight: '700',
                        color: colors.text,
                        marginBottom: 16,
                      }}
                    >
                      当前状态
                    </Text>

                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                      {getOrganizationIcon(currentOrganization)}
                      <View style={{ marginLeft: 12, flex: 1 }}>
                        <Text
                          style={{
                            fontSize: 16,
                            fontWeight: '600',
                            color: colors.text,
                          }}
                        >
                          {OrganizationNames[currentOrganization].zh}
                        </Text>
                        {currentRank && (
                          <Text
                            style={{
                              fontSize: 14,
                              color: colors.textSecondary,
                              marginTop: 2,
                            }}
                          >
                            等级: {getRankInfo(currentOrganization, currentRank)?.zh || currentRank}
                          </Text>
                        )}
                      </View>
                    </View>

                    <Text
                      style={{
                        fontSize: 14,
                        color: colors.textSecondary,
                        lineHeight: 20,
                      }}
                    >
                      {getOrganizationDescription(currentOrganization)}
                    </Text>

                    {currentOrganization !== 'independent' && (
                      <TouchableOpacity
                        style={{
                          marginTop: 16,
                          paddingVertical: 10,
                          paddingHorizontal: 16,
                          borderRadius: 8,
                          backgroundColor: colors.error + '20',
                          borderWidth: 1,
                          borderColor: colors.error,
                        }}
                        onPress={handleLeaveOrganization}
                        disabled={isLoading}
                      >
                        <Text
                          style={{
                            color: colors.error,
                            fontSize: 14,
                            fontWeight: '600',
                            textAlign: 'center',
                          }}
                        >
                          离开组织
                        </Text>
                      </TouchableOpacity>
                    )}
                  </View>
                </BlurView>

                {/* 可加入的组织 */}
                <Text
                  style={{
                    fontSize: 18,
                    fontWeight: '700',
                    color: colors.text,
                    marginBottom: 16,
                  }}
                >
                  可加入的组织
                </Text>

                {Object.values(Organization)
                  .filter(org => org !== 'independent' && org !== currentOrganization)
                  .map((org) => (
                    <BlurView
                      key={org}
                      intensity={30}
                      style={{
                        borderRadius: 16,
                        overflow: 'hidden',
                        borderWidth: 1,
                        borderColor: colors.border,
                        marginBottom: 16,
                      }}
                    >
                      <TouchableOpacity
                        style={{ padding: 20 }}
                        onPress={() => handleJoinOrganization(org)}
                        disabled={isLoading}
                        activeOpacity={0.8}
                      >
                        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                          {getOrganizationIcon(org)}
                          <View style={{ marginLeft: 12, flex: 1 }}>
                            <Text
                              style={{
                                fontSize: 16,
                                fontWeight: '600',
                                color: colors.text,
                              }}
                            >
                              {OrganizationNames[org].zh}
                            </Text>
                            <Text
                              style={{
                                fontSize: 12,
                                color: colors.textSecondary,
                                marginTop: 2,
                              }}
                            >
                              {OrganizationNames[org].en}
                            </Text>
                          </View>
                          <TrendingUp size={16} color={colors.accent} />
                        </View>

                        <Text
                          style={{
                            fontSize: 14,
                            color: colors.textSecondary,
                            lineHeight: 20,
                            marginBottom: 12,
                          }}
                        >
                          {getOrganizationDescription(org)}
                        </Text>

                        <View
                          style={{
                            paddingVertical: 8,
                            paddingHorizontal: 12,
                            borderRadius: 6,
                            backgroundColor: colors.accent + '20',
                          }}
                        >
                          <Text
                            style={{
                              color: colors.accent,
                              fontSize: 12,
                              fontWeight: '600',
                              textAlign: 'center',
                            }}
                          >
                            点击加入
                          </Text>
                        </View>
                      </TouchableOpacity>
                    </BlurView>
                  ))}
              </Animated.View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

export default function OrganizationsScreen() {
  return (
    <ProtectedRoute>
      <OrganizationsContent />
    </ProtectedRoute>
  );
}
