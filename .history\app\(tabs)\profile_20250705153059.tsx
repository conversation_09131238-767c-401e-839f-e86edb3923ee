import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
  TextInput,
  Modal,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  User,
  CreditCard as Edit3,
  Save,
  X,
  Camera,
  Crown,
  Star,
  Coins,
  Award,
  MapPin,
  Calendar,
  Mail,
  Shield,
  Sword,
  Sparkles,
  TrendingUp,
  Settings,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { useToast } from '@/contexts/ToastContext';

const { width, height } = Dimensions.get('window');

function ProfileContent() {
  const { isDark } = useTheme();
  const { user, profile, updateProfile } = useAuth();
  const { showToast } = useToast();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  const [showEditModal, setShowEditModal] = useState(false);
  const [editForm, setEditForm] = useState({
    username: profile?.username || '',
    full_name: profile?.full_name || '',
    avatar_url: profile?.avatar_url || '',
    character_class: profile?.character_class || 'wanderer',
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Update form when profile changes
    if (profile) {
      setEditForm({
        username: profile.username || '',
        full_name: profile.full_name || '',
        avatar_url: profile.avatar_url || '',
        character_class: profile.character_class || 'wanderer',
      });
    }
  }, [profile]);

  const handleSaveProfile = async () => {
    if (!editForm.username.trim()) {
      Alert.alert('错误', '用户名不能为空');
      return;
    }

    setIsLoading(true);
    try {
      const { error } = await updateProfile({
        username: editForm.username.trim(),
        full_name: editForm.full_name.trim(),
        avatar_url: editForm.avatar_url.trim(),
        character_class: editForm.character_class,
      });

      if (error) {
        Alert.alert('错误', '更新失败，请重试');
      } else {
        setShowEditModal(false);
        showToast('个人资料已更新', 'success');
      }
    } catch (error) {
      Alert.alert('错误', '更新失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const getCharacterClassInfo = (characterClass: string) => {
    switch (characterClass) {
      case 'herb_merchant':
        return {
          name: '草药商人',
          icon: '🌿',
          description: '专精于草药贸易和炼金术',
          color: colors.success,
        };
      case 'lone_hunter':
        return {
          name: '独行猎手',
          icon: '🏹',
          description: '擅长探索和战斗的冒险者',
          color: colors.accent,
        };
      case 'wanderer':
      default:
        return {
          name: '流浪者',
          icon: '🎒',
          description: '自由自在的旅行者',
          color: colors.secondary,
        };
    }
  };

  const characterInfo = getCharacterClassInfo(
    profile?.character_class || 'wanderer'
  );

  const getExperienceProgress = () => {
    const currentExp = profile?.experience || 0;
    const currentLevel = profile?.level || 1;
    const expForNextLevel = currentLevel * 100; // Simple formula
    const progress = (currentExp % 100) / 100;
    return { progress, expForNextLevel, currentExp: currentExp % 100 };
  };

  const expInfo = getExperienceProgress();

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    header: {
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    titleContainer: {
      alignItems: 'center',
    },
    mainTitle: {
      fontSize: 28,
      color: colors.text,
      textAlign: 'center',
      marginBottom: 8,
      fontWeight: '700',
      textShadowColor: isDark
        ? 'rgba(255, 255, 255, 0.1)'
        : 'rgba(0, 0, 0, 0.2)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    titleUnderline: {
      width: 80,
      height: 3,
      backgroundColor: colors.accent,
      borderRadius: 2,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    editButton: {
      position: 'absolute',
      top: 20,
      right: 20,
      borderRadius: 25,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    editButtonGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
    },
    editButtonText: {
      color: colors.background,
      fontSize: 14,
      fontWeight: '600',
      marginLeft: 6,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 30,
    },
    profileSection: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    profileCard: {
      padding: 24,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: colors.border,
      alignItems: 'center',
    },
    avatarContainer: {
      position: 'relative',
      marginBottom: 20,
    },
    avatarFrame: {
      width: 120,
      height: 120,
      borderRadius: 60,
      borderWidth: 4,
      borderColor: characterInfo.color,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: isDark ? 0.5 : 0.3,
      shadowRadius: 12,
      elevation: 12,
    },
    avatarImage: {
      width: '100%',
      height: '100%',
    },
    avatarPlaceholder: {
      width: '100%',
      height: '100%',
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    characterBadge: {
      position: 'absolute',
      bottom: -5,
      right: -5,
      backgroundColor: characterInfo.color,
      borderRadius: 20,
      width: 40,
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 3,
      borderColor: colors.background,
    },
    characterEmoji: {
      fontSize: 20,
    },
    levelBadge: {
      position: 'absolute',
      top: -10,
      left: -10,
      backgroundColor: colors.accent,
      borderRadius: 15,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderWidth: 2,
      borderColor: colors.background,
    },
    levelText: {
      color: colors.background,
      fontSize: 12,
      fontWeight: '700',
    },
    profileInfo: {
      alignItems: 'center',
      marginBottom: 20,
    },
    username: {
      fontSize: 24,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 4,
    },
    fullName: {
      fontSize: 16,
      color: colors.textSecondary,
      marginBottom: 8,
    },
    characterClass: {
      fontSize: 14,
      color: characterInfo.color,
      fontWeight: '600',
      marginBottom: 4,
    },
    characterDescription: {
      fontSize: 12,
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    joinDate: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 12,
    },
    joinDateText: {
      fontSize: 12,
      color: colors.textSecondary,
      marginLeft: 6,
    },
    statsSection: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    statsCard: {
      padding: 20,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
    },
    statsHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 20,
    },
    statsTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginLeft: 8,
    },
    statsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    statItem: {
      width: '48%',
      backgroundColor: colors.surfaceSecondary,
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      marginBottom: 12,
    },
    statIcon: {
      marginBottom: 8,
    },
    statNumber: {
      fontSize: 20,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 12,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    progressSection: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    progressCard: {
      padding: 20,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
    },
    progressHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 16,
    },
    progressTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginLeft: 8,
    },
    experienceContainer: {
      marginBottom: 16,
    },
    experienceHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    experienceLabel: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.text,
    },
    experienceText: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    progressBarContainer: {
      height: 8,
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 4,
      overflow: 'hidden',
    },
    progressBarFill: {
      height: '100%',
      backgroundColor: colors.accent,
      borderRadius: 4,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      width: width * 0.9,
      maxHeight: height * 0.8,
      borderRadius: 20,
      overflow: 'hidden',
    },
    modalContent: {
      padding: 20,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: colors.text,
    },
    closeButton: {
      padding: 8,
    },
    formContainer: {
      marginBottom: 20,
    },
    inputGroup: {
      marginBottom: 16,
    },
    inputLabel: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 8,
    },
    textInput: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: colors.border,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: colors.text,
    },
    characterClassContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 8,
    },
    characterClassOption: {
      flex: 1,
      padding: 12,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: colors.border,
      alignItems: 'center',
      marginHorizontal: 4,
    },
    characterClassOptionActive: {
      borderColor: colors.accent,
      backgroundColor: colors.accent + '20',
    },
    characterClassEmoji: {
      fontSize: 24,
      marginBottom: 4,
    },
    characterClassName: {
      fontSize: 12,
      fontWeight: '600',
      color: colors.text,
      textAlign: 'center',
    },
    saveButton: {
      borderRadius: 12,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    saveButtonDisabled: {
      opacity: 0.7,
    },
    saveButtonGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      paddingHorizontal: 24,
    },
    saveButtonText: {
      color: colors.background,
      fontSize: 18,
      fontWeight: '700',
      marginLeft: 8,
    },
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const characterClasses = [
    { id: 'wanderer', name: '流浪者', emoji: '🎒' },
    { id: 'herb_merchant', name: '草药商人', emoji: '🌿' },
    { id: 'lone_hunter', name: '独行猎手', emoji: '🏹' },
  ];

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{
          uri: isDark
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg'
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg',
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary,
          ]}
          style={dynamicStyles.overlay}
        >
          <SafeAreaView style={dynamicStyles.safeArea}>
            {/* Header */}
            <Animated.View
              style={[
                dynamicStyles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }],
                },
              ]}
            >
              <View style={dynamicStyles.titleContainer}>
                <Text style={dynamicStyles.mainTitle}>个人资料</Text>
                <View style={dynamicStyles.titleUnderline} />
                <Text style={dynamicStyles.subtitle}>管理您的角色信息</Text>
              </View>

              {/* Edit Button */}
              <TouchableOpacity
                style={dynamicStyles.editButton}
                onPress={() => setShowEditModal(true)}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={[colors.accent, colors.warning]}
                  style={dynamicStyles.editButtonGradient}
                >
                  <Edit3 size={16} color={colors.background} />
                  <Text style={dynamicStyles.editButtonText}>编辑</Text>
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>

            <ScrollView
              style={dynamicStyles.scrollView}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={dynamicStyles.scrollContent}
            >
              {/* Profile Section */}
              <Animated.View
                style={[
                  dynamicStyles.profileSection,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                <BlurView intensity={30} style={dynamicStyles.profileCard}>
                  <View style={dynamicStyles.avatarContainer}>
                    <View style={dynamicStyles.avatarFrame}>
                      {profile?.avatar_url ? (
                        <Image
                          source={{ uri: profile.avatar_url }}
                          style={dynamicStyles.avatarImage}
                          resizeMode="cover"
                        />
                      ) : (
                        <View style={dynamicStyles.avatarPlaceholder}>
                          <User size={40} color={colors.textSecondary} />
                        </View>
                      )}
                    </View>
                    <View style={dynamicStyles.characterBadge}>
                      <Text style={dynamicStyles.characterEmoji}>
                        {characterInfo.icon}
                      </Text>
                    </View>
                    <View style={dynamicStyles.levelBadge}>
                      <Text style={dynamicStyles.levelText}>
                        Lv.{profile?.level || 1}
                      </Text>
                    </View>
                  </View>

                  <View style={dynamicStyles.profileInfo}>
                    <Text style={dynamicStyles.username}>
                      {profile?.username || '未设置'}
                    </Text>
                    <Text style={dynamicStyles.fullName}>
                      {profile?.full_name || '未设置姓名'}
                    </Text>
                    <Text style={dynamicStyles.characterClass}>
                      {characterInfo.name}
                    </Text>
                    <Text style={dynamicStyles.characterDescription}>
                      {characterInfo.description}
                    </Text>

                    <View style={dynamicStyles.joinDate}>
                      <Calendar size={14} color={colors.textSecondary} />
                      <Text style={dynamicStyles.joinDateText}>
                        加入时间:{' '}
                        {profile?.created_at
                          ? formatDate(profile.created_at)
                          : '未知'}
                      </Text>
                    </View>
                  </View>
                </BlurView>
              </Animated.View>

              {/* Stats Section */}
              <Animated.View
                style={[
                  dynamicStyles.statsSection,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                <BlurView intensity={30} style={dynamicStyles.statsCard}>
                  <View style={dynamicStyles.statsHeader}>
                    <Award size={24} color={colors.accent} />
                    <Text style={dynamicStyles.statsTitle}>角色属性</Text>
                  </View>
                  <View style={dynamicStyles.statsGrid}>
                    <View style={dynamicStyles.statItem}>
                      <Crown
                        size={24}
                        color={colors.accent}
                        style={dynamicStyles.statIcon}
                      />
                      <Text style={dynamicStyles.statNumber}>
                        {profile?.level || 1}
                      </Text>
                      <Text style={dynamicStyles.statLabel}>等级</Text>
                    </View>
                    <View style={dynamicStyles.statItem}>
                      <Coins
                        size={24}
                        color={colors.warning}
                        style={dynamicStyles.statIcon}
                      />
                      <Text style={dynamicStyles.statNumber}>
                        {profile?.gold || 100}
                      </Text>
                      <Text style={dynamicStyles.statLabel}>金币</Text>
                    </View>
                    <View style={dynamicStyles.statItem}>
                      <Star
                        size={24}
                        color={colors.secondary}
                        style={dynamicStyles.statIcon}
                      />
                      <Text style={dynamicStyles.statNumber}>
                        {profile?.reputation || 0}
                      </Text>
                      <Text style={dynamicStyles.statLabel}>声望</Text>
                    </View>
                    <View style={dynamicStyles.statItem}>
                      <Sparkles
                        size={24}
                        color={colors.success}
                        style={dynamicStyles.statIcon}
                      />
                      <Text style={dynamicStyles.statNumber}>
                        {profile?.experience || 0}
                      </Text>
                      <Text style={dynamicStyles.statLabel}>经验值</Text>
                    </View>
                  </View>
                </BlurView>
              </Animated.View>

              {/* Progress Section */}
              <Animated.View
                style={[
                  dynamicStyles.progressSection,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                <BlurView intensity={30} style={dynamicStyles.progressCard}>
                  <View style={dynamicStyles.progressHeader}>
                    <TrendingUp size={24} color={colors.success} />
                    <Text style={dynamicStyles.progressTitle}>成长进度</Text>
                  </View>

                  <View style={dynamicStyles.experienceContainer}>
                    <View style={dynamicStyles.experienceHeader}>
                      <Text style={dynamicStyles.experienceLabel}>
                        经验值进度
                      </Text>
                      <Text style={dynamicStyles.experienceText}>
                        {expInfo.currentExp}/{expInfo.expForNextLevel}
                      </Text>
                    </View>
                    <View style={dynamicStyles.progressBarContainer}>
                      <View
                        style={[
                          dynamicStyles.progressBarFill,
                          { width: `${expInfo.progress * 100}%` },
                        ]}
                      />
                    </View>
                  </View>
                </BlurView>
              </Animated.View>
            </ScrollView>

            {/* Edit Profile Modal */}
            <Modal
              visible={showEditModal}
              animationType="slide"
              transparent={true}
              onRequestClose={() => setShowEditModal(false)}
            >
              <View style={dynamicStyles.modalOverlay}>
                <BlurView intensity={50} style={dynamicStyles.modalContainer}>
                  <ScrollView
                    style={dynamicStyles.modalContent}
                    showsVerticalScrollIndicator={false}
                  >
                    <View style={dynamicStyles.modalHeader}>
                      <Text style={dynamicStyles.modalTitle}>编辑个人资料</Text>
                      <TouchableOpacity
                        style={dynamicStyles.closeButton}
                        onPress={() => setShowEditModal(false)}
                      >
                        <X size={24} color={colors.text} />
                      </TouchableOpacity>
                    </View>

                    <View style={dynamicStyles.formContainer}>
                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>用户名 *</Text>
                        <TextInput
                          style={dynamicStyles.textInput}
                          placeholder="输入用户名"
                          placeholderTextColor={colors.textSecondary}
                          value={editForm.username}
                          onChangeText={(text) =>
                            setEditForm({ ...editForm, username: text })
                          }
                        />
                      </View>

                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>姓名</Text>
                        <TextInput
                          style={dynamicStyles.textInput}
                          placeholder="输入真实姓名"
                          placeholderTextColor={colors.textSecondary}
                          value={editForm.full_name}
                          onChangeText={(text) =>
                            setEditForm({ ...editForm, full_name: text })
                          }
                        />
                      </View>

                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>头像链接</Text>
                        <TextInput
                          style={dynamicStyles.textInput}
                          placeholder="输入头像图片URL"
                          placeholderTextColor={colors.textSecondary}
                          value={editForm.avatar_url}
                          onChangeText={(text) =>
                            setEditForm({ ...editForm, avatar_url: text })
                          }
                        />
                      </View>

                      <View style={dynamicStyles.inputGroup}>
                        <Text style={dynamicStyles.inputLabel}>角色职业</Text>
                        <View style={dynamicStyles.characterClassContainer}>
                          {characterClasses.map((charClass) => (
                            <TouchableOpacity
                              key={charClass.id}
                              style={[
                                dynamicStyles.characterClassOption,
                                editForm.character_class === charClass.id &&
                                  dynamicStyles.characterClassOptionActive,
                              ]}
                              onPress={() =>
                                setEditForm({
                                  ...editForm,
                                  character_class: charClass.id,
                                })
                              }
                            >
                              <Text style={dynamicStyles.characterClassEmoji}>
                                {charClass.emoji}
                              </Text>
                              <Text style={dynamicStyles.characterClassName}>
                                {charClass.name}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </View>
                      </View>

                      <TouchableOpacity
                        style={[
                          dynamicStyles.saveButton,
                          isLoading && dynamicStyles.saveButtonDisabled,
                        ]}
                        onPress={handleSaveProfile}
                        disabled={isLoading}
                      >
                        <LinearGradient
                          colors={[colors.success, colors.secondary]}
                          style={dynamicStyles.saveButtonGradient}
                        >
                          <Save size={20} color={colors.background} />
                          <Text style={dynamicStyles.saveButtonText}>
                            {isLoading ? '保存中...' : '保存更改'}
                          </Text>
                        </LinearGradient>
                      </TouchableOpacity>
                    </View>
                  </ScrollView>
                </BlurView>
              </View>
            </Modal>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

export default function ProfileScreen() {
  return (
    <ProtectedRoute>
      <ProfileContent />
    </ProtectedRoute>
  );
}
