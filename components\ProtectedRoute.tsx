import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { getColors } from '@/constants/Colors';
import { router } from 'expo-router';
import { LogIn } from 'lucide-react-native';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const { isDark } = useTheme();
  const colors = getColors(isDark);

  React.useEffect(() => {
    if (!loading && !user) {
      // Don't auto-redirect, let user choose when to go to login
    }
  }, [user, loading]);

  const handleLoginPress = () => {
    console.log('Login button pressed, navigating to login...');
    try {
      // 方法1: 尝试使用 dismissAll 清除导航栈，然后导航到登录页面
      router.dismissAll();
      router.replace('/login');
    } catch (error) {
      console.error('DismissAll + Replace error:', error);
    }
  };

  if (!user) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.content}>
          <LogIn size={64} color={colors.textSecondary} style={styles.icon} />
          <Text style={[styles.title, { color: colors.text }]}>需要登录</Text>
          <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
            请登录以访问此功能
          </Text>
          <TouchableOpacity
            style={[styles.loginButton, { backgroundColor: colors.primary }]}
            onPress={handleLoginPress}
            activeOpacity={0.8}
          >
            <LogIn size={20} color={colors.background} />
            <Text
              style={[styles.loginButtonText, { color: colors.background }]}
            >
              前往登录
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return <>{children}</>;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
  },
  icon: {
    marginBottom: 24,
    opacity: 0.6,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  loginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loginButtonText: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  text: {
    fontSize: 18,
    fontWeight: '600',
  },
});
