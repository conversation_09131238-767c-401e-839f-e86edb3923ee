0 verbose cli E:\Node\node.exe e:\Node\node_modules\npm\bin\npm-cli.js
1 info using npm@10.7.0
2 info using node@v20.15.1
3 silly config:load:file:E:\Nvm\nvm\v20.15.1\node_modules\npm\npmrc
4 silly config:load:file:D:\.0000\scarboroughfair\.npmrc
5 silly config:load:file:C:\Users\<USER>\.npmrc
6 silly config:load:file:e:\Node\etc\npmrc
7 verbose title npm create vite@latest scarborough-market-web react-ts
8 verbose argv "create" "vite@latest" "scarborough-market-web" "--template" "react-ts"
9 verbose logfile logs-max:10 dir:d:\.0000\scarboroughfair\_logs\2025-07-05T10_06_21_486Z-
10 verbose logfile d:\.0000\scarboroughfair\_logs\2025-07-05T10_06_21_486Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly logfile done cleaning log files
13 http fetch GET 200 https://registry.npmjs.org/create-vite 4082ms (cache revalidated)
14 silly idealTree buildDeps
15 silly fetch manifest create-vite@7.0.0
16 http fetch GET 200 https://registry.npmjs.org/create-vite 342ms (cache miss)
17 silly placeDep ROOT create-vite@7.0.0 OK for:  want: 7.0.0
18 warn EBADENGINE Unsupported engine {
18 warn EBADENGINE   package: 'create-vite@7.0.0',
18 warn EBADENGINE   required: { node: '^20.19.0 || >=22.12.0' },
18 warn EBADENGINE   current: { node: 'v20.15.1', npm: '10.7.0' }
18 warn EBADENGINE }
19 silly reify moves {}
20 silly audit bulk request { 'create-vite': [ '7.0.0' ] }
21 silly tarball no local data for create-vite@https://registry.npmjs.org/create-vite/-/create-vite-7.0.0.tgz. Extracting by manifest.
22 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 600ms
23 silly audit report {}
24 http fetch GET 200 https://registry.npmjs.org/create-vite/-/create-vite-7.0.0.tgz 4581ms (cache miss)
25 verbose exit 0
26 info ok
