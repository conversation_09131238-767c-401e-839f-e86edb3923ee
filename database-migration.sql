-- 数据库迁移脚本
-- 为 profiles 表添加缺失的字段

-- 添加 bio 字段（个人简介）
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bio TEXT;
COMMENT ON COLUMN profiles.bio IS '用户个人简介';

-- 添加 location 字段（所在地）
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS location TEXT;
COMMENT ON COLUMN profiles.location IS '用户所在地';

-- 添加 avatar_url 字段（头像URL）
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS avatar_url TEXT;
COMMENT ON COLUMN profiles.avatar_url IS '用户头像URL';

-- 确保 updated_at 字段存在
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();
COMMENT ON COLUMN profiles.updated_at IS '最后更新时间';

-- 创建更新时间的触发器（可选）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 profiles 表创建触发器
DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 验证表结构
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'profiles'
ORDER BY ordinal_position;
