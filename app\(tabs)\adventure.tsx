import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Compass, BookOpen, SquareCheck as CheckSquare, Leaf, ChevronRight } from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { router } from 'expo-router';

const { width, height } = Dimensions.get('window');

function AdventureContent() {
  const { isDark } = useTheme();
  const { user, profile } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const adventureOptions = [
    {
      id: 'exploration',
      title: '探索世界',
      subtitle: '发现隐藏的宝藏与秘密',
      icon: Compass,
      color: colors.accent,
      route: '/exploration',
    },
    {
      id: 'tasks',
      title: '任务大厅',
      subtitle: '完成任务获得奖励',
      icon: CheckSquare,
      color: colors.primary,
      route: '/tasks',
    },
    {
      id: 'diary',
      title: '草药日记',
      subtitle: '记录自然的智慧',
      icon: BookOpen,
      color: colors.success,
      route: '/diary',
    },
  ];

  const handleOptionPress = (route: string) => {
    router.push(route as any);
  };

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    header: {
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    adventurerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 20,
      width: '100%',
    },
    adventurerFrame: {
      width: 70,
      height: 70,
      borderRadius: 35,
      borderWidth: 3,
      borderColor: colors.accent,
      overflow: 'hidden',
      position: 'relative',
      marginRight: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.5 : 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    adventurerImage: {
      width: '100%',
      height: '100%',
    },
    adventureBadge: {
      position: 'absolute',
      bottom: -5,
      right: -5,
      backgroundColor: colors.accent,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.background,
    },
    adventurerInfo: {
      flex: 1,
    },
    adventurerTitle: {
      fontSize: 18,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 2,
    },
    adventurerSubtitle: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    titleContainer: {
      alignItems: 'center',
    },
    mainTitle: {
      fontSize: 28,
      color: colors.text,
      textAlign: 'center',
      marginBottom: 8,
      fontWeight: '700',
      textShadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.2)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    titleUnderline: {
      width: 80,
      height: 3,
      backgroundColor: colors.accent,
      borderRadius: 2,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 30,
    },
    optionsContainer: {
      paddingHorizontal: 20,
    },
    optionCard: {
      marginBottom: 16,
      borderRadius: 16,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    optionContent: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 20,
    },
    optionIconContainer: {
      width: 60,
      height: 60,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    optionInfo: {
      flex: 1,
    },
    optionTitle: {
      fontSize: 18,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 4,
    },
    optionSubtitle: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    chevronContainer: {
      padding: 8,
    },
    statsContainer: {
      paddingHorizontal: 20,
      marginTop: 20,
    },
    statsCard: {
      padding: 20,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
    },
    statsHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 16,
    },
    statsTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginLeft: 8,
    },
    statsGrid: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    statItem: {
      alignItems: 'center',
    },
    statNumber: {
      fontSize: 20,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    statDivider: {
      width: 1,
      height: 30,
      backgroundColor: colors.border,
    },
  });

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{ 
          uri: isDark 
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary
          ]}
          style={dynamicStyles.overlay}
        >
          <SafeAreaView style={dynamicStyles.safeArea}>
            {/* Header */}
            <Animated.View 
              style={[
                dynamicStyles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
                }
              ]}
            >
              <View style={dynamicStyles.adventurerContainer}>
                <View style={dynamicStyles.adventurerFrame}>
                  <Image
                    source={{ uri: profile?.avatar_url || 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                    style={dynamicStyles.adventurerImage}
                    resizeMode="cover"
                  />
                  <View style={dynamicStyles.adventureBadge}>
                    <Compass size={16} color={colors.background} />
                  </View>
                </View>
                <View style={dynamicStyles.adventurerInfo}>
                  <Text style={dynamicStyles.adventurerTitle}>冒险之旅</Text>
                  <Text style={dynamicStyles.adventurerSubtitle}>探索、发现、成长</Text>
                </View>
              </View>

              <View style={dynamicStyles.titleContainer}>
                <Text style={dynamicStyles.mainTitle}>冒险中心</Text>
                <View style={dynamicStyles.titleUnderline} />
                <Text style={dynamicStyles.subtitle}>开启您的奇幻冒险</Text>
              </View>
            </Animated.View>

            <ScrollView 
              style={dynamicStyles.scrollView} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={dynamicStyles.scrollContent}
            >
              {/* Adventure Options */}
              <Animated.View 
                style={[
                  dynamicStyles.optionsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                {adventureOptions.map((option) => (
                  <BlurView key={option.id} intensity={30} style={dynamicStyles.optionCard}>
                    <TouchableOpacity 
                      style={dynamicStyles.optionContent}
                      onPress={() => handleOptionPress(option.route)}
                      activeOpacity={0.8}
                    >
                      <View style={[
                        dynamicStyles.optionIconContainer,
                        { backgroundColor: option.color + '20' }
                      ]}>
                        <option.icon size={32} color={option.color} />
                      </View>
                      <View style={dynamicStyles.optionInfo}>
                        <Text style={dynamicStyles.optionTitle}>{option.title}</Text>
                        <Text style={dynamicStyles.optionSubtitle}>{option.subtitle}</Text>
                      </View>
                      <View style={dynamicStyles.chevronContainer}>
                        <ChevronRight size={24} color={colors.textSecondary} />
                      </View>
                    </TouchableOpacity>
                  </BlurView>
                ))}
              </Animated.View>

              {/* Adventure Stats */}
              <Animated.View 
                style={[
                  dynamicStyles.statsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <BlurView intensity={30} style={dynamicStyles.statsCard}>
                  <View style={dynamicStyles.statsHeader}>
                    <Leaf size={24} color={colors.success} />
                    <Text style={dynamicStyles.statsTitle}>冒险统计</Text>
                  </View>
                  <View style={dynamicStyles.statsGrid}>
                    <View style={dynamicStyles.statItem}>
                      <Text style={dynamicStyles.statNumber}>12</Text>
                      <Text style={dynamicStyles.statLabel}>已完成任务</Text>
                    </View>
                    <View style={dynamicStyles.statDivider} />
                    <View style={dynamicStyles.statItem}>
                      <Text style={dynamicStyles.statNumber}>5</Text>
                      <Text style={dynamicStyles.statLabel}>探索区域</Text>
                    </View>
                    <View style={dynamicStyles.statDivider} />
                    <View style={dynamicStyles.statItem}>
                      <Text style={dynamicStyles.statNumber}>28</Text>
                      <Text style={dynamicStyles.statLabel}>草药发现</Text>
                    </View>
                  </View>
                </BlurView>
              </Animated.View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

export default function AdventureScreen() {
  return (
    <ProtectedRoute>
      <AdventureContent />
    </ProtectedRoute>
  );
}