{"_id": "resolve-pkg-maps", "name": "resolve-pkg-maps", "dist-tags": {"latest": "1.0.0"}, "versions": {"1.0.0": {"name": "resolve-pkg-maps", "version": "1.0.0", "description": "Resolve package.json exports & imports maps", "keywords": ["node.js", "package.json", "exports", "imports"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/privatenumber/resolve-pkg-maps.git"}, "funding": "https://github.com/privatenumber/resolve-pkg-maps?sponsor=1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.cts", "exports": {"require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "imports": {"#resolve-pkg-maps": {"types": "./src/index.ts", "development": "./src/index.ts", "default": "./dist/index.mjs"}}, "gitHead": "6d788049958514dbce52ef2bebc633f0ad336b51", "bugs": {"url": "https://github.com/privatenumber/resolve-pkg-maps/issues"}, "homepage": "https://github.com/privatenumber/resolve-pkg-maps#readme", "_id": "resolve-pkg-maps@1.0.0", "_nodeVersion": "16.15.1", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==", "shasum": "616b3dc2c57056b5588c31cdf4b3d64db133720f", "tarball": "https://registry.npmjs.org/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz", "fileCount": 7, "unpackedSize": 15037, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH2nKtsnR3yCO7KnmVsfDC4c5a3A18gA08qdTNW+Ejv3AiB+22ZyL+Q7To1NB3WHxFKXQx5kR88TDNbccWpDf+ChvA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjme3KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmre0w//cF5pQkE8N8H1JqKTPUqc2TK1T7l4vNDPM2T1nbx9JEpfJxXd\r\nLHWpJF00J7XbdjtifzbcqpAQ6pHgc8igksn+yvh4S2G9a/sDyqvq1TBuVlet\r\nStcpoeU5mpV1urmWxMDZe6KRREbG681cpqi3tPYhY2BF740cVSotVTpiVzwq\r\n9vKM1f1tP2SmDJThvsf8JJT8FV0Vp5Xn+FtVyD3y1+ksR4a2WVLpw3wH9nTg\r\nidMW+y/srk+aQLJ4+WeFYBT1BehPSKGDi36fZmo4KTYnrbHa5xEpL8HGoiqK\r\nS9TcyHyROLj8xcLbC8RcU6WQI/yN6qkOrbsKVzIeF0uBMhBNt3NEHLBDC0xd\r\nRK52dADLMPeG6qZK7scYIYdoY+xMPM1qFpEFIvq2p2neVyu5EALnQEqrfxiG\r\nIR4P907S2TSaGSMZ6j+n4zi/T+bYa08WDgU74LMz2BPxb4k0Ez4A3ImWVCuA\r\nv14NjYszV/If1HSgzp7KqRlta27AR6NkHpCO5G5rhkSTU2X17DjoNfigJQbb\r\niqh4lDJCgRm9bI1oHH/FHXMdJTWs4XwAjzSugiZODNv/mxVcbx2tphdVs5QA\r\n2yt0pwYEK5+QTe0pzCT0DvPGcGx6GhJUX/6avgGJMFD+SJY8VEwxHMbRObJK\r\n05zyy+b54JRb/ttkVcA2QDc5WSHbBEvyL/g=\r\n=R7dP\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-pkg-maps_1.0.0_1671032266006_0.7710089388051458"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-12-14T15:37:46.006Z", "1.0.0": "2022-12-14T15:37:46.218Z", "modified": "2022-12-14T15:37:46.383Z"}, "maintainers": [{"name": "<PERSON>rok<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Resolve package.json exports & imports maps", "homepage": "https://github.com/privatenumber/resolve-pkg-maps#readme", "keywords": ["node.js", "package.json", "exports", "imports"], "repository": {"type": "git", "url": "git+https://github.com/privatenumber/resolve-pkg-maps.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/privatenumber/resolve-pkg-maps/issues"}, "license": "MIT", "readme": "# resolve-pkg-maps\n\nUtils to resolve `package.json` subpath & conditional [`exports`](https://nodejs.org/api/packages.html#exports)/[`imports`](https://nodejs.org/api/packages.html#imports) in resolvers.\n\nImplements the [ESM resolution algorithm](https://nodejs.org/api/esm.html#resolver-algorithm-specification). Tested [against Node.js](/tests/) for accuracy.\n\n<sub>Support this project by ⭐️ starring and sharing it. [Follow me](https://github.com/privatenumber) to see what other cool projects I'm working on! ❤️</sub>\n\n## Usage\n\n### Resolving `exports`\n\n_utils/package.json_\n```json5\n{\n    // ...\n    \"exports\": {\n        \"./reverse\": {\n            \"require\": \"./file.cjs\",\n            \"default\": \"./file.mjs\"\n        }\n    },\n    // ...\n}\n```\n\n```ts\nimport { resolveExports } from 'resolve-pkg-maps'\n\nconst [packageName, packageSubpath] = parseRequest('utils/reverse')\n\nconst resolvedPaths: string[] = resolveExports(\n    getPackageJson(packageName).exports,\n    packageSubpath,\n    ['import', ...otherConditions]\n)\n// => ['./file.mjs']\n```\n\n### Resolving `imports`\n\n_package.json_\n```json5\n{\n    // ...\n    \"imports\": {\n        \"#supports-color\": {\n            \"node\": \"./index.js\",\n            \"default\": \"./browser.js\"\n        }\n    },\n    // ...\n}\n```\n\n```ts\nimport { resolveImports } from 'resolve-pkg-maps'\n\nconst resolvedPaths: string[] = resolveImports(\n    getPackageJson('.').imports,\n    '#supports-color',\n    ['node', ...otherConditions]\n)\n// => ['./index.js']\n```\n\n## API\n\n### resolveExports(exports, request, conditions)\n\nReturns: `string[]`\n\nResolves the `request` based on `exports` and `conditions`. Returns an array of paths (e.g. in case a fallback array is matched).\n\n#### exports\n\nType:\n```ts\ntype Exports = PathOrMap | readonly PathOrMap[]\n\ntype PathOrMap = string | PathConditionsMap\n\ntype PathConditionsMap = {\n    [condition: string]: PathConditions | null\n}\n```\n\nThe [`exports` property](https://nodejs.org/api/packages.html#exports) value in `package.json`.\n\n#### request\n\nType: `string`\n\nThe package subpath to resolve. Assumes a normalized path is passed in (eg. [repeating slashes `//`](https://github.com/nodejs/node/issues/44316)).\n\nIt _should not_ start with `/` or `./`.\n\nExample: if the full import path is `some-package/subpath/file`, the request is `subpath/file`.\n\n\n#### conditions\n\nType: `readonly string[]`\n\nAn array of conditions to use when resolving the request. For reference, Node.js's default conditions are [`['node', 'import']`](https://nodejs.org/api/esm.html#:~:text=defaultConditions%20is%20the%20conditional%20environment%20name%20array%2C%20%5B%22node%22%2C%20%22import%22%5D.).\n\nThe order of this array does not matter; the order of condition keys in the export map is what matters instead.\n\nNot all conditions in the array need to be met to resolve the request. It just needs enough to resolve to a path.\n\n---\n\n### resolveImports(imports, request, conditions)\n\nReturns: `string[]`\n\nResolves the `request` based on `imports` and `conditions`. Returns an array of paths (e.g. in case a fallback array is matched).\n\n#### imports\n\nType:\n```ts\ntype Imports = {\n    [condition: string]: PathOrMap | readonly PathOrMap[] | null\n}\n\ntype PathOrMap = string | Imports\n```\n\nThe [`imports` property](https://nodejs.org/api/packages.html#imports) value in `package.json`.\n\n\n#### request\n\nType: `string`\n\nThe request resolve. Assumes a normalized path is passed in (eg. [repeating slashes `//`](https://github.com/nodejs/node/issues/44316)).\n\n> **Note:** In Node.js, imports resolutions are limited to requests prefixed with `#`. However, this package does not enforce that requirement in case you want to add custom support for non-prefixed entries.\n\n#### conditions\n\nType: `readonly string[]`\n\nAn array of conditions to use when resolving the request. For reference, Node.js's default conditions are [`['node', 'import']`](https://nodejs.org/api/esm.html#:~:text=defaultConditions%20is%20the%20conditional%20environment%20name%20array%2C%20%5B%22node%22%2C%20%22import%22%5D.).\n\nThe order of this array does not matter; the order of condition keys in the import map is what matters instead.\n\nNot all conditions in the array need to be met to resolve the request. It just needs enough to resolve to a path.\n\n---\n\n### Errors\n\n#### `ERR_PACKAGE_PATH_NOT_EXPORTED`\n - If the request is not exported by the export map\n\n#### `ERR_PACKAGE_IMPORT_NOT_DEFINED`\n  - If the request is not defined by the import map\n\n#### `ERR_INVALID_PACKAGE_CONFIG`\n\n  - If an object contains properties that are both paths and conditions (e.g. start with and without `.`)\n  - If an object contains numeric properties \n  \n#### `ERR_INVALID_PACKAGE_TARGET`\n  - If a resolved exports path is not a valid path (e.g. not relative or has protocol)\n  - If a resolved path includes `..` or `node_modules`\n  - If a resolved path is a type that cannot be parsed\n\n## FAQ\n\n### Why do the APIs return an array of paths?\n\n`exports`/`imports` supports passing in a [fallback array](https://github.com/jkrems/proposal-pkg-exports/#:~:text=Whenever%20there%20is,to%20new%20cases.) to provide fallback paths if the previous one is invalid:\n\n```json5\n{\n    \"exports\": {\n        \"./feature\": [\n            \"./file.js\",\n            \"./fallback.js\"\n        ]\n    }\n}\n```\n\nNode.js's implementation [picks the first valid path (without attempting to resolve it)](https://github.com/nodejs/node/issues/44282#issuecomment-1220151715) and throws an error if it can't be resolved. Node.js's fallback array is designed for [forward compatibility with features](https://github.com/jkrems/proposal-pkg-exports/#:~:text=providing%20forwards%20compatiblitiy%20for%20new%20features) (e.g. protocols) that can be immediately/inexpensively validated:\n\n```json5\n{\n    \"exports\": {\n        \"./core-polyfill\": [\"std:core-module\", \"./core-polyfill.js\"]\n    }\n}\n```\n\nHowever, [Webpack](https://webpack.js.org/guides/package-exports/#alternatives) and [TypeScript](https://github.com/microsoft/TypeScript/blob/71e852922888337ef51a0e48416034a94a6c34d9/src/compiler/moduleSpecifiers.ts#L695) have deviated from this behavior and attempts to resolve the next path if a path cannot be resolved.\n\nBy returning an array of matched paths instead of just the first one, the user can decide which behavior to adopt.\n\n### How is it different from [`resolve.exports`](https://github.com/lukeed/resolve.exports)?\n\n`resolve.exports` only resolves `exports`, whereas this package resolves both `exports` & `imports`. This comparison will only cover resolving `exports`.\n\n- Despite it's name, `resolve.exports` handles more than just `exports`. It takes in the entire `package.json` object to handle resolving `.` and [self-references](https://nodejs.org/api/packages.html#self-referencing-a-package-using-its-name). This package only accepts `exports`/`imports` maps from `package.json` and is scoped to only resolving what's defined in the maps.\n\n- `resolve.exports` accepts the full request (e.g. `foo/bar`), whereas this package only accepts the requested subpath (e.g. `bar`).\n\n- `resolve.exports` only returns the first result in a fallback array. This package returns an array of results for the user to decide how to handle it.\n\n- `resolve.exports` supports [subpath folder mapping](https://nodejs.org/docs/latest-v16.x/api/packages.html#subpath-folder-mappings) (deprecated in Node.js v16 & removed in v17) but seems to [have a bug](https://github.com/lukeed/resolve.exports/issues/7). This package does not support subpath folder mapping because Node.js has removed it in favor of using subpath patterns.\n\n- Neither resolvers rely on a file-system\n\nThis package also addresses many of the bugs in `resolve.exports`, demonstrated in [this test](/tests/exports/compare-resolve.exports.ts).\n", "readmeFilename": "README.md"}