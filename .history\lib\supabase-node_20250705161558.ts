import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error(
    '❌ 请配置 SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY 环境变量'
  );
}

export const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);
