{"_id": "@esbuild/openbsd-arm64", "_rev": "14-42fd1ae85e1aa8f4e729753690a4fa61", "name": "@esbuild/openbsd-arm64", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.0.1": {"name": "@esbuild/openbsd-arm64", "version": "0.0.1", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.0.1", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "3ec74f7714d53b7069710d0665fd629e2d43d840", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.0.1.tgz", "fileCount": 2, "integrity": "sha512-87kxwOiAQ6jF2Varulbu3BvILT203nSsdOvA24MEv+itmeC0kBphunvJANgcTlg8x5lf2wtiz8+mnc0Ai/qoOg==", "signatures": [{"sig": "MEYCIQDIrcvxL2rMkhfYFfUe/YsPlbbjbGFWC6xAPLUQRBuMiAIhAOsAMDCtS4tezjNvR0wFPY7HnnLpGb/sLuZ4rE3eTs1Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 532}, "engines": {"node": ">=12"}, "gitHead": "b7220009d0fde2e89917ed3ea97c6b8ca04adbf1", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.0.1_1719769543264_0.3644942174725192", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "@esbuild/openbsd-arm64", "version": "0.22.0", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.22.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "7cb42e3a0d3da039d1a4b7ccbd0c19b0f71ae453", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.22.0.tgz", "fileCount": 3, "integrity": "sha512-<PERSON>zlhu/YyITmXwKSB+Zu/QqD7cxrjrpiw85cc0Rbd3AWr2wsgp+dWbWOE8MqHaLW9NKMZvuL0DhbJbvzR7F6Zvg==", "signatures": [{"sig": "MEQCIFicHvaLsr6qcRkD9JauO/Dakz1NaOnyvXr+zwMp1chfAiBOXwXJlaf/Ke5PJIZtPMcQQLJJpGXnTCFPaEH9jmE/LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372461}, "engines": {"node": ">=18"}, "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.22.0_1719779860284_0.2356870049158184", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "@esbuild/openbsd-arm64", "version": "0.23.0", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.23.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "72fc55f0b189f7a882e3cf23f332370d69dfd5db", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.23.0.tgz", "fileCount": 3, "integrity": "sha512-suXjq53gERueVWu0OKxzWqk7NxiUWSUlrxoZK7usiF50C6ipColGR5qie2496iKGYNLhDZkPxBI3erbnYkU0rQ==", "signatures": [{"sig": "MEYCIQC+6h7RMC61xshWJXxB2NX3iozt8AHPyBJ9BOWyAOzwNgIhAKwrYcLuJMl0r2TC2hEqwhn2jpyhGtg4NEZHlCzL7Erf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372461}, "engines": {"node": ">=18"}, "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.23.0_1719891215960_0.8947388456607726", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "@esbuild/openbsd-arm64", "version": "0.23.1", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.23.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "05c5a1faf67b9881834758c69f3e51b7dee015d7", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.23.1.tgz", "fileCount": 3, "integrity": "sha512-3x37szhLexNA4bXhLrCC/LImN/YtWis6WXr1VESlfVtVeoFJBRINPJ3f0a/6LV8zpikqoUg4hyXw0sFBt5Cr+Q==", "signatures": [{"sig": "MEQCIGxU4U3yyp6R7KJVmS3+3V54B99qCVD42zvX6BslnbjTAiBTpw3o1jyJnwhniy3PWAp4XcfoAuJ4A+sQcDHBrwxvbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9372461}, "engines": {"node": ">=18"}, "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.23.1_1723846387061_0.14930368564966812", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "@esbuild/openbsd-arm64", "version": "0.24.0", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.24.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "5d904a4f5158c89859fd902c427f96d6a9e632e2", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.24.0.tgz", "fileCount": 3, "integrity": "sha512-MD9uzzkPQbYehwcN583yx3Tu5M8EIoTD+tUgKF982WYL9Pf5rKy9ltgD0eUgs8pvKnmizxjXZyLt0z6DC3rRXg==", "signatures": [{"sig": "MEYCIQCvFETxWdWiDBzcZaqQ0eOmdNwyEsPQDVKG6i28+YLCgwIhAPQpOhliE1mSTm/jiMy3LkKb5oB62BZ7vBDs4TUObo8V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9569069}, "engines": {"node": ">=18"}, "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.24.0_1726970775778_0.6290145734894095", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "@esbuild/openbsd-arm64", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "117fe1658cab3f2b42c10ed1fae908f571acf4af", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.24.1.tgz", "fileCount": 3, "integrity": "sha512-wy2psEw0wc+xbSB4Et3XZaONClCagOlQTsqRJaLtCcPggnuZMfb17c5T5w6RO6pFF5J2SWoM7+MJuWUEzvQN+Q==", "signatures": [{"sig": "MEYCIQDUstpO70HSFMJr2Jx1keZPkFhxguUz0jPsFu3jluatJgIhAP0KiOmBmh0U0qh65ukbmeOlO6A/22nAJ07qtHeH0tWj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9569069}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.24.1_1734673229821_0.07682383815466376", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/openbsd-arm64", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "58b00238dd8f123bfff68d3acc53a6ee369af89f", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.24.2.tgz", "fileCount": 3, "integrity": "sha512-YQbi46SBct6iKnszhSvdluqDmxCJA+Pu280Av9WICNwQmMxV7nLRHZfjQzwbPs3jeWnuAhE9Jy0NrnJ12Oz+0A==", "signatures": [{"sig": "MEUCIEYvplvY8ZnHHttwjq8I92dSefhzhyXEWNYXVC4voY9vAiEAwkgwO1GBXU5D5In3mbyvRan6K++oP20HsRKTlFUDTuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9569069}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.24.2_1734717352096_0.08728590777037071", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/openbsd-arm64", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "8fd55a4d08d25cdc572844f13c88d678c84d13f7", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.0.tgz", "fileCount": 3, "integrity": "sha512-21sUNbq2r84YE+SJDfaQRvdgznTD8Xc0oc3p3iW/a1EVWeNj/SdUCbm5U0itZPQYRuRTW20fPMWMpcrciH2EJw==", "signatures": [{"sig": "MEUCIQCxroMjNz9Avv1LraHJ1VTTRqaVryFnmP+XqNdTSMNPYwIgBRxegpCLuHbH6sxxYc2gbHo96o9uDXr1Tm+/sAmTSJc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9569069}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.25.0_1738983719684_0.4493517058259433", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/openbsd-arm64", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "d9021b884233673a05dc1cc26de0bf325d824217", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.1.tgz", "fileCount": 3, "integrity": "sha512-Na9T3szbXezdzM/Kfs3GcRQNjHzM6GzFBeU1/6IV/npKP5ORtp9zbQjvkDJ47s6BCgaAZnnnu/cY1x342+MvZg==", "signatures": [{"sig": "MEYCIQCiW7QOLn85ldo5QkEpJFmrBNLgGvaWOl7auObv6Hjg7QIhAIziebHKavaET0Em1my5aAMKUJ6hGrbmm2+I/T0tM709", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9569069}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.25.1_1741578309761_0.8475919071009486", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/openbsd-arm64", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "f9caf987e3e0570500832b487ce3039ca648ce9f", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.2.tgz", "fileCount": 3, "integrity": "sha512-dcXYOC6NXOqcykeDlwId9kB6OkPUxOEqU+rkrYVqJbK2hagWOMrsTGsMr8+rW02M+d5Op5NNlgMmjzecaRf7Tg==", "signatures": [{"sig": "MEUCIQDdTgRNoGy+NU4AadkePzfaTd6JnHAxvrSy9hkeAgJkVwIgO2mdTjBB9yFbeev+lq78hnIntrUHjC1tf7vuxAxA7hc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9569069}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.25.2_1743355963347_0.9675726175568138", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/openbsd-arm64", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "ca078dad4a34df192c60233b058db2ca3d94bc5c", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.3.tgz", "fileCount": 3, "integrity": "sha512-zGAVApJEYTbOC6H/3QBr2mq3upG/LBEXr85/pTtKiv2IXcgKV0RT0QA/hSXZqSvLEpXeIxah7LczB4lkiYhTAQ==", "signatures": [{"sig": "MEUCIHYCW1YM0UpYjyC/imMmMHP61AfbekyEs57AJXY1IexiAiEA2iw166bXlM9FNw2KUzmHL258UePoNHGR3/cMtiHhSGk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9569069}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.25.3_1745380532085_0.10621904759230372", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/openbsd-arm64", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/openbsd-arm64@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["openbsd"], "cpu": ["arm64"], "dist": {"shasum": "f272c2f41cfea1d91b93d487a51b5c5ca7a8c8c4", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.4.tgz", "fileCount": 3, "integrity": "sha512-Ct2WcFEANlFDtp1nVAXSNBPDxyU+j7+tId//iHXU2f/lN5AmO4zLyhDcpR5Cz1r08mVxzt3Jpyt4PmXQ1O6+7A==", "signatures": [{"sig": "MEUCIAoN5GgbGW7w2hyAjCVwAQrfqqicVMRjhCm662Fsw6o7AiEAjKvI396RUibAdqTbMgpp7W1ylElrReAJXrCr71E/3/w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9569069}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/openbsd-arm64_0.25.4_1746491428551_0.26728045581262605", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/openbsd-arm64", "version": "0.25.5", "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["openbsd"], "cpu": ["arm64"], "_id": "@esbuild/openbsd-arm64@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==", "shasum": "2a796c87c44e8de78001d808c77d948a21ec22fd", "tarball": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz", "fileCount": 3, "unpackedSize": 9569069, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDpUXT9sUF8JUNiUM0QS939lHn9FYJa8K2NJmVqsvZREQIgcd30/VaDsPUMOYRdTPKZJw6UnsHXkTZirX7k5XA62hI="}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/openbsd-arm64_0.25.5_1748315548074_0.1502666641047985"}, "_hasShrinkwrap": false}}, "time": {"created": "2024-06-30T17:45:43.172Z", "modified": "2025-05-27T03:12:28.514Z", "0.0.1": "2024-06-30T17:45:43.452Z", "0.22.0": "2024-06-30T20:37:40.512Z", "0.23.0": "2024-07-02T03:33:36.244Z", "0.23.1": "2024-08-16T22:13:07.339Z", "0.24.0": "2024-09-22T02:06:16.033Z", "0.24.1": "2024-12-20T05:40:30.055Z", "0.24.2": "2024-12-20T17:55:52.372Z", "0.25.0": "2025-02-08T03:01:59.985Z", "0.25.1": "2025-03-10T03:45:10.042Z", "0.25.2": "2025-03-30T17:32:43.603Z", "0.25.3": "2025-04-23T03:55:32.356Z", "0.25.4": "2025-05-06T00:30:28.840Z", "0.25.5": "2025-05-27T03:12:28.338Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "The OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the OpenBSD ARM 64-bit binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "readmeFilename": "README.md"}