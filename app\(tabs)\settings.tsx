import React from 'react';
import { router } from 'expo-router';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Switch,
  Animated,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  User,
  Bell,
  Moon,
  Sun,
  Globe,
  CircleHelp as HelpCircle,
  LogOut,
  ChevronRight,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { getColors } from '@/constants/Colors';

export default function SettingsScreen() {
  const { theme, toggleTheme, isDark } = useTheme();
  const colors = getColors(isDark);
  const [notificationsEnabled, setNotificationsEnabled] = React.useState(true);

  const settingsItems = [
    {
      icon: User,
      title: '个人资料',
      subtitle: '编辑你的角色信息',
      hasChevron: true,
      onPress: () => router.push('/settings/profile'),
    },
    {
      icon: Bell,
      title: '通知设置',
      subtitle: '管理推送通知',
      hasSwitch: true,
      switchValue: notificationsEnabled,
      onSwitchChange: setNotificationsEnabled,
    },
    {
      icon: isDark ? Sun : Moon,
      title: '夜间模式',
      subtitle: isDark ? '切换到明亮主题' : '切换到深色主题',
      hasSwitch: true,
      switchValue: isDark,
      onSwitchChange: toggleTheme,
    },
    {
      icon: Globe,
      title: '语言设置',
      subtitle: '选择显示语言',
      hasChevron: true,
      onPress: () => console.log('Language pressed'),
    },
    {
      icon: HelpCircle,
      title: '帮助与支持',
      subtitle: '获取帮助和反馈',
      hasChevron: true,
      onPress: () => console.log('Help pressed'),
    },
  ];

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    overlay: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    header: {
      alignItems: 'center',
      paddingTop: 60,
      paddingBottom: 30,
      paddingHorizontal: 20,
    },
    title: {
      fontFamily: 'MaShanZheng-Regular',
      fontSize: 32,
      color: colors.text,
      textAlign: 'center',
      marginBottom: 10,
      textShadowColor: isDark
        ? 'rgba(255, 255, 255, 0.1)'
        : 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    settingsContainer: {
      paddingHorizontal: 20,
      paddingBottom: 30,
    },
    settingItem: {
      marginBottom: 12,
      borderRadius: 16,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    settingContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
    },
    settingLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    logoutIconContainer: {
      backgroundColor: isDark
        ? 'rgba(255, 107, 107, 0.2)'
        : 'rgba(220, 53, 69, 0.1)',
    },
    textContainer: {
      flex: 1,
    },
    settingTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 2,
    },
    logoutText: {
      color: colors.error,
    },
    settingSubtitle: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    settingRight: {
      marginLeft: 12,
    },
    logoutItem: {
      marginTop: 20,
    },
    footer: {
      alignItems: 'center',
      paddingVertical: 30,
      paddingHorizontal: 20,
    },
    footerText: {
      fontSize: 14,
      color: colors.textSecondary,
      fontWeight: '600',
      marginBottom: 4,
    },
    footerSubtext: {
      fontSize: 12,
      color: colors.textTertiary,
    },
    themeToggleContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 20,
      padding: 4,
      marginLeft: 8,
    },
    themeToggleButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      minWidth: 60,
      alignItems: 'center',
    },
    themeToggleButtonActive: {
      backgroundColor: colors.accent,
    },
    themeToggleText: {
      fontSize: 12,
      fontWeight: '600',
      color: colors.textSecondary,
    },
    themeToggleTextActive: {
      color: colors.primary,
    },
  });

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{
          uri: isDark
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg'
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg',
        }}
        style={dynamicStyles.container}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[colors.backgroundSecondary, colors.backgroundTertiary]}
          style={dynamicStyles.overlay}
        >
          <SafeAreaView style={dynamicStyles.scrollView}>
            <ScrollView showsVerticalScrollIndicator={false}>
              <View style={dynamicStyles.header}>
                <Text style={dynamicStyles.title}>设置</Text>
                <Text style={dynamicStyles.subtitle}>管理你的账户和偏好</Text>
              </View>

              <View style={dynamicStyles.settingsContainer}>
                {settingsItems.map((item, index) => (
                  <BlurView
                    key={index}
                    intensity={30}
                    style={dynamicStyles.settingItem}
                  >
                    <TouchableOpacity
                      style={dynamicStyles.settingContent}
                      onPress={item.onPress}
                      disabled={item.hasSwitch}
                      activeOpacity={0.7}
                    >
                      <View style={dynamicStyles.settingLeft}>
                        <View
                          style={[
                            dynamicStyles.iconContainer,
                            item.title === '夜间模式' && {
                              backgroundColor: colors.accent + '20',
                            },
                          ]}
                        >
                          <item.icon
                            size={24}
                            color={
                              item.title === '夜间模式'
                                ? colors.accent
                                : colors.text
                            }
                          />
                        </View>
                        <View style={dynamicStyles.textContainer}>
                          <Text style={dynamicStyles.settingTitle}>
                            {item.title}
                          </Text>
                          <Text style={dynamicStyles.settingSubtitle}>
                            {item.subtitle}
                          </Text>
                        </View>
                      </View>
                      <View style={dynamicStyles.settingRight}>
                        {item.hasSwitch && item.title === '夜间模式' ? (
                          <View style={dynamicStyles.themeToggleContainer}>
                            <TouchableOpacity
                              style={[
                                dynamicStyles.themeToggleButton,
                                !isDark &&
                                  dynamicStyles.themeToggleButtonActive,
                              ]}
                              onPress={() => !isDark && toggleTheme()}
                            >
                              <Sun
                                size={16}
                                color={
                                  !isDark
                                    ? colors.primary
                                    : colors.textSecondary
                                }
                              />
                            </TouchableOpacity>
                            <TouchableOpacity
                              style={[
                                dynamicStyles.themeToggleButton,
                                isDark && dynamicStyles.themeToggleButtonActive,
                              ]}
                              onPress={() => isDark && toggleTheme()}
                            >
                              <Moon
                                size={16}
                                color={
                                  isDark ? colors.primary : colors.textSecondary
                                }
                              />
                            </TouchableOpacity>
                          </View>
                        ) : item.hasSwitch ? (
                          <Switch
                            value={item.switchValue}
                            onValueChange={item.onSwitchChange}
                            trackColor={{
                              false: colors.surfaceSecondary,
                              true: colors.accent,
                            }}
                            thumbColor={
                              item.switchValue
                                ? colors.background
                                : colors.textTertiary
                            }
                          />
                        ) : null}
                        {item.hasChevron && (
                          <ChevronRight
                            size={20}
                            color={colors.textSecondary}
                          />
                        )}
                      </View>
                    </TouchableOpacity>
                  </BlurView>
                ))}

                <BlurView
                  intensity={30}
                  style={[dynamicStyles.settingItem, dynamicStyles.logoutItem]}
                >
                  <TouchableOpacity
                    style={dynamicStyles.settingContent}
                    onPress={() => console.log('Logout pressed')}
                    activeOpacity={0.7}
                  >
                    <View style={dynamicStyles.settingLeft}>
                      <View
                        style={[
                          dynamicStyles.iconContainer,
                          dynamicStyles.logoutIconContainer,
                        ]}
                      >
                        <LogOut size={24} color={colors.error} />
                      </View>
                      <View style={dynamicStyles.textContainer}>
                        <Text
                          style={[
                            dynamicStyles.settingTitle,
                            dynamicStyles.logoutText,
                          ]}
                        >
                          退出登录
                        </Text>
                        <Text style={dynamicStyles.settingSubtitle}>
                          安全退出当前账户
                        </Text>
                      </View>
                    </View>
                    <ChevronRight size={20} color={colors.error} />
                  </TouchableOpacity>
                </BlurView>
              </View>

              <View style={dynamicStyles.footer}>
                <Text style={dynamicStyles.footerText}>
                  斯卡布罗集市 v1.0.0
                </Text>
                <Text style={dynamicStyles.footerSubtext}>
                  © 2024 Medieval Fantasy App
                </Text>
              </View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}
