{"_id": "read-cmd-shim", "_rev": "46-ea32e4af0147ab2d17abf34448571842", "name": "read-cmd-shim", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "read-cmd-shim", "version": "1.0.0", "author": {"url": "http://re-becca.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "read-cmd-shim@1.0.0", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "dist": {"shasum": "00c4abc7b92d623caeb7ed4bbf580bf5e43e9a25", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-1.0.0.tgz", "integrity": "sha512-kGfykCflcXdupBfzS1OLlRAWADRDv7Ht5S8YdIFVRuV2VKUVObbb1L8IfXezWoqwZBpvNCTS2SJYjuDWgZ3Ygg==", "signatures": [{"sig": "MEQCIFzZ6do1De39JCzFnORqRCjryXsHrpPqmAbd5y8Y8YyzAiBFXO0NBq56TLmY+0MS2AgRSvIHTJxgTNnzwlyl5mCtvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "00c4abc7b92d623caeb7ed4bbf580bf5e43e9a25", "gitHead": "094627616b319c37456abe9f2bb2ce513966eaad", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "3.3.0", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "_nodeVersion": "3.1.0", "dependencies": {"graceful-fs": "^4.1.2"}, "devDependencies": {"tap": "^1.4.1", "rimraf": "^2.4.3", "cmd-shim": "^2.0.1", "standard": "^5.2.2"}}, "1.0.1": {"name": "read-cmd-shim", "version": "1.0.1", "author": {"url": "http://re-becca.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "read-cmd-shim@1.0.1", "maintainers": [{"name": "iarna", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "dist": {"shasum": "2d5d157786a37c055d22077c32c53f8329e91c7b", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-1.0.1.tgz", "integrity": "sha512-ncStF2gqrdDvUwvpTnf+lQ2FG8W47ejCeh6R2Pf3Ze0iTNnfLXyilYVNTJNibDcWtzoNixO2rMfOf/3kAcaH5A==", "signatures": [{"sig": "MEUCIQCyw6uG8Iz+pPnFKgo0aW/Uvvn3+DN7xYRKRA1YL5xk8wIgHZu3WtfHexMAsiHkyI1z5CdJNXf2dqo0+td44nNpTaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2d5d157786a37c055d22077c32c53f8329e91c7b", "gitHead": "7c50879bf49743a1c69f9d7f0ba1638fc46bb40c", "scripts": {"test": "standard && tap test/*.js"}, "_npmUser": {"name": "iarna", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "3.3.0", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "_nodeVersion": "3.1.0", "dependencies": {"graceful-fs": "^4.1.2"}, "devDependencies": {"tap": "^1.4.1", "rimraf": "^2.4.3", "cmd-shim": "^2.0.1", "standard": "^5.2.2"}}, "1.0.2": {"name": "read-cmd-shim", "version": "1.0.2", "author": {"url": "http://re-becca.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "read-cmd-shim@1.0.2", "maintainers": [], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "dist": {"shasum": "75a8324a7f1e0b2a830fbd1c3391a25fc657cc54", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-7c52L8k5B4Rag4EmfgwU//r/2XGGZqDz7yCHBPX3Tt1fAyIyG1TkRsRZxQhCWvAwQBruu6Wx/P3ptOVqKgfcLA==", "signatures": [{"sig": "MEUCIHLdFDGqfDdkNmzTBcsYuhVmR5SnWry4X8TRh7YvYywKAiEA01Q6nZq3Qdz3gA0asOAgmUk8x+toagR4VZSyMf9y26c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVFzJCRA9TVsSAnZWagAAIicP/1BeD4elbB5Vy2RNCWHA\noYmg15eflJl6kno0BCaMk5X+kRi1H+FkyYg8BUAK0QZSqByMZxcwCLu83g4g\ngNXTT8P0XXXANDqhrbW27bCr9qaTdsMIcJBD4FUlvQphpVWcrlb0XsyTs5KR\nV+nEBQy/p2y0w+4m5Dp3XGEaX06XaFmM8u8PBxexVA2HoZpzdmCSVmtpeLAF\nMgtqmaG0c+NisSUVD3xGmF+raUWztMK7ybu7jpRSduKJkzDCt9vLKJnVT5ch\ndyW/qlMDAk8eI1+s4ogWwYjogmQrD/TeT/b+aokQNPvddKi/nRf+jwuMROTf\nAIRCfdR3hSDf544o+3GlCXPjE+ZydsTv4OvbOPzn+WY8GFEMtUGHnRuF3FIc\n7ln0M2ylNfzbnM4Fe4HDv+g3kQrkzouyrM3XvnDTADMXIedIqvNx7DhEkqdi\nxTwkN8aO9L1Ndtvrk2mnKKdUqiFSoROz1BUHk8Wo+qe7T/TzdnFWYu7Xr6aQ\nM3omdAWixfNKQIa5vmstatGgunRJpZiz5pbKj2ZtfJMuqCuwH/x04cceYE0h\nd6cmZXzR6urgUKywt+u+wkZPtSAkVfQFu1njNGGoAGfP8PWRFEBtrcjgnhuC\noPDCy3b2vyV0RAnI+5CfD38pRT1Su1nlA5fLnAnxl3wTAONxCKUtE7WhRvpc\nHhOg\r\n=5LGH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "a4c4bb0a8f2d681af44ed651f51606403e12f09d", "scripts": {"test": "tap test/*.js --100", "pretest": "standard"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"graceful-fs": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.7.0", "rimraf": "^2.4.3", "cmd-shim": "^2.1.0", "standard": "^5.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/read-cmd-shim_1.0.2_1565809865114_0.6581198524062861", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "read-cmd-shim", "version": "1.0.3", "author": {"url": "http://re-becca.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "read-cmd-shim@1.0.3", "maintainers": [], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "dist": {"shasum": "b246608c8e76e332a99be7811c096a4baf60015a", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-1.0.3.tgz", "fileCount": 10, "integrity": "sha512-HUHb2imlZ8xBJjiZZRx0Ag9JfZ3jxQRfORMQXWCDeHE6PCCnpQrMq6LhyNqEPnMXhMDDIyq/BK7pBbhNy9zDDA==", "signatures": [{"sig": "MEQCIBYylri9/x9/JDOcrrmCsfzRJ7oIDXA4hYk565V7ja7YAiBjDyIi1WPLEzv3s66iPOJH2uBeYjUTHvdSJxU6Wis+Gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVGQmCRA9TVsSAnZWagAABk8P/i+6bTnsVjKhdROcQeTQ\nm0jPu1vEEXKuzjB2IGxDZXyvXkogOLeqq+F9Gfgruagl4FrwsyNz8K8MiM0x\n6dSbdu+DrIw/2VRrmhvrQbigCMirmU2++IQJQb7+3vkgZ3OXiG+2K7Rim8wS\nz5XvNDif50I+XlHZ8KLrD5tXZH04vjEWDSkPsNvNqs3JsCTFlMeaT3WVpmsd\nrCBtyKJ7UPDJsuSRykn5ZqUUCRjTa/XEDo1wfsroyrytOg9f7O3KNHtCaQJ6\nRRKqFQZKUFk16rYs0R7YSySOxhO06Pb4b2uss1thoGVFUoYnI44Q/eZQIh9d\ndtkm8A8QeOeYe5Gbkm5yZHM3cSNofRfSsF94X+Dyaeg2nV8dO3jx7hyC39oY\n5tL6HNzQqAJbUIPT4c6RgazC6sex4wRmYTosGI7c0gCEmocRqgBDepG3mypH\nDIiWk6wf1CtI1mewdIE+OUAHiewazhOOLun+XSFrvgv1/9ed8UoHtZrZaf00\nW6Uupgm8huR+YQwtgBuwQUYb/vYY7bswmsYJQLMdnfVeE1r2STlrSWIX6Md1\naswSRrsSgNIk4JZXcGqz0ws0mixFm9FmyQUgLSVhjvWNS7xMxyx924dThRUb\nPuQH6i4moFyVQIVNqH14gOBa2W6HXdttxH2CiREN0/TybfRbD00tsZKBwzD7\nOv/o\r\n=IPX2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "aa9e076fcfab9e4159da4833f0b257dfb4471ea6", "scripts": {"test": "tap test/*.js --100", "pretest": "standard"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"graceful-fs": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.7.0", "rimraf": "^2.4.3", "cmd-shim": "^3.0.0", "standard": "^5.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/read-cmd-shim_1.0.3_1565811749782_0.9952839009438261", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "read-cmd-shim", "version": "1.0.4", "author": {"url": "http://re-becca.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "read-cmd-shim@1.0.4", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "dist": {"shasum": "b4a53d43376211b45243f0072b6e603a8e37640d", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-1.0.4.tgz", "fileCount": 4, "integrity": "sha512-Pqpl3qJ/QdOIjRYA0q5DND/gLvGOfpIz/fYVDGYpOXfW/lFrIttmLsBnd6IkyK10+JHU9zhsaudfvrQTBB9YFQ==", "signatures": [{"sig": "MEQCIG3B9TyX4qqo0+Ifi5/B/z3NsCDDDBaTgCvzcftidyBrAiBGzz6EIDzOfI6/sYeleCcTHfpUoeeWjBd0xofUy3Samg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXE39CRA9TVsSAnZWagAAtJ0P/3KlOfgczjv+RJDdvr5d\nvLfJCsiUiCjb/I1NsUapeOGH82nYw+9CVLuNbiRRUlWfKOt+0NEw+s/cTLNA\nl0vDiMlTaNWTpD0CrbPTwe6UkWiWD8P8+4tVSHMIRSGSAKZj0/YebbMkch2h\nhhTVWwhzWS9U6/d0Zqa2cG0kdMwQoL6+3XFdVMhNAVxZnAJ0Zl5cF4li7QRK\n6/WERNBTkFNsRZoTAQDNQX1yGqVprIsCw6kyOsrXCacDZG0+pKurnSZc7gLj\nfEHS3FjBSdNet+3I5st/t0StTp6kEOJG1rAmc99HM7Kk6BTXyutMaAihQ066\nbbpaW9mNUwyZ/59DlhBIaKCdI0RBGk48bRz8g1u2110OLtKdU/ZkIax76K8x\ncGUPpEMRsw4btMsMx0ham1inu9X8Gx776tgs6C6cnApLnkznwmoTjftvfMBN\n4XK370ZjykbTcxIEh+VhVFDvIMuf3yZwQGgw25ItCaVi2V1By+ag+rlwDI1l\nCHN3uLMMvj1mh+g2K/mKnN4HomjNFqR9n/fMhhHkKe5fW3fgmqXLY097jkmw\n4dA4WDEa3WvLe0PDPASHEcqGUMzoNhrJt8OliMSp8t1YxkFWTgO7zZBMxXgG\npUKBEvCKNZi/BZaYF0kwfcsXK81v0d5c1dyHzzSLrQ9SbMCPMnvBFPvzWUhk\nlP+K\r\n=NKR8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9679b59d8fe2103ed2736862ba4b28742bfea3d7", "scripts": {"test": "tap test/*.js --100", "pretest": "standard"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "6.11.0", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"graceful-fs": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.7.0", "rimraf": "^2.4.3", "cmd-shim": "^3.0.0", "standard": "^5.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/read-cmd-shim_1.0.4_1566330365094_0.544572457369793", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "read-cmd-shim", "version": "1.0.5", "author": {"url": "http://re-becca.org/", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "read-cmd-shim@1.0.5", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "dist": {"shasum": "87e43eba50098ba5a32d0ceb583ab8e43b961c16", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-1.0.5.tgz", "fileCount": 4, "integrity": "sha512-v5yCqQ/7okKoZZkBQUAfTsQ3sVJtXdNfbPnI5cceppoxEVLYA3k+VtV2omkeo8MS94JCy4fSiUwlRBAwCVRPUA==", "signatures": [{"sig": "MEQCIGBDzyQ5AnXIogzUbpeHp2cFfIESZViP5nIOjaVkCpOMAiBhMA3KgT/ZNZ8phCTmAvAnTBUqzy48mEHBXDupAEd+HA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwcseCRA9TVsSAnZWagAAmZgP/3fw8BQwV1gcAKBUnygi\nE8UoshUZqiQNt5jrTP29j7PAgWLlvQGO+qd5+KWVIIFgnZwpHzk7c4mC7FNI\n+IT3oMZv3vQBj+7TVeBUigehwauFzh0rT4uVN/rMa1S98BjdjR7rfgcqjfVC\nZnLL+76eS6V0nxIUPBuNFaNN8x3w2o5Mq0MYL6Xv0w/PBsaZaKyuQBvKFysb\nWtk9IN7oGZKETNZIvKkoBUUHlhBljxdOcWIhA6zjINH5hdD99VjkoBqAPNRO\n0L8CZ+qe4u/cvnu8arnzC0aKFtTn6mwdx50BieaWjejGPuE7RfcQShjDTIgl\npGCspGjji9HPEEIYR3VNSZy430bAkDuxKjPpUtJZIy3CT5sQJcC6+2S0kK24\n4PXeKAPQlOWZy124atRzxnqjKrl9fkW9itLQEK8YRnZs7886sSXwDBZ7q7Yd\naL/HGRcePKKabHy47G5g0PafoTResSmViQc/6MrQX+lxBBS47WOYRDVzeGHH\nj+Znp1qeHPaeMG2gD6JcbcCelU2ep/K3aeWLSC9ulvzUhrozXlDJtV/6Rftd\nDAE2/xf0fWBPQiYOAj0anSopoIVnA4QBGQ9FQrh5sSUNHh5iQQ/Sft8Qj97L\nR3XuUi3jqAOUgR8x5EJjUqG3Lg4riW0CQ0HEuaJJ0Ks7BNmoL/pd6BbmkDfx\nq6yl\r\n=PrYy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "4f69e6131b9cb460fbec4b81bd510228ded7d082", "scripts": {"test": "tap test/*.js --100", "pretest": "standard"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "6.12.1", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"graceful-fs": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.7.0", "rimraf": "^2.4.3", "cmd-shim": "^3.0.0", "standard": "^5.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/read-cmd-shim_1.0.5_1572981533661_0.7967034134792066", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "read-cmd-shim", "version": "2.0.0", "license": "ISC", "_id": "read-cmd-shim@2.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "tap": {"check-coverage": true}, "dist": {"shasum": "4a50a71d6f0965364938e9038476f7eede3928d9", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-HJpV9bQpkl6KwjxlJcBoqu9Ba0PQg8TqSNIOrulGt54a0uup0HtevreFHzYzkm0lpnleRdNBzXznKrgxglEHQw==", "signatures": [{"sig": "MEQCIBfe32AHBOHGM6t9R1W6tpmX+00lCImwUHvzKupDLvOcAiAZPW3CBQfz4xzY9TSBY/DBrtcPUB60434sebF48WhWqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLpR/CRA9TVsSAnZWagAABWAP/jNyyU0EcuXS9mkuFKkv\nstOmB5OghSG+V8H4MuhNh7L+7D6zIXRJsnWEnCIgfOVWYvcWYjortZW0aCns\nZrruTqN2yrAKt3EYQiqUBj57zk+TR0xt7/O4W+0FnTKxDLc8aWD1Fak85xCz\nHQMohVuvKGTs2CTvlaLrFy8L/RrYgSBHIQpKJbNvEKPuOJZTNo5YVjlvkwNA\nKFE1q68zmJEVyonUoOoEI7Qw9IKR4yoJWntIGJiyJiJWNgNIcesHR85bXT2o\nbaKB8Qyx6YaabRwyofus31CJ+Bukg9eE9t/UmSgvV4zta4ldOvd0K3a59KPC\nHcnp0Vgu/s0M7mdPsQ50TtuT7ScFEp6YPAp9C+W7xoEnAslbyE7KOTxgVZYQ\nNKXGD9vJDt7BSm+gQNjP3N2rNIx2rQOiw+6D3QPQ11dRXieIShaSjyMYrI6b\nIAUfRiibedG4VZqCAr2u803fIXNEBGtTFb5xbSVgK+cmmC68zUMdZmb8hGHa\nDxKlIP0duLTDuK8fAL8voAIUHNnOhiG43eP4ke0eKoO65kiTXFE3eweF/UwR\n/nefLkoqRNMEDbcQvorTWd8nwCwrFwccc0hPn7eWP1ExLNqhjoM0QxQr3D3T\n/6Tv169WAffrinLI3luwnKbrwBF1ZVTEhE34xae4dClM71uA0oIrXOZBY1Kj\npylU\r\n=Al6g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "8280d613d917054384fa3b4cb47afee3cbfe412b", "scripts": {"test": "tap", "preversion": "npm t", "postversion": "npm publish", "prepublishOnly": "git push --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "_nodeVersion": "13.4.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "rimraf": "^3.0.0", "cmd-shim": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/read-cmd-shim_2.0.0_1580110974833_0.7312494442755049", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "read-cmd-shim", "version": "3.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "read-cmd-shim@3.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "tap": {"check-coverage": true}, "dist": {"shasum": "62b8c638225c61e6cc607f8f4b779f3b8238f155", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-KQDVjGqhZk92PPNRj9ZEXEuqg8bUobSKRw+q0YQ3TKI5xkce7bUJobL4Z/OtiEbAAv70yEpYIXp4iQ9L8oPVog==", "signatures": [{"sig": "MEQCIHr9rvkZfLVDysChouJrfO9K8nNfuoBz2UTjPTTT86zzAiAIf+nj1xsnCS4LQmOTNHrgopB0x7fWslObjRiYtH5BFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTHC6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqG7w//emPh4fELc+Ak8FS1EcsNKk6CZ6BF49RIvwTdUdvYWXbAtL6Y\r\nOLyIo40JydRX9VZXQkWavXxA4xZVvlU/Cn6CcdodAq4qAamkJgUR7PVzaWuB\r\nzVXAd0rGvVIbqOrgk2lowPOKcotfOYOxOIzrkSs7mSWQIlSTZwbs1nmmV+Uk\r\nffj/9OIrcHvNgkfmOzoVJhTLzqURgqJFrNe/8DipXPk5D0BL0Q5MIa6PPJms\r\nx/wNK9Zgoa2fVh/yxYn/wB98IqBkm/tWAQ/saPrAQgbUDrxD8EVLonmBPqmx\r\nmzKP2L9bVilHDdSz5UZLhDDhVkscnEMmpebiy/b6qlpqe3vn4lvK5pCn5vjV\r\nJI6ifr/kqAn7nLS/Hyll0gk1FmYmK7rvqC+OoTcoepBA8719uyEHe+REAJqx\r\nGIzM6fESX+MF3KP0MXztbV2RWvXeDZbl6cjUKKzEiQJkWSxJcjoiAJuMX3G2\r\nnxP1xotaFI5EH/tIMqOyc4XBqeZ7zTOHtpxHCNtKBuLLhs5upAdTTSOrc2/o\r\ncNnsScZxoknubzbl7RZjgtPwMkkOMRBRZn8CnEc++PNAlesRXnk3Ln7J7wck\r\n/+sLBUf678M3bZYO2kM4bMpMKFFBXksFuaPCS+h/ysvl/oApCetF4KIiKJ7b\r\nkVhLO+OxjJRPsUZrX+a2jooVjczAS/S9Bso=\r\n=CZqR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "bb6ab3b06c4b953df5959e163f214e97e72bd344", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "templateOSS": {"version": "3.2.2", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "rimraf": "^3.0.0", "cmd-shim": "^4.0.0", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/read-cmd-shim_3.0.0_1649176761903_0.8609925005031223", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "read-cmd-shim", "version": "3.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "read-cmd-shim@3.0.1", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "tap": {"check-coverage": true}, "dist": {"shasum": "868c235ec59d1de2db69e11aec885bc095aea087", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-3.0.1.tgz", "fileCount": 4, "integrity": "sha512-kEmDUoYf/CDy8yZbLTmhB1X9kkjf9Q80PCNsDMb7ufrGd6zZSQA1+UyjrO+pZm5K/S4OXCWJeiIt1JA8kAsa6g==", "signatures": [{"sig": "MEUCIQCiSLiZYxfd7FPjj/lrc90/HuzYd7hNHBH8SMEyu8FDNQIgPCMZm3iSK/MH47CadAsS7Dub81Y98Zr/9f+qdhh0mB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi+9CeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3xxAAhwG4zC+kXK51B6pa4oC7tyJk99yK+Ha9423dgom4JA/vgJiT\r\nwvq/XAewqvOBAHufduV6hS/CaHWyXfLJAhTkhxcLFTJi/JcuagAl5Kc6rK7H\r\nE8dhfVSK0rNVle1AmbrwSO/ySYYcB20YiclhGxUk9r1xY4jH73phVLFaMQVW\r\nPitVZ41x/bm2vvzvkMXu732eRVge5VLmqp+NDlQerkdm6ruMnCSjqIp/QhdX\r\nkWwB0l8vzUpAbfM/Fbdi+V5SOLprH6dAqFmZK6wjPainr9kYYzMw/fWJG2PO\r\nKpkvNJFRgO5hQugBiLKYX/jfO4ZC3/nuvf7v7sZfp7pzKuDe6i/MMfwfihx0\r\neETBEWoBtkob7yJABT9dpGgi/0tVYYMV/EoRzn2K9BqSUj8K9bdjGcjXG54D\r\nvKKzgEUPWRPNqPsB+pkKlGq7Ouzd7INSzDWA/AHcIncIQoB8uN3FnE0bQmq6\r\nihC+MItKNdrv73dSXbY2nSR7fd5/XXqGSboaGwgSGJqoTuxPrr2spquYCuVV\r\nIIWhCDt017xNaa1J8dXFy5qbPoCDOrnsuh2PvI3jUKvRWLxPthwZw2sjLZVu\r\ntrywkwYmmq6A0NJv2WTaAbJ6mgHs5Q2msSIm25nWfgw8lxGKlhVDU9khDruY\r\nz4P8i0POKxXooWU7Lk91rQxFgO98iBTwmQE=\r\n=Mrig\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "5933ebef1036d2a4692a9f570063399b7225d73c", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "8.17.0", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "templateOSS": {"version": "3.5.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.6.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "rimraf": "^3.0.0", "cmd-shim": "^5.0.0", "@npmcli/template-oss": "3.5.0", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/read-cmd-shim_3.0.1_1660670110301_0.33665384273513843", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "read-cmd-shim", "version": "4.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "read-cmd-shim@4.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "640a08b473a49043e394ae0c7a34dd822c73b9bb", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-yILWifhaSEEytfXI76kB9xEEiG1AiozaCJZ83A87ytjRiN+jVibXjedjCRNjoZviinhG+4UkalO3mWTd8u5O0Q==", "signatures": [{"sig": "MEUCIHzv6vehDkllXhhEaK1aXmFGe4HKJk4/Y6gfjFLM3zaSAiEAyQUykfPLoUUNRt236maz1iWiEAixrbEEBYQeA1PTYb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSPI2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3lw/8CxOTrKtRS0oqAFtoG5OZ8HzmWuAV0eyi/0/Dr1k4sCutPHnI\r\nYbIQ45UWX9rBH6wKwhQ4Ic65aN2pBHZi14xlIRmM1CFTw3FPFclzVvlFAS4w\r\nz0FTkv+PIFf8d0fr9+ziuA5tzm+GSg6oqGjj2VW2YlDa6+GJbTnsn2GqAgdu\r\nLFxS8t/6ocax1iCoipteSkiWQdp8uWSGvu9BfCZ1Z8dVwPqpIl8ouqjdglHJ\r\n1YUdgHiQ9nznwcu1lvFOGR8kb8lYR/2fHdRVE+IAjj6hHQ+l/4lLA4TRfG9K\r\nTr+dYaZLohaAf5Eyl9fWcVoLzaytCvbcE0tA0V7iR+Om+6UFehZ+sDQGY6re\r\n5rulrcG4GqYmtmLmsYt5c5mreTLnqI5xII6QqnieeUVv+hHYzpfFQvl5mbbW\r\nXyBJJse6zM1Zp50MkeMxZRFDLAtfYv7htnf/mRncIVdzSvvVYKVLoSEYX7kk\r\nTC0H1CdnMBZ/AsNm8W/5uxBvw3yvsdsNTQ7lJ0K0fbwih2MkgqR5DrYZPAOA\r\nifJyn+Ct7nkm/B7KfVZ477x57bs9/4nMI9SGMFY8/kFuAp6qBWdNapmeInRJ\r\nArBavMmFyXIVnWEVQbRuJsjTAxkp7RH7cgcNdpRgIw/sj3jIFuShOhqZCTci\r\n9ZHBxj7q7Xyr83i45jEqx3NsdPJ9VGnK89w=\r\n=t2/y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "c038d3e89f5a400bd111bbf91428a1b18181aee8", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "templateOSS": {"version": "4.5.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.10.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "rimraf": "^3.0.0", "cmd-shim": "^5.0.0", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/read-cmd-shim_4.0.0_1665724982357_0.24341209080620319", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "read-cmd-shim", "version": "5.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "read-cmd-shim@5.0.0", "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/read-cmd-shim#readme", "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "6e5450492187a0749f6c80dcbef0debc1117acca", "tarball": "https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-5.0.0.tgz", "fileCount": 4, "integrity": "sha512-SEbJV7tohp3DAAILbEMPXavBjAnMN0tVnh4+9G8ihV4Pq3HYF9h8QNez9zkJ1ILkv9G2BjdzwctznGZXgu/HGw==", "signatures": [{"sig": "MEUCIQCm0fe7LH9fv39D+wrOCEHeA5dHm2SmljvUu65EJSKWFwIgLPrK2SPU99cK2n9Vu6bpT59JQlB+1fVSDXGXCCfH3/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/read-cmd-shim@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 5209}, "main": "lib/index.js", "engines": {"node": "^18.17.0 || >=20.5.0"}, "gitHead": "3a9c4014da4b81556b070ad2e3ac82ac29bd3a36", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "directories": {}, "templateOSS": {"publish": true, "version": "4.23.3", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "cmd-shim": "^7.0.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/read-cmd-shim_5.0.0_1727205802551_0.4202968054470553", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2015-09-09T20:14:50.889Z", "modified": "2025-05-14T20:03:37.216Z", "1.0.0": "2015-09-09T20:14:50.889Z", "1.0.1": "2015-09-09T21:48:26.193Z", "1.0.2": "2019-08-14T19:11:05.273Z", "1.0.3": "2019-08-14T19:42:29.904Z", "1.0.4": "2019-08-20T19:46:05.226Z", "1.0.5": "2019-11-05T19:18:53.758Z", "2.0.0": "2020-01-27T07:42:54.965Z", "3.0.0": "2022-04-05T16:39:22.059Z", "3.0.1": "2022-08-16T17:15:10.594Z", "4.0.0": "2022-10-14T05:23:02.527Z", "5.0.0": "2024-09-24T19:23:22.743Z"}, "bugs": {"url": "https://github.com/npm/read-cmd-shim/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/read-cmd-shim#readme", "repository": {"url": "git+https://github.com/npm/read-cmd-shim.git", "type": "git"}, "description": "Figure out what a cmd-shim is pointing at. This acts as the equivalent of fs.readlink.", "maintainers": [{"email": "<EMAIL>", "name": "gar"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "owlstronaut"}], "readme": "# read-cmd-shim\n\nFigure out what a [`cmd-shim`](https://github.com/ForbesLindesay/cmd-shim)\nis pointing at.  This acts as the equivalent of\n[`fs.readlink`](https://nodejs.org/api/fs.html#fs_fs_readlink_path_callback).\n\n### Usage\n\n```\nconst readCmdShim = require('read-cmd-shim')\n\nreadCmdShim('/path/to/shim.cmd').then(destination => {\n  …\n})\n\nconst destination = readCmdShim.sync('/path/to/shim.cmd')\n```\n\n### readCmdShim(path) -> Promise\n\nReads the `cmd-shim` located at `path` and resolves with the _relative_\npath that the shim points at. Consider this as roughly the equivalent of\n`fs.readlink`.\n\nThis can read both `.cmd` style that are run by the Windows Command Prompt\nand Powershell, and the kind without any extension that are used by Cygwin.\n\nThis can return errors that `fs.readFile` returns, except that they'll\ninclude a stack trace from where `readCmdShim` was called.  Plus it can\nreturn a special `ENOTASHIM` exception, when it can't find a cmd-shim in the\nfile referenced by `path`.  This should only happen if you pass in a\nnon-command shim.\n\n### readCmdShim.sync(path)\n\nSame as above but synchronous. Errors are thrown.\n", "readmeFilename": "README.md", "users": {"iarna": true, "mattmcfarland": true}}