/**
 * 用户角色、组织和等级系统类型定义
 * Scarborough Market - 中世纪奇幻交易中心
 */

// 用户角色枚举
export enum UserRole {
  HERB_MERCHANT = 'herb_merchant', // 草本商人
  WANDERER = 'wanderer', // 流浪者
  LONE_HUNTER = 'lone_hunter', // 伶仃猎手
}

// 用户角色显示名称
export const UserRoleNames: Record<UserRole, { zh: string; en: string }> = {
  [UserRole.HERB_MERCHANT]: { zh: '草本商人', en: 'Herb Merchant' },
  [UserRole.WANDERER]: { zh: '流浪者', en: 'Wanderer' },
  [UserRole.LONE_HUNTER]: { zh: '伶仃猎手', en: 'Lone Hunter' },
};

// 组织枚举
export enum Organization {
  DARK_BROTHERHOOD = 'dark_brotherhood', // 暗转兄弟会
  STORMCLOAKS = 'stormcloaks', // 风暴斗篷
  THIEVES_SYNDICATE = 'thieves_syndicate', // 盗赋公司
  WINTERHOLD_ACADEMY = 'winterhold_academy', // 冬保学院
  IMPERIAL_LEGION = 'imperial_legion', // 帝国军团
  INDEPENDENT = 'independent', // 独立
}

// 组织显示名称
export const OrganizationNames: Record<
  Organization,
  { zh: string; en: string }
> = {
  [Organization.DARK_BROTHERHOOD]: {
    zh: '暗转兄弟会',
    en: 'Dark Turn Brotherhood',
  },
  [Organization.STORMCLOAKS]: { zh: '风暴斗篷', en: 'Stormcloaks' },
  [Organization.THIEVES_SYNDICATE]: { zh: '盗赋公司', en: 'Thieves Syndicate' },
  [Organization.WINTERHOLD_ACADEMY]: {
    zh: '冬保学院',
    en: 'Winterhold Academy',
  },
  [Organization.IMPERIAL_LEGION]: { zh: '帝国军团', en: 'Imperial Legion' },
  [Organization.INDEPENDENT]: { zh: '独立', en: 'Independent' },
};

// 暗转兄弟会等级
export enum DarkBrotherhoodRank {
  INITIATE = 'initiate', // 见习
  BLADE = 'blade', // 隐刃
  DARK_RITE = 'dark_rite', // 暗祭
  BLOOD_SHADOW = 'blood_shadow', // 血影
  NIGHT_MOTHER = 'night_mother', // 夜母
  GHOST_KING = 'ghost_king', // 幽王
}

// 风暴斗篷等级
export enum StormcloaksRank {
  ABSOLUTE_RANK = 'absolute_rank', // 绝级 - 镜魇
  EARTH_RANK = 'earth_rank', // 地级 - 灼骨
  KILLER_RANK = 'killer_rank', // 杀级 - 无冥
  HEAVENLY_RANK = 'heavenly_rank', // 天级 - 绝风
}

// 盗赋公司等级
export enum ThievesSyndicateRank {
  NESTLING = 'nestling', // 雀栖
  NIGHTWALKER = 'nightwalker', // 夜行
  DARK_BLADE = 'dark_blade', // 暗刃
  LIGHT_SHADOW = 'light_shadow', // 光影
  GUILDMASTER = 'guildmaster', // 公领
}

// 冬保学院等级
export enum WinterholdAcademyRank {
  APPRENTICE = 'apprentice', // 学徒
  RUNEMASTER = 'runemaster', // 铭文师
  SORCERER = 'sorcerer', // 术士
  DEAN = 'dean', // 院长
}

// 帝国军团等级
export enum ImperialLegionRank {
  RECRUIT = 'recruit', // 新兵
  CENTURION = 'centurion', // 百夫长
  GENERAL = 'general', // 将军
  MARSHAL = 'marshal', // 元帅
}

// 统一的等级类型
export type OrganizationRank =
  | DarkBrotherhoodRank
  | StormcloaksRank
  | ThievesSyndicateRank
  | WinterholdAcademyRank
  | ImperialLegionRank
  | null;

// 等级显示信息接口
export interface RankInfo {
  zh: string;
  en: string;
  description?: string;
  sword?: string; // 武器名称（风暴斗篷专用）
  skill?: string; // 技能名称（风暴斗篷专用）
  background?: string; // 背景描述（风暴斗篷专用）
}

// 暗转兄弟会等级信息
export const DarkBrotherhoodRankInfo: Record<DarkBrotherhoodRank, RankInfo> = {
  [DarkBrotherhoodRank.INITIATE]: { zh: '见习', en: 'Initiate' },
  [DarkBrotherhoodRank.BLADE]: { zh: '隐刃', en: 'Blade' },
  [DarkBrotherhoodRank.DARK_RITE]: { zh: '暗祭', en: 'Dark Rite' },
  [DarkBrotherhoodRank.BLOOD_SHADOW]: { zh: '血影', en: 'Blood Shadow' },
  [DarkBrotherhoodRank.NIGHT_MOTHER]: { zh: '夜母', en: 'Night Mother' },
  [DarkBrotherhoodRank.GHOST_KING]: { zh: '幽王', en: 'Ghost King' },
};

// 风暴斗篷等级信息
export const StormcloaksRankInfo: Record<StormcloaksRank, RankInfo> = {
  [StormcloaksRank.HEAVENLY_RANK]: {
    zh: '天级 - 绝风',
    en: 'Heavenly Rank - Absolute Wind',
    sword: '镇夜',
    skill: '千刃归宗',
    background: '最孤傲的剑术宗师',
  },
  [StormcloaksRank.KILLER_RANK]: {
    zh: '杀级 - 无冥',
    en: 'Killer Rank - No Shade',
    sword: '影裂',
    skill: '雾行杀诀',
    background: '生死之间如幽灵般来去',
  },
  [StormcloaksRank.EARTH_RANK]: {
    zh: '地级 - 灼骨',
    en: 'Earth Rank - Scorch Bone',
    sword: '赤蝰',
    skill: '焚骨咒刃',
    background: '火毒双修，诡异无常',
  },
  [StormcloaksRank.ABSOLUTE_RANK]: {
    zh: '绝级 - 镜魇',
    en: 'Absolute Rank - Mirror Nightmare',
    sword: '断念',
    skill: '镜中杀',
    background: '心灵幻术型刺客',
  },
};

// 盗赋公司等级信息
export const ThievesSyndicateRankInfo: Record<ThievesSyndicateRank, RankInfo> =
  {
    [ThievesSyndicateRank.NESTLING]: { zh: '雀栖', en: 'Nestling' },
    [ThievesSyndicateRank.NIGHTWALKER]: { zh: '夜行', en: 'Nightwalker' },
    [ThievesSyndicateRank.DARK_BLADE]: { zh: '暗刃', en: 'Dark Blade' },
    [ThievesSyndicateRank.LIGHT_SHADOW]: { zh: '光影', en: 'Light Shadow' },
    [ThievesSyndicateRank.GUILDMASTER]: { zh: '公领', en: 'Guildmaster' },
  };

// 冬保学院等级信息
export const WinterholdAcademyRankInfo: Record<
  WinterholdAcademyRank,
  RankInfo
> = {
  [WinterholdAcademyRank.APPRENTICE]: { zh: '学徒', en: 'Apprentice' },
  [WinterholdAcademyRank.RUNEMASTER]: { zh: '铭文师', en: 'Runemaster' },
  [WinterholdAcademyRank.SORCERER]: { zh: '术士', en: 'Sorcerer' },
  [WinterholdAcademyRank.DEAN]: { zh: '院长', en: 'Dean' },
};

// 帝国军团等级信息
export const ImperialLegionRankInfo: Record<ImperialLegionRank, RankInfo> = {
  [ImperialLegionRank.RECRUIT]: { zh: '新兵', en: 'Recruit' },
  [ImperialLegionRank.CENTURION]: { zh: '百夫长', en: 'Centurion' },
  [ImperialLegionRank.GENERAL]: { zh: '将军', en: 'General' },
  [ImperialLegionRank.MARSHAL]: { zh: '元帅', en: 'Marshal' },
};

// 用户资料扩展接口
export interface UserProfile {
  id: string;
  username: string;
  email: string;
  bio?: string;
  location?: string;
  avatar_url?: string;
  role?: UserRole;
  organization?: Organization;
  organization_rank?: OrganizationRank;
  reputation?: number;
  gold?: number;
  tasks_completed?: number;
  shop_id?: string;
  created_at: string;
  updated_at: string;
}

// 商店接口
export interface Shop {
  id: string;
  owner_id: string;
  name: string;
  description?: string;
  latitude: number;
  longitude: number;
  is_open: boolean;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
}

// 商品接口
export interface Product {
  id: string;
  shop_id: string;
  name: string;
  description: string;
  price: number;
  image_url?: string;
  stock: number;
  is_available: boolean;
  discovered_year?: number; // 发现年份，如 1200
  created_at: string;
  updated_at: string;
}

// 订单接口
export interface Order {
  id: string;
  buyer_id: string;
  seller_id: string;
  product_id: string;
  quantity: number;
  total_price: number;
  status: 'pending' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

// 组织据点接口
export interface OrganizationStronghold {
  id: string;
  organization: Organization;
  name: string;
  latitude: number;
  longitude: number;
  description?: string;
}

// 工具函数：获取组织的等级列表
export function getOrganizationRanks(
  organization: Organization
): OrganizationRank[] {
  switch (organization) {
    case Organization.DARK_BROTHERHOOD:
      return Object.values(DarkBrotherhoodRank);
    case Organization.STORMCLOAKS:
      return Object.values(StormcloaksRank);
    case Organization.THIEVES_SYNDICATE:
      return Object.values(ThievesSyndicateRank);
    case Organization.WINTERHOLD_ACADEMY:
      return Object.values(WinterholdAcademyRank);
    case Organization.IMPERIAL_LEGION:
      return Object.values(ImperialLegionRank);
    default:
      return [];
  }
}

// 工具函数：获取等级信息
export function getRankInfo(
  organization: Organization,
  rank: OrganizationRank
): RankInfo | null {
  if (!rank) return null;

  switch (organization) {
    case Organization.DARK_BROTHERHOOD:
      return DarkBrotherhoodRankInfo[rank as DarkBrotherhoodRank] || null;
    case Organization.STORMCLOAKS:
      return StormcloaksRankInfo[rank as StormcloaksRank] || null;
    case Organization.THIEVES_SYNDICATE:
      return ThievesSyndicateRankInfo[rank as ThievesSyndicateRank] || null;
    case Organization.WINTERHOLD_ACADEMY:
      return WinterholdAcademyRankInfo[rank as WinterholdAcademyRank] || null;
    case Organization.IMPERIAL_LEGION:
      return ImperialLegionRankInfo[rank as ImperialLegionRank] || null;
    default:
      return null;
  }
}
