import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error(
    '❌ 请配置 SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY 环境变量'
  );
}

export const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);
