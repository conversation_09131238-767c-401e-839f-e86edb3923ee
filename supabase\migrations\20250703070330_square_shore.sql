/*
  # Initial Database Schema for Scarborough Fair Market App

  1. New Tables
    - `profiles` - User profiles with character information
    - `herbs` - Master list of herbs and their properties
    - `herb_discoveries` - User herb discovery records
    - `products` - Marketplace products
    - `tasks` - Available tasks and quests
    - `exploration_areas` - Explorable locations
    - `transactions` - Purchase/sale records
    - `user_inventory` - User item inventory

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
    - Secure user data access

  3. Functions
    - Auto-create profile on user signup
    - Update timestamps automatically
*/

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username text UNIQUE,
  full_name text,
  avatar_url text,
  character_class text DEFAULT 'wanderer',
  level integer DEFAULT 1,
  experience integer DEFAULT 0,
  gold integer DEFAULT 100,
  reputation integer DEFAULT 0,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create herbs table
CREATE TABLE IF NOT EXISTS herbs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  scientific_name text NOT NULL,
  description text NOT NULL,
  rarity text DEFAULT 'common' CHECK (rarity IN ('common', 'uncommon', 'rare', 'legendary')),
  base_price integer NOT NULL DEFAULT 10,
  effects jsonb DEFAULT '[]',
  image_url text,
  created_at timestamptz DEFAULT now()
);

-- Create herb discoveries table
CREATE TABLE IF NOT EXISTS herb_discoveries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  herb_id uuid REFERENCES herbs(id) ON DELETE CASCADE NOT NULL,
  location text NOT NULL,
  date_found timestamptz NOT NULL,
  notes text,
  quality integer DEFAULT 100 CHECK (quality >= 0 AND quality <= 100),
  created_at timestamptz DEFAULT now()
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text NOT NULL,
  category text DEFAULT 'herbs' CHECK (category IN ('herbs', 'potions', 'decorations', 'equipment')),
  price integer NOT NULL,
  original_price integer,
  rarity text DEFAULT 'common' CHECK (rarity IN ('common', 'rare', 'legendary')),
  in_stock boolean DEFAULT true,
  effects jsonb,
  image_url text,
  seller_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create tasks table
CREATE TABLE IF NOT EXISTS tasks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text NOT NULL,
  type text DEFAULT 'herb_collection' CHECK (type IN ('herb_collection', 'exploration', 'trading', 'crafting')),
  difficulty text DEFAULT 'easy' CHECK (difficulty IN ('easy', 'medium', 'hard', 'legendary')),
  progress integer DEFAULT 0,
  max_progress integer NOT NULL,
  reward_gold integer DEFAULT 0,
  reward_experience integer DEFAULT 0,
  reward_items jsonb,
  time_limit timestamptz,
  status text DEFAULT 'available' CHECK (status IN ('available', 'in_progress', 'completed', 'expired')),
  assigned_to uuid REFERENCES profiles(id) ON DELETE SET NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create exploration areas table
CREATE TABLE IF NOT EXISTS exploration_areas (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text NOT NULL,
  difficulty text DEFAULT 'easy' CHECK (difficulty IN ('easy', 'medium', 'hard', 'legendary')),
  coordinates jsonb NOT NULL,
  discovered_by uuid[] DEFAULT '{}',
  rewards jsonb DEFAULT '[]',
  requirements text,
  time_required integer DEFAULT 30,
  created_at timestamptz DEFAULT now()
);

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  buyer_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  seller_id uuid REFERENCES profiles(id) ON DELETE SET NULL,
  product_id uuid REFERENCES products(id) ON DELETE CASCADE NOT NULL,
  quantity integer DEFAULT 1,
  total_amount integer NOT NULL,
  transaction_type text DEFAULT 'purchase' CHECK (transaction_type IN ('purchase', 'sale', 'trade')),
  status text DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
  created_at timestamptz DEFAULT now()
);

-- Create user inventory table
CREATE TABLE IF NOT EXISTS user_inventory (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  product_id uuid REFERENCES products(id) ON DELETE CASCADE NOT NULL,
  quantity integer DEFAULT 1,
  acquired_at timestamptz DEFAULT now(),
  UNIQUE(user_id, product_id)
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE herbs ENABLE ROW LEVEL SECURITY;
ALTER TABLE herb_discoveries ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE exploration_areas ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_inventory ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can read own profile"
  ON profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile"
  ON profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

-- Herbs policies (public read)
CREATE POLICY "Anyone can read herbs"
  ON herbs
  FOR SELECT
  TO authenticated
  USING (true);

-- Herb discoveries policies
CREATE POLICY "Users can read own discoveries"
  ON herb_discoveries
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create own discoveries"
  ON herb_discoveries
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own discoveries"
  ON herb_discoveries
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

-- Products policies
CREATE POLICY "Anyone can read products"
  ON products
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can create products"
  ON products
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = seller_id);

CREATE POLICY "Users can update own products"
  ON products
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = seller_id);

-- Tasks policies
CREATE POLICY "Anyone can read available tasks"
  ON tasks
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can update assigned tasks"
  ON tasks
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = assigned_to);

-- Exploration areas policies
CREATE POLICY "Anyone can read exploration areas"
  ON exploration_areas
  FOR SELECT
  TO authenticated
  USING (true);

-- Transactions policies
CREATE POLICY "Users can read own transactions"
  ON transactions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

CREATE POLICY "Users can create transactions"
  ON transactions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = buyer_id);

-- User inventory policies
CREATE POLICY "Users can read own inventory"
  ON user_inventory
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own inventory"
  ON user_inventory
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- Functions
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, username, full_name)
  VALUES (new.id, new.raw_user_meta_data->>'username', new.raw_user_meta_data->>'full_name');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE handle_new_user();

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add update triggers
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at
  BEFORE UPDATE ON products
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at
  BEFORE UPDATE ON tasks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();