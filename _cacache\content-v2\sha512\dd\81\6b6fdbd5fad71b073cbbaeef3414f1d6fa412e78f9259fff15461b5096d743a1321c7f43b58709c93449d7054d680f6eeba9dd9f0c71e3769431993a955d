{"_id": "proc-log", "_rev": "32-c6b76cd79e2a8ab0772f21660ad33e9e", "name": "proc-log", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "proc-log", "version": "1.0.0", "author": {"url": "https://izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "proc-log@1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/proc-log#readme", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "dist": {"shasum": "0d927307401f69ed79341e83a0b2c9a13395eb77", "tarball": "https://registry.npmjs.org/proc-log/-/proc-log-1.0.0.tgz", "fileCount": 4, "integrity": "sha512-aCk8AO51s+4JyuYGg3Q/a6gnrlDO09NpVWePtjp7xwphcoQ04x5WAfCyugcsbLooWcMJ87CLkD4+604IckEdhg==", "signatures": [{"sig": "MEQCICtxishmKTpuYKh0gwKbmM49wQx+z77fV5SoZxa/x9PVAiB1bReA9q+TPxNknpXQIQ6Pj627lfD8VOExt75KtLD+Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3529, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeJdgCRA9TVsSAnZWagAAF5IP/1eNi6mGt4enqs2uTwFC\niX55NdQBWtBQ5/ctC1moeUqrkpt/i0KhosTJh0Kifb5cVBYsJHUFBJBJYdvH\nLs8CJVvMNSGt2fMHB/1Q44xNwxLHigTQ0ovqmfat1gfTCCp01WsiKL+aAJf7\np94eLHYQi5VlkgtFH23ZHuJEzGnW64Ybbsvz5lSnSxbdK2FSyUuVK3XmZAJb\n0rYvl7BTIhvZdVmfiyBnUlwUbLljUgpxeX6GX5I8RTfhbgm2QNuDjpgFDhBv\nhtsmSXpD5rKia4nDWNY/Ct0FwsVQfen6CWNk25fmw/V0Gmhdn7WiGEjXW89v\nZEtknHAjRIZ+TNK+Whc8PEEB5tPsrPZVSCgFmO+05snokGc8V0XP45rT8Fuc\nxFjLXRDZqjKpjCCMa/QMnn3wZSaCqCqsFbWwkfYXou61Ry9nPzWL1N4iMijO\nD1vQvkmxDEwmXzRzgVJeSXAJvLPOBTWmuOMmHMHlfLJCch/289/X8moniCE6\na9PkOlg1fO/3Olzrn/s+v0a391pkfvc5vF+i9AuMHLc/Q4WhfCEOGGcLEfK9\nb5LBVJqVaB1x672Xe/r9QH2SuPwx0zkLLWFZ2BvwTTQOCOyQ1W7+VrYo2YjY\nSNbf9ckyjLvWJQaebU66XScnZp+ODvJOGRd2b6U6/cFGPu7qjSH9ZFRvDgrj\ndfBQ\r\n=iZuW\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "a3ebf6f6588c1a89ba573b498bfdfa21b71b4aef", "scripts": {"snap": "tap", "test": "tap", "postsnap": "eslint index.js test/*.js --fix", "posttest": "eslint index.js test/*.js", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/proc-log.git", "type": "git"}, "_npmVersion": "7.9.0", "description": "just emit 'log' events on the process object", "directories": {}, "_nodeVersion": "15.3.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.0.2", "eslint": "^7.9.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proc-log_1.0.0_1618515808246_0.33636671349896585", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "proc-log", "version": "2.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "proc-log@2.0.0", "maintainers": [{"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/proc-log#readme", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "dist": {"shasum": "25f8cb346a5d08e27f2422b3ca6ba8379bcbf8ba", "tarball": "https://registry.npmjs.org/proc-log/-/proc-log-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-I/35MfCX2H8jBUhKN8JB8nmqvQo/nKdrBodBY7L3RhDSPPyvOHwLYNmPuhwuJq7a7C3vgFKWGQM+ecPStcvOHA==", "signatures": [{"sig": "MEYCIQCmzPjw2wVqirypeTHp4A8J+M+qFnCigZqN/XwnDD0CSwIhAIdbP6hRh1n7QwT9hG0cedF2ubqUIFYdVtcCkSWzN9rU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBWzKCRA9TVsSAnZWagAABNQP/2n7O2dda/91D8yi9H/n\n6ZteH6o1SHK2L9TLw8MN/ynojeq8yfUE9SIUyou0oRZerM7nVjd6kbw86iRz\nW021GlatUjeftWmr08JnTuXXq5vWoyXvXmDPPIe+HLYZHWQXLVPw7+zlJKij\nFe+aXqZvaUy2e+fp2UtsaAiiPRyEBppwWmAAUjwUh4OFf93qA03EG2Jw64fF\nelelb1+f9ArIZIpuZ/qcVEhZ5l0rI24OfhrQGMCMkEDLUQMzSXQvOwfN4MUg\niMczL5TKYfIYuryMQjrfVe7Vf33FxEBtaLEpOEcUO+fsdBogm9NBMmIwx2oz\nYiDZg9guqp/2faItvK+Z/0x5ySNZWMAwLOVmbn37oQBtfE3gFDthCjysT9Nq\nEx2gH9boNMs2Ti2+6GbdHLIQSA3wXGaqoFRHyWH8tUbD80We6cEUWB3TBaoQ\n45HvTeCaMIKlMZSR2twT515hwLpdUhey/yeVv/63B+F5GjdG8rCJWlBfaLjY\nhFKFKGkuIOUOeB2kqTCsetjEIoJM6ndWkZJRgLXhEuEds08kjaBwqqEC9Lqw\nr1ViUaFiuhe8JyxhfhtUFiYbJU8vdHtlEkQ0GyQ769pg7bMA8P1gNSrZdKMs\nA+P5h0mBjXVlonYny0rMCFBVOTProz00w0ck+nv2T0x2Ze5isgInYZ17SdnC\n/Ki8\r\n=1Nk3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}, "gitHead": "adccecc2bf5e77427e3fefe826a8e5a1a57640d7", "scripts": {"lint": "eslint '**/*.js'", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "npm-template-check", "postsnap": "eslint index.js test/*.js --fix", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "template-copy": "npm-template-copy --force", "prepublishOnly": "git push origin --follow-tags"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/proc-log.git", "type": "git"}, "_npmVersion": "8.4.1", "description": "just emit 'log' events on the process object", "directories": {}, "templateOSS": {"version": "2.7.1"}, "_nodeVersion": "16.14.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^15.1.6", "@npmcli/template-oss": "^2.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/proc-log_2.0.0_1644522698587_0.5470729257783982", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "proc-log", "version": "2.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "proc-log@2.0.1", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/proc-log#readme", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "dist": {"shasum": "8f3f69a1f608de27878f91f5c688b225391cb685", "tarball": "https://registry.npmjs.org/proc-log/-/proc-log-2.0.1.tgz", "fileCount": 4, "integrity": "sha512-Kcmo2FhfDTXdcbfDH76N7uBYHINxc/8GW7UAVuVP9I+Va3uHSerrnKV6dLooga/gh7GlgzuCCr/eoldnL1muGw==", "signatures": [{"sig": "MEQCIDovd0CjshDX0XUBoW2m0P5CxXoS056rKA2wOzSUXArHAiABuCWVbFcNC9Jsd0Q3IeAt6JjawcDRz3R0qEBZ9qBNMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQh+MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrW4xAAhOqyDdX42UkizrLSsVstrXA05GLHTAtMIuCI34ZshSlqdf5k\r\nz5NeF1Z90T52mtmqKlfWpq4zzGSDgdf0xSQdjZuM9zmyNShIQ0ILx1L4tPUb\r\nbWMYDKcjy+4s672ds7YRk10tlTuP3IKvMvUVM5MEt9m9KJCKZ7c6VuvaE3fj\r\nn6TWSel9PQfLltZbMFcPw+Gjhv5qOjJ69LWEUzoU5iL6vCp0OV/FKYRkqsjg\r\nRc8uw4xIuWF41fMG74eL7CrniOIBArXA8dduQ+RiOkFbXAY6lQhKm/+09YCq\r\nmPuyL7XPcS/Hm5B1JZidkuyo2k3vmDoLxHPf1DThr510uazvpRR4GdNyDANN\r\njjOgFpPrt4C+Vf2aqr6MNUX6DrkH125C/y2Sm6wRsUjOnrSwNceX+tHU2XJf\r\n8xxi++lLzfKJ52bjO2U/qktJpDaTmBgSeueI2CCrVa13dJM9XUiDf18cDGfH\r\nBQBVBq1ZnBYCdm1V01Gren1Uv70C4G3+5a8qG+SxQn2FXLoPVb05iom6oPZY\r\niuYMisS3g9D8QAyFHFOBx6AlC3D0lk3YUrnjiq+JFpH9ROpNO2gtJEuI2CJl\r\nM/Q/N5LDHRUetpDKlpSVGHlwJsoCy5YltCSSkL+z95MZ/r7URX7V9k0Zq32h\r\nVyPO/tq+MSPP2HBjd6ycsQe1C5xXzQtWcbw=\r\n=uaEm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "71042352b67eb0a81e8cfea9b5e51966b9a84ead", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "eslint index.js test/*.js --fix", "posttest": "npm run lint", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/proc-log.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "just emit 'log' events on the process object", "directories": {}, "templateOSS": {"version": "3.2.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "3.2.0", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proc-log_2.0.1_1648500620397_0.38798423133177296", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "proc-log", "version": "3.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "proc-log@3.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/proc-log#readme", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "fb05ef83ccd64fd7b20bbe9c8c1070fc08338dd8", "tarball": "https://registry.npmjs.org/proc-log/-/proc-log-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-++Vn7NS4Xf9NacaU9Xq3URUuqZETPsf8L4j5/ckhaRYsfPeRyzGw+iDjFhV/Jr3uNmTvvddEJFWh5R1gRgUH8A==", "signatures": [{"sig": "MEUCIHifbHgF6M3IpbNVu8XNpsbTUeNJM3qIs5XF1g0YLliHAiEAuptlCUzpR9akldhsbgMLMxeuPC7hOWXL8fkpJ1WgP7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSPI6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjPQ//UPSrpMvLqry94iJkulfi5zOWvurCtHjX2ttnkkZ/s0H+SigN\r\nvOe8PQcIacbmrBqYSydLuM4XBf3vUErBZXjwegNzTY28q0kNL6JMZink/28e\r\nAaKu2EY50BfKG/yB+StonfnfXiv9XGqtIvdqoVF0yMKbwghjVUNfLb3mjx4+\r\n4D3S3obKZPksS1up5yLCMyMOiz3h5W30M05Ipyyp0IHTVc4NQB7zpVN/fVrt\r\nRBt/Z0aml3JPe+A2tKq8rtIFegVsuyp8oDT/xGugZRNI7cuK3bLTQxQXZvJW\r\neusL/Q/TzuyieN35bZuzrdzk0Ofs7Sx4LUEcbwv2AbBOhUdSZHM71gXe4GHq\r\nP+5qLsmZEoQ/dDbaQ/mYst/bYlvb36Avt0naN+SizCBrS7AuNErb+vW6Lud6\r\nnNF4q0iT0IBERbMAKqMUfAoriTidLInRwHL+YA7Zd4Plw9djUqWF2X3L7q1z\r\n/kwvinhV6tP04LpOlnKOlB5eUD8yyWF5WN0vJPRJu5TseUMOAdSEQ0/qlwRh\r\nA32FMskN+dBEHy6mDa5fJfMkXA8/lARwdyHKq/PvNbQCJCL5FU6WhPgk3heK\r\n94sOQbe4zwx6VHJ1InfFRElXW/MsmN+S4sQnUhbQVHOQp+04h38e54IqqcwF\r\nQbFZXz2zcDVctGitwZjS6dIuinKsLDuXivk=\r\n=QR6L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "a3318aac6541572d897f404c1db7d905016c5cfe", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "eslint index.js test/*.js --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/proc-log.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "just emit 'log' events on the process object", "directories": {}, "templateOSS": {"version": "4.5.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.10.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/proc-log_3.0.0_1665724986606_0.6562993514020836", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "proc-log", "version": "4.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "proc-log@4.0.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/proc-log#readme", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "b8aac7609599a5aea22c23a7544523bc1ff85eda", "tarball": "https://registry.npmjs.org/proc-log/-/proc-log-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-v1lzmYxGDs2+OZnmYtYZK3DG8zogt+CbQ+o/iqqtTfpyCmGWulCTEQu5GIbivf7OjgIkH2Nr8SH8UxAGugZNbg==", "signatures": [{"sig": "MEUCIDOPLHGvLEa/XkGjJD3bD9V1Nz7QSQZXZOuJ49mtjWmMAiEAs0FYXQI5Y+YLkS+j4RQ+ZgguYidV3P8JFwRRD8bEzN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/proc-log@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 7265}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "ad99316a9f70e3beced2f0a0709649b6fd7b3e52", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "eslint index.js test/*.js --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/proc-log.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "just emit 'log' events on the process object", "directories": {}, "templateOSS": {"publish": true, "version": "4.21.3", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "20.12.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/proc-log_4.0.0_1712941001450_0.09906957453402687", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "proc-log", "version": "4.1.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "proc-log@4.1.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/proc-log#readme", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "ab6c1552b454e9e467cb58119179e4557d109b34", "tarball": "https://registry.npmjs.org/proc-log/-/proc-log-4.1.0.tgz", "fileCount": 4, "integrity": "sha512-dmQ2iPw2nJMi9/4dpaG1wd0m1GE+K5kW7RGbjy5hoEEGnhPIzsm+klBO5RGGdcoYbWsNtU2KSNAdEldts+icLg==", "signatures": [{"sig": "MEUCIQDFjto5R5AGTaZnse3SonjBcvLLaZFQ2e+mNOCO99xviAIgenDJJxSG2+oBTE9kd/Q4r8oOMM4wFsg0lY8jvPvduEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/proc-log@4.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 11937}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "3dbd032fc792e66ab4987265eb9c3c1bd7667386", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "eslint index.js test/*.js --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/proc-log.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "just emit 'log' events on the process object", "directories": {}, "templateOSS": {"publish": true, "version": "4.21.3", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "20.12.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/proc-log_4.1.0_1713198992922_0.0027306484928157904", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "proc-log", "version": "4.2.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "proc-log@4.2.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/proc-log#readme", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "b6f461e4026e75fdfe228b265e9f7a00779d7034", "tarball": "https://registry.npmjs.org/proc-log/-/proc-log-4.2.0.tgz", "fileCount": 4, "integrity": "sha512-g8+OnU/L2v+wyiVK+D5fA34J7EH8jZ8DDlvwhRCMxmMj7UCBvxiO1mGeN+36JXIKF4zevU4kRBd8lVgG9vLelA==", "signatures": [{"sig": "MEUCIQCNStNROA4yNJ9PelPPE2Zu/qXkWLycFqs2cUlUrvGcfQIgQloGakWeTeBxeHy8tAlNpXjvlDF+X8/oZk7PB1DfNrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/proc-log@4.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 12261}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "953e6035670f9afe2ec93f6286d76db2828854d6", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "postsnap": "eslint index.js test/*.js --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/proc-log.git", "type": "git"}, "_npmVersion": "10.5.2", "description": "just emit 'log' events on the process object", "directories": {}, "templateOSS": {"publish": true, "version": "4.21.3", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "20.12.1", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.21.3", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/proc-log_4.2.0_1713300592930_0.14959539526116528", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "proc-log", "version": "5.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "proc-log@5.0.0", "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/proc-log#readme", "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}, "dist": {"shasum": "e6c93cf37aef33f835c53485f314f50ea906a9d8", "tarball": "https://registry.npmjs.org/proc-log/-/proc-log-5.0.0.tgz", "fileCount": 4, "integrity": "sha512-Azwzvl90HaF0aCz1JrDdXQykFakSSNPaPoiZ9fm5qJIMHioDZEi7OAdRwSm6rSoPtY3Qutnm3L7ogmg3dc+wbQ==", "signatures": [{"sig": "MEQCIBDwbtbw4ZO3G0Ar3DKMzs/lxt6nkwOZJQqQjCNhmP+ZAiB/lBILWDPgnRuP5QJD+aNySwJjefjFDP8tVNyJPgOQKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/proc-log@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 12287}, "main": "lib/index.js", "engines": {"node": "^18.17.0 || >=20.5.0"}, "gitHead": "8105dea8ca31296f5826e62683e63539401d6a8e", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "postsnap": "eslint index.js test/*.js --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/proc-log.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "just emit 'log' events on the process object", "directories": {}, "templateOSS": {"publish": true, "version": "4.23.3", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.8.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/proc-log_5.0.0_1725495681824_0.5708126841081362", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2021-04-15T19:43:28.190Z", "modified": "2025-05-14T20:03:34.311Z", "1.0.0": "2021-04-15T19:43:28.370Z", "2.0.0": "2022-02-10T19:51:38.758Z", "2.0.1": "2022-03-28T20:50:20.548Z", "3.0.0": "2022-10-14T05:23:06.806Z", "4.0.0": "2024-04-12T16:56:41.595Z", "4.1.0": "2024-04-15T16:36:33.064Z", "4.2.0": "2024-04-16T20:49:53.093Z", "5.0.0": "2024-09-05T00:21:21.957Z"}, "bugs": {"url": "https://github.com/npm/proc-log/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/proc-log#readme", "repository": {"url": "git+https://github.com/npm/proc-log.git", "type": "git"}, "description": "just emit 'log' events on the process object", "maintainers": [{"email": "<EMAIL>", "name": "gar"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "owlstronaut"}], "readme": "# proc-log\n\nEmits events on the process object which a listener can consume and print to the terminal or log file.\n\nThis is used by various modules within the npm CLI stack in order to send log events that can be consumed by a listener on the process object.\n\nCurrently emits `log`, `output`, `input`, and `time` events.\n\n## API\n\n```js\nconst { log, output, input, time } = require('proc-log')\n```\n\n#### output\n* `output.standard(...args)` calls `process.emit('output', 'standard', ...args)`\n  \n  This is for general standard output.  Consumers will typically show this on stdout (after optionally formatting or filtering it).\n\n* `output.error(...args)` calls `process.emit('output', 'error', ...args)`\n  \n  This is for general error output.  Consumers will typically show this on stderr (after optionally formatting or filtering it).\n\n* `output.buffer(...args)` calls `process.emit('output', 'buffer', ...args)`\n  \n  This is for buffered output.  Consumers will typically buffer this until they are ready to display.\n\n* `output.flush(...args)` calls `process.emit('output', 'flush', ...args)`\n  \n  This is to indicate that the output buffer should be flushed.\n\n* `output.LEVELS` an array of strings of all output method names\n\n#### log\n* `log.error(...args)` calls `process.emit('log', 'error', ...args)`\n  \n  The highest log level.  For printing extremely serious errors that indicate something went wrong.\n\n* `log.warn(...args)` calls `process.emit('log', 'warn', ...args)`\n  \n  A fairly high log level.  Things that the user needs to be aware of, but which won't necessarily cause improper functioning of the system.\n\n* `log.notice(...args)` calls `process.emit('log', 'notice', ...args)`\n  \n  Notices which are important, but not necessarily dangerous or a cause for excess concern.\n\n* `log.info(...args)` calls `process.emit('log', 'info', ...args)`\n  \n  Informative messages that may benefit the user, but aren't particularly important.\n\n* `log.verbose(...args)` calls `process.emit('log', 'verbose', ...args)`\n  \n  Noisy output that is more detail that most users will care about.\n\n* `log.silly(...args)` calls `process.emit('log', 'silly', ...args)`\n  \n  Extremely noisy excessive logging messages that are typically only useful for debugging.\n\n* `log.http(...args)` calls `process.emit('log', 'http', ...args)`\n  \n  Information about HTTP requests made and/or completed.\n\n* `log.timing(...args)` calls `process.emit('log', 'timing', ...args)`\n  \n  Timing information.\n\n* `log.pause()` calls `process.emit('log', 'pause')`\n  \n  Used to tell the consumer to stop printing messages.\n\n* `log.resume()` calls `process.emit('log', 'resume')`\n  \n  Used to tell the consumer that it is ok to print messages again.\n\n* `log.LEVELS` an array of strings of all log method names\n\n#### input\n\n* `input.start(fn?)` calls `process.emit('input', 'start')`\n\n  Used to tell the consumer that the terminal is going to begin reading user input. Returns a function that will call `input.end()` for convenience.\n  \n  This also takes an optional callback which will run `input.end()` on its completion. If the callback returns a `Promise` then `input.end()` will be run during `finally()`.\n\n* `input.end()` calls `process.emit('input', 'end')`\n\n  Used to tell the consumer that the terminal has stopped reading user input.\n\n* `input.read(...args): Promise` calls `process.emit('input', 'read', resolve, reject, ...args)`\n\n  Used to tell the consumer that the terminal is reading user input and returns a `Promise` that the producer can `await` until the consumer has finished its async action.\n  \n  This emits `resolve` and `reject` functions (in addition to all passed in arguments) which the consumer must use to resolve the returned `Promise`.\n\n#### time\n\n* `time.start(timerName, fn?)` calls `process.emit('time', 'start', 'timerName')`\n\n  Used to start a timer with the specified name. Returns a function that will call `time.end()` for convenience.\n  \n  This also takes an optional callback which will run `time.end()` on its completion. If the callback returns a `Promise` then `time.end()` will be run during `finally()`.\n\n* `time.end(timerName)` calls `process.emit('time', 'end', timeName)`\n\n  Used to tell the consumer to stop a timer with the specified name.\n\n## Examples\n\n### log\n\nEvery `log` method calls `process.emit('log', level, ...otherArgs)` internally.  So in order to consume those events you need to do `process.on('log', fn)`.\n\n#### Colorize based on level\n\nHere's an example of how to consume `proc-log` log events and colorize them based on level:\n\n```js\nconst chalk = require('chalk')\n\nprocess.on('log', (level, ...args) => {\n  if (level === 'error') {\n    console.log(chalk.red(level), ...args)\n  } else {\n    console.log(chalk.blue(level), ...args)\n  }\n})\n```\n\n#### Pause and resume\n\n`log.pause` and `log.resume` are included so you have the ability to tell your consumer that you want to pause or resume your display of logs. In the npm CLI we use this to buffer all logs on init until we know the correct loglevel to display.  But we also setup a second handler that writes everything to a file even if paused.\n\n```js\nlet paused = true\nconst buffer = []\n\n// this handler will buffer and replay logs only after `procLog.resume()` is called\nprocess.on('log', (level, ...args) => {\n  if (level === 'resume') {\n    buffer.forEach((item) => console.log(...item))\n    paused = false\n    return\n  } \n\n  if (paused) {\n    buffer.push([level, ...args])\n  } else {\n    console.log(level, ...args)\n  }\n})\n\n// this handler will write everything to a file\nprocess.on('log', (...args) => {\n  fs.appendFileSync('debug.log', args.join(' '))\n})\n```\n\n### input\n\n### `start` and `end`\n\n**producer.js**\n```js\nconst { output, input } = require('proc-log')\nconst { readFromUserInput } = require('./my-read')\n\n// Using callback passed to `start`\ntry {\n  const res = await input.start(\n    readFromUserInput({ prompt: 'OK?', default: 'y' })\n  )\n  output.standard(`User said ${res}`)\n} catch (err) {\n  output.error(`User cancelled: ${err}`)\n}\n\n// Manually calling `start` and `end`\ntry {\n  input.start()\n  const res = await readFromUserInput({ prompt: 'OK?', default: 'y' })\n  output.standard(`User said ${res}`)\n} catch (err) {\n  output.error(`User cancelled: ${err}`)\n} finally {\n  input.end()\n}\n```\n\n**consumer.js**\n```js\nconst { read } = require('read')\n\nprocess.on('input', (level) => {\n  if (level === 'start') {\n    // Hide UI to make room for user input being read\n  } else if (level === 'end') {\n    // Restore UI now that reading is ended\n  }\n})\n```\n\n### Using `read` to call `read()`\n\n**producer.js**\n```js\nconst { output, input } = require('proc-log')\n\ntry {\n  const res = await input.read({ prompt: 'OK?', default: 'y' })\n  output.standard(`User said ${res}`)\n} catch (err) {\n  output.error(`User cancelled: ${err}`)\n}\n```\n\n**consumer.js**\n```js\nconst { read } = require('read')\n\nprocess.on('input', (level, ...args) => {\n  if (level === 'read') {\n    const [res, rej, opts] = args\n    read(opts).then(res).catch(rej)\n  }\n})\n```", "readmeFilename": "README.md"}