import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
  TextInput,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  User,
  Save,
  X,
  Camera,
  ArrowLeft,
  Shield,
  Crown,
  Sword,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { router } from 'expo-router';
import {
  UserRole,
  Organization,
  UserRoleNames,
  OrganizationNames,
  getOrganizationRanks,
  getRankInfo,
} from '@/types/roles-organizations';

const { width, height } = Dimensions.get('window');

function ProfileSettingsContent() {
  const { isDark } = useTheme();
  const {
    user,
    profile,
    updateProfile,
    updateRole,
    joinOrganization,
    leaveOrganization,
  } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    username: profile?.username || '',
    email: user?.email || '',
    bio: (profile as any)?.bio || '',
    location: (profile as any)?.location || '',
    role: (profile as any)?.role || null,
    organization: (profile as any)?.organization || 'independent',
  });

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // 更新基本资料
      await updateProfile({
        username: formData.username,
        bio: formData.bio,
        location: formData.location,
      } as any);

      // 如果角色发生变化，更新角色
      if (formData.role && formData.role !== (profile as any)?.role) {
        await updateRole(formData.role);
      }

      // 如果组织发生变化，更新组织
      if (formData.organization !== (profile as any)?.organization) {
        if (formData.organization === 'independent') {
          await leaveOrganization();
        } else {
          await joinOrganization(formData.organization);
        }
      }

      setIsEditing(false);
      Alert.alert('成功', '个人资料已更新');
    } catch (error) {
      Alert.alert('错误', '更新失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      username: profile?.username || '',
      email: user?.email || '',
      bio: (profile as any)?.bio || '',
      location: (profile as any)?.location || '',
      role: (profile as any)?.role || null,
      organization: (profile as any)?.organization || 'independent',
    });
    setIsEditing(false);
  };

  return (
    <View style={{ flex: 1 }}>
      <ImageBackground
        source={{
          uri: isDark
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg'
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg',
        }}
        style={{ flex: 1, width: width, height: height }}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary,
          ]}
          style={{ flex: 1 }}
        >
          <SafeAreaView style={{ flex: 1 }}>
            {/* Header */}
            <Animated.View
              style={[
                {
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 20,
                  paddingTop: 20,
                  paddingBottom: 15,
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              <TouchableOpacity
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: colors.surfaceSecondary,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 16,
                }}
                onPress={() => router.back()}
                activeOpacity={0.8}
              >
                <ArrowLeft size={20} color={colors.text} />
              </TouchableOpacity>

              <Text
                style={{
                  fontSize: 20,
                  fontWeight: '700',
                  color: colors.text,
                  flex: 1,
                }}
              >
                个人资料设置
              </Text>

              {!isEditing && (
                <TouchableOpacity
                  style={{
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    borderRadius: 20,
                    backgroundColor: colors.accent + '20',
                  }}
                  onPress={() => setIsEditing(true)}
                  activeOpacity={0.8}
                >
                  <Text
                    style={{
                      color: colors.accent,
                      fontSize: 14,
                      fontWeight: '600',
                    }}
                  >
                    编辑
                  </Text>
                </TouchableOpacity>
              )}
            </Animated.View>

            <ScrollView
              style={{ flex: 1 }}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{
                paddingHorizontal: 20,
                paddingBottom: 100, // Add extra padding for tab bar
              }}
            >
              {/* Profile Content */}
              <Animated.View
                style={[
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                <BlurView
                  intensity={30}
                  style={{
                    borderRadius: 16,
                    overflow: 'hidden',
                    borderWidth: 1,
                    borderColor: colors.border,
                    marginBottom: 20,
                  }}
                >
                  <View style={{ padding: 20, alignItems: 'center' }}>
                    <View style={{ position: 'relative', marginBottom: 16 }}>
                      <Image
                        source={{
                          uri:
                            profile?.avatar_url ||
                            'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg',
                        }}
                        style={{
                          width: 100,
                          height: 100,
                          borderRadius: 50,
                          borderWidth: 3,
                          borderColor: colors.secondary,
                        }}
                        resizeMode="cover"
                      />
                      {isEditing && (
                        <TouchableOpacity
                          style={{
                            position: 'absolute',
                            bottom: 0,
                            right: 0,
                            width: 32,
                            height: 32,
                            borderRadius: 16,
                            backgroundColor: colors.accent,
                            justifyContent: 'center',
                            alignItems: 'center',
                            borderWidth: 2,
                            borderColor: colors.background,
                          }}
                          activeOpacity={0.8}
                        >
                          <Camera size={16} color={colors.background} />
                        </TouchableOpacity>
                      )}
                    </View>

                    <Text
                      style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: colors.text,
                        marginBottom: 4,
                      }}
                    >
                      {profile?.username || '用户'}
                    </Text>

                    <Text
                      style={{
                        fontSize: 14,
                        color: colors.textSecondary,
                      }}
                    >
                      {user?.email || '邮箱未设置'}
                    </Text>
                  </View>
                </BlurView>

                {/* Form Fields */}
                <BlurView
                  intensity={30}
                  style={{
                    borderRadius: 16,
                    overflow: 'hidden',
                    borderWidth: 1,
                    borderColor: colors.border,
                  }}
                >
                  <View style={{ padding: 20 }}>
                    <Text
                      style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: colors.text,
                        marginBottom: 16,
                      }}
                    >
                      个人信息
                    </Text>

                    <View style={{ marginBottom: 16 }}>
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: '600',
                          color: colors.text,
                          marginBottom: 8,
                        }}
                      >
                        用户名
                      </Text>
                      <TextInput
                        style={{
                          padding: 12,
                          fontSize: 16,
                          color: colors.text,
                          backgroundColor: colors.surfaceSecondary,
                          borderRadius: 8,
                          borderWidth: 1,
                          borderColor: colors.border,
                        }}
                        value={formData.username}
                        onChangeText={(text) =>
                          setFormData({ ...formData, username: text })
                        }
                        placeholder="输入用户名"
                        placeholderTextColor={colors.textSecondary}
                        editable={isEditing}
                      />
                    </View>

                    <View style={{ marginBottom: 16 }}>
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: '600',
                          color: colors.text,
                          marginBottom: 8,
                        }}
                      >
                        个人简介
                      </Text>
                      <TextInput
                        style={{
                          padding: 12,
                          fontSize: 16,
                          color: colors.text,
                          backgroundColor: colors.surfaceSecondary,
                          borderRadius: 8,
                          borderWidth: 1,
                          borderColor: colors.border,
                          height: 80,
                          textAlignVertical: 'top',
                        }}
                        value={formData.bio}
                        onChangeText={(text) =>
                          setFormData({ ...formData, bio: text })
                        }
                        placeholder="介绍一下自己..."
                        placeholderTextColor={colors.textSecondary}
                        multiline
                        editable={isEditing}
                      />
                    </View>

                    {/* 角色选择 */}
                    <View style={{ marginBottom: 16 }}>
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: '600',
                          color: colors.text,
                          marginBottom: 8,
                        }}
                      >
                        <Shield size={16} color={colors.accent} /> 角色
                      </Text>
                      {isEditing ? (
                        <View
                          style={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            gap: 8,
                          }}
                        >
                          {Object.values(UserRole).map((role) => (
                            <TouchableOpacity
                              key={role}
                              style={{
                                paddingHorizontal: 12,
                                paddingVertical: 8,
                                borderRadius: 20,
                                backgroundColor:
                                  formData.role === role
                                    ? colors.accent + '30'
                                    : colors.surfaceSecondary,
                                borderWidth: 1,
                                borderColor:
                                  formData.role === role
                                    ? colors.accent
                                    : colors.border,
                              }}
                              onPress={() => setFormData({ ...formData, role })}
                            >
                              <Text
                                style={{
                                  color:
                                    formData.role === role
                                      ? colors.accent
                                      : colors.text,
                                  fontSize: 12,
                                  fontWeight: '600',
                                }}
                              >
                                {UserRoleNames[role].zh}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </View>
                      ) : (
                        <Text
                          style={{
                            padding: 12,
                            fontSize: 16,
                            color: colors.text,
                            backgroundColor: colors.surfaceSecondary,
                            borderRadius: 8,
                            borderWidth: 1,
                            borderColor: colors.border,
                          }}
                        >
                          {formData.role
                            ? UserRoleNames[formData.role].zh
                            : '未选择'}
                        </Text>
                      )}
                    </View>

                    {/* 组织选择 */}
                    <View style={{ marginBottom: 16 }}>
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: '600',
                          color: colors.text,
                          marginBottom: 8,
                        }}
                      >
                        <Crown size={16} color={colors.secondary} /> 组织
                      </Text>
                      {isEditing ? (
                        <View style={{ gap: 8 }}>
                          {Object.values(Organization).map((org) => (
                            <TouchableOpacity
                              key={org}
                              style={{
                                paddingHorizontal: 12,
                                paddingVertical: 10,
                                borderRadius: 8,
                                backgroundColor:
                                  formData.organization === org
                                    ? colors.secondary + '20'
                                    : colors.surfaceSecondary,
                                borderWidth: 1,
                                borderColor:
                                  formData.organization === org
                                    ? colors.secondary
                                    : colors.border,
                                flexDirection: 'row',
                                alignItems: 'center',
                              }}
                              onPress={() =>
                                setFormData({ ...formData, organization: org })
                              }
                            >
                              <Sword
                                size={14}
                                color={
                                  formData.organization === org
                                    ? colors.secondary
                                    : colors.textSecondary
                                }
                              />
                              <Text
                                style={{
                                  color:
                                    formData.organization === org
                                      ? colors.secondary
                                      : colors.text,
                                  fontSize: 14,
                                  fontWeight: '600',
                                  marginLeft: 8,
                                }}
                              >
                                {OrganizationNames[org].zh}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </View>
                      ) : (
                        <Text
                          style={{
                            padding: 12,
                            fontSize: 16,
                            color: colors.text,
                            backgroundColor: colors.surfaceSecondary,
                            borderRadius: 8,
                            borderWidth: 1,
                            borderColor: colors.border,
                          }}
                        >
                          {formData.organization
                            ? OrganizationNames[formData.organization].zh
                            : '独立'}
                        </Text>
                      )}
                    </View>

                    {/* Action Buttons */}
                    {isEditing && (
                      <View
                        style={{
                          flexDirection: 'row',
                          gap: 12,
                          marginTop: 16,
                        }}
                      >
                        <TouchableOpacity
                          style={{
                            flex: 1,
                            paddingVertical: 12,
                            borderRadius: 8,
                            backgroundColor: colors.surfaceSecondary,
                            borderWidth: 1,
                            borderColor: colors.border,
                            alignItems: 'center',
                            flexDirection: 'row',
                            justifyContent: 'center',
                          }}
                          onPress={handleCancel}
                          activeOpacity={0.8}
                        >
                          <X size={16} color={colors.text} />
                          <Text
                            style={{
                              color: colors.text,
                              fontSize: 14,
                              fontWeight: '600',
                              marginLeft: 8,
                            }}
                          >
                            取消
                          </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={{
                            flex: 1,
                            borderRadius: 8,
                            overflow: 'hidden',
                          }}
                          onPress={handleSave}
                          disabled={isLoading}
                          activeOpacity={0.8}
                        >
                          <LinearGradient
                            colors={[colors.success, colors.secondary]}
                            style={{
                              paddingVertical: 12,
                              alignItems: 'center',
                              flexDirection: 'row',
                              justifyContent: 'center',
                            }}
                          >
                            <Save size={16} color={colors.background} />
                            <Text
                              style={{
                                color: colors.background,
                                fontSize: 14,
                                fontWeight: '600',
                                marginLeft: 8,
                              }}
                            >
                              {isLoading ? '保存中...' : '保存'}
                            </Text>
                          </LinearGradient>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                </BlurView>
              </Animated.View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

export default function ProfileSettingsScreen() {
  return (
    <ProtectedRoute>
      <ProfileSettingsContent />
    </ProtectedRoute>
  );
}
