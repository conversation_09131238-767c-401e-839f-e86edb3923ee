import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  MapPin,
  Package,
  User,
  Bell,
  Sword,
  Shield,
  Crown,
  Coins,
  Star,
  ChevronRight,
  Map as MapIcon,
  Moon,
  Sun,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { router } from 'expo-router';

const { width, height } = Dimensions.get('window');

function HomeContent() {
  const { isDark, toggleTheme } = useTheme(); // 使用 toggleTheme 更新主题
  const { user, profile } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isShopOpen, setIsShopOpen] = useState(false);

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Update time every minute
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  const adventureCards = [
    {
      icon: Sword,
      title: '今日任务',
      subtitle: '完成3个冒险任务',
      progress: '2/3',
      color: colors.primary,
      bgColor: colors.primary + '1A',
    },
    {
      icon: Shield,
      title: '装备强化',
      subtitle: '提升装备等级',
      progress: '强化中',
      color: colors.secondary,
      bgColor: colors.secondary + '1A',
    },
    {
      icon: Crown,
      title: '声望等级',
      subtitle: `当前等级: ${profile?.level || 1}`,
      progress: `${profile?.reputation || 0}声望`,
      color: colors.accent,
      bgColor: colors.accent + '1A',
    },
    {
      icon: Coins,
      title: '金币收益',
      subtitle: '今日收入',
      progress: profile?.gold?.toString() || '100',
      color: colors.warning,
      bgColor: colors.warning + '1A',
    },
  ];

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    notificationBar: {
      paddingHorizontal: 20,
      paddingTop: 10,
      paddingBottom: 15,
    },
    notificationContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
    },
    notificationLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    characterNotification: {
      position: 'relative',
      marginRight: 12,
    },
    notificationAvatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      borderWidth: 2,
      borderColor: colors.primary,
    },
    thymeBackpack: {
      position: 'absolute',
      bottom: -5,
      right: -5,
      backgroundColor: colors.primary,
      borderRadius: 10,
      paddingHorizontal: 4,
      paddingVertical: 2,
    },
    thymeIcon: {
      fontSize: 10,
    },
    notificationText: {
      flex: 1,
    },
    notificationTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 2,
    },
    notificationSubtitle: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    notificationButton: {
      padding: 8,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 30,
    },
    header: {
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 20,
    },
    wandererContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 20,
    },
    wandererFrame: {
      width: 80,
      height: 80,
      borderRadius: 40,
      borderWidth: 3,
      borderColor: colors.secondary,
      overflow: 'hidden',
      position: 'relative',
      marginRight: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.5 : 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    wandererImage: {
      width: '100%',
      height: '100%',
    },
    cloakOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    cloakGradient: {
      flex: 1,
    },
    mistEffect: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 20,
    },
    mistBlur: {
      flex: 1,
    },
    timeContainer: {
      alignItems: 'flex-start',
    },
    timeText: {
      fontSize: 24,
      fontWeight: 'bold',
      color: colors.text,
      marginBottom: 4,
    },
    dateText: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    titleContainer: {
      alignItems: 'center',
    },
    mainTitle: {
      fontSize: 28,
      fontWeight: 'bold',
      color: colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    titleUnderline: {
      width: 60,
      height: 3,
      backgroundColor: colors.accent,
      borderRadius: 2,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    navigationContainer: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    navigationBar: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 16,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: colors.border,
    },
    navItem: {
      alignItems: 'center',
      flex: 1,
    },
    navIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 8,
      backgroundColor: colors.surfaceSecondary,
    },
    navText: {
      fontSize: 12,
      color: colors.textSecondary,
      fontWeight: '500',
    },
    shopControlContainer: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    shopControlBar: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 12,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
    },
    shopButton: {
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 12,
      minWidth: 80,
      alignItems: 'center',
    },
    shopButtonText: {
      color: colors.background,
      fontSize: 16,
      fontWeight: '600',
    },
    mapContainer: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    mapCard: {
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
      overflow: 'hidden',
    },
    mapContent: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 40,
    },
    mapText: {
      fontSize: 18,
      color: colors.text,
      fontWeight: '600',
      marginTop: 12,
    },
    cardsContainer: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    adventureCard: {
      marginBottom: 12,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
      overflow: 'hidden',
    },
    cardContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
    },
    cardLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    cardIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
    },
    cardTextContainer: {
      flex: 1,
    },
    cardTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 4,
    },
    cardSubtitle: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    cardRight: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    cardProgress: {
      fontSize: 14,
      fontWeight: '600',
      marginRight: 8,
    },
    statsContainer: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    statsCard: {
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
      padding: 20,
      overflow: 'hidden',
    },
    statsTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    statsGrid: {
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statNumber: {
      fontSize: 20,
      fontWeight: 'bold',
      color: colors.text,
      marginTop: 8,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 12,
      color: colors.textSecondary,
    },
  });

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{
          uri: isDark
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg'
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg',
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary,
          ]}
          style={dynamicStyles.overlay}
        >
          <SafeAreaView style={dynamicStyles.safeArea}>
            {/* Notification Bar with Theme Toggle */}
            <Animated.View
              style={[
                dynamicStyles.notificationBar,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }],
                },
              ]}
            >
              <BlurView
                intensity={30}
                style={dynamicStyles.notificationContent}
              >
                <View style={dynamicStyles.notificationLeft}>
                  <View style={dynamicStyles.characterNotification}>
                    <Image
                      source={{
                        uri:
                          profile?.avatar_url ||
                          'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg',
                      }}
                      style={dynamicStyles.notificationAvatar}
                      resizeMode="cover"
                    />
                    <View style={dynamicStyles.thymeBackpack}>
                      <Text style={dynamicStyles.thymeIcon}>🎒🌿</Text>
                    </View>
                  </View>
                  <View style={dynamicStyles.notificationText}>
                    <Text style={dynamicStyles.notificationTitle}>
                      {profile?.username || '冒险者'}
                    </Text>
                    <Text style={dynamicStyles.notificationSubtitle}>
                      等级 {profile?.level || 1} • {profile?.gold || 100} 金币
                    </Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={dynamicStyles.notificationButton}
                  onPress={toggleTheme}
                >
                  {isDark ? (
                    <Sun size={20} color={colors.primary} />
                  ) : (
                    <Moon size={20} color={colors.primary} />
                  )}
                </TouchableOpacity>
              </BlurView>
            </Animated.View>

            <ScrollView
              style={dynamicStyles.scrollView}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={dynamicStyles.scrollContent}
            >
              {/* Header with Wanderer */}
              <Animated.View
                style={[
                  dynamicStyles.header,
                  {
                    opacity: fadeAnim,
                    transform: [
                      { translateY: slideAnim },
                      { scale: scaleAnim },
                    ],
                  },
                ]}
              >
                <View style={dynamicStyles.wandererContainer}>
                  <View style={dynamicStyles.wandererFrame}>
                    <Image
                      source={{
                        uri:
                          profile?.avatar_url ||
                          'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg',
                      }}
                      style={dynamicStyles.wandererImage}
                      resizeMode="cover"
                    />
                    <View style={dynamicStyles.cloakOverlay}>
                      <LinearGradient
                        colors={[
                          colors.textSecondary + '66',
                          colors.textSecondary + 'B3',
                        ]}
                        style={dynamicStyles.cloakGradient}
                      />
                    </View>
                    <View style={dynamicStyles.mistEffect}>
                      <BlurView intensity={15} style={dynamicStyles.mistBlur} />
                    </View>
                  </View>
                  <View style={dynamicStyles.timeContainer}>
                    <Text style={dynamicStyles.timeText}>
                      {formatTime(currentTime)}
                    </Text>
                    <Text style={dynamicStyles.dateText}>集市时间</Text>
                  </View>
                </View>

                <View style={dynamicStyles.titleContainer}>
                  <Text style={dynamicStyles.mainTitle}>每日集市冒险</Text>
                  <View style={dynamicStyles.titleUnderline} />
                  <Text style={dynamicStyles.subtitle}>
                    探索神秘的斯卡布罗集市
                  </Text>
                </View>
              </Animated.View>

              {/* Navigation Bar */}
              <Animated.View
                style={[
                  dynamicStyles.navigationContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                <BlurView intensity={25} style={dynamicStyles.navigationBar}>
                  <TouchableOpacity
                    style={dynamicStyles.navItem}
                    activeOpacity={0.7}
                  >
                    <View style={dynamicStyles.navIconContainer}>
                      <MapPin size={24} color={colors.textTertiary} />
                    </View>
                    <Text style={dynamicStyles.navText}>任务</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={dynamicStyles.navItem}
                    activeOpacity={0.7}
                  >
                    <View style={dynamicStyles.navIconContainer}>
                      <Package size={24} color={colors.textTertiary} />
                    </View>
                    <Text style={dynamicStyles.navText}>商品</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={dynamicStyles.navItem}
                    activeOpacity={0.7}
                  >
                    <View style={dynamicStyles.navIconContainer}>
                      <User size={24} color={colors.textTertiary} />
                    </View>
                    <Text style={dynamicStyles.navText}>角色</Text>
                  </TouchableOpacity>
                </BlurView>
              </Animated.View>

              {/* Shop Control Buttons */}
              <Animated.View
                style={[
                  dynamicStyles.shopControlContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                <BlurView intensity={25} style={dynamicStyles.shopControlBar}>
                  <TouchableOpacity
                    style={[
                      dynamicStyles.shopButton,
                      {
                        backgroundColor: isShopOpen
                          ? colors.textSecondary
                          : colors.primary,
                      },
                    ]}
                    onPress={() => setIsShopOpen(true)}
                    activeOpacity={0.7}
                  >
                    <Text style={dynamicStyles.shopButtonText}>启市</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      dynamicStyles.shopButton,
                      {
                        backgroundColor: !isShopOpen
                          ? colors.textSecondary
                          : colors.primary,
                      },
                    ]}
                    onPress={() => setIsShopOpen(false)}
                    activeOpacity={0.7}
                  >
                    <Text style={dynamicStyles.shopButtonText}>归营</Text>
                  </TouchableOpacity>
                </BlurView>
              </Animated.View>

              {/* Dynamic Map */}
              {isShopOpen && (
                <Animated.View
                  style={[
                    dynamicStyles.mapContainer,
                    {
                      opacity: fadeAnim,
                      transform: [{ translateY: slideAnim }],
                    },
                  ]}
                >
                  <BlurView intensity={30} style={dynamicStyles.mapCard}>
                    <View style={dynamicStyles.mapContent}>
                      <MapIcon size={40} color={colors.accent} />
                      <Text style={dynamicStyles.mapText}>探索地图</Text>
                    </View>
                  </BlurView>
                </Animated.View>
              )}

              {/* Adventure Cards */}
              <Animated.View
                style={[
                  dynamicStyles.cardsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                {adventureCards.map((card, index) => (
                  <BlurView
                    key={index}
                    intensity={30}
                    style={dynamicStyles.adventureCard}
                  >
                    <TouchableOpacity
                      style={dynamicStyles.cardContent}
                      activeOpacity={0.8}
                    >
                      <View style={dynamicStyles.cardLeft}>
                        <View
                          style={[
                            dynamicStyles.cardIconContainer,
                            { backgroundColor: card.bgColor },
                          ]}
                        >
                          <card.icon size={28} color={card.color} />
                        </View>
                        <View style={dynamicStyles.cardTextContainer}>
                          <Text style={dynamicStyles.cardTitle}>
                            {card.title}
                          </Text>
                          <Text style={dynamicStyles.cardSubtitle}>
                            {card.subtitle}
                          </Text>
                        </View>
                      </View>
                      <View style={dynamicStyles.cardRight}>
                        <Text
                          style={[
                            dynamicStyles.cardProgress,
                            { color: card.color },
                          ]}
                        >
                          {card.progress}
                        </Text>
                        <ChevronRight size={20} color={colors.textSecondary} />
                      </View>
                    </TouchableOpacity>
                  </BlurView>
                ))}
              </Animated.View>

              {/* Stats Section */}
              <Animated.View
                style={[
                  dynamicStyles.statsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }],
                  },
                ]}
              >
                <BlurView intensity={30} style={dynamicStyles.statsCard}>
                  <Text style={dynamicStyles.statsTitle}>今日统计</Text>
                  <View style={dynamicStyles.statsGrid}>
                    <View style={dynamicStyles.statItem}>
                      <Star size={24} color={colors.accent} />
                      <Text style={dynamicStyles.statNumber}>12</Text>
                      <Text style={dynamicStyles.statLabel}>任务完成</Text>
                    </View>
                    <View style={dynamicStyles.statItem}>
                      <Coins size={24} color={colors.warning} />
                      <Text style={dynamicStyles.statNumber}>850</Text>
                      <Text style={dynamicStyles.statLabel}>金币获得</Text>
                    </View>
                    <View style={dynamicStyles.statItem}>
                      <Crown size={24} color={colors.secondary} />
                      <Text style={dynamicStyles.statNumber}>+25</Text>
                      <Text style={dynamicStyles.statLabel}>声望提升</Text>
                    </View>
                  </View>
                </BlurView>
              </Animated.View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

export default function Home() {
  return (
    <ProtectedRoute>
      <HomeContent />
    </ProtectedRoute>
  );
}
