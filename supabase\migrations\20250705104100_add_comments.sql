-- 添加表和字段注释
-- 这个脚本应该在主迁移脚本执行成功后运行

-- 为 profiles 表字段添加注释
DO $$
BEGIN
    -- 检查并添加 profiles 表字段注释
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'email') THEN
        COMMENT ON COLUMN profiles.email IS '用户邮箱';
        RAISE NOTICE '✅ 添加 profiles.email 注释';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'bio') THEN
        COMMENT ON COLUMN profiles.bio IS '用户个人简介';
        RAISE NOTICE '✅ 添加 profiles.bio 注释';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'location') THEN
        COMMENT ON COLUMN profiles.location IS '用户所在地';
        RAISE NOTICE '✅ 添加 profiles.location 注释';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'avatar_url') THEN
        COMMENT ON COLUMN profiles.avatar_url IS '用户头像URL';
        RAISE NOTICE '✅ 添加 profiles.avatar_url 注释';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'role') THEN
        COMMENT ON COLUMN profiles.role IS '用户角色：草本商人、流浪者、伶仃猎手';
        RAISE NOTICE '✅ 添加 profiles.role 注释';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'organization') THEN
        COMMENT ON COLUMN profiles.organization IS '所属组织';
        RAISE NOTICE '✅ 添加 profiles.organization 注释';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'organization_rank') THEN
        COMMENT ON COLUMN profiles.organization_rank IS '组织内等级';
        RAISE NOTICE '✅ 添加 profiles.organization_rank 注释';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'reputation') THEN
        COMMENT ON COLUMN profiles.reputation IS '声望值';
        RAISE NOTICE '✅ 添加 profiles.reputation 注释';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'gold') THEN
        COMMENT ON COLUMN profiles.gold IS '金币数量';
        RAISE NOTICE '✅ 添加 profiles.gold 注释';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'tasks_completed') THEN
        COMMENT ON COLUMN profiles.tasks_completed IS '完成任务数';
        RAISE NOTICE '✅ 添加 profiles.tasks_completed 注释';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'shop_id') THEN
        COMMENT ON COLUMN profiles.shop_id IS '关联商店ID';
        RAISE NOTICE '✅ 添加 profiles.shop_id 注释';
    END IF;
END $$;

-- 为 shops 表添加注释
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'shops') THEN
        COMMENT ON TABLE shops IS '商店信息表';
        
        COMMENT ON COLUMN shops.owner_id IS '店主用户ID';
        COMMENT ON COLUMN shops.name IS '商店名称';
        COMMENT ON COLUMN shops.description IS '商店描述';
        COMMENT ON COLUMN shops.latitude IS '纬度';
        COMMENT ON COLUMN shops.longitude IS '经度';
        COMMENT ON COLUMN shops.is_open IS '是否营业中';
        COMMENT ON COLUMN shops.is_approved IS '是否已审批';
        
        RAISE NOTICE '✅ 添加 shops 表注释';
    END IF;
END $$;

-- 为 products 表添加注释
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'products') THEN
        COMMENT ON TABLE products IS '商品信息表';
        
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'products' AND column_name = 'shop_id') THEN
            COMMENT ON COLUMN products.shop_id IS '所属商店ID';
        END IF;
        
        COMMENT ON COLUMN products.name IS '商品名称';
        COMMENT ON COLUMN products.description IS '商品描述';
        COMMENT ON COLUMN products.price IS '商品价格';
        COMMENT ON COLUMN products.image_url IS '商品图片URL';
        COMMENT ON COLUMN products.stock IS '库存数量';
        COMMENT ON COLUMN products.is_available IS '是否可购买';
        COMMENT ON COLUMN products.discovered_year IS '发现年份';
        
        RAISE NOTICE '✅ 添加 products 表注释';
    END IF;
END $$;

-- 为 orders 表添加注释
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
        COMMENT ON TABLE orders IS '订单信息表';
        
        COMMENT ON COLUMN orders.buyer_id IS '买家用户ID';
        COMMENT ON COLUMN orders.seller_id IS '卖家用户ID';
        COMMENT ON COLUMN orders.product_id IS '商品ID';
        COMMENT ON COLUMN orders.quantity IS '购买数量';
        COMMENT ON COLUMN orders.total_price IS '总价';
        COMMENT ON COLUMN orders.status IS '订单状态';
        
        RAISE NOTICE '✅ 添加 orders 表注释';
    END IF;
END $$;

-- 为 organization_strongholds 表添加注释
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'organization_strongholds') THEN
        COMMENT ON TABLE organization_strongholds IS '组织据点信息表';
        
        COMMENT ON COLUMN organization_strongholds.organization IS '所属组织';
        COMMENT ON COLUMN organization_strongholds.name IS '据点名称';
        COMMENT ON COLUMN organization_strongholds.latitude IS '纬度';
        COMMENT ON COLUMN organization_strongholds.longitude IS '经度';
        COMMENT ON COLUMN organization_strongholds.description IS '据点描述';
        
        RAISE NOTICE '✅ 添加 organization_strongholds 表注释';
    END IF;
END $$;

-- 完成提示
DO $$
BEGIN
    RAISE NOTICE '📝 所有注释添加完成！';
END $$;
