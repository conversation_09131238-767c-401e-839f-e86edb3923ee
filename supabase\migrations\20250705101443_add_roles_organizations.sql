-- 添加角色和组织系统到 Scarborough Market
-- 为用户添加角色、组织、等级等字段

-- 创建用户角色枚举类型
CREATE TYPE user_role AS ENUM (
  'herb_merchant',  -- 草本商人
  'wanderer',       -- 流浪者
  'lone_hunter'     -- 伶仃猎手
);

-- 创建组织枚举类型
CREATE TYPE organization AS ENUM (
  'dark_brotherhood',    -- 暗转兄弟会
  'stormcloaks',        -- 风暴斗篷
  'thieves_syndicate',  -- 盗赋公司
  'winterhold_academy', -- 冬保学院
  'imperial_legion',    -- 帝国军团
  'independent'         -- 独立
);

-- 为 profiles 表添加新字段
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS role user_role;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS organization organization DEFAULT 'independent';
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS organization_rank TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS reputation INTEGER DEFAULT 0;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS gold INTEGER DEFAULT 100;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS tasks_completed INTEGER DEFAULT 0;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS shop_id UUID;

-- 添加字段注释
COMMENT ON COLUMN profiles.role IS '用户角色：草本商人、流浪者、伶仃猎手';
COMMENT ON COLUMN profiles.organization IS '所属组织';
COMMENT ON COLUMN profiles.organization_rank IS '组织内等级';
COMMENT ON COLUMN profiles.reputation IS '声望值';
COMMENT ON COLUMN profiles.gold IS '金币数量';
COMMENT ON COLUMN profiles.tasks_completed IS '完成任务数';
COMMENT ON COLUMN profiles.shop_id IS '关联商店ID';

-- 创建商店表
CREATE TABLE IF NOT EXISTS shops (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  owner_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  is_open BOOLEAN DEFAULT false,
  is_approved BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 添加商店表注释
COMMENT ON TABLE shops IS '商店信息表';
COMMENT ON COLUMN shops.owner_id IS '店主用户ID';
COMMENT ON COLUMN shops.name IS '商店名称';
COMMENT ON COLUMN shops.description IS '商店描述';
COMMENT ON COLUMN shops.latitude IS '纬度';
COMMENT ON COLUMN shops.longitude IS '经度';
COMMENT ON COLUMN shops.is_open IS '是否营业中';
COMMENT ON COLUMN shops.is_approved IS '是否已审批';

-- 创建商品表
CREATE TABLE IF NOT EXISTS products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  shop_id UUID NOT NULL REFERENCES shops(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  price DECIMAL(10, 2) NOT NULL CHECK (price >= 0),
  image_url TEXT,
  stock INTEGER DEFAULT 0 CHECK (stock >= 0),
  is_available BOOLEAN DEFAULT true,
  discovered_year INTEGER DEFAULT 1200,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 添加商品表注释
COMMENT ON TABLE products IS '商品信息表';
COMMENT ON COLUMN products.shop_id IS '所属商店ID';
COMMENT ON COLUMN products.name IS '商品名称';
COMMENT ON COLUMN products.description IS '商品描述';
COMMENT ON COLUMN products.price IS '商品价格';
COMMENT ON COLUMN products.image_url IS '商品图片URL';
COMMENT ON COLUMN products.stock IS '库存数量';
COMMENT ON COLUMN products.is_available IS '是否可购买';
COMMENT ON COLUMN products.discovered_year IS '发现年份';

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  buyer_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  seller_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  total_price DECIMAL(10, 2) NOT NULL CHECK (total_price >= 0),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 添加订单表注释
COMMENT ON TABLE orders IS '订单信息表';
COMMENT ON COLUMN orders.buyer_id IS '买家用户ID';
COMMENT ON COLUMN orders.seller_id IS '卖家用户ID';
COMMENT ON COLUMN orders.product_id IS '商品ID';
COMMENT ON COLUMN orders.quantity IS '购买数量';
COMMENT ON COLUMN orders.total_price IS '总价';
COMMENT ON COLUMN orders.status IS '订单状态';

-- 创建组织据点表
CREATE TABLE IF NOT EXISTS organization_strongholds (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  organization organization NOT NULL,
  name TEXT NOT NULL,
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 添加组织据点表注释
COMMENT ON TABLE organization_strongholds IS '组织据点信息表';
COMMENT ON COLUMN organization_strongholds.organization IS '所属组织';
COMMENT ON COLUMN organization_strongholds.name IS '据点名称';
COMMENT ON COLUMN organization_strongholds.latitude IS '纬度';
COMMENT ON COLUMN organization_strongholds.longitude IS '经度';
COMMENT ON COLUMN organization_strongholds.description IS '据点描述';

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为各表创建更新时间触发器
DROP TRIGGER IF EXISTS update_shops_updated_at ON shops;
CREATE TRIGGER update_shops_updated_at
    BEFORE UPDATE ON shops
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_orders_updated_at ON orders;
CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 插入一些示例组织据点
INSERT INTO organization_strongholds (organization, name, latitude, longitude, description) VALUES
('dark_brotherhood', '暗影圣所', 54.2781, -1.4360, '暗转兄弟会的秘密据点，隐藏在斯卡伯勒的地下'),
('stormcloaks', '风暴要塞', 54.2850, -1.4250, '风暴斗篷的训练场所，位于海岸悬崖'),
('thieves_syndicate', '盗贼公会', 54.2750, -1.4400, '盗赋公司的总部，伪装成普通商铺'),
('winterhold_academy', '冬保学院', 54.2900, -1.4300, '魔法学院的分院，研究古老的符文'),
('imperial_legion', '帝国军营', 54.2800, -1.4200, '帝国军团的驻地，维护市场秩序');

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_organization ON profiles(organization);
CREATE INDEX IF NOT EXISTS idx_shops_owner_id ON shops(owner_id);
CREATE INDEX IF NOT EXISTS idx_shops_is_open ON shops(is_open);
CREATE INDEX IF NOT EXISTS idx_products_shop_id ON products(shop_id);
CREATE INDEX IF NOT EXISTS idx_products_is_available ON products(is_available);
CREATE INDEX IF NOT EXISTS idx_orders_buyer_id ON orders(buyer_id);
CREATE INDEX IF NOT EXISTS idx_orders_seller_id ON orders(seller_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_strongholds_organization ON organization_strongholds(organization);
