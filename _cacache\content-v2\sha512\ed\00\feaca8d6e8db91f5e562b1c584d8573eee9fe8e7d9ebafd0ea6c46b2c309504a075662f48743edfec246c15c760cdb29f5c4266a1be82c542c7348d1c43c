{"_id": "@esbuild/freebsd-x64", "_rev": "95-8aa122d4cb50a792da9c8285114090d5", "name": "@esbuild/freebsd-x64", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.15.18": {"name": "@esbuild/freebsd-x64", "version": "0.15.18", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.15.18", "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a1b6a944897343b23cc8e2081e755ccd3aa95f4d", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.15.18.tgz", "fileCount": 3, "integrity": "sha512-fVhmZ5u46PsEHl9a0C9zSqFdVfItDsbAULouO+HSlWrVgDs4iParA6OELx/PcJ58XLsQOlwM285qwYWgdI65Sg==", "signatures": [{"sig": "MEUCIFvwOm7a0ev5KhCG8lv3pIuDE2+hUXJkbQ003whUtxN+AiEApXp2BZwKR+tbFkzZZfFz4DWIRvKvtcz5/ZfYsdDDXL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8622558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjoJJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdmA/+KVABDjYC9F6hLI5V92fYDVThah/5ntxXhiGel1sw4dVFrODK\r\n4UrhKkIjYd14+YpOWj97/JWWogTSxWFbmzG4hyADDLjiag0sqDfrlRS1yNN0\r\nEYcx80M/zuiCzeFHSij+d5TOwopwsqbkSrsjH5Y3fmbSwYfAM37xHzcF5nrx\r\nmBKi3eP8MhUwsN47J7ogTzb6p8q4YdZQ/EE/qW6DdP+dnXzYT5V9CWpyzxd3\r\ntdassXqi9TysapU7xs68G0YYQZqpp5rabaBs4M/JuC8m2fUAAi7ZNUZF1fAV\r\nTOvIL/cOUa+8G/1YSaSPWToEBksqxy1/AJkDiuW733snDxQ7o2LnCIfgjmTQ\r\nDSBzJ+0l7bzyzg2m+HwE2JV0dT0a8dsYHW1pxXo1wvMnsyrhnv3NrIyNGv4Q\r\nU6StuV+h8A6O+C/m5qFsVThb8UxFalztjEaD1yRD2bo3WcVuI/b/VakWfUvO\r\n6Yf3/t3bjpWA6bjVRzYA+ISbEMpclT1VXkMrdPvTrN2e/0mUnSr5Em+6vubW\r\nF2Vfk23cacJGVUn5i3w6WRtNI8iPiduElhNrQHVVlG+lyV3gAwZKkBg4Sgbd\r\nRFCt20NVWJZvJFATdg6Rf3KtuKCaXLelfUUwCAz4aW0bfX3HjyNF7hRUwzAF\r\nd5PYwRkIutJ5KFl5SwHSn/t8wW3El0vuD0I=\r\n=CuWp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "07e607164c880e03e13f86aa50a58cd6d44ec084", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.15.18_1670283849419_0.12298826077969083", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@esbuild/freebsd-x64", "version": "0.16.0", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "40836988ae0a31d3deeb5788765a81428d4ec071", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.0.tgz", "fileCount": 3, "integrity": "sha512-70LMjM1P72raUNIzNtuV9mbzx5A+dY9+idS/9pdwWyle8/QeS5u3XF/6NO868XsOG+LhY4B3+nY0T1u9DjVApw==", "signatures": [{"sig": "MEUCIQCxxNqmq43y8bflWXB7LYnlPRUbMY/wlwQAxE5s2RrnngIgbxIxAqRR/5EIoKjRtasD5ycyNYHS8ag3a+yVXoDxG4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8630749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA6UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDKw//W+zMiaTwykoPRI0ZHWjYaKHNFSiDgLbPdU3HeaTiFH9iYkd0\r\nErogs6K5lig+4vkpMQTLjAmfjXAFWr4wUE4gMgYmv+HiiXuJuhP4oonaoAiA\r\nRaGnGcdUja/8xHCRakrQnCVgxN969R/VgG7SoiggCaQee9CGXBn3zM9nMsVg\r\nQfLEMPqK/EPf8jh+3uT1lLYdvCTG2KS/OQC/2tkyo/hvd/FU2+M4obsLjsqN\r\nEDWg58icMBy1xLs6OB0nTHwjR6tIFHQj0ISkLvqcGkPS+u23La5CrfVRFexR\r\nt1fJ63qr9bHzT7uRTB5YpbJO2BSexL+7cEcIVF+cNd17KHDZkwKmsyA/vds8\r\n+AdtvjQEtQHnUdTWLduQNB/sD70AOLa90wpQnnjoAL1HZEkrRn73fRy2Ay3f\r\nBRjEwADAUO1yM5vW/rRCCJBzm5WIGnE5izwchQXwfPj2jXNLffJ10yeSRP9F\r\nr/Q5dYkSzgqhicXJyD2Gd/pWPx87cZ3TIgtSdvNcv/AIeLr/s4/r2LzcrPkE\r\nTN1O2dWwDm5PZ8OG9NU0ECsF19KFp42cbSsrnwjxnfaHrvbVLgddN50n8x1B\r\n2uDSguFNwMrfiPP59zXXBLbvPnUZgtpYrFueLP9eGx7Up3DHLHZQls2KLKBg\r\n+O0ArPiFFVDcnd9ssuh8x45RdlGD9k6eckc=\r\n=MUru\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.0_1670385300263_0.8685998137111461", "host": "s3://npm-registry-packages"}}, "0.16.1": {"name": "@esbuild/freebsd-x64", "version": "0.16.1", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "48fba6c63463409452ebc8544139e091b7797afb", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.1.tgz", "fileCount": 3, "integrity": "sha512-BKYAYhsgD/6/mOeOwMSEcTyL9GlFBNr2LkgWEaugUp/oXCC+ScCH/EqphD3Jp5MsMNIk71b0YqDDveDHXuwcLw==", "signatures": [{"sig": "MEYCIQDKi+UZFeaB/BsBIkwxzdNAW/SQOabE0yC5KhSkXyGR3wIhAKZr5A/FkcwbqBombs/Ef2ktmVIp8CNDmqoYPFbl5fYn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8630749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBsJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotsA//TgCrqIwIu/hfQUUWM7sg04IuPln54j5mgshqbfOvoLqRzpNT\r\nfj05FtuoRexzumXggoDTTLHBHNooR6IW2P4yoqARPMhh3rU+iPTRNY16vU1O\r\n+1mPsVfR62/LAGGGVh9dqF3FIV0v6uroXOmw8ZGu8HcVbAxCX/fi/YeuTKKd\r\nrqZXCXXwv6g7Xm5cIJMSEVi0dA1bAUZTSVQsd0MgQKvsHvqrhst6xmgXTm8a\r\nY1Kyp/DO9vf5+F1V0XzA5RbJs4RLNfD00ActmXBAuDRcy3IGOTYnDZULSvEy\r\nz2L4rG/wYkm3nLK+pEODR7trMPtF0ZefMuV7xLohDKHFPUrESwEsAxL1Zj+A\r\nHq1GzMjabRSkhrJxh6ozNY6RDzNWhwnDoFCEpOUmft2eo6OU9WKO8FbAPND/\r\nRfzlhilhJ8aFcagmFs4d6vriuPOY/jEOc0Ibd7LkOx0M8HWy8ArOGpYgFxMc\r\nXhU9hWbcM/ek+9F0ASsRl5nMKVWLbJUCwQ28mvEtNS2Q2HpcqP/wimpKwN5S\r\npq7A+zuUY/6iicK1Ozph6G94k8HRRb/FiQaIjLrLBOIqMqvZHfMOoaBQZXX4\r\nhsX+tqIf4/yY3ZuvFBMCrCbDrMY4iYGf/Nvy195CFXxwKiQteOW7O6TqBJsn\r\nUFZL4M73Bp3Wlbc3vEpcR9d0BUod7v40UlI=\r\n=TQSw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.1_1670388488947_0.4895033590982949", "host": "s3://npm-registry-packages"}}, "0.16.2": {"name": "@esbuild/freebsd-x64", "version": "0.16.2", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "7e68876fbe0e6e8fc9f45f880a2b393e61f54bf3", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.2.tgz", "fileCount": 3, "integrity": "sha512-uvbv99Wg2T489bqUz4gYVb2IpSSZZP/uTkaZpaLN+h3x58FmsLT4o7bF1Refd2JIKuONxSobljlk5/K/RD9SsQ==", "signatures": [{"sig": "MEUCIF/SJzb8f98EocCVV1VKNEQKHADxGIcMLfyoYOmj0NhGAiEAiFUQsMksUdXYfT3IQqWw1Ny6iWzY4fnz+ARGrroh248=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8638941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYtfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEQg//f1Apvggkw+ar73pAee6iwUDgz2kW81Fr/MqBPuHcJnjOSCSM\r\ntjviaDX+qGzuRnLdu1djkGijALGBgphZ11785KbXlqU9Kvd0cYS/0AUrEKZr\r\nesbL8ZbwRAsdPdvC8+ppQAIfLKW3wwvAcnSO1nyLo7/1mhnGWv36FKxDwyH5\r\nMkMfsJmVqMKLhX5coQiX3TfMjUTR0rbz/jr+Wx/XCzxwDseI+hSuBYXCjneS\r\nSY8JTXgRcTu8m81Tmhe6RaXizv9cl16OAtO+Z6A+zMvwNgcJS40tLoaOW83j\r\nRRXlfc8ufOC1A92s3KUB14P9NFGkyo6jBefNbMLaJpKKTeYK86EcX1WH739w\r\nksjRJv72IwB0qEud85dgpPXDDGqZsGk9afPe5UV4FYhAUKL3WLzfkzZCOHzc\r\nXvgyXx7xmGCKXZL72yzQ+LJl9+C9K1Q9gJBCgRusVLj6OCzWgsP9pIbbl32j\r\neiKkMz430LEOvB9CIfOiqbVoxh2ltPbD5Jv+zlRl3VGfNQygFClyQi3JAdMi\r\nxlTCyP6pdfUocd064VcUKeP3cdRYDgkjdKd3ijSKL25u8p7txzGpfxoOATo5\r\nT3iHoc5Or5ZhPNv7KVCHVBJlu7G2IHQLjXIccA6wqrwsJMrmUlEz4tE1ZUFv\r\nyytC0Z8bJl/hMQqEvuvQdboA5aeiDgDUv4o=\r\n=LbuP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.2_1670482783290_0.2508954760928035", "host": "s3://npm-registry-packages"}}, "0.16.3": {"name": "@esbuild/freebsd-x64", "version": "0.16.3", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "f4edd1464cb072799ed6b8ab5178478e71c13459", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.3.tgz", "fileCount": 3, "integrity": "sha512-TfoDzLw+QHfc4a8aKtGSQ96Wa+6eimljjkq9HKR0rHlU83vw8aldMOUSJTUDxbcUdcgnJzPaX8/vGWm7vyV7ug==", "signatures": [{"sig": "MEUCIQCac8rdjkL82oNKx6Uji8Oh+VEjfjP3vKCminDAdcL3NgIgKZZFyJHb5rEyRdxG9/TKnZb4YPbWyJkS/rF+kSqHSeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8638941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkVDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEmBAAmNxtLTa8APlJZbdycKS7t6VUWLQhMkq+7yokUCktVSWTsjLA\r\nUIC/y6smTE/a+A7qIJXD3Wysy+E2W6OomSDnvPCw0oGUW2c4RittsQ0q0l/C\r\n9aUDVBhGwyPe6acUsGkJlAZOMI9FIbC2ahzJRcgxy6iPUAXMrziSEcjOC1Yf\r\nJqDlewKL6PlPi53+FasS8i+xzNk/YCt/mwxEGV98/gWMI7wXSQ5OwjvOQirQ\r\n2XW3xVdJYzrqg0dcirrd/vN6KEXKjdW3p7voQ9K2XDzCWYncdH9G/8Hg/3uO\r\nhZ4FrAE+JawEi9V6ZuJQi0GCVKxk7pGoixf0ZLHwVOJfAEcQFGG6DweLdExa\r\nb6aXf9iiKYBn9WBosqRHsCvg1a1X2QC1GXIZdXQLWuv2HKO8dGQiK/G1whAZ\r\nL8/6x5Fyo67xJGpEIIUyEomeFsB3O606leP6jC+mtmmwyAdRkAaTE0LAIhgQ\r\nMEwpdyVL9/h9Xm7af+V67Bd3RZQzcyXnEVa7SYpjIJHuxJ4OOwGE542f5wE4\r\n1yasLxX3Z6vMPsC8aHHETv1Khz3NUFjK3TW9reth1Z4EuqGcyrT30Z/k8xlV\r\nxgweOGCk+oD1gE+My5THSHlfHARl6xkkOOf27+KhZ/W5G0+b4BYsRurcdpC2\r\n/3PTNlFmVdhmDO7jWJ6IHDK8TdIZGMviuII=\r\n=yatY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.3_1670530371580_0.7589875153577739", "host": "s3://npm-registry-packages"}}, "0.16.4": {"name": "@esbuild/freebsd-x64", "version": "0.16.4", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "44701ba4a5497ba64eec0a6c9e221d8f46a25e72", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.4.tgz", "fileCount": 3, "integrity": "sha512-vAP+eYOxlN/Bpo/TZmzEQapNS8W1njECrqkTpNgvXskkkJC2AwOXwZWai/Kc2vEFZUXQttx6UJbj9grqjD/+9Q==", "signatures": [{"sig": "MEQCIEUcKxb1ebMPj3lLfw3WiBPqtxOg5wivipKrPgUndBZWAiAmSrJKBu73MQvqrPZ04sLAuNyAJVYziDCzFTSOEIxE9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8638941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAIBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqn1g//Ugmwb1HuiDPQhPeMMJIwv1KY06i3A/InXAioPUhPQ8sdA+Gg\r\nl4RlkSappCJkwGvzyi5qUTm6bpNPjJjcEp68LIJbgliHKfop5ZW1r9y978fI\r\nvQlceXmMw+hOuu5swSwqQKP62Q9xu7kTUuXDciWmTcqFdoa7nl3MGrrtOB2b\r\nSbkmfxmi4fqxsgGFnp3c6idWU5HKA0JrCLgujjuGmHa0x8m+9fxe9+Xl8sXP\r\npB0t0UYlCs1sO4xbj6QslBxTmRKBqqn1fC33u47jYam3aKZuF5RD+AD6XzA7\r\nqAhDnh/UIsNKszuj6dodWyBqUnFbEvSnX+oUfVd23NxF6SmFJ30TKaEUVs7Z\r\nETW6ydMawJVscKuuilVkvFm139RMC7atcjQ9GE0G7Db5OFBiq8IaL2wujmIW\r\neIfuKJIAFe/QwQj6+rEn5SGkpcNPN7PQQ5WCHui4zOebKT+JdMbfTSMQBx8z\r\nkbYkpmYe8DQ6lXV6UR9I1MXDZ/MkXVenYufeekos6Fk8LnK3Xn6tHMlpxCNL\r\nwz/I0SDKOXpaQqMT+/fSzBj6Hc/AQ3tF8mLokatsrrapIu8mit2Zq5660r4P\r\nqU2vW4aIvYOVesnZTc3C2UXtN46jKJwu9fMbKMXLL8IDThKbyn8G+Z06tRVb\r\nMEUVwwgSd7hCXbFpN9KXPXjOxXMR9iB8XMI=\r\n=+Q2v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.4_1670644225286_0.9220731468799828", "host": "s3://npm-registry-packages"}}, "0.16.5": {"name": "@esbuild/freebsd-x64", "version": "0.16.5", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "79bf9368634f390d16f5032a9b6603f02b0d999b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.5.tgz", "fileCount": 3, "integrity": "sha512-qCdC0T7XUxngX8otO4nmPUE/cHZfvF8jk+GMr9qkAGP0nIMACD7t/AWoY2N5rsn5/dOJ1VKM/aMF4wCFBP5AqQ==", "signatures": [{"sig": "MEYCIQCdlDvSs3lzRXBssahEaIrRmvyr2fPS8U/jyfPg2/FFEAIhAPEWC4Esqzwe5hgzNldsat3NiHd8KIB0U8eJX+wYxe4s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8651229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLq7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrABw//XcNEgXgnIbCl/V0eF+RFvxSqhR7Q7a57XkEeD/7qsYzrUx8d\r\nAt+pac0/8OGdNmBNXfIuzAku3+tkb2+9B68uAOSJX1rgr2QpY/dlXhpMmzwc\r\nbSXRw7NcMfWkbOT5AleD8DENd3id7ON6zIaeDNeiaDrSNZweti3jeDqIdVFP\r\nc1Ro9r1HSlkyxqPMUc8tw/0/fv40RR4JTfuJRUK+sng3ThiqoABVl9C6HvCz\r\naM1UWS3KqhZE762M6ndxV+Gbb+Ty95vBtieNYLRz5V3wpfx6yFDYSs1zRAwU\r\nNKce1ZxDlLE54YCZWaRF7Xx2KHEdpSgHkLJIBOfvJBmT7ZbWGLuq1P4hBipU\r\nDCMeGJ6otb6fwUT8jty8T5l5vJ8kcZ4A17d5jv0SJCNIgRFOwkqGkTspLW34\r\ntEg+AV/3K3Bz5MR/Sr4Glq0EyVS33Y2gN0+QFl3xfIpEZeCD8eRRM5EvbNTH\r\nM4ZL6ehefa6E5voCS3yOjwyn3LcM5yJFSKteceDYidqDLarAk2FYpvsqpD8V\r\nWpeloJ4C33QUY9Ts98Y0Rp265Y5Uj9W5ezl1dAvkE5j0KgLoaIKId2rJr85s\r\n6dEhVkMB69648Jgj+Ed7NFoKYLk4w8UmZJAXPBaJntLu+mqA78aswEK041fC\r\nuPf/N1HnuaEcnnJgtu9FGAosQcqXEhTy3pw=\r\n=wQT3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.5_1670953659273_0.5184008928424153", "host": "s3://npm-registry-packages"}}, "0.16.6": {"name": "@esbuild/freebsd-x64", "version": "0.16.6", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "7e892ec08c9a11863e86feff169452f3cde967af", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.6.tgz", "fileCount": 3, "integrity": "sha512-/BneBfb5v+VAqjDLt8Q/5llb7smIEJVPd1afNJDShRfj2qr5nIwh1FJaOjoEWe6I1sucdKJ/EbwOujH+iBkW/g==", "signatures": [{"sig": "MEUCICznRrOhl3sjgBIAQCpR2jvqa/JA3kUQ90sXIDk4U7dpAiEAvZuS75DPVhq2v+hJZyxmTl123bavr+p7ApowRGEu2BA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8655325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV2+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqd7Q//TnBtrVtrTplXsHie6MqpG8hx57QsZPe+ehW2HtTc0xmiO/Su\r\nPW7/BW0v4IFYox3EDQc3RBJxZJ2FWR4E/vhN8t3EElSG+J+0BBe0B1CM3uwV\r\n4YNh2wGxS5yoQ/AwgxVRJyDfi8UxiID3OfRxR3Lnw6d2aHjbCBb7Lt1TtT3z\r\nBmagrlntlDQa3YUX3n6o8xi3/qc0vnFgSYoAYzSzzjYfHH8TUx/dL6niZVCL\r\n1f6u9aKHKm6Ze+4QzB3nhHVHh4Bjc7sPE1mwx+voFx8szTT0A9Y3c5oQPYFV\r\nHmIEObKJ0dIY2gSE+2hwvm2UQd2rsLzAoU6CKrHJZysulZCUHl4B1o2AJGbB\r\n5KHch+Sl2YqF/qZPZ3b4uLL1VtrAanLD97AIxmtufERKRZxWR/r12ho9kU+H\r\nxnAFYMLJmTbhhkJlvbbx2V4YEjVTuP6K/imp3ELKmGBm8xQlrXFeIFuxkQef\r\n+xgehHu2LYLbJBEYkRZiGbiSKd/JR8Rt8I03ayS1X8h6S/DOVeAJzbWr5KME\r\n2bu/+lagqUWVjLKp64AqGMlE2mcxkm8LwkiCdA66Cpv/qI5TuzXLcX83cLPa\r\nvCQAHqndLmbi7lfsw00Xohpb5HdhDGIFt+NhX+pfGnCCMF8jjl0JJWGPSwvg\r\n2WLRal2H9Q+hqp/vAkv3mikZqmTPmJXk4II=\r\n=oWbO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.6_1670995389808_0.7113207567585873", "host": "s3://npm-registry-packages"}}, "0.16.7": {"name": "@esbuild/freebsd-x64", "version": "0.16.7", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "bc01c146e6af5430c5eb325844de43f01e0264c4", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.7.tgz", "fileCount": 3, "integrity": "sha512-5Ahf6jzWXJ4J2uh9dpy5DKOO+PeRUE/9DMys6VuYfwgQzd6n5+pVFm58L2Z2gRe611RX6SdydnNaiIKM3svY7g==", "signatures": [{"sig": "MEYCIQD/B7JlhwUOOTgBay7RC8iDS1XXZu9mQxUNcEju1OjFGgIhAIBUUtslm0NyWW5A79aiEHbDRM6Ta5wKXQ2vkrz8+4Ba", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8663517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlJdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpE6A/9G4+RWNvJ26iaJdS1G3YUFDbt4CywdLi3c9CYduEVxHad1RIw\r\njPkWGB/aAWnFys+LrWnTxV4NgA5lDIfKA9jR26PdiU2mzYHiyB1yFmsQXEhg\r\nBleUzsHl6io3brxXatzPbosqwsmKlWOTP8VktTLnSoq3pSoJ7bjTBqsbOTfT\r\naPA0pSOoQBndC1/9eylk5teApeqyMi4WraW+btSbjM+LF8Q0Vc2Q6t/nLNxc\r\nsB73Hf3mCwnQlYeWwlbOj8x943MqDGOfgzSL3pa2fl4/NvPFjUkT1b5bx5/M\r\nkYHbHIUhjD8uWJExtNpSRSKjvRRxiBkPXWFPDfhK2TC7nfulA7cxWTU3c37v\r\nGnmJUOVsTyQoDA6ZlUBCoBJSE9MLxGI6svjIVFtFTqoy68BrPKmtndT9v97R\r\nO8DeitN6eO2LmIuNdk8wLaGFTXG1S777Yfd8oGHpkPMuuUyjqxMNo0ojy2g6\r\nKT2StZiaGV7yGSBbTgKJeJ5Fq8qER/ImOF6MwYVTzf4yEKi6e97ReB+UGgvm\r\nVnrWICfKw/stjmZA10yujs6sv6dtHYMQMj4wuD7EoLZSQmVyTYx871NqZmAU\r\nzEL/7eqKImPPcmy2U7JCt2ll1XE95ANx4s4bTRwA3Y0U6DebquXeoDjVk5z7\r\nxkHuO16+FptSwP2U2azzl7+4p5qY5rNVP+E=\r\n=wc8o\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.7_1671058012971_0.6640320625542193", "host": "s3://npm-registry-packages"}}, "0.16.8": {"name": "@esbuild/freebsd-x64", "version": "0.16.8", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "fbec93f8fd4864b5882d62e45d2adf535ef2f582", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.8.tgz", "fileCount": 3, "integrity": "sha512-UcsCaR25C0tZWnoImprPzr7vMEMjLImlTQAIfWXU2wvjF4gBWKO9GEH2JlsKYqBjfWfGgH+HHoGSF/evZbKyxA==", "signatures": [{"sig": "MEUCIHOU8ykLEZAzGO9DoPgDjxhOZ8rLul6wVmryWspiNeOkAiEAv+8uNe3whzHLStVphAEfCmVToQDrxOzCpfGolui+77M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8671709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQF6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLHQ//bn486kKtnkq2zMHQ97Z4mkYHlhArTzfQ9mLra/bNAvHj4GkZ\r\nHXF1PFzSnwfa4/lrek0sCyyGCRJGhA30znkcBOnr6z8UwgELck9x6wsS5hDu\r\n7nOitWNS6ZEiYwUDKnydIVYM83fT/xMmHV/zLEtJC2oi0rpbcsB1hEkH3kCE\r\n8/Riq8XUxDyvHaJBPR6/WCATLPGd6baFu3p3NkmPxKUYYucijadgwJyW1Yk+\r\n/x6qF6JCf6bwJ2uyRVs+dE7GeYdePUZSxYRogPO6hB+2eiJXtfvs5Cc2e2HY\r\nTVFYUhsK+xVKe6efLSgRGIC22m7yMCdLH8IL68uCrq0yp0qAyazgv+qltzOC\r\n/iJ9J0zoNJWeG1VYhNs4yXi+PbTz6SNKP8sgzA5DKl1ca7NwrgdcktS09lXx\r\n1t+SwOQa9k13NXCM2DUn5tlOwgiS+F2AIjIP9DohXGP6RcDozTOcVZQ2X4FH\r\ndfciV0/sLsNFdUBxtrQfAEzuK5R0Ibkkqn+R6YjdVMg6feClNJO1v9zQlhHJ\r\ne75V5RhVj24KkU3nTToiA70tUbWhVePNke/rXkpg9wjU6Vl+SHowArWcYtP3\r\nZwRoms4hB2Fogqgxa6bTPNZBYheA2ma4n4vOZytZyXvF6qqHqNpO2p1keD+1\r\nW6cnqDRIVxSoVSGEvoUSwUI1Ba97UIYuwkU=\r\n=IsqC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.8_1671233914505_0.24366164482377206", "host": "s3://npm-registry-packages"}}, "0.16.9": {"name": "@esbuild/freebsd-x64", "version": "0.16.9", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "2db48ffeeab149c2b970494a60b82bf3004b8630", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.9.tgz", "fileCount": 3, "integrity": "sha512-G<PERSON>ors4vaMJ7lzGOuhzNc7jvgsQZqErGA8rsW+nck8N1nYu86CvsJW2seigVrQQWOV4QzEP8Zf3gm+QCjA2hnBQ==", "signatures": [{"sig": "MEUCIQD1OtL4cDVsLfk3q+BIUwSqnOqMp4VMy3pPkDQE54oV2wIgOU3IBfSKdIG6ptqZZnorJGD3QCmcap8W9J5MJFboRMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8675805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpeVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8SQ//dbg10lZ0hfUQbuFJ450YD5lhMTq2NCWCm9Zcsn+jchYrivGb\r\nsHGsCGXou2AsnYIwYFMeRWpOs1S+5aq6JhxYZZJ4v9mSqGGfKsDri7qXynqw\r\nqLKu3iqQ9lq2xFNa6TXODUJyvB5GqarLLajorNSkhhWB8ybaeGNs6pD6fm4a\r\ncEYC2wqt/bjT1d532hkrDSP+/5XSR0xFV8BzdAZ4WsnlQrDz3DltOinObbMy\r\nK+lZm6OhAgN+avGWdrrPrtG9eQf1nn1dD9RLUVYUBl5xX/KT3PqfgmBM1NPb\r\nKXXzObXa5fNqWKU+zf+uXznVtmFGHg5zjlndnEbl5AQsNacqHjxjIwS2+5Sr\r\niEfkXq+rhD24vrUHXiRlFDW7Ro+L++WtRLATxc5t2FglvehVm/CVidcKbuvz\r\ndEOIP5vIYVcVpzBcS7Xx2PscKYjP0KwHw17EBVoRpCtl5/PsVsZrmUdSjKPS\r\nAfgjGEIvXGfNuaq98lhbUwNeZ3JG8rSPysMtk7wmeK+FnUjDG3sTdxqfNCVS\r\ndoOa4psoOTLxx3Ub4Se/P0d78AW+NiGSHsDo6m4SSS1UPizEJWMqh1pylVCp\r\n2rJerqba7DpSCKQ9/ANkgA7EQlOWNs7aX6faCP4t2HZTGQM50PDPenX2YjM3\r\nFta9AQVCT8mLFdH12iLxZtHTwiD8R62emY4=\r\n=MBQW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.9_1671337876936_0.7355612004844461", "host": "s3://npm-registry-packages"}}, "0.16.10": {"name": "@esbuild/freebsd-x64", "version": "0.16.10", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a9804ab1b9366f915812af24ad5cfc1c0db01441", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.10.tgz", "fileCount": 3, "integrity": "sha512-5YVc1zdeaJGASijZmTzSO4h6uKzsQGG3pkjI6fuXvolhm3hVRhZwnHJkforaZLmzvNv5Tb7a3QL2FAVmrgySIA==", "signatures": [{"sig": "MEUCIGE928N2HUwnokVtqsN2gbPVWzKBU2/3+bYFhrJpxf9qAiEAu/xOiMPem/xtztsof9+SzJhqTd9Ribgq19qx5g9MM1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8688094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPMnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCoQ//XS/2g67DJ3RI2xWxFLcT2MBcorw2/yfXwPgaqVEE1G9IxpSu\r\nsoGJ86gbRUTO16Zlc7JC0cmQQfqa+iQppWh6chd6MbmrDqellwqH2fbjSX1L\r\nRk1yjOMoOMTYEVmb0ajGWVGiaORdLPtW75TNyu2PsVxtKmNnfU7p3UzYCpXd\r\nqCKJqQpCcOjhirtSEzNjnL7Wx0C2D4VlwwcmG6EXRb2mlHH28UBl8Y02JEug\r\nlYQoGyoKTgGrvfTFXBlo+O6zK1bYYJ/tWJm9mdGgkgRd/qFGYCwM5DAQLGF9\r\npBpZM0jihvPbd7nQybnmUY492a+hnw4L5JqUwbMq4IAlv+gb+HR6alghthH/\r\n8rLGM5H++x0scvKaxXf/UzqTmENqCHlIu9dZeWh3I2GdfxQ2zy5nAAH2hiun\r\ns6ciqrvCq7wd60gje8Br0j7u7DAfWM5aCoDNSRJJeuNPuXGaWYMVpt8TqXKD\r\nlmj1MKAm45b0t4jLpI+drTkc2pOO7ylqZQpGfFBHeOOv1COJgHkussWE3S8K\r\nVjJ1BRVzaOMS0CqB6k8BnJsAL+512rR3JEXvosMSulp+ZIaevbH1vsILYL24\r\nreGbyr4CFAmV/W98IwHwSJtytLIIgHsWubosK6Fwkd4mWIwddXDHKtA8QFRA\r\n82KWCg6wVKnAtwxSrsOGagCMvbhFF7lqijU=\r\n=2MyU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.10_1671492391223_0.3960220065923894", "host": "s3://npm-registry-packages"}}, "0.16.11": {"name": "@esbuild/freebsd-x64", "version": "0.16.11", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "9bfc8cfaed2dbd9afd94347bd5b22fa6c937393a", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.11.tgz", "fileCount": 3, "integrity": "sha512-L7hr6VnpqZzYEDVQeaViz1QnmfFRCRm3zVtljbYi/CU6InKs6tda1J3pAvqVsbNpbGMA9AvyiyBrgjJAFCawVg==", "signatures": [{"sig": "MEUCIQCAqKDjr8GjNVF2gvIFhb+SwehRnaLKWbxKxvwAu33MEwIgbRDTmcczDRpcQ+5+tqAI9nLmgP218UCEEsObHYSLeT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8688094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqky0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNKhAAhzeA7afHlLOjQU46eRu+aNwyzIHOPk36A2+yPX+97SwCYBAk\r\naPWqJEAqvKmxElM7M8WLLqDyJeAy7cM7tNURYvhNFqQzF+orL1WZkJ+xiZ/I\r\neqOdK1MPT7fJVttDawbet0xyvqkHabg7HmWydGiQG7Q52yrTWCUa4NuwvX1l\r\ngHBW9EgrSk1MSH/O8jY0HmvW6T4WxnP2SDthYWHrW0DwlDlCnM9fAELOwjbE\r\nlr3vvv6vkFAmu8q4yI1SWVO7IYdAmFBmvYm+t7+JZ11XhdNw5s4j2E5HxTeK\r\ngnWk2GdSlRa9n7DYHAII/kb/pb08SYIkRyxLzYAd1wLH8BONYe0BNA9+muWn\r\nUGGYeMDIFZhQNo8EzLgikyKV86uNWOHYgTM8aEdr/kxwwWlZJ9rDcTyG1mKe\r\nDEN1+IevmFg/IuzvcQSXTx5IMYHaUb6jhMw1yngg/l2/UpK+Kqrx3BI8xF6u\r\nmKQvDgjVoei4xSt0mS7jP6RddZ6Ok6mR//qo9mdAJi4/1kso63Rwh2QdckN2\r\nri/xgOC5vu51SxUbY1cPn/T3zQKK7JFOMERR+32Ac9nZ+Sr0yu4Fx7EbdVE4\r\nKvjHtzHo5XZMiR1UAEe4UwSvplo+QcthFgKUZtrN52liSatlHy1c6qsCOYpm\r\nlO90VbuflWOFbxPJoJHZk4v+3+tFkO/n+0M=\r\n=k66k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.11_1672105140032_0.35154024510936543", "host": "s3://npm-registry-packages"}}, "0.16.12": {"name": "@esbuild/freebsd-x64", "version": "0.16.12", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "e682a61cde8d6332aaeb4c2b28fce0d833928903", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.12.tgz", "fileCount": 3, "integrity": "sha512-KUKB9w8G/xaAbD39t6gnRBuhQ8vIYYlxGT2I+mT6UGRnCGRr1+ePFIGBQmf5V16nxylgUuuWVW1zU2ktKkf6WQ==", "signatures": [{"sig": "MEYCIQDhu/FyE+xkVUwUne7Ak/wClMrrfj/5En7I/pas5Yx/8QIhANX70gMSn5zBcixQX2QoYGKj3INDNxeDEWslm9kYsrqR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8688094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6Q5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpklBAAkarE9HZP5RrSJYCvO+mXRPKvQLKgbiD4N+Uj+HIWgZ3uJ51O\r\nqF3SQLcq4M8W6tasnUNkDzffZZyjkP5eXECxUM5pHq5N+Opsgit1Q7UVvRpK\r\nbwAQjVyzOS1RasKLxavOr9KgnjrU1pi51h3QmVQmRhn13EFxKa2BiQ280Vm4\r\nVr0yZQWP92QDGG2CpmTYF4vTWzGwcH38x8aQpv9+bo7DlYt2BYd3wIA48jTL\r\nFEpapT+oGxCfKrfiv2IeseNjHZjf3QmoCoDHd/M53A4k9hjjBlJdth5JTgxF\r\nYOUx+r9mkKGPNQrBV1hftQs3yYkdifQHOOFEDJ1UB3i6mUDgvZX2oZTd0zP2\r\n3Qd7PcU1bvOmVHfGLBMRIq8tfEW6FJ6hnVestgO6l1Bu28vGdP0O/wUjUmOB\r\nquN9GzEGeXJx8Zk/iPyFo6Gdzfe5tnza0cr7uslcYgTHAnrzYW89OBMfSUnm\r\n6aajEOqjmmLJddq0MwkG93UjpJxumjlmC+J1vY0LG2+lNGc7lgB38vtV1U80\r\npucIEB3vvxFrOIwGT5bP7QanBILTjMTXxD/zWt3jQm/w9O0QjdRf2mkP6wN0\r\nJrfmtAMhkVpCCY58McHiUcfBmshVQb3pNMGh/ndO9zT6xEY7E+5YgUwktCSX\r\nNsmBG1A6GkYsot/ayIKEsPhABgv2wdiOBo4=\r\n=Z21C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.12_1672193081252_0.04074298025466416", "host": "s3://npm-registry-packages"}}, "0.16.13": {"name": "@esbuild/freebsd-x64", "version": "0.16.13", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "ec64a31cabb08343bb4520a221324b40509dffc8", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.13.tgz", "fileCount": 3, "integrity": "sha512-Ez15oqV1vwvZ30cVLeBW14BsWq/fdWNQGMOxxqaSJVQVLqHhvgfQ7gxGDiN9tpJdeQhqJO+Q0r02/Tce5+USNg==", "signatures": [{"sig": "MEQCIACRgtEMobd/R8ik0zOjARdEgnSMLsehxGVWBWHuNgmhAiA4vK60Q1DrE/t6CAe65tFDRf+7X1VvrhkB9wmxE3HTwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8696286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2FNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTEw//XVi0JSfbXJIjkRO6tAdktAVzpRVEZGZGIJ9Dgu5zcwaHnx/W\r\nOsL1unMeJZB6sFSHuEPO4WJvTNHox+/OGLibwuXywFT4nJK0vKNuMH4nJmg+\r\n2Hb8XdhSjeK74wKDl3Qag1bIdO43m9jSgCp4oykoVPXyWLSFo8Piw9GrV2jO\r\nakVQlQemZQfae7+htbSEVRUJ2dRFmEGKJbE51V+DVsMxajDyAWxDoO54sX/G\r\nUcVFTSXVdEcXjS+8meMqBCq9cRJ3Gxh4cOAn4x9k6dMi9Yx4Qv0zTqBrWuhZ\r\nvXrfRhCR74M1fqaVCalbaXMkZTzJfK/X7jXL80eqnBlH2D4iUeSMulngcvqW\r\n1YcNwvpn0txUEWAR3a2Iy6x/tMRh/E+kFSlQ/2rXygswn6VLxX6/U1xIUmLJ\r\noLwGinmCHTmZhraFzYXTkuWL+qyVVmmuObsJf8csSDqA+pba/F+Pm/w1lRZJ\r\ncPxX+WyvnpFFFnR8CtunVuAVvDXcFiIb5Ajand7c+CDdELGq53+UmhIFjfYM\r\nKfBOVuk7VzeGm2E22ghydi/RCjWRKOG1huHeDuwUc98iC56kAVdoz30ue3yf\r\nknN9pmt7qmdXNsVG1VtMaehzmrusWnLSRr1D75aK4FVRPSaNqHAzznWJAYhX\r\nMu/SQusLUI19a4YiI/ek70hfQTCafu34JSY=\r\n=L5k+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.13_1672700237130_0.06078666722237425", "host": "s3://npm-registry-packages"}}, "0.16.14": {"name": "@esbuild/freebsd-x64", "version": "0.16.14", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "d3cf84ff28357ac8d0123309bac37fcfcdd98f53", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.14.tgz", "fileCount": 3, "integrity": "sha512-7ALTAn6YRRf1O6fw9jmn0rWmOx3XfwDo7njGtjy1LXhDGUjTY/vohEPM3ii5MQ411vJv1r498EEx2aBQTJcrEw==", "signatures": [{"sig": "MEQCIG+sko1kfzovSt1MZNILArhZ5wlGpAP4236hBIENTheBAiAfZQmOGvpssIsEBIuLWpCbGvXcP2y2eiQhAEDnLvWAAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8733150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd3HACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoxxg//YCmbF9Ba8RBq40d6fUdhk8fc3WanSa52CCLOyG3jewrFMP0q\r\nnRK3oV5Z3Bu60EKgmFCfTbeXeb2eGBS6Db8rg5qL/W24JUnpqqzP9jGS2djN\r\nbtmfL4JR42ZQTxAPmlXo2G4mwqqHzn4vwAQsaULtRORlCT4FDRepNgEVQ0xF\r\nhmTPmH5yIxCzCWhhSW3Ey0rQTZX2fmoSRX6I4HL6ffPh6F0lMW2LHbNzxhB9\r\nYukZXd5i8kks3bGMmq8pliK6XdS1vRnc1vclQIPvhFjGnA7pocR+qWm3HalQ\r\ng56QXFOatrOkqlfORTqgpBsfCHMr0xI72F8WuTYV7L/7xYp0pL66peCbsXNU\r\nYnSuz+8qh0QXFpaiwDpRmxuL+N8QR1rk9jBBZWmDxIZxmuLhyynjynbJv55c\r\npodw0MZnfeJaidgwONBxAmqBq2dqGiDGPBNsr/seVLG6XFqmfufH4wi/KWQb\r\nY7qQiCbMbc48y5HYEfEHyxsmAIWkiS6546qfXg1Vlv9ysqPWwbcBmBkSoJtr\r\nkKf9lO/UhhYLbHHZrymGuO6SoipyrbZ217yOuafvxCLnM0r3kv4zgoPOa6z8\r\nrgjl2MP01tm3CnRwnYcTVx0+m9khv4k6JYc2Q0v0c/GoGFtBAaiLYGnP1BiY\r\nmCBM2/TPAMaE6FbrUFBLsTefs7XIJ+ESGU8=\r\n=CpT8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.14_1672863175031_0.24653172609393392", "host": "s3://npm-registry-packages"}}, "0.16.15": {"name": "@esbuild/freebsd-x64", "version": "0.16.15", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "68855666ecf1616e2a927154148d4409cd8bc55b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.15.tgz", "fileCount": 3, "integrity": "sha512-LE8mKC6JPR04kPLRP9A6k7ZmG0k2aWF4ru79Sde6UeWCo7yDby5f48uJNFQ2pZqzUUkLrHL8xNdIHerJeZjHXg==", "signatures": [{"sig": "MEQCIFLGO2JqnE7GWLsfuKSVvGCXLyJz372CNZdvzKwmqRPiAiBSPl28nuBrEAo8Cfu/wRr6TC59+9PfacCGXW6EWwBOkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8733150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPKuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNow//dNaZC/pmDkyx9PhD1wXXyXcYKX4iMMCu2ik59LbU3HHyknA/\r\n8F5yB/ldiXkSMlPvW9KcCl2Rjm3AXvl9ZZTNEDt52XnHTFjtqaalT2wHAPtM\r\nS645JmJOokdg6YiO2tobORwqDW0keaTP/7bSuEFwtmZQPF1TVdTBEnJMeLnD\r\n9m7bThF8E3Jsw0/IbCi41dOQn9W9I6f8xJkZSD0o+K1YON9Sj0iuLOyFHD91\r\nPgJO0uo/C2EMbl19aKcRhrz5tsN62E9q5hk2hmuu11E6gAQwhGqP75pnDEHs\r\nGEB74KB/gJcSh9zQkwQvcUFlXdZZUdhGY+lkJdsZZYmpiJn7WumioaK06mCI\r\nYcrpfh95ZPYZAXGvkQC43+IEgFTiVbv+V2IMDIO1yxUHMCjEzXgnfHj0crei\r\nlhXMYWQT0TSFMQhq5mw5Gn/dfCxD3qK+l8U7RMgQIH+Tycn/GdUwm99JRGUK\r\nBfPvaZ0KnNfT/TMb+1juLx4TCrA7+BmkZTKB7yzR8nqGtFwMcOlRrOi/EVwC\r\nzGqxjgL2NLujPSmcElZJEey4HLKaTKQl6ofOlCY8eawb/9R+gWSxd9yPxKfV\r\nyWs1OBbUX4sFT36q0CWr6y/gDdwSuQd7ndwkXch6EkTPyBTwjCoAPFgvqXu9\r\nApQsts+1JHpgGvuF1OMjSuNBgL27L9BBg44=\r\n=5jZ9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.15_1673065133634_0.3677801486623353", "host": "s3://npm-registry-packages"}}, "0.16.16": {"name": "@esbuild/freebsd-x64", "version": "0.16.16", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "02e8a81b7e56040b5eb883896de445a6cd3501f0", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.16.tgz", "fileCount": 3, "integrity": "sha512-8p3v1D+du2jiDvSoNVimHhj7leSfST9YlKsAEO7etBfuqjaBMndo0fmjNLp0JCMld+XIx9L80tooOkyUv1a1PQ==", "signatures": [{"sig": "MEUCIQCFlzlQK8QRNnBgPB3afXWXz7iS9UyYlFvo3rbIf1/JOwIgTWzeIXSFvjoChTOlsbzJV6CIdxaJeQcIJ6N2OfUo6Us=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8737246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0clACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpC6Q//Z6wxxtbQBi49zjmY6ta1JMQv+r+JuFo38QJzQN9DZLMGO6Un\r\nSbEtgHurkkRuYoCegLQEPayoot/APVVGmDOJ7Ii2eDDTQnXEPxZ6r6kmoej7\r\nzluJUjamQ3wh7Go54EJZ/23CNvYHMAB1pCqYvSNhUSdIVR30reKv7iMOoo6p\r\nf8iD0WtckJWZ9mXwQn7YoS5HOVLHoyp5ac/geR5O5l5izDFvsI1UHUJyrgSR\r\nZqM9uL2YjuG+MZcC0QP91jYdLy/eNm2xXqm1C+vv2/dQBn9nbaYqKbaWOw6w\r\n71MMHXqVWIeV2E9HyCvcvuwevuxgVXEwz+VJddL1uvzGSmUDBTY8JBG2ifVs\r\noYSmwRrUPi0+RHx0qO4Cz54uV9iFuev69dftgToGsVyx7wXHDggCUeiTlCgH\r\n0afGN6QM3v/11N9bJA502/SvD5gv0X4vJlF/iyraOTdZY3CZ+n28tHE0A2gx\r\n+gdxjJJ9NZs9IhCLnoz8qq180awlgrOdvLievBawGVKST0/wRYKNjwTNEZCt\r\n0GVe5HBX43zTd64cd0gPBmSLjtojW08kHsSh/TBugU0PSs49e/SYFvaKPcVY\r\n4OeiBGZ5jIRTc4UHAq7J9S/H4LOZ72reRAqQYl3VM0W2LwBxdHxEMJAZKrY/\r\nQHbFxCmy08GrTRzm8t63nfpEEcx6Rf6llDo=\r\n=Hdbi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.16_1673217828728_0.5307014173853897", "host": "s3://npm-registry-packages"}}, "0.16.17": {"name": "@esbuild/freebsd-x64", "version": "0.16.17", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.16.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "636306f19e9bc981e06aa1d777302dad8fddaf72", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.16.17.tgz", "fileCount": 3, "integrity": "sha512-8ScTdNJl5idAKjH8zGAsN7RuWcyHG3BAvMNpKOBaqqR7EbUhhVHOqXRdL7oZvz8WNHL2pr5+eIT5c65kA6NHug==", "signatures": [{"sig": "MEUCIGi/pNvo4mGCs+KG4flFDu71COO8oSTtL+KHkPVOmUSsAiEAiH4SjZBG2XMxr6/uk/AlDDnfrKusZJkjU3AvMP7u6JA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8753630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzDjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp25g/9FYxCDTNe8mXyI8Pt1sub1ygaFlfAloXjh4rLVAT0ifWUlRqt\r\no5fq15Po+poReWsRgKjiGIc3Tg0aBfUWAs33X4Y2RJA/bsWUQD6xB7bNGSvH\r\nD+zv8cfSGKOH7RDI/I1N13C8yT7Zmm+KdwGJs0Zo9mfJiFXDLa6To9M1Y7Dv\r\nzQqVg0+lPro9NONqvzTM/izxnarpvq7xq0BnVjrLYy+lrFiM2/3iIV6I3uIi\r\nYR4wrqT1A17gnU53CkhB+BvEUJaHtj6Kvb5259zZ0/QsPWmFsmPLdoudzisl\r\nqDHeaboz27gRme9X+hZrI4eBMI3EwozpM/eTmiJCJW6V/LzU2imQgF51uIct\r\nkg6R9o09DfH3bK53nQmGSkC2fopsS/LnJCnT3AjVC6T7zvXU2J1L35IMcfRt\r\nwdVz1ULPZ6zGZQjdea/pY+MDPqwnGpI0M6ga+oJaG1IQalqmvbf3iRZMRPde\r\n6H08L8Lx7Tfp9DgT5kXt6Hd577zafkKShh9F7khNTFiaeJOLiX0m0N0UbqyB\r\nwQCt1DUb66oRroSiuZBMjqws0Y8Z1MJZj3s9hUsNKPr6Ww4Yqrfbz8TOeIH9\r\nfGjXUj2db86TVgLt96JQTRXIsZYrTn2QPV9DG3Ag0a6ZIWBica4Jw7CEK8U5\r\n1R/RWCA51/E9O9RJHv9z/gZmHW7xlSq8esQ=\r\n=ZOOo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.16.17_1673474274884_0.17474471727909835", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@esbuild/freebsd-x64", "version": "0.17.0", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "427f2a07c997fb30f1a8906b070e28959f38f1e2", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.0.tgz", "fileCount": 3, "integrity": "sha512-qCsNRsVTaC3ekwZcb2sa7l1gwCtJK3EqCWyDgpoQocYf3lRpbAzaCvqZSF2+NOO64cV+JbedXPsFiXU1aaVcIg==", "signatures": [{"sig": "MEUCIQCEflYl9Y/1u+LOk+OWCKf/MIRkC6asd3/hgGMpEx7lVQIgJKBZb1kBfhQubiB6haF7dxO1ss9cLVc04YYGZAB335U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8999389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjCjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqj6BAAl4EmaqjJS32xYG+6GsMGe1BWkWA6Ub4SuMsdX31j6pxJfayZ\r\n5YuByLnfJPrIqko+8/Q84Va0Ntddgc/Daws611vrzL17doLHtWh2x5AHiWjp\r\nj7OdOPjoyn7PxmAxeX9oJstxOm4dnIiNfESlvnT/MISFPGa4+7uNTLhY1raD\r\nVqy8xmBxaafrjjnSQhorzz2a31Kmtnx3Ch9y9vNmqsfAlrVNzeBEEVpzf4F+\r\nBED3StPbFCFOLsr5u/oGHqj3N+aRPQ4OGrNzT3m4NrRginKKlBHOiD2TpcZ9\r\nW+uuFowjzjK6cGFRpJcc0dhhoVDqSJexzr65yEm4OufNZys0zRu2ypipPeqn\r\nOYoCmmD6AzlVnGcrKlEDxvrCvWVBdj8laLq9L3CGuc1gsuJehPPkZsKtVzVH\r\n41nkVyrc65YPAg+6Lz7X6l2zBz1BjyD0Fw4UYC10mXtFecGf5h8KvZrs4+pG\r\nOk3QHYybmzzc3vgpERuJEOwLiYaCsbv4yRdmf80EFi1w0ucuXPrAQcdbzum1\r\nEtuASPKzS64g3PgbaGvjJZTSPTzhDRBOpd+Qocti65LaHZlJrts6A2BqdBQF\r\nYeUBXaZ93txw3qMwfoyFOCSDl4OTHzZl8e/YZjwXt1ozVavzGH/UieXReL/L\r\nEeHCusBJlxNLEObx9Bq56/vwf1/wzuxFdJs=\r\n=4nMv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.0_1673670819360_0.4727241350326383", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@esbuild/freebsd-x64", "version": "0.17.1", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "224c915e03cdc66bc59a9578c97f15c75bbaa638", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.1.tgz", "fileCount": 3, "integrity": "sha512-wBoJECwLWzapCND5Ime9G5QY5B/cLLXh4oCtUMnckcV5D4rL5M5WN63zOMWjFDDGpiPhnePHaDnRSV8AhxdUMg==", "signatures": [{"sig": "MEQCIGj6SJJc9BaBwn4T/zzQjWS/pRNgVgA+V7HkNRQc5uQtAiB9S/8u/nTEwHGlSoUjOcS5sIBN4G6lBqot37XCPYrP/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9007581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZHtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqm7g/5AdV849a3G5NZzw4PuOXLu4WIsJ9q76bRp9R0nv0L1cjZuwic\r\nIxZ0XUzXJH9CT0t6rjC7HzlabmPbOYGBJ3NlvmTJjIoHr5hScCFKASEwZ65Y\r\nbE5O/gFzNQW3CJfXKWiv2utoc9rrWtpjrGuwPBT1yFdwBxdG2n1YUH3kL2hS\r\niEZD0iZgq2PvVs0JoLaaQrX5z0amJrORoMrcY/m1+ThDfwwdAZkZr/MF/9+Y\r\nMMdCFdRGVJ1PCsgpvUTmj/L0gQO26OM/GlhpNp2Gjv0z0F9I8fvw8CLfcBEs\r\nVkjccntXgBn/iRgHuaHCjiGNSnBFY3N918h14Ea4CFigdIAhQANiTg2JQb4G\r\nhKF1RNBq+txIA4w/9gFmMuiMWFcMEPzrKnmoXbY8ecXja5+z7EA25iRDroAp\r\ncTlZuqTE5Ij2rHWCIuiEvAkapKWOb4L1V2sphNLB/9D7J7SWuNR1F+8ovskP\r\nbF6oeoKMWmxOr14Lmgc1kkoUpFsjXf7EKPTekw8pRrZzFnemPY0sZPpKNa+o\r\nrkEhVdoQ0TGrVh/JUhjfLbkI0D4Vzp+ZhRg+aR61ds5JOj+0ZouNV052Q7fV\r\nlGTbj4gzqToMh0cGx+Mg92/mczk19f6pH2q0fHUSB4sPLgQoQbyX5eTITKBU\r\n7RqFVkUf5nu2X80JhitWS1vdnKY4J44eJ3I=\r\n=MpkU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.1_1673892333574_0.7746090417274927", "host": "s3://npm-registry-packages"}}, "0.17.2": {"name": "@esbuild/freebsd-x64", "version": "0.17.2", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "7b29d68def0ab7c5a21e3d8ec67a7a47db5f9993", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.2.tgz", "fileCount": 3, "integrity": "sha512-NgooSKWSnrNKRuiumY1dg7KAGpsyXIMcwyOXN9imnqe8VFjqqrEOMqZRik0C1wlfLjiSCuMsj+YUSmBMAJMt0A==", "signatures": [{"sig": "MEYCIQCFpIXeSyMyRXWixcZ62LQTq1vhjgUtkkvGYA1Fmg2MTQIhAMDULiz3V5TlJHcLjt1lYBPejH/0qS3tb+EleJ3QyciD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9007581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkKrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnSQ/7Ba+m7i/UrK6SJ959IhQob/o3DMhF1S7Rahxl/8YcRKFRtV5W\r\nG6s3rxF2hG+V61kBwH8pitmbFgWE72ss70KWw3OB/ykb38JbGAIhPZDBsx2X\r\nKwKGhC8rKaMIJgpzwtw6ZFaBC/bZOLfxNvY42/6AU1yKGTODpJNyBQT9V+X0\r\n9AI4po/OueX41+ngTaAIAk9LcrJOo7GKfGRo7sRTjziq4saooRF57N5drVyw\r\n1RVYkOxSZ8dTUNbP+KJbuxNtzaFQ1JRuwLv4mrO4yYirvfaoYVAehAwU5FFh\r\nhueqKPdHNH68EOY9U+c5x2oz0XqXcQT8n1rSZgUiJcVmD6k/LhQAFRLziZhk\r\nK4PgOZXmyIpXJLczFhP9la/vWMAqpbsZi5AqnyjxVHtCNeU+zdls7yq9hdXG\r\n72KXcuECW9fEe9fapzs2lMgyhL7Fv4DVP7+Eu+2RuPHZ3XodxQGe0LW78uHI\r\ni5XphW7yfnlQieVZUlnpRRc8rm7y/Xsz2zqed0CsW/399w/VSgVRuGd6Ar1L\r\nVwvEHuGTTGv6y+CVg/D2Q7j2eeqzhOkPgOUXSGAPcxGuwJeVnX8BH4Vao3Qc\r\nfMEBGHQm842ERM+YF75CcL2HgWMRBMiCgLs/YE8UGkERS6skrmc1pc/unobs\r\nKZux6FAqh4PerialUWmD88T26JWOi6ON090=\r\n=Qlcp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.2_1673937578826_0.27832397318465185", "host": "s3://npm-registry-packages"}}, "0.17.3": {"name": "@esbuild/freebsd-x64", "version": "0.17.3", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "8760eedc466af253c3ed0dfa2940d0e59b8b0895", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.3.tgz", "fileCount": 3, "integrity": "sha512-feq+K8TxIznZE+zhdVurF3WNJ/Sa35dQNYbaqM/wsCbWdzXr5lyq+AaTUSER2cUR+SXPnd/EY75EPRjf4s1SLg==", "signatures": [{"sig": "MEUCIQCr0KtZnjm7I8UVU4WNPgw17FlexucE83PPabz7JFeh+wIgJyP3KiBNww588FQcZyoEbr5jb1qHWen+yZZMwbOZh74=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9007581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEUMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4Hg/9Ge0otwsEUxNOFY9UPvV6euAt35moULcjlMy7Kqa93lhW6dC5\r\nLaIHeOhJeSd9I8rGRT6pfSI+EMrCNcOMzOWlU4XMxcBXBraI0pPANqX1yoz/\r\nVn9yeBCbd9x0BN/PChv4mWMH3WalCJ8iFvgGPETzkbRGy1ZloegQniSxMIM9\r\nGRFAT6IfAIR8Var/bgOUWWp5lW6HlpHCZr43V6mb/Zr+bz5D7cKfQ0HW4h3o\r\nii31/XOriHCEXFTVFkLn3pbX2S/oKyYUSt5d070aKNurWzlm1eKFt9D3w+J6\r\n+m9eSKbRkslzJ/mBSFIfswypxMOeMW36GSPn0Cyf3XFF1LMd1SDTH30sJ6yZ\r\ntjbi4ouskabeP/Z4esjA+6G4zJFuEDrPJRecrccABI1ccDuuhqfhCLc/liIt\r\naEehUN1FFz6uME8QRPAoruPOlVVqkzesL+Lk0xq8QO+o6tN9c1CQ0/YMRcMs\r\nbvGpfKAGFDe+fdYlZwI8J3HM3E0wuVumQuJSvDH1gISgtXLneKLbvYkstLsm\r\n6tsM3Vg4xirAIAcT2TCYnK0sH54U1kznTg48zgVecH2N0TW0Qfbytgh3N+79\r\n6KJ6NXKBpfXfuFvAYph++Ab1fmJG47obKXHor8jbVfrm/PVa2QUq4Ak8FxBn\r\n5d7N4n4b/SMNhD50GNuz1grms/1mYK1K168=\r\n=8pbG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.3_1674069259757_0.2902569468218552", "host": "s3://npm-registry-packages"}}, "0.17.4": {"name": "@esbuild/freebsd-x64", "version": "0.17.4", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "314eff900a71abf64d4e5bea31e430d8ebd78d79", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.4.tgz", "fileCount": 3, "integrity": "sha512-U4iWGn/9TrAfpAdfd56eO0pRxIgb0a8Wj9jClrhT8hvZnOnS4dfMPW7o4fn15D/KqoiVYHRm43jjBaTt3g/2KA==", "signatures": [{"sig": "MEQCIEAUQzRZ4W9N7WGjMuc99ZjNzeVF3TcPQYvA7FvLe2jtAiBK+mUm/luP0C1XHOAo6GsFjxAe+jmRDj/du1i0Z21xSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9015773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrntRAAnV168dfiW+ubms+WiRq4plE+FvEejgvxKG/Wtjlx1KbgXJWw\r\nAqDKJBdl0Lx5l3M0a4hYyC503qMG/kULG6TAzWE0zUCr37XrFbj3FnREuY3W\r\n/BFW5vBltP8Rzlza+ksMh6R3iT564Vj4sjJohBj3Yro2W7/XO+sfCbjyjnXU\r\nevTMhrVu4FSXz7zZy94Rp+m+f+EFtd2pMGgndLAnSHPkVDEfS9bgiT66krvE\r\nLy/s8NPi5dSCt3ByJ0yR5yppjPN4wdEh9iE6WKK8u4SmXzV3eMhx6tDybSud\r\nq4PRgFfWeycRkGj4FisJyFdwrYiQvFnOxOOqcuAwbEZVZH7cCfKqdmplM0Fl\r\nTVIABkvq+igGQBj10vvO3Gi2bCWI9efh1Vb+rpkDvcD7vSvLr1Ewf7PfUPR5\r\n5JZGa3mvGRzRqdz741NwMiH8zeCZcJizNyGOafabiO9wZnBlFU2bGX+inQIu\r\nI28hTlo/8sHTF1IRJiV/Dm82qlbbg0h5Gtmn6fjeh45XYPRCSFKcqbwjhTjd\r\nlk2C+LM3X9nfQ6w4h7CtjTeDlpB7F5FD+T7NMpco055LcuOCkkY+yJWjqHb0\r\nj69Q7qxqYzdd3+qPUICJ2oelsCxbWb0g7H5SQ12BUDVPzIJUt8mqgLbFOZqv\r\nprJMENWQOA1fB8dMLyT8eMattawarC2G1sE=\r\n=8fRx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.4_1674368018798_0.6674518627840644", "host": "s3://npm-registry-packages"}}, "0.17.5": {"name": "@esbuild/freebsd-x64", "version": "0.17.5", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c9c1960fa3e1eada4e5d4be2a11a2f04ce14198f", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.5.tgz", "fileCount": 3, "integrity": "sha512-olgGYND1/XnnWxwhjtY3/ryjOG/M4WfcA6XH8dBTH1cxMeBemMODXSFhkw71Kf4TeZFFTN25YOomaNh0vq2iXg==", "signatures": [{"sig": "MEQCIEMxyxONhns9O6Gm75KZHUvSWP5NCq+AF3vk9D1BlpivAiAu116mhGkCIejlHHUC/HR+UOC/OgKtDuagW0UAHwu1xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9023965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/3ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZvQ/8D7n7dTAHBeqSw72T4itlcN68+EwWesgJDsukCX4BkZw7VBsA\r\niQUXaQLtFHfG3cAYz+HJAJ/4AMriRDLDUki3KQnNC/ROEFJuBVrBUMvm+pOH\r\nx4bI5suVCKdSlXf2kMFkO2gLqzW6dGrhMZ7DKUU/YV2dVnVuXSC9DtsV5cJM\r\nZjIE+FItANqciDsPa8WHu0Sljj3T4aR2Sfjjz2KPg0M2fOqn99mQ3XNz4VXl\r\nUcz+l5mLP4BmBGrquIwSkK8EKzGdXJYzK+svDbrWHSC0ellJBtX8Qh3/sTR4\r\nlVznpQ+JbbA2EIeH1B2voRzj+eLxlRIA+Gk0QWaFbwooaCHb4csPxx4I/rWI\r\nmSSAtoBDFiou9vrau9ByXPdWL4lY/8WclVLI+N+e8a4aK/DZkL9JgRsH5muf\r\n8GfT/iTUp7YM2gwiBklEB1n94gJYaUQomijfmN6VS4rw/mxDhB8j5I2MtEtH\r\n96Hz5zmegpNHa8vFqRseV4IylUynYx2qsailrpi4rtquQkhHoqvOGaBq6QYX\r\nz5EAT+bthmRqY/AVRfphcF21Xp3yp+S8mXCT1SS2iwpxggGMmSMTYhGrIC+N\r\nsTT5zPTOXeVERk20lNmQqeTsZhXPTQ/O0r+5Uvjc3ZqqdoRBAAix9FJtjw2+\r\nZrhRpHbZVSuQez/5zuupcaU2FT5bj6y5o94=\r\n=xElK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.5_1674837465547_0.9898123006129054", "host": "s3://npm-registry-packages"}}, "0.17.6": {"name": "@esbuild/freebsd-x64", "version": "0.17.6", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "1896170b3c9f63c5e08efdc1f8abc8b1ed7af29f", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.6.tgz", "fileCount": 3, "integrity": "sha512-Uh3HLWGzH6FwpviUcLMKPCbZUAFzv67Wj5MTwK6jn89b576SR2IbEp+tqUHTr8DIl0iDmBAf51MVaP7pw6PY5Q==", "signatures": [{"sig": "MEYCIQD4xefIv8xk6THF9hH/EfrnlXlzCRsn83u4E5FOaPB7SgIhAI5PNN1TSXQFKiySyu4d0uWWX7lHK2eTPXnbqvxZrCkA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8987101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TI5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+xg//VA5ZIXr4e+Nk++nxmdkLVk5zwk72GGltOWxGVMAPjYGYFECX\r\nfBGQat2VSX8i2yQzwApzBe1jpGgqvQINhpq1P7XmT2XjL3cJMDOE0f8SLsfM\r\nvmy1WyLKb+A+fXZgzIJJlpTT38iREt6D/MpGpBI5ZVwG/xHLaPzhKJD3TbBd\r\n6OaF/Le6zVv6rgEcmqOO+GUsVQ0qz/aVie6Silv2FJ6L55aiUhzg5N04SZ+3\r\nsK6nYyeKL5KYvNewxloGaZFYGAFpnUpZ8ddCK4YmFMpa5VQFQMiT3Ouhrs1/\r\nDFTmUjN9GX1ZHqcIzG2dpPeEr6cgNkNc0ukLnt37wg9R0BQbC2oN6nYCs+jn\r\nYf/2Np2tfqiT9Nv0gsm1Kt9TJTxRcO5InVbs63Hb6P0ycu95Ch+MplBYnBDT\r\n/QlixKfFzQXiWGDEZxzbSUZaxaq2jBJBWL/lmvp2iTvs/Mlc/d97x5oQPn7j\r\ngEKhR8cme8m0py/cqbPYHyDVWnNp+jqQeWwlSidh1SXDb89wvD65XmwG6Ipq\r\ncFIIxTNjLN8PSSYNH0h1gmVcmq843RtRDtsi2YT1WaXoOfEYzBIJZIwWS69F\r\nosVF7hxbC/PI87/TunEzA9tMf0j80GpEw8Et28MBw7L/qvLMuaL4erjOnnNk\r\nk55PViDNqNci6EAJ/1VLu24CpRfGzXzlftQ=\r\n=EGsQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.6_1675702841154_0.8789979905341776", "host": "s3://npm-registry-packages"}}, "0.17.7": {"name": "@esbuild/freebsd-x64", "version": "0.17.7", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "b0e409e1c7cc05412c8dd149c2c39e0a1dee9567", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.7.tgz", "fileCount": 3, "integrity": "sha512-8kSxlbjuLYMoIgvRxPybirHJeW45dflyIgHVs+jzMYJf87QOay1ZUTzKjNL3vqHQjmkSn8p6KDfHVrztn7Rprw==", "signatures": [{"sig": "MEQCIGxTa/iQ60+gJGIdEfwyIiKnkjhFCU0l+brMcMYqh9TtAiAr6hX8QIAXqSd5LumnNvfBTfkKLvB8PsEzA5gjRASExw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8987101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XMgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUpw//ZwhH7A915Pfee3s7xYNDw+OppovJq561iZFofK1mEipnrvH+\r\nv1k300OR1xhgV4qLYvJK0l7iEJ4m+DgLT5aI3GkJVAwOm92fxW8qCASPR/6L\r\nsysPOSpLMYpkhT+4Vp/rsZA3eIG5H/DgWR91Mu7livyIBa5eZ4rB0ZSgnbRZ\r\nQIXQy3MSO3oyjKcyPdyN7WEwrWKV9LG4ZqPkzNIna42fmnSUHUYcZxtX8Mor\r\nF0At51ZstzYcPiN7ndRFQUWLSDsxM91TicpK03qCEViRMar4bPPgQYtuixxu\r\nERk10BgR/+UF+0LdSqrM/r6qokYjVC/psi5m30IbRG05FG9yNLWLuPLoVosb\r\niFuTgWsc6mX/52A3jDL72xneMsi0FAu/ITMzMdi9I4qwDiUtvmhNUC3QnXWr\r\n83oG4b84KzOrGh1WuQ6NGhcBO0vFlALcTsp9tOCLrVVhamKDp+R+4jF9w5q0\r\nVD/J4sMXP8tXcbzd+bNZ0pao0xvSPvW6Qh2LJzy8U1n0q9BO8vKhfIphHS5c\r\nf0NogzOyVhOBSd1mEQomG6AkCsf12sydOkcjbp9UXucNQe1XE06dDISrbKOz\r\no8FuKxJOAc2zwMP107NFcZUycuaNBLlGDeiHlPk2+rh/Y71SyloM2bLDxCkD\r\nTzHo/JE3pDj9K5kCWldxat2jO0QUiNxBlTM=\r\n=onUm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.7_1675981600626_0.48574101102969425", "host": "s3://npm-registry-packages"}}, "0.17.8": {"name": "@esbuild/freebsd-x64", "version": "0.17.8", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "1855e562f2b730f4483f6e94086e9e2597feb4c3", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.8.tgz", "fileCount": 3, "integrity": "sha512-xpFJb08dfXr5+rZc4E+ooZmayBW6R3q59daCpKZ/cDU96/kvDM+vkYzNeTJCGd8rtO6fHWMq5Rcv/1cY6p6/0Q==", "signatures": [{"sig": "MEQCIDQjTpyOVo6SycWhrycdGOCyye+ARxCQFanVCZHPZ7TrAiBGTcONYl42Hk85X3Ijm2ukrYeR/yN26KAysUaTBOU6TQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8995293, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6do6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoM3w//RmMQMarGVDfgVsj/dQphDgPhEsvwYfsBDMZ6BAiqAkMnTh/K\r\n22pi73xI1BXZhaBlBUbokYJroZlMxlEXGNf24z8ZY8+gc6wPI5C/F2cTD9qg\r\nEG5t1ymoNgEIpAVxCjQr24C92sLxuFmUK0h1apRiUrtnKQHaweSdzsCIjguQ\r\nLaPr4qex9XDz+2coi8lUtE6Dr29GPo6ZcCHWTSJjV8ke1RpNkOjhfkjqvm7q\r\nIl3NTvXlgmk/F071XasaOrRhnkdFaTCFJiTErvCehEpxkk4VadhGi+8fJ09E\r\nBlUGAejXlE49PP7m1tK+318pb/mFGg7YUO6L8gZVsbyl/LqrcS9AI6wltDlt\r\n6kOPLhhz+COgQuT5/Ttn6Iwca1BypsguycJaUbTnz9IaPgzrro0/mJAQhwTG\r\nkBUcV0SDUKDG4MfIpBikKn7lcDxzOKC3C26A3oC0kI+jZVD15KuST24jhYxX\r\n0/eXmPS3lDLFcAQw7vZE8br7wc+dooJNaBUUeo323lWp/pvnBiFfHmzH5EYv\r\nU8XJT3gEf+W5v3zAYWw13HNzbcGERchiLmNLD7aY+ilk4GLRpd+kfLGyQIzf\r\nZwgxkeC4DwH2Yp8DlN3SO/buxZBEKcpd3I3GkMUyoUJe4BN/S5VIotnFbFNf\r\nYJb9ceCvCEkcgtytGQkmcXODk6IUxRAWlBo=\r\n=pxcO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.8_1676270138016_0.2865249585732563", "host": "s3://npm-registry-packages"}}, "0.17.9": {"name": "@esbuild/freebsd-x64", "version": "0.17.9", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "1d2889efe8e7a824d15175081c7992f7ae8be925", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.9.tgz", "fileCount": 3, "integrity": "sha512-o/qhS0gbIdS0AjgiT0mbdiRIyNVRD31N81c1H7NNM4p6jVkSvScqo0v9eYJ+30mPhJsL26BwSNiuFJzD/SCyuw==", "signatures": [{"sig": "MEYCIQCkSUcNDRLU50b9aa6kyoA5NgWujNsXS9TIs5n02EE6lgIhAIJ1JBpFnXGRnA9kyiohgu1lGJiDiCimkQtHc/DorHIf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8995293, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mAvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowvQ//UW1AuOXGF6m28QrjBR57IGVsdUqudysej0YvjQu2dcDhJib4\r\nmqGeiCGb83nZ7GUoCwXNPofae1Q1ij5sDPKyyt/yLOobnfpPodzVyXtAzWST\r\n2+RSAs5LXmwfiKnSmVAZH8ODm4UPPmIG6cWD3lsyZcdfHoXIeA48cqlnyYx/\r\nzZb5s9r/t3/g7vtTI6Okl4oxQl1e0vqs67C5cvqRaBdqvI0BSqyTwLIwzahD\r\nJktvle9xAVXIcEgG+27TBKo5Dded9cfOp7H4Aq5obd4ezLMcQwMvfIbJVa0q\r\n0DJ/BRCvMVjiSfQ/U4Ofdgi9PhVOMvMJqbaJJlYFq8+0+BFPQATQPCELfag8\r\nL/p/zjM5TxoMQhx421qT7q9G79GRs0uKv5JvTJr5DtnPIcRw/C8WTX8Ck4e0\r\nA2LBuqdJJ97V69SrkPP0j5hPVi73S1clntZhMk3Gxfg0Nwm82DQu/74CpOdj\r\nLJxdHMi8WpjDUQ7WTVcxin36qBS5+MBciWQ9XnvONcp3Qw1Rig73lWWd94pE\r\nwVsZvZWVJV9IGtHb8xbsGo6+CFB04t8v6mMeotQl2jT0Dkp/xv5nzBu9M5Xb\r\nbrgoGEGH6j4K5oes9tQwDkvxBaJrOq1z1mFlnKNOu/gBIm7tf6JUtpcmFktV\r\nCEvEc97XEROzDon1yu+KrHCXjtKFuOPw3Do=\r\n=9r+m\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.9_1676828719026_0.7886565278828306", "host": "s3://npm-registry-packages"}}, "0.17.10": {"name": "@esbuild/freebsd-x64", "version": "0.17.10", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "cd0a1b68bffbcb5b65e65b3fd542e8c7c3edd86b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.10.tgz", "fileCount": 3, "integrity": "sha512-0m0YX1IWSLG9hWh7tZa3kdAugFbZFFx9XrvfpaCMMvrswSTvUZypp0NFKriUurHpBA3xsHVE9Qb/0u2Bbi/otg==", "signatures": [{"sig": "MEUCIQDZTl05gfRy20MDBUIKYakXT4egTqX/00tkZK0fPCvwNwIgXedhh7tkeyjBLyJnEbR6N7fp4+bdfPO4h0dpxbS681k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8995294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87PtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOow/5AcLPAgzIMm1yyt5u5WRemzK5X4p3gMnMZ+k+Ta4UvnjGrFNN\r\ng42XXuSkLRRpxYTEAk9m1GlNl0Afh1wlE5Q24bv8cjG/vIfYqhnxdWbBZeL7\r\n+sZdymG19gjRZEb9RvIwPo9IJL0G8zjnZQelvyIdK2TSje5NNxu9TyVgTqSB\r\nGM19WawsPeMJjrL8Tybo0fHSaOJOCTqVLJToS3CO7zBpPLI0PHPw0xUmDgrG\r\napQeMllaXgoIUXqzK4VLCG6r4c+xHkbz9uO3d+fZxr6oV1HXMsP6N7fP7j2n\r\niBKbHx1Q8ZH0faRj0M61JAExj3RhroVT/347GXGWqa4SmdgDPU7J16j1q1Xw\r\nvat8bk8xBOyBbYWJHVjBHeJrMX6HHuag7jRLdlEf4g/ndvGbg/KADJlSemgZ\r\nr/xZiTI9WyWXVJUgomIfBQGrTsCJEzezliargHKFJII1hgeleDnGjWvFsfZA\r\nEwHQ7SUVJ2Mj3Nm5TGRWQk6XI+j28Qt8G4xy6CexYrjnHutxudBKzW6UfFAz\r\nLy4pHgIFDgj7JtoN4Yy5OtDpQfQ90EvlPAE9bRp3yMPy4BiLvTjL9bPW8yX4\r\n6z8jgSR5dER86ImZTbaBRt3JhauinNpRSewmj+6+VmbV4myiz61PgCe8eHoF\r\nmAPe0hD4PTJZBDNI8K0s1drTWESqXRqt9vE=\r\n=IjFg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.10_1676915693439_0.7855623831970417", "host": "s3://npm-registry-packages"}}, "0.17.11": {"name": "@esbuild/freebsd-x64", "version": "0.17.11", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "e850b58b8fabf8e9ef0e125af3c25229ad2d6c38", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.11.tgz", "fileCount": 3, "integrity": "sha512-iPgenptC8i8pdvkHQvXJFzc1eVMR7W2lBPrTE6GbhR54sLcF42mk3zBOjKPOodezzuAz/KSu8CPyFSjcBMkE9g==", "signatures": [{"sig": "MEUCIAqhUpinU7Qjk/DPoDrwrnc9bpJBnWcCdkwrLpn4rTIYAiEAlBTQyWBak0fchXAaNRKqNTGaF5xcMepQcGWFpIY14Pg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9011678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAndKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8kxAAgqT0LdWV3228uJ2LJPaH7NbmHVJ/nyRXF9sYrIc5gL7xv7LN\r\nL4xmLqu6nGoltJimSoxNhYZsRtb/C5MyFwtMPC4detV+iyc/nAcrZN1vNDg1\r\n4UH6Hs+skkAp/iXHIaG3PILXAhXhhL4ab4jjdnlDjpdMNjLq9ZdqIJUVy1Tk\r\ndT465UhhbYipe2+Pm3FOpeHCjJTxuDzJaeTwMOOwG/vUOR+Bn30frYPY20fL\r\nb+5mFbaHuizwarranvUuN5Ui3aMqjQ1z9SqQw84HYXFu41kXyD8rL2aYTas4\r\nyucfBK54K8HRzNsN9cwvAjcMCaMquHrrx74qZY5Lh+RrnCn7ehskl0SoZkeR\r\nDRYl6VHNruSCBGWj/sSYT9mda4LaxQJBsucAeJAjcx7Q0lgRpKm+Pk2U2LgP\r\nr4ze61ibzOm3dOU96b++ZWeBdCYPPYE41JoX/co6VanjsGMfeLDOgu9Dl2ce\r\nuCiVplsAC6y29HK90A4fCftfKHfpBgzWDi2Pwl6xi9AjyYN6uXI97VyyFgVx\r\nQM8Q0WW2oS6e10kKv1a81fR4NREWyP+AXMchtd7O/MSUBzC2Y1t5w45bUeQM\r\niZGFhZ9/hMG1xL7IDfCAa0b+QgjNieRrYVfuPed8oIpEM11g/zXXhZ+0EUsj\r\nFKj8zY7HsGD724wOAZAoUYlQTANBWHCkHJs=\r\n=Hyzb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.11_1677883209747_0.25007013744260576", "host": "s3://npm-registry-packages"}}, "0.17.12": {"name": "@esbuild/freebsd-x64", "version": "0.17.12", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "0e090190fede307fb4022f671791a50dd5121abd", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.12.tgz", "fileCount": 3, "integrity": "sha512-A0Xg5CZv8MU9xh4a+7NUpi5VHBKh1RaGJKqjxe4KG87X+mTjDE6ZvlJqpWoeJxgfXHT7IMP9tDFu7IZ03OtJAw==", "signatures": [{"sig": "MEYCIQCXAHCunhFW/vTzLl6fRwbH5/2tzRg/BAU6dK2pII/BTgIhAPWVN17diek0DAMsWOyr6qrELXx47KqJy57M0Ggr8j1J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9044446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAWzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8bg//aDb7Py+W0f3ZJIjpUm8FtVJy/wo3nGCL87FFgyoYC4TQTdXK\r\nWp5OEnr8dLraNzVV+Lg5EUU7NrQGZelefj2at4j85p2ZlAXkrieuZayF0Iwt\r\nnOhIwmrMHhKjht5uSF5RHMcBxcpme19STCaGTRX7EBGgfKRBZcGf8SpHkU/4\r\nRpgUD1891j0OoKGnWxGZNpS/XOD+c0ymiOLRMWxBqdeDVomwT7H5c6o+Z7rO\r\nNlq9+DGN6WNo36t1eVJR0EP9wTh/a1eP9UjtOW1Osil9RYfmEyeDagxfbNVv\r\nHYNeXRxLvcPzGiKragvwYqpftNOBW/fkDwbs10q7hjVubB8w9wX5PWDZfer0\r\nSZjxNpU4Dr+tGV9BW461BYOBEcgqWfgyrg9cOLWs6WU4UcjKoyco8nAzGpuR\r\ncAB0XWrmaxQNvGlTkILnhyAGx6PwINiObyC2iOCSxQ8P/nua91ogo9HjNIld\r\ndIlAs+CT+dNjEribUNpuKuQ7+MMUSE7e8r2prO6aUdqjQDwGGzeJ4n4NOlv0\r\nLnZ9sf7sjzTxLEBpNJxdIaR6YQUJLsQilq1e/bRPtxieDaJHgG52ZiZsRchK\r\nfDbFABI7auiBmSRBCaKKikYQP/4OsOy+b//Ezwn2sHHDjYbW2hCkoGhzmARw\r\n7d3/ftH+bJNpKaLUIV/nYipmQ29iQrxu4ks=\r\n=gf8a\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.12_1679033779055_0.2736229975843678", "host": "s3://npm-registry-packages"}}, "0.17.13": {"name": "@esbuild/freebsd-x64", "version": "0.17.13", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "349b22850b18323690f90127117d83f351303a7b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.13.tgz", "fileCount": 3, "integrity": "sha512-/zbkgEO4gY2qGZr9UNAGI38w/FwUY4bx4EC88k9VeiCKNr3ukNgwH/oIgB5Z9/OqpkNLlcS4w9e2d/MIiy5fbw==", "signatures": [{"sig": "MEQCIEnHEbcDuoRdNXTreFHJBqejPyJwCNJdkljz6xrh35rlAiB8HdaXNcrGbQ9h/90EloODk15Mv1q6Bv8Qc3N3P9L+Eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9044446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfKAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFDQ//bqXemUWZN2lSO0dVohCT0ZBr094Hs66O49PgoNgCJT0OfmRI\r\nQOxrEH+9XmBUDfXHHRaPrRFQvdobf/AqjbG5XDzfvO7ZoN/rC4GzvUWwI2gf\r\n/cP91hcBfxKBhP8Pyi6CCG1J4laUPXbtqLxCWgaiqMRc2+erWn6CqufDh8Bh\r\nrGcKn6mCT1xkRONAXm/FjuP1TV970KlcPKGqUCU/s8vviyL3Bt+kS1SrBQxB\r\nHKppGmCS0hqglatJp9pFWltD8hwUbEX3Rtftge22DPcsGZcV0rb9L+ySXFfZ\r\nEtpy/ZTUas/8WFVnd8h30ALJJVdotvBLoDjmVH+uJaqQHzmLEBvXaK+fOkOq\r\nRvH1ic9p8oCcAiU2//UsOb8cdLHWq7g86/cnrlOHu2/cXTpOzR5yWPt3KGfs\r\nFZg0laBM6OhH1f/1Ks3ZoWPo84qruZa0IosjXaDT9/bTxOVXCGLr1R35jlCq\r\nMT4jajE2JkL6BmF6k/EPbTw+i4jV8uZsqPVzbUeFVuGntSH8YWfJ2wK/FckR\r\nQlbUrc8PdJ3aqD6+L1zeZuOa4uSj7MfawngJo/AXC1uXaTg5jsj5uFww4A8t\r\n9lOuIFyUJZY6urs1XNb66CfNydGS6OHUZ6foJu5OJvyEi+ZsqsBbyXa+iHHK\r\nnK2z1WpkW6oSoGCL0zWyxLoBu35Tuhoieuk=\r\n=0zMh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.13_1679684224287_0.3984889722695888", "host": "s3://npm-registry-packages"}}, "0.17.14": {"name": "@esbuild/freebsd-x64", "version": "0.17.14", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a6b6b01954ad8562461cb8a5e40e8a860af69cbe", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.14.tgz", "fileCount": 3, "integrity": "sha512-hd9mPcxfTgJlolrPlcXkQk9BMwNBvNBsVaUe5eNUqXut6weDQH8whcNaKNF2RO8NbpT6GY8rHOK2A9y++s+ehw==", "signatures": [{"sig": "MEYCIQDmR1/n4HtqJrMTNeTGPiqC8c4UhwUlogB21RqUFbYVoQIhALX1LIae7Q8trZfkLVSciYjW33yICJc6pGzFmRbcNMJd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9085406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7JMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodjA/+N7/8vGZ6bANHfzi4pBtzgHJkTtjJfpEcFCe+uK66TV9XUJNx\r\n9We3A78TQKhjsRy38i8ht0fu8CH6od8pfiXRiwzAzifcqwuLkYQFcWLmYCqv\r\nJUtWtLU+cQsSMSCKSYqXNe05ng4/ENNaeAev16cKFxrA8lzQVuzhQTaBmSKU\r\neHbsdvAZ+CvVOpIys5ituIxtcOjZAIqB956U1dyjsY4OqHECETu/3OsYabuH\r\n9UIpggKBTR+brXcICpMxNBmBO5hImtO96bPcN21MLht8pbP1iU0bL5XE6pXe\r\nA0qmaCD6Fl2/rFs4NE+xOICjG5Ug9qWGdu8/pfQuKeef/crZ44uKVA9A6/zo\r\nWvcIn39dK5WoSArUvBlJQXmi1XOTM95n5e8ZUD+KxtohGKEQD/a2wpEIX4x1\r\n1slzsuGN5uWUf0D5qO7SXrFllzE26lEmqpYIMntpDvd37YUDPjx3ibZIcvwP\r\nzUypts71+YRtO6H/kCwUFlykE8QUNIPl/kRP9eRoRnYaxqpyIZ076GtdpG/J\r\naqf+SJMoLDbUVkqhXsYtlVAKURLtDr/pB6p9yDsKvKsfOMyaHGyt6Dtc28Nq\r\nTounzTX8dY4d8T5nlIrvm2/OiFJA7fpmAOzgOc8E/viKfc6fqplHM4meQyF+\r\nL0t944LNaBPe26/HVTPeh55gvKtsDRrVwmg=\r\n=TeEY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.14_1679798860698_0.27107211251218644", "host": "s3://npm-registry-packages"}}, "0.17.15": {"name": "@esbuild/freebsd-x64", "version": "0.17.15", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "b513a48446f96c75fda5bef470e64d342d4379cd", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.15.tgz", "fileCount": 3, "integrity": "sha512-3TWAnnEOdclvb2pnfsTWtdwthPfOz7qAfcwDLcfZyGJwm1SRZIMOeB5FODVhnM93mFSPsHB9b/PmxNNbSnd0RQ==", "signatures": [{"sig": "MEYCIQDVfXGCpWDh6bxwKTrFQjtiID3UXrY4XLMcKPqwUlXC1AIhAPkZViAwwq7VIpCId5rGn3HgU2SL9vtwEVJIOUE6EFD5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9085406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK+nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrawxAAnmA0vqlZSGHopv8s1dLJ5Ws9p3vnhP4qIRtfNorF/0EF3+pP\r\n8f37dcYj01Fh5FwAuEazsoN6El4zFWEk0fUE/j6VIJB0ELDjAoqmZzJ6aAn8\r\nzti8GxPdHvKxkSshrVMNZQ+1py741YmG1MiNEy3U6MTOWKBKeAoYWQeWEJ8v\r\n2AALINUwtDY/38tcsojGnOcK73YcnTy95YHpvJ52JjKilP5mlJzNcoXXZQlz\r\nF4lliLsZFNcklniyciKvbRU/9b5Ur/tvF4m96+hsLAqiZap464JBlZk2FaWI\r\nLpdnxQOURgjcA02giMkuUp2DdV83C4JyaOaQVOqhEbtnBePkPAWxByM4Ex6m\r\nskFg1ZI1d9LoGoKr+/1hs1jS5cJ9phDnw9BJ+lv34X+fj3sI93NbpmTcdOVV\r\nRvdis+dBZbqoSV49wXe1qI350RP9h3zMsMBIsg8Fe6GLCNDka20jw8PXMC/Y\r\nYW9YBk9uLNKUhquImUjJHg29thFtfUQEk8SRh8QERfbWgLQ1ZGcIcyrxAMDK\r\nN665ObqAPGurySYam7im3jXknf45LR00l9Jy3iqLXAi6TLIvc0UC2vBf+4eX\r\nwUAa4LhSu3Yz9XvJ2N1Bzd8c7HL/Q2+kcFU6jsblie4Kt5pbJt7unbSwvTkz\r\nHwOxrnbRYvaStbtaNbqCFdJbjIxnJMiUnR4=\r\n=cpN9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.15_1680388007161_0.24243072023274004", "host": "s3://npm-registry-packages"}}, "0.17.16": {"name": "@esbuild/freebsd-x64", "version": "0.17.16", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "84af4430a07730b50bbc945a90cf7036c1853b76", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.16.tgz", "fileCount": 3, "integrity": "sha512-rHV6zNWW1tjgsu0dKQTX9L0ByiJHHLvQKrWtnz8r0YYJI27FU3Xu48gpK2IBj1uCSYhJ+pEk6Y0Um7U3rIvV8g==", "signatures": [{"sig": "MEQCIGsQTl5/JgVU1E4Yg3E98k5nr8FC7O4c2nq5VgU3QRkWAiAqGM1sCulAoSNDRlVORghPLVjLDVz5Q9Pjle2ziirD0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9085406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5HyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpobw//c9r/lZtgymdEQ2RzsWjhmbRKhLyJBZMPgJWoPciZIUYO8eVl\r\nUDLJ7+4oWTLoO9Hk7zCsT6HQpIPSMBBDi08xUsCEY0iJDHkEkvnziQ8fn+xS\r\nP2g3msK4yDg0N+Ou9WjEDELfY0wERPa5R/F57sKSJv6qLWNk3YkB0KADOBMg\r\nj4X9aTWVNwlXPPDxDlFOI7p7nb4mUPeSfwpm4jT03Ah4vwhyN7wDhxDN9Wt9\r\n917hlBVO0LfF77NMzLOUbDQ19vdqsZ4cNbnrR2mJ6+obPZjUfXe2vGa/GtsN\r\n7IWO8nh7CJG5RN0jeeRfKILfCXN4mdHFGVsu8w6D98e2Y2lLqLuK58Houjob\r\nVq6uvmeVXN9iBYmhEm2RLqBYASHlc8BDyVICN5cHPECd2VAfVtUSAg+jpNAD\r\n+iQ0B8MH2/0J0O76EWNo3EkKwlYOhX3C1+sBWHzV8fVONcobpQeUCbeUTDH3\r\ntUnjiVvlQMnhpdK5pGsyz9wMkS9gRaz/jYiFfzcm6OARa3fX7nrzwjPOJXKv\r\nw4GjMP1ed5HfvOg8EhV1poDRGd/ktpECwTBA+TBJRpNMVjN+0ybWFbJDl0OI\r\n6G5/xvp4ZNbeJsBDpsmedT16AsrB1odF0A4gPtHOjhur2+aNthMDrraW5Oax\r\nQNXYynm+1cyMJ1MnVmiPDsJTfoGKGa9b4RI=\r\n=RmwV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.16_1681101298582_0.6869392817770115", "host": "s3://npm-registry-packages"}}, "0.17.17": {"name": "@esbuild/freebsd-x64", "version": "0.17.17", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "4dd99acbaaba00949d509e7c144b1b6ef9e1815b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.17.tgz", "fileCount": 3, "integrity": "sha512-lQRS+4sW5S3P1sv0z2Ym807qMDfkmdhUYX30GRBURtLTrJOPDpoU0kI6pVz1hz3U0+YQ0tXGS9YWveQjUewAJw==", "signatures": [{"sig": "MEQCIH2JM9UNX+L+mP+ncKbaAV25uYIpeOf5B9OhsuT0SbUdAiBUbsIml40RYOYiVh7qnLsU8kC4srSCgh7bH4W168X31A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9093598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGdbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJgg/+LLMSkZ1ZG6Flgdm3VDVMcCmv/zKuoKgmiT6O4RMx2PNWnfw/\r\nS0SIFHACitM1L+8l4v0ZPTRAPUE0Jy6+d5R277mEyiiRAmYFCbQy52LTrqSm\r\ne2c4ETH42CXXtZr1Z159KrX4NiL7DCSWNgwuCu5Q0GwhhV7nIB3AaR24hUrg\r\nkUnfKnCdgcy5obebiQMBeKFAf1A5djh7aMt7hsSfn9Lopwb+tPq2ON3nQMgr\r\n2BN9ukyBUOemCq93z8E4YiOnxa6M6wGqPxXNGR/ypB4mfk2zCwW1x0GFtkGN\r\nuLQufKARJwo0D9IfJPh+4hRIo2o203NvxTKxnSaiJn55JkU9ZS154ifLG04z\r\nFZ3mCDdI2hQ8IJCmp1ejTlmIemWEpMBt98KpmP+PDBBOgScs5vLKPPfKs5gY\r\nsn1Wv06K5nREHMqyr3IhiFU5zBMjgjkynGGk66esXKnBBKK33OOcmPm3J2XI\r\nR4Bxe0YwHfYp1nvA/10NgFZzSyC1xOKUQzUwbm+ZjQVyqFBxHGFCvbqx85LQ\r\nNxvmaH73cHIx4l7LYQuUwGc+qRNihBGx1UBxyFHXuTTIIGPU9E5s5pXodTiw\r\nx9iqAygDePVoUeAS/9KBzQxrXSVBRKVZ5ppIKQpICXFd6qT9VFZBNhj8YxsI\r\nGHy366fM4VjUwj6AiexTvWc9lvA5B1vxAnA=\r\n=h0d3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.17_1681680218756_0.4159180495987096", "host": "s3://npm-registry-packages"}}, "0.17.18": {"name": "@esbuild/freebsd-x64", "version": "0.17.18", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "af59e0e03fcf7f221b34d4c5ab14094862c9c864", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.18.tgz", "fileCount": 3, "integrity": "sha512-FQFbRtTaEi8ZBi/A6kxOC0V0E9B/97vPdYjY9NdawyLd4Qk5VD5g2pbWN2VR1c0xhzcJm74HWpObPszWC+qTew==", "signatures": [{"sig": "MEUCIFpYPI/VjUc3j78cmIeGgycA4sG2Rnu0dEQPmljL0zThAiEAvS2hL3pe9aL6lD928sIV8lTCQF2LOVMPxWXu0Wmfl/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9093598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREZzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOqw//bpxLPqHzr/CfeJGLzOzFMhgaEQ+095cZEioSzZQkbLtgCQnJ\r\nWd+e+Jc1tGCTrwZcIbYqJfeNjR0ZPnbI2TRxlywYL8gbK/8Ul8WQGH+T0G+v\r\nSaHuRbihNgNd9IMxvdfwauUr0R1pejqWeC4mjuiQKDd86g1dZCjnc5dfCQhG\r\nE18hNLE81Q98a2OkFpCVa9ZWH6TqjJFPXjwEEZp1LHKus91i1nWHe7MM0Bzo\r\nC+NLLuJklwDYlUbzgx1nCHaNbweCEsgGIB6AkHgFHhkn0qu2B9meW8uPAjo3\r\nGaP/P8EJ+7gzZWIBBAD8Z4krhpXRYl2yPoZ97FrOaUTKuGht4aL57EymFy06\r\nbGRv9Y9ZxPgz97aPDnu5sndX+7Pa50dRwOIobOP/gbUT7bNwgFuDYlt61lEE\r\nYv0MopmJWi9ykxSF2vNY9eQlb3+dGpRFxLXSU1Ci2UlX3XcQiElB7/foFO3D\r\n4xzELsM+QEJ2KSSFsCLAq5vIB8karGlq+uJJahHWQ9g3T7ziO4QGNQt/Qqzc\r\np5anEtySJAmr46L755LUeheGbcqceR3fk/talSZbRoiume+J5wXpline+FjT\r\n8clQwr6nNhrn4f5tC8WlCG8GEbMWCQgDjsko3Vw05pazuhOOXqHhX6PfjLEI\r\n3FeGlitT4AxPpgyBExFH2S8qtD6jVKo5ZPE=\r\n=kbnM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.18_1682196082835_0.0739949194021392", "host": "s3://npm-registry-packages"}}, "0.17.19": {"name": "@esbuild/freebsd-x64", "version": "0.17.19", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.17.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "0769456eee2a08b8d925d7c00b79e861cb3162e4", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.17.19.tgz", "fileCount": 3, "integrity": "sha512-4lu+n8Wk0XlajEhbEffdy2xy53dpR06SlzvhGByyg36qJw6Kpfk7cp45DR/62aPH9mtJRmIyrXAS5UWBrJT6TQ==", "signatures": [{"sig": "MEQCID9jQ4F2tT3C1s6/KqnBU6eU/diy0h5aLeUuacHgtFU/AiARQ3T3cD+1ZC6ZGahR9gepDWBKiJe+D2vaJHHHq61Ghw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9097694}, "engines": {"node": ">=12"}, "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.17.19_1683936386611_0.9789711648085315", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@esbuild/freebsd-x64", "version": "0.18.0", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c6a1bd133747de0c687abb137e17a197a2cced03", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.0.tgz", "fileCount": 3, "integrity": "sha512-GdkJAB3ZBiYnie9iFO9v/CM4ko0dm5SYkUs97lBKNLHw9mo4H9IXwGNKtUztisEsmUP0IWfEi4YTWOJF3DIO4w==", "signatures": [{"sig": "MEUCIQDuoMUpC0Mh6kLmAQhRNEpkXav+KIjjFGpEbjZUIcgXcQIgfr6GJohqFq+E+s7rAF96xKYjRkl8ZPjcawDXFWh6+Oo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9105885}, "engines": {"node": ">=12"}, "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.0_1686345857628_0.37221173529411145", "host": "s3://npm-registry-packages"}}, "0.18.1": {"name": "@esbuild/freebsd-x64", "version": "0.18.1", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "3ab647a9f1557ce8f099e4910854000cd831989a", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.1.tgz", "fileCount": 3, "integrity": "sha512-w03zjxyg51qktv0JKsV+AbY3uSb1Awifs8IkKQSUXdP3sdPxxmPzZLrlJ1+LjKZRiSa4yP/Haayi/hriNVoIdQ==", "signatures": [{"sig": "MEYCIQCabBa6aVaKMLq9TDQiGiVEME9YQHI4R9KFUagG0ckSLQIhANhDRxHoEFmw5Mq5Vos0eh3RC4HRGz80aQcIL2sIKEmP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9114077}, "engines": {"node": ">=12"}, "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.1_1686545503049_0.9167530839761577", "host": "s3://npm-registry-packages"}}, "0.18.2": {"name": "@esbuild/freebsd-x64", "version": "0.18.2", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "dce7efd96172d8a2e728296b041a04be7f44f26e", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.2.tgz", "fileCount": 3, "integrity": "sha512-ZSR9On/rXoYuAtrXo5hYKy7OuZwKZyFh2rr6L3TX4UeR1tWLf84aLyAFt7e0tlRbh4zNgqFx+ePWmsSHw7L9Bw==", "signatures": [{"sig": "MEYCIQCaxUo94psZiJ76T/vqmvAsvkLJRR5EZ1jl+dIHW0xKNwIhAJVfrnKxq+OpaPf6nOmUpAH8fEgMmRq8x4En1sRGA4zg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9122269}, "engines": {"node": ">=12"}, "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.2_1686624027654_0.9349787227432143", "host": "s3://npm-registry-packages"}}, "0.18.3": {"name": "@esbuild/freebsd-x64", "version": "0.18.3", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c1a268389a4e8147b55bad8325521563202fa3b9", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.3.tgz", "fileCount": 3, "integrity": "sha512-xyITfrF0G3l1gwR79hvNCCWKQ/16uK14xNNPFgzjbIqF4EpBvhO6l3jrWxXFUW51z6dVIl2Szh3x3uIbBWzH1Q==", "signatures": [{"sig": "MEUCICR6NI4C4DO1UyO47XYltDOBFtFCu75HkCHCV6v8B4dxAiEAk84zoj8FA2o/VazkzgbaAgI3BclZMFkaNH+H5kbt3Oo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9122269}, "engines": {"node": ">=12"}, "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.3_1686831650915_0.7026703854673726", "host": "s3://npm-registry-packages"}}, "0.18.4": {"name": "@esbuild/freebsd-x64", "version": "0.18.4", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "7c942bc3ca10ed140764a58dcc04e60e5bdca11a", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.4.tgz", "fileCount": 3, "integrity": "sha512-1bHfgMz/cNMjbpsYxjVgMJ1iwKq+NdDPlACBrWULD7ZdFmBQrhMicMaKb5CdmdVyvIwXmasOuF4r6Iq574kUTA==", "signatures": [{"sig": "MEUCICAVCBOtQUl/o7fx6Cdla0drlD93+K0vcDW44skth5iuAiEAyuOFIJqlxCj2Inare/RPwZdHVsZ1wBVc3IrFWrhdhjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9126365}, "engines": {"node": ">=12"}, "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.4_1686929896030_0.6295392772188135", "host": "s3://npm-registry-packages"}}, "0.18.5": {"name": "@esbuild/freebsd-x64", "version": "0.18.5", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "5374704c357d7d60b8af674c2e61adea383b1b02", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.5.tgz", "fileCount": 3, "integrity": "sha512-GNTMSJ55gl7Tf5VUqVRkMJhRGzH6vI9vFBfZCj4Zjm7RgfXCWxLnTyjMgZZKT8pOzW40KD2KlrGbqwnnJWyGWw==", "signatures": [{"sig": "MEQCIGp+AzMjX+aAGpd9oz9JSCap6+Zy9dig1tl9972Kc0DlAiB6vCEt+nQdVUMw3x67BA6aittP59A3PYYNaXwoPt2KBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9146845}, "engines": {"node": ">=12"}, "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.5_1687222351050_0.8739987974249692", "host": "s3://npm-registry-packages"}}, "0.18.6": {"name": "@esbuild/freebsd-x64", "version": "0.18.6", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "59195dbc35a1c73acf9f3804f4245702fc0669de", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.6.tgz", "fileCount": 3, "integrity": "sha512-HbDLlkDZqUMBQaiday0pJzB6/8Xx/10dI3xRebJBReOEeDSeS+7GzTtW9h8ZnfB7/wBCqvtAjGtWQLTNPbR2+g==", "signatures": [{"sig": "MEUCIC6pUa8re+2FsiCadpz1aT4E1fp8FTvKMewkKHOmkm9aAiEAwg8ZBw3MyV3jQj1iqCbs+2i18YtfCG7PHeR1cIYdBaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9155037}, "engines": {"node": ">=12"}, "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.6_1687303490116_0.4853135382509679", "host": "s3://npm-registry-packages"}}, "0.18.7": {"name": "@esbuild/freebsd-x64", "version": "0.18.7", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "8eb59bf0015e4abd5ce3fc16094eb11174167b83", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.7.tgz", "fileCount": 3, "integrity": "sha512-rbfjbgSvzWRjQMKIntogK1d2oIAiA/ZVayXfK1WjcIIMQYLg74sAoT8CZBj30+bwn13YR0t6lgIxA1mJaS2Lhw==", "signatures": [{"sig": "MEQCIAkveOjB0pfdbbqQAogJ7h7CzmApkHMRBn19z10Slvj5AiA2PhZaCkbTa/bDp4ftT7b9Ltse/b4mIpfBJHx0oIBmHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9195997}, "engines": {"node": ">=12"}, "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.7_1687574782133_0.8005108288518301", "host": "s3://npm-registry-packages"}}, "0.18.8": {"name": "@esbuild/freebsd-x64", "version": "0.18.8", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "764ab393d3df7c34815508ce12eb2eb57b4f0a48", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.8.tgz", "fileCount": 3, "integrity": "sha512-j0dgVXUyInggnvEgFGS7FXTQncRwAmHkgAy8YE52kOsozkimpapE3Kuuwb6MXbhnqLvJevaFgGSAlseDlkXAlg==", "signatures": [{"sig": "MEUCIQCy7m1PnllWddt2V0JtkfktKeXOymZmbxqpaYkUYGxn8AIgSYp5bZgsXY6jmJVgJ4GactPnnRxaTwBQGUhVAyXz2x4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9200093}, "engines": {"node": ">=12"}, "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.8_1687663149075_0.1058725563206595", "host": "s3://npm-registry-packages"}}, "0.18.9": {"name": "@esbuild/freebsd-x64", "version": "0.18.9", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "87a33b75e4be4c7b3d2cf15aef0cd1e1b0d24043", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.9.tgz", "fileCount": 3, "integrity": "sha512-BsOYio/4p/6RWG+sDQXVYet8qQ0bB91rfO0YNk5s0HlqE9vEth3Yi1jFNi4v7bUA4vQDWWoybpA/9NTz1sM88A==", "signatures": [{"sig": "MEQCIAzWrzp/1CKOQTjcNJr/OcwxzGXy0HVFVj0myvk1lyAxAiBuAFO8N4GYVXi6U9DZjS+INT8w+q7SfFIWOdYfYT6moA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9216477}, "engines": {"node": ">=12"}, "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.9_1687757270298_0.9851365903757257", "host": "s3://npm-registry-packages"}}, "0.18.10": {"name": "@esbuild/freebsd-x64", "version": "0.18.10", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "e96ba0a380ec38055fd4654dd7a96c90e32e4bbb", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.10.tgz", "fileCount": 3, "integrity": "sha512-Hi/ycUkS6KTw+U9G5PK5NoK7CZboicaKUSVs0FSiPNtuCTzK6HNM4DIgniH7hFaeuszDS9T4dhAHWiLSt/Y5Ng==", "signatures": [{"sig": "MEUCICU4H1EucNhWyoFF9LHMIisy+8N+Zh618qPK+EXLKHN6AiEAtN3PWGBnvoKRUxcXs/K7dJhIxHDVDZC3vgf29ulena8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9216478}, "engines": {"node": ">=12"}, "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.10_1687814424555_0.011408970728751244", "host": "s3://npm-registry-packages"}}, "0.18.11": {"name": "@esbuild/freebsd-x64", "version": "0.18.11", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c5de1199f70e1f97d5c8fca51afa9bf9a2af5969", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.11.tgz", "fileCount": 3, "integrity": "sha512-XtuPrEfBj/YYYnAAB7KcorzzpGTvOr/dTtXPGesRfmflqhA4LMF0Gh/n5+a9JBzPuJ+CGk17CA++Hmr1F/gI0Q==", "signatures": [{"sig": "MEUCIGgavtptKKZXefOgp+nzP/aIxBFs0+XsxBTQb0Nmg3PyAiEAsUTgigRmEf9NmF0E/kDt5NP2rI91xUKzijUDnO4doEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9216478}, "engines": {"node": ">=12"}, "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.11_1688191430122_0.39893529920113724", "host": "s3://npm-registry-packages"}}, "0.18.12": {"name": "@esbuild/freebsd-x64", "version": "0.18.12", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "367ebe738a43caced16564a4d2f05d24880b767c", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.12.tgz", "fileCount": 3, "integrity": "sha512-zK0b9a1/0wZY+6FdOS3BpZcPc1kcx2G5yxxfEJtEUzVxI6n/FrC2Phsxj/YblPuBchhBZ/1wwn7AyEBUyNSa6g==", "signatures": [{"sig": "MEUCIEyZz+h6Juv1upS6bjEQHEOcXWctdr4ARidZmdhF8j/1AiEApmZ3euj3B3ixCrFkMMBQom0T7qp7keamWVnRMdlL/GU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9216478}, "engines": {"node": ">=12"}, "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.12_1689212042993_0.26981786230009", "host": "s3://npm-registry-packages"}}, "0.18.13": {"name": "@esbuild/freebsd-x64", "version": "0.18.13", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "0b1dfde3ff1b18f03f71e460f91dc463e6a23903", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.13.tgz", "fileCount": 3, "integrity": "sha512-pGzWWZJBInhIgdEwzn8VHUBang8UvFKsvjDkeJ2oyY5gZtAM6BaxK0QLCuZY+qoj/nx/lIaItH425rm/hloETA==", "signatures": [{"sig": "MEQCIE1f6XUXBRJq7RbnHJ2E34hIijH2eiqnNvVmltKTAhPSAiBCificJZ7VoQz7jkvKFWHFnMArmSCF5cikM2ZQJG+rJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9216478}, "engines": {"node": ">=12"}, "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.13_1689388632497_0.6128228993915215", "host": "s3://npm-registry-packages"}}, "0.18.14": {"name": "@esbuild/freebsd-x64", "version": "0.18.14", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "effaa4c5d7bab695b5e6fae459eaf49121fbc7c3", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.14.tgz", "fileCount": 3, "integrity": "sha512-ha4BX+S6CZG4BoH9tOZTrFIYC1DH13UTCRHzFc3GWX74nz3h/N6MPF3tuR3XlsNjMFUazGgm35MPW5tHkn2lzQ==", "signatures": [{"sig": "MEUCIQCIOHNaNwuZ+2rlFeqtCFvyuaRvOD5vAsu24F7f+j1MUgIgNgkzb9dCK5uqKu9zPttvkHKKh+H/3gXYscCwqY1DPBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9253342}, "engines": {"node": ">=12"}, "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.14_1689656416183_0.287429331364665", "host": "s3://npm-registry-packages"}}, "0.18.15": {"name": "@esbuild/freebsd-x64", "version": "0.18.15", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "09694fc601dd8d3263a1075977ee7d3488514ef8", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.15.tgz", "fileCount": 3, "integrity": "sha512-62jX5n30VzgrjAjOk5orYeHFq6sqjvsIj1QesXvn5OZtdt5Gdj0vUNJy9NIpjfdNdqr76jjtzBJKf+h2uzYuTQ==", "signatures": [{"sig": "MEYCIQDPfDC3gzrXJqvi9BszGBMGNhYmfiWYnQ1NJyd9YJENjgIhAP5RIkZFuHvFsKatYRXzV0EWzFt6RbeeWcJndDkRHvpE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9265630}, "engines": {"node": ">=12"}, "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.15_1689857591813_0.7915997265879828", "host": "s3://npm-registry-packages"}}, "0.18.16": {"name": "@esbuild/freebsd-x64", "version": "0.18.16", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "24f73956436495cc7a5a4bf06be6b661aea6a2c1", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.16.tgz", "fileCount": 3, "integrity": "sha512-YM98f+PeNXF3GbxIJlUsj+McUWG1irguBHkszCIwfr3BXtXZsXo0vqybjUDFfu9a8Wr7uUD/YSmHib+EeGAFlg==", "signatures": [{"sig": "MEQCIFe2JCJpOqZZb4An1wD7x2+YT3aXsEww75wKnhL2iy3JAiAcbzf/bozJGQxlQHqXxwhOYQnIUg5KBSKhNAGdnV5LCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9265630}, "engines": {"node": ">=12"}, "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.16_1690087677245_0.4011257835333579", "host": "s3://npm-registry-packages"}}, "0.18.17": {"name": "@esbuild/freebsd-x64", "version": "0.18.17", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "16b6e90ba26ecc865eab71c56696258ec7f5d8bf", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.17.tgz", "fileCount": 3, "integrity": "sha512-sOxEvR8d7V7Kw8QqzxWc7bFfnWnGdaFBut1dRUYtu+EIRXefBc/eIsiUiShnW0hM3FmQ5Zf27suDuHsKgZ5QrA==", "signatures": [{"sig": "MEYCIQCTlbaUjO1EyXMOd5CovK4WChnIInDAr+misZ4iMBXKWwIhAMf5VERp7QiVBNezJg1+ag+1iIEKIJxL4WfKr96jkIpP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9286110}, "engines": {"node": ">=12"}, "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.17_1690335650606_0.5514780028635891", "host": "s3://npm-registry-packages"}}, "0.18.18": {"name": "@esbuild/freebsd-x64", "version": "0.18.18", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "f00e54a3b65824ac3c749173bec9cd56d95fe73b", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.18.tgz", "fileCount": 3, "integrity": "sha512-n823w35wm0ZOobbuE//0sJjuz1Qj619+AwjgOcAJMN2pomZhH9BONCtn+KlfrmM/NWZ+27yB/eGVFzUIWLeh3w==", "signatures": [{"sig": "MEQCICAdyTP9P/tl7JD+6xOlUNXdQyXZx2YA2/CxcrZHbxrNAiBwvvBw6Iy+FwAdv6TR1BPe7jOgB1VkLEQ0ysmZvb2T7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9306590}, "engines": {"node": ">=12"}, "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.18_1691255185410_0.15433705029272526", "host": "s3://npm-registry-packages"}}, "0.18.19": {"name": "@esbuild/freebsd-x64", "version": "0.18.19", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "ef6f5a85c1bb029fb0076da5b223e50b353e615c", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.19.tgz", "fileCount": 3, "integrity": "sha512-hBxgRlG42+W+j/1/cvlnSa+3+OBKeDCyO7OG2ICya1YJaSCYfSpuG30KfOnQHI7Ytgu4bRqCgrYXxQEzy0zM5Q==", "signatures": [{"sig": "MEUCIFU3fq0mu74BB5mpgzxM7QWdr7GyFY7BC4wXP8nNuxPJAiEArC3EKido27xlucrPQ66OKxti+JhPWRCpfOI/EqKnpoM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9339358}, "engines": {"node": ">=12"}, "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.19_1691376674365_0.06455130892600947", "host": "s3://npm-registry-packages"}}, "0.18.20": {"name": "@esbuild/freebsd-x64", "version": "0.18.20", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.18.20", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "c1eb2bff03915f87c29cece4c1a7fa1f423b066e", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.18.20.tgz", "fileCount": 3, "integrity": "sha512-tgWRPPuQsd3RmBZwarGVHZQvtzfEBOreNuxEMKFcd5DaDn2PbBxfwLcj4+aenoh7ctXcbXmOQIn8HI6mCSw5MQ==", "signatures": [{"sig": "MEUCIDHGpTRC/GWQDFWCRJ/h97pywE1tXJ1eQMfqoSyEyrIrAiEA+jzNfkaA+zY9aG5f+NqD1e54VhXZVZkAADb+a+To4CE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9351646}, "engines": {"node": ">=12"}, "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.18.20_1691468092511_0.38298274968424884", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@esbuild/freebsd-x64", "version": "0.19.0", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "14d497e9e858fba2bb9b16130602b7f5944bc09c", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.0.tgz", "fileCount": 3, "integrity": "sha512-NMdBSSdgwHCqCsucU5k1xflIIRU0qi1QZnM6+vdGy5fvxm1c8rKh50VzsWsIVTFUG3l91AtRxVwoz3Lcvy3I5w==", "signatures": [{"sig": "MEUCIQD1qHt6vFK4SUW0XnnlQRo+DbhcW0M9F8Z7qRfWxUf9yAIgQCO8BCQrp9eUpFnFpft8KpAcpcd2PsFL1pZs6hp/vFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9396701}, "engines": {"node": ">=12"}, "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.0_1691509942984_0.4263449525048275", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@esbuild/freebsd-x64", "version": "0.19.1", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "f7658f988c88d6b63777678b89fb8137a07a9df6", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.1.tgz", "fileCount": 3, "integrity": "sha512-DrFMGLF0/aAcZgwhtZr1cby7aHlalpFjLCe5CiI8mm0Kqhhc8gyNZKreaZzvir8CQe0H17p9xx6M9ben5R3r0g==", "signatures": [{"sig": "MEQCICTk7pfFRKpEZKOcx3cx8+FFpU0Z1bg1FGAa2I+anxfAAiAbP5+58c1bBwU/w5kUNtWyzZm+94djK0Ky7zvBG6bQIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9413085}, "engines": {"node": ">=12"}, "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.1_1691769447219_0.11401217698137311", "host": "s3://npm-registry-packages"}}, "0.19.2": {"name": "@esbuild/freebsd-x64", "version": "0.19.2", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "01b96604f2540db023c73809bb8ae6cd1692d6f3", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.2.tgz", "fileCount": 3, "integrity": "sha512-nSO5uZT2clM6hosjWHAsS15hLrwCvIWx+b2e3lZ3MwbYSaXwvfO528OF+dLjas1g3bZonciivI8qKR/Hm7IWGw==", "signatures": [{"sig": "MEQCIG4yiV3fzsP66H1I00AdM4xuzGNY/o2svGkoknr0Jv6iAiAxELLYlpWCgof+nXcJtyzavLm6YLBw5Fcs9CMvALkYQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9413085}, "engines": {"node": ">=12"}, "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.2_1691978296565_0.30412266191249016", "host": "s3://npm-registry-packages"}}, "0.19.3": {"name": "@esbuild/freebsd-x64", "version": "0.19.3", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "3f283099810ef1b8468cd1a9400c042e3f12e2a7", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.3.tgz", "fileCount": 3, "integrity": "sha512-nXesBZ2Ad1qL+Rm3crN7NmEVJ5uvfLFPLJev3x1j3feCQXfAhoYrojC681RhpdOph8NsvKBBwpYZHR7W0ifTTA==", "signatures": [{"sig": "MEQCIA4RfcEPY5g0JsbElFhp1+OqX/zK2SLkpmbCuopPUNKpAiB62rExahvCqvcLtyX6YXIH01XCvJgqC6VOXAX323wSVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9421277}, "engines": {"node": ">=12"}, "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.3_1694653939544_0.14832258109301266", "host": "s3://npm-registry-packages"}}, "0.19.4": {"name": "@esbuild/freebsd-x64", "version": "0.19.4", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "873edc0f73e83a82432460ea59bf568c1e90b268", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.4.tgz", "fileCount": 3, "integrity": "sha512-3sRbQ6W5kAiVQRBWREGJNd1YE7OgzS0AmOGjDmX/qZZecq8NFlQsQH0IfXjjmD0XtUYqr64e0EKNFjMUlPL3Cw==", "signatures": [{"sig": "MEUCIAN7a+bTkCm7ytvRQbM66uC0Y5XJHFd8xbeolvoX8JwqAiEAjVTuNNgnciDONeOY3waTR0cK3cxdLSWDVkNv9j7ya2g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9421277}, "engines": {"node": ">=12"}, "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.4_1695865607369_0.2388210969030855", "host": "s3://npm-registry-packages"}}, "0.19.5": {"name": "@esbuild/freebsd-x64", "version": "0.19.5", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "288f7358a3bb15d99e73c65c9adaa3dabb497432", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.5.tgz", "fileCount": 3, "integrity": "sha512-1CCwDHnSSoA0HNwdfoNY0jLfJpd7ygaLAp5EHFos3VWJCRX9DMwWODf96s9TSse39Br7oOTLryRVmBoFwXbuuQ==", "signatures": [{"sig": "MEYCIQCAiGMB6LNfX1EEitcXzonKRlxy/nihQ1mzFN8R8ku2sgIhANZm4bGGkiiyg7M2NZ3AwXpkApiFxc1fJXeZRi1YuGCB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9429469}, "engines": {"node": ">=12"}, "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.5_1697519428398_0.37851658968808466", "host": "s3://npm-registry-packages"}}, "0.19.6": {"name": "@esbuild/freebsd-x64", "version": "0.19.6", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "ae0230860e27df204a616671e028ff8fdffa009a", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.6.tgz", "fileCount": 3, "integrity": "sha512-hn9qvkjHSIB5Z9JgCCjED6YYVGCNpqB7dEGavBdG6EjBD8S/UcNUIlGcB35NCkMETkdYwfZSvD9VoDJX6VeUVA==", "signatures": [{"sig": "MEQCICffBTQHYAM0JHyaEYNgVd9AdzJu/1TVyaZUG78NYgGeAiAZlVRSh40KNozmmIhfcHi7q201PT3FPqwa+cq/uAVeyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9454045}, "engines": {"node": ">=12"}, "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.6_1700377888666_0.14016761005213652", "host": "s3://npm-registry-packages"}}, "0.19.7": {"name": "@esbuild/freebsd-x64", "version": "0.19.7", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "ec3708488625d70e565968ceea1355e7c8613865", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.7.tgz", "fileCount": 3, "integrity": "sha512-CdXOxIbIzPJmJhrpmJTLx+o35NoiKBIgOvmvT+jeSadYiWJn0vFKsl+0bSG/5lwjNHoIDEyMYc/GAPR9jxusTA==", "signatures": [{"sig": "MEQCIDc7JDRsA5MjjJEvvBL3eRVHVyQroLpg06qJ8jDBFpWnAiAF00wPySJwJRk9QY2Se5NhES/DzDEtYTrUqmIDPlbOqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9490909}, "engines": {"node": ">=12"}, "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.7_1700528451606_0.21398236843775087", "host": "s3://npm-registry-packages"}}, "0.19.8": {"name": "@esbuild/freebsd-x64", "version": "0.19.8", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "abac03e1c4c7c75ee8add6d76ec592f46dbb39e3", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.8.tgz", "fileCount": 3, "integrity": "sha512-ICvZyOplIjmmhjd6mxi+zxSdpPTKFfyPPQMQTK/w+8eNK6WV01AjIztJALDtwNNfFhfZLux0tZLC+U9nSyA5Zg==", "signatures": [{"sig": "MEUCIDUJpQyIdavydI30/ldLXECFeEcCZBgr7cwzz8VrUmdsAiEAsb/Tv4jza/571ngpNJNASTdM6W5jYP+BJZvS2npT5Qw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9490909}, "engines": {"node": ">=12"}, "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.8_1701040074696_0.4206246603164834", "host": "s3://npm-registry-packages"}}, "0.19.9": {"name": "@esbuild/freebsd-x64", "version": "0.19.9", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a3db52595be65360eae4de1d1fa3c1afd942e1e4", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.9.tgz", "fileCount": 3, "integrity": "sha512-WMLgWAtkdTbTu1AWacY7uoj/YtHthgqrqhf1OaEWnZb7PQgpt8eaA/F3LkV0E6K/Lc0cUr/uaVP/49iE4M4asA==", "signatures": [{"sig": "MEQCIBMUBx3l1opLjiMBJz7k2eHiPMmSFEv9/ziD19tVyzxlAiAUkUu1B63tmmz6fmVFjD/pqLt8ZhYGX0C/jM97kXvVkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9601501}, "engines": {"node": ">=12"}, "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.9_1702184962032_0.8515982372837181", "host": "s3://npm-registry-packages"}}, "0.19.10": {"name": "@esbuild/freebsd-x64", "version": "0.19.10", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "8ad9e5ca9786ca3f1ef1411bfd10b08dcd9d4cef", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.10.tgz", "fileCount": 3, "integrity": "sha512-G5<PERSON>PspryHu1T3uX8WiOEUa6q6OlQh6gNl4CO4Iw5PS+Kg5bVggVFehzXBJY6X6RSOMS8iXDv2330VzaObm4Ag==", "signatures": [{"sig": "MEQCIHoHe6cLMDorfz9Hy6TSQdRuunXsSYylpmHnnP/4TESMAiA2fxzrbIwAPmaBh5/yeepetaLQFkYSfT70B1MvOUqF/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9601502}, "engines": {"node": ">=12"}, "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.10_1702945287513_0.5994425128192455", "host": "s3://npm-registry-packages"}}, "0.19.11": {"name": "@esbuild/freebsd-x64", "version": "0.19.11", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "7bdcc1917409178257ca6a1a27fe06e797ec18a2", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.11.tgz", "fileCount": 3, "integrity": "sha512-JkUqn44AffGXitVI6/AbQdoYAq0TEullFdqcMY/PCUZ36xJ9ZJRtQabzMA+Vi7r78+25ZIBosLTOKnUXBSi1Kw==", "signatures": [{"sig": "MEQCIHqL4aPLcY51+5aDyBqibXiEHm3HaZtTdI4Nhl/0UsKxAiBJYDArzycJdMq1o9rv9khoRHFSh/gfZ4S1tMDm41D0KQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9609694}, "engines": {"node": ">=12"}, "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.11_1703881905881_0.5710998025008671", "host": "s3://npm-registry-packages"}}, "0.19.12": {"name": "@esbuild/freebsd-x64", "version": "0.19.12", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.19.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "37a693553d42ff77cd7126764b535fb6cc28a11c", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.19.12.tgz", "fileCount": 3, "integrity": "sha512-EYoXZ4d8xtBoVN7CEwWY2IN4ho76xjYXqSXMNccFSx2lgqOG/1TBPW0yPx1bJZk94qu3tX0fycJeeQsKovA8gg==", "signatures": [{"sig": "MEYCIQC+49yxPpl55KfKiqmRurZ9gF8+wigtL8q7hQGecJ2s4AIhAJP+GdHhv0AU1K+mq/lycxEDOg5pU10xaT8h9vvodAHo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9605598}, "engines": {"node": ">=12"}, "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.19.12_1706031617535_0.8611157914376655", "host": "s3://npm-registry-packages"}}, "0.20.0": {"name": "@esbuild/freebsd-x64", "version": "0.20.0", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.20.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "3f9ce53344af2f08d178551cd475629147324a83", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.20.0.tgz", "fileCount": 3, "integrity": "sha512-uG8B0WSepMRsBNVXAQcHf9+Ko/Tr+XqmK7Ptel9HVmnykupXdS4J7ovSQUIi0tQGIndhbqWLaIL/qO/cWhXKyQ==", "signatures": [{"sig": "MEYCIQDyoaZcdPlpGe2Y8/MKvy3kC4ZN122Y9PBZ+xV0QgqIxQIhAPfeZvIpGEo2OuxjUovo8lgAkUv5G+ieqFajBw0Ff9LI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9609737}, "engines": {"node": ">=12"}, "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.20.0_1706374167481_0.9320249405267693", "host": "s3://npm-registry-packages"}}, "0.20.1": {"name": "@esbuild/freebsd-x64", "version": "0.20.1", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.20.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "41c9243ab2b3254ea7fb512f71ffdb341562e951", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.20.1.tgz", "fileCount": 3, "integrity": "sha512-itPwCw5C+Jh/c624vcDd9kRCCZVpzpQn8dtwoYIt2TJF3S9xJLiRohnnNrKwREvcZYx0n8sCSbvGH349XkcQeg==", "signatures": [{"sig": "MEYCIQDC1klVD1u8uiM9op6Y40AxXeUqdU3w5vydX8CoWHxr1wIhAIudrw2LO82iT23gg7/PDhzmYMEnyzNs36JUxlt2OBvP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9622025}, "engines": {"node": ">=12"}, "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.20.1_1708324677162_0.38906837811796047", "host": "s3://npm-registry-packages"}}, "0.20.2": {"name": "@esbuild/freebsd-x64", "version": "0.20.2", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.20.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "aa5ea58d9c1dd9af688b8b6f63ef0d3d60cea53c", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.20.2.tgz", "fileCount": 3, "integrity": "sha512-d+DipyvHRuqEeM5zDivKV1KuXn9WeRX6vqSqIDgwIfPQtwMP4jaDsQsDncjTDDsExT4lR/91OLjRo8bmC1e+Cw==", "signatures": [{"sig": "MEYCIQC1TFQ+zfPqfKGmwfHrJ/aFrJ2FK/fO/4z4Uipl16xLEQIhAP60URgbA/Pk8/ZedwlPDf8zX0m/sneUZ/TbX5RNBMcK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9626121}, "engines": {"node": ">=12"}, "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.20.2_1710445777122_0.4464951121337717", "host": "s3://npm-registry-packages"}}, "0.21.0": {"name": "@esbuild/freebsd-x64", "version": "0.21.0", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.21.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "a0efcda8ad78d74824cfa949515f1b04397fca67", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.21.0.tgz", "fileCount": 3, "integrity": "sha512-uwRL7kSN9tfFBpa7o9HQjEgxPsQsSmOz2ALQ30dxMNT22xS49s8nUtFi7bJ+kM/pcTHcnhyJwJPCY7cwlbQbWQ==", "signatures": [{"sig": "MEQCIH/gy+5ZE0/HFE/bsHFdKpqeeZMLSx6EO4nMC2MGtlDpAiBaklloAFG4h0C3ihfgLdsOS673Zys0wg2ny+1+NJKklQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9703945}, "engines": {"node": ">=12"}, "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.21.0_1715050341110_0.6815486406438283", "host": "s3://npm-registry-packages"}}, "0.21.1": {"name": "@esbuild/freebsd-x64", "version": "0.21.1", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.21.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "d36af9085edb34244b41e5a57640e6b4452cbec2", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.21.1.tgz", "fileCount": 3, "integrity": "sha512-paAkKN1n1jJitw+dAoR27TdCzxRl1FOEITx3h201R6NoXUojpMzgMLdkXVgCvaCSCqwYkeGLoe9UVNRDKSvQgw==", "signatures": [{"sig": "MEUCIQC/6K9d4Fu+E1bk7XzyE6htSR4/WRmNo/B0NConif8jFAIgH1lwsTc72UqRPAySCxmnjfW/RqzLeIn7UuutYeRgQ8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9699849}, "engines": {"node": ">=12"}, "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.21.1_1715100891420_0.5746677358942534", "host": "s3://npm-registry-packages"}}, "0.21.2": {"name": "@esbuild/freebsd-x64", "version": "0.21.2", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.21.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "6493aa56760521125badd41f78369f18c49e367e", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.21.2.tgz", "fileCount": 3, "integrity": "sha512-ShS+R09nuHzDBfPeMUliKZX27Wrmr8UFp93aFf/S8p+++x5BZ+D344CLKXxmY6qzgTL3mILSImPCNJOzD6+RRg==", "signatures": [{"sig": "MEQCIGteU/faoq6IRteeWBkM9zP/PVXvw7GdC7HNg6ag9k9ZAiAEISJU7w7rwMeHEaIlb6nJDywVHT5IH0ywIuj41uHS/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9703945}, "engines": {"node": ">=12"}, "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.21.2_1715545971544_0.9671410667727687", "host": "s3://npm-registry-packages"}}, "0.21.3": {"name": "@esbuild/freebsd-x64", "version": "0.21.3", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.21.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "1a5da2bf89f8d67102820d893d271a270ae55751", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.21.3.tgz", "fileCount": 3, "integrity": "sha512-tci+UJ4zP5EGF4rp8XlZIdq1q1a/1h9XuronfxTMCNBslpCtmk97Q/5qqy1Mu4zIc0yswN/yP/BLX+NTUC1bXA==", "signatures": [{"sig": "MEUCIBYpHpQ9ZkElT3VrIpEQdS97649gszHsPm/fEZmsyKB7AiEA7SRHeaCRr8eMCFK1Jslti8XJt9Z/3/rWWBeQH9g2h6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9695753}, "engines": {"node": ">=12"}, "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.21.3_1715806345088_0.8548878509184834", "host": "s3://npm-registry-packages"}}, "0.21.4": {"name": "@esbuild/freebsd-x64", "version": "0.21.4", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.21.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "723299b9859ccbe5532fecbadba3ac33019ba8e8", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.21.4.tgz", "fileCount": 3, "integrity": "sha512-8d9y9eQhxv4ef7JmXny7591P/PYsDFc4+STaxC1GBv0tMyCdyWfXu2jBuqRsyhY8uL2HU8uPyscgE2KxCY9imQ==", "signatures": [{"sig": "MEQCIE2I0sF+P9WND+qqw58lbhrGhkdKeOF5udWGSYxI3eySAiA1eZKf46Z1DhXlFjE/eUbVwP0zuW5kY+EIiCoqu40U2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9708041}, "engines": {"node": ">=12"}, "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.21.4_1716603047111_0.6831166137573872", "host": "s3://npm-registry-packages"}}, "0.21.5": {"name": "@esbuild/freebsd-x64", "version": "0.21.5", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.21.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "aa615cfc80af954d3458906e38ca22c18cf5c261", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz", "fileCount": 3, "integrity": "sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==", "signatures": [{"sig": "MEQCIG+ZfIXmxkhjdjT5ibZQt0BrysbzmRPeUOjGPkSHC/H/AiB2C5mbNLFtL96ldOnHSmvol79apQ8JNo27yvJB0miLBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9712137}, "engines": {"node": ">=12"}, "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.21.5_1717967813003_0.23328802157153627", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "@esbuild/freebsd-x64", "version": "0.22.0", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.22.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "7dbd35616a71f8a9b61a9435c5a79d87fc0b2f1a", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.22.0.tgz", "fileCount": 3, "integrity": "sha512-BFgyYwlCwRWyPQJtkzqq2p6pJbiiWgp0P9PNf7a5FQ1itKY4czPuOMAlFVItirSmEpRPCeImuwePNScZS0pL5Q==", "signatures": [{"sig": "MEQCIH2uaxP7nsqSEEzohr1oveEKlkAZ6z2NLCrk8W3O/xWXAiBnIhnjZpjAgE+b/plbpl7I9D8oHGP5kohRptiZ5E1voQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9966251}, "engines": {"node": ">=18"}, "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.22.0_1719779861825_0.6338619554423186", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "@esbuild/freebsd-x64", "version": "0.23.0", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.23.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "1003a6668fe1f5d4439e6813e5b09a92981bc79d", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.23.0.tgz", "fileCount": 3, "integrity": "sha512-XKDVu8IsD0/q3foBzsXGt/KjD/yTKBCIwOHE1XwiXmrRwrX6Hbnd5Eqn/WvDekddK21tfszBSrE/WMaZh+1buQ==", "signatures": [{"sig": "MEQCIBeV8kcXaa3fHkWxKOWPELaxp+PiEIlTmUyWeYF/DKy6AiBrL5iRTghrLp1flayINNVSvCoJ4HpHVi1qZhe8Oce0cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9966251}, "engines": {"node": ">=18"}, "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.23.0_1719891217272_0.3756415158409727", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "@esbuild/freebsd-x64", "version": "0.23.1", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.23.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "69bd8511fa013b59f0226d1609ac43f7ce489730", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.23.1.tgz", "fileCount": 3, "integrity": "sha512-lK1eJeyk1ZX8UklqFd/3A60UuZ/6UVfGT2LuGo3Wp4/z7eRTRYY+0xOu2kpClP+vMTi9wKOfXi2vjUpO1Ro76g==", "signatures": [{"sig": "MEUCIFj+j8n41NJANve5VKFni2JPSyEXUGwr0Kusl7scBW7xAiEAzCB6GdcRKszyGqEHEbesdtX5Bf+zsBDNbTjsv90KAfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9970347}, "engines": {"node": ">=18"}, "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.23.1_1723846388036_0.26625967259165106", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "@esbuild/freebsd-x64", "version": "0.24.0", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.24.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "e0e2ce9249fdf6ee29e5dc3d420c7007fa579b93", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.24.0.tgz", "fileCount": 3, "integrity": "sha512-D3H+xh3/zphoX8ck4S2RxKR6gHlHDXXzOf6f/9dbFt/NRBDIE33+cVa49Kil4WUjxMGW0ZIYBYtaGCa2+OsQwQ==", "signatures": [{"sig": "MEUCIQDnb/qEcd/mElAyeImVNrYcl7bWiXF3iPagO4iGVOptMAIga4YSCjXN8ilL8xkqscmW7s8jflcZJQzIVwSZYCXGSuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10175147}, "engines": {"node": ">=18"}, "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.24.0_1726970776062_0.8177189954334474", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "@esbuild/freebsd-x64", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "92829d867397cdd2b2712263f645ead20e2db912", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.24.1.tgz", "fileCount": 3, "integrity": "sha512-7hm+A84yjna/LTVLad+8iG5cB/Ik+M/ekSrN4ALs9GolbwcyvtjSD+xoPhFFAg8D7xVu0JdDIoNNZ6+KWLcPoQ==", "signatures": [{"sig": "MEUCIQDbpsFu+fFe8wK/dr+1y8ngglYZR1jFv5OKbacfCwYqogIgP+l5wGsBerghlgdbu/BhNVzzTKPjVM+fNyNClDGVvbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10183339}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.24.1_1734673233199_0.9372268553048322", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/freebsd-x64", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "3fb1ce92f276168b75074b4e51aa0d8141ecce7f", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.24.2.tgz", "fileCount": 3, "integrity": "sha512-TvW7wE/89PYW+IevEJXZ5sF6gJRDY/14hyIGFXdIucxCsbRmLUcjseQu1SyTko+2idmCw94TgyaEZi9HUSOe3Q==", "signatures": [{"sig": "MEUCIFb0QbX6dSPfKCtIml5R3udi2y6eweWs6GuH0eAeSMsYAiEAiL5Y0D/+lXOcMEGIThrGKyAoQZqxKvOm6EI9Ib4eINU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10183339}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.24.2_1734717353151_0.4190920441658066", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/freebsd-x64", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "f3b694d0da61d9910ec7deff794d444cfbf3b6e7", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.0.tgz", "fileCount": 3, "integrity": "sha512-mrSgt7lCh07FY+hDD1TxiTyIHyttn6vnjesnPoVDNmDfOmggTLXRv8Id5fNZey1gl/V2dyVK1VXXqVsQIiAk+A==", "signatures": [{"sig": "MEYCIQC+Y2upnV/PFosmMDOefUQQNFzy1TiUjrHCaU0iTVhfFwIhAMXRH+ocEPHzzD35HQr65s1vOJXFSk+pVCOnp3E6EQQr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10236587}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.25.0_1738983719840_0.5002912348871811", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/freebsd-x64", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "362fc09c2de14987621c1878af19203c46365dde", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.1.tgz", "fileCount": 3, "integrity": "sha512-0IZWLiTyz7nm0xuIs0q1Y3QWJC52R8aSXxe40VUxm6BB1RNmkODtW6LHvWRrGiICulcX7ZvyH6h5fqdLu4gkww==", "signatures": [{"sig": "MEQCIG0CWFtrYl1tdgenA0dqne107hk9lzOHgqRg71OFTr5MAiA2n461pvxC2IsME3NQ9v2N9aoqh321aDNkar6pQBq23A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10244779}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.25.1_1741578310140_0.04753604547886736", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/freebsd-x64", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "88a9d7ecdd3adadbfe5227c2122d24816959b809", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.2.tgz", "fileCount": 3, "integrity": "sha512-6qyyn6TjayJSwGpm8J9QYYGQcRgc90nmfdUb0O7pp1s4lTY+9D0H9O02v5JqGApUyiHOtkz6+1hZNvNtEhbwRQ==", "signatures": [{"sig": "MEUCIATVsIiytEqoyp3pSM9JOO5spKdNB0kMnhbWCE2UWM01AiEAwX2MEreUuv1hD9wPziWh6op0zdpa7Hss1xpS+BV+Z8I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10248875}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.25.2_1743355963704_0.3175733270253134", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/freebsd-x64", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "8ad35c51d084184a8e9e76bb4356e95350a64709", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.3.tgz", "fileCount": 3, "integrity": "sha512-Q+wSjaLpGxYf7zC0kL0nDlhsfuFkoN+EXrx2KSB33RhinWzejOd6AvgmP5JbkgXKmjhmpfgKZq24pneodYqE8Q==", "signatures": [{"sig": "MEUCIDClGsEMLY+tkyqOy0iKsT6XwBA2IhEmefIgU9zivG/KAiEAv7yYnt6YVHE9Re7MLAA7h03PbhY5AfLhKeqSTGXld1w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10257067}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.25.3_1745380535286_0.4570901410675694", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/freebsd-x64", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/freebsd-x64@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["freebsd"], "cpu": ["x64"], "dist": {"shasum": "91da08db8bd1bff5f31924c57a81dab26e93a143", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.4.tgz", "fileCount": 3, "integrity": "sha512-0FgvOJ6UUMflsHSPLzdfDnnBBVoCDtBTVyn/MrWloUNvq/5SFmh13l3dvgRPkDihRxb77Y17MbqbCAa2strMQQ==", "signatures": [{"sig": "MEUCIC4R890Bt+WNO39OAP12K7ceYt2VqYVEq1h5OyZ0i9s0AiEA7R3scTAn05WDzrl8eva80CbfLsZT/3b48YvPcQNDvSE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10261163}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/freebsd-x64_0.25.4_1746491428538_0.4353928090282526", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/freebsd-x64", "version": "0.25.5", "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["freebsd"], "cpu": ["x64"], "_id": "@esbuild/freebsd-x64@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw==", "shasum": "71c77812042a1a8190c3d581e140d15b876b9c6f", "tarball": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz", "fileCount": 3, "unpackedSize": 10261163, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAhNA1CeK3JkMbcLdXUTxHzzx1oLRnkkU4xFMXvB4VPKAiEApNKeogA08n9ZWv3Ho9luwcpUAPNFJiMoWSdwj9lmchA="}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/freebsd-x64_0.25.5_1748315548437_0.3761839973601393"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-12-05T23:44:09.356Z", "modified": "2025-05-27T03:12:28.848Z", "0.15.18": "2022-12-05T23:44:09.696Z", "0.16.0": "2022-12-07T03:55:00.553Z", "0.16.1": "2022-12-07T04:48:09.205Z", "0.16.2": "2022-12-08T06:59:43.592Z", "0.16.3": "2022-12-08T20:12:51.859Z", "0.16.4": "2022-12-10T03:50:25.560Z", "0.16.5": "2022-12-13T17:47:39.554Z", "0.16.6": "2022-12-14T05:23:09.984Z", "0.16.7": "2022-12-14T22:46:53.276Z", "0.16.8": "2022-12-16T23:38:34.780Z", "0.16.9": "2022-12-18T04:31:17.288Z", "0.16.10": "2022-12-19T23:26:31.418Z", "0.16.11": "2022-12-27T01:39:00.340Z", "0.16.12": "2022-12-28T02:04:41.511Z", "0.16.13": "2023-01-02T22:57:17.395Z", "0.16.14": "2023-01-04T20:12:55.347Z", "0.16.15": "2023-01-07T04:18:53.974Z", "0.16.16": "2023-01-08T22:43:48.980Z", "0.16.17": "2023-01-11T21:57:55.156Z", "0.17.0": "2023-01-14T04:33:39.684Z", "0.17.1": "2023-01-16T18:05:33.852Z", "0.17.2": "2023-01-17T06:39:39.085Z", "0.17.3": "2023-01-18T19:14:20.000Z", "0.17.4": "2023-01-22T06:13:39.089Z", "0.17.5": "2023-01-27T16:37:45.768Z", "0.17.6": "2023-02-06T17:00:41.421Z", "0.17.7": "2023-02-09T22:26:40.917Z", "0.17.8": "2023-02-13T06:35:38.265Z", "0.17.9": "2023-02-19T17:45:19.317Z", "0.17.10": "2023-02-20T17:54:53.780Z", "0.17.11": "2023-03-03T22:40:10.036Z", "0.17.12": "2023-03-17T06:16:19.440Z", "0.17.13": "2023-03-24T18:57:04.597Z", "0.17.14": "2023-03-26T02:47:40.948Z", "0.17.15": "2023-04-01T22:26:47.376Z", "0.17.16": "2023-04-10T04:34:58.879Z", "0.17.17": "2023-04-16T21:23:38.984Z", "0.17.18": "2023-04-22T20:41:23.064Z", "0.17.19": "2023-05-13T00:06:26.905Z", "0.18.0": "2023-06-09T21:24:18.003Z", "0.18.1": "2023-06-12T04:51:43.297Z", "0.18.2": "2023-06-13T02:40:27.912Z", "0.18.3": "2023-06-15T12:20:51.285Z", "0.18.4": "2023-06-16T15:38:16.327Z", "0.18.5": "2023-06-20T00:52:31.307Z", "0.18.6": "2023-06-20T23:24:50.358Z", "0.18.7": "2023-06-24T02:46:22.427Z", "0.18.8": "2023-06-25T03:19:09.500Z", "0.18.9": "2023-06-26T05:27:50.536Z", "0.18.10": "2023-06-26T21:20:24.865Z", "0.18.11": "2023-07-01T06:03:50.442Z", "0.18.12": "2023-07-13T01:34:03.309Z", "0.18.13": "2023-07-15T02:37:12.792Z", "0.18.14": "2023-07-18T05:00:16.376Z", "0.18.15": "2023-07-20T12:53:12.250Z", "0.18.16": "2023-07-23T04:47:57.489Z", "0.18.17": "2023-07-26T01:40:50.852Z", "0.18.18": "2023-08-05T17:06:25.685Z", "0.18.19": "2023-08-07T02:51:14.661Z", "0.18.20": "2023-08-08T04:14:52.850Z", "0.19.0": "2023-08-08T15:52:23.296Z", "0.19.1": "2023-08-11T15:57:27.481Z", "0.19.2": "2023-08-14T01:58:16.878Z", "0.19.3": "2023-09-14T01:12:19.827Z", "0.19.4": "2023-09-28T01:46:47.712Z", "0.19.5": "2023-10-17T05:10:28.642Z", "0.19.6": "2023-11-19T07:11:28.956Z", "0.19.7": "2023-11-21T01:00:51.979Z", "0.19.8": "2023-11-26T23:07:55.007Z", "0.19.9": "2023-12-10T05:09:22.302Z", "0.19.10": "2023-12-19T00:21:27.781Z", "0.19.11": "2023-12-29T20:31:46.141Z", "0.19.12": "2024-01-23T17:40:17.815Z", "0.20.0": "2024-01-27T16:49:27.745Z", "0.20.1": "2024-02-19T06:37:57.393Z", "0.20.2": "2024-03-14T19:49:37.318Z", "0.21.0": "2024-05-07T02:52:21.364Z", "0.21.1": "2024-05-07T16:54:51.665Z", "0.21.2": "2024-05-12T20:32:51.819Z", "0.21.3": "2024-05-15T20:52:25.265Z", "0.21.4": "2024-05-25T02:10:47.382Z", "0.21.5": "2024-06-09T21:16:53.222Z", "0.22.0": "2024-06-30T20:37:42.109Z", "0.23.0": "2024-07-02T03:33:37.630Z", "0.23.1": "2024-08-16T22:13:08.306Z", "0.24.0": "2024-09-22T02:06:16.345Z", "0.24.1": "2024-12-20T05:40:33.548Z", "0.24.2": "2024-12-20T17:55:53.447Z", "0.25.0": "2025-02-08T03:02:00.085Z", "0.25.1": "2025-03-10T03:45:10.390Z", "0.25.2": "2025-03-30T17:32:43.950Z", "0.25.3": "2025-04-23T03:55:35.584Z", "0.25.4": "2025-05-06T00:30:28.787Z", "0.25.5": "2025-05-27T03:12:28.683Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "The FreeBSD 64-bit binary for esbuild, a JavaScript bundler.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is the FreeBSD 64-bit binary for esbuild, a JavaScript bundler and minifier. See https://github.com/evanw/esbuild for details.\n", "readmeFilename": "README.md"}