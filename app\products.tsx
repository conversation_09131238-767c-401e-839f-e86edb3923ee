import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { 
  ShoppingCart, 
  Star, 
  Heart,
  Plus,
  Minus,
  Coins,
  Sparkles,
  Leaf,
  Crown,
  Shield,
  Gem,
  ChevronRight
} from 'lucide-react-native';

const { width, height } = Dimensions.get('window');

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: 'herbs' | 'decorations';
  rating: number;
  reviews: number;
  image: string;
  inStock: boolean;
  rarity: 'common' | 'rare' | 'legendary';
  effects?: string[];
}

export default function ProductsScreen() {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'herbs' | 'decorations'>('all');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [favorites, setFavorites] = useState<string[]>([]);

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const products: Product[] = [
    {
      id: '1',
      name: '治愈药剂',
      description: '由新鲜香芹制成的强效治愈药剂，能够快速恢复生命值',
      price: 150,
      originalPrice: 200,
      category: 'herbs',
      rating: 4.8,
      reviews: 124,
      image: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
      inStock: true,
      rarity: 'rare',
      effects: ['恢复50HP', '持续5分钟', '无副作用'],
    },
    {
      id: '2',
      name: '百里香精华',
      description: '珍贵的百里香精华，提升魔法抗性和精神力',
      price: 300,
      category: 'herbs',
      rating: 4.9,
      reviews: 89,
      image: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
      inStock: true,
      rarity: 'legendary',
      effects: ['魔法抗性+20%', '精神力+15', '持续30分钟'],
    },
    {
      id: '3',
      name: '力量药剂',
      description: '增强体力和攻击力的草本药剂',
      price: 120,
      category: 'herbs',
      rating: 4.6,
      reviews: 156,
      image: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
      inStock: true,
      rarity: 'common',
      effects: ['攻击力+25%', '体力+10', '持续15分钟'],
    },
    {
      id: '4',
      name: '古老护身符',
      description: '神秘的古老护身符，提供持续的保护效果',
      price: 500,
      originalPrice: 650,
      category: 'decorations',
      rating: 4.7,
      reviews: 67,
      image: 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg',
      inStock: true,
      rarity: 'legendary',
      effects: ['防御力+30%', '幸运值+5', '永久效果'],
    },
    {
      id: '5',
      name: '水晶项链',
      description: '闪闪发光的水晶项链，增强魅力和交易能力',
      price: 250,
      category: 'decorations',
      rating: 4.5,
      reviews: 98,
      image: 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg',
      inStock: true,
      rarity: 'rare',
      effects: ['魅力+20', '交易折扣10%', '永久效果'],
    },
    {
      id: '6',
      name: '皇家徽章',
      description: '象征地位的皇家徽章，提升声望和影响力',
      price: 800,
      category: 'decorations',
      rating: 4.9,
      reviews: 34,
      image: 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg',
      inStock: false,
      rarity: 'legendary',
      effects: ['声望+50', '影响力+25', '永久效果'],
    },
  ];

  const filteredProducts = products.filter(product => {
    if (selectedCategory === 'all') return true;
    return product.category === selectedCategory;
  });

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return '#4A7043';
      case 'rare': return '#D4A017';
      case 'legendary': return '#8B4513';
      default: return '#B0B7A4';
    }
  };

  const getRarityText = (rarity: string) => {
    switch (rarity) {
      case 'common': return '普通';
      case 'rare': return '稀有';
      case 'legendary': return '传说';
      default: return '未知';
    }
  };

  const toggleFavorite = (productId: string) => {
    setFavorites(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handlePurchase = (product: Product) => {
    console.log('Purchase:', product.name, 'Quantity:', quantity);
    // Here you would implement purchase logic
  };

  return (
    <View style={styles.container}>
      <ImageBackground
        source={{ uri: 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' }}
        style={styles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={['rgba(245, 232, 199, 0.95)', 'rgba(245, 232, 199, 0.9)', 'rgba(245, 232, 199, 0.85)']}
          style={styles.overlay}
        >
          <SafeAreaView style={styles.safeArea}>
            {/* Header with Merchant */}
            <Animated.View 
              style={[
                styles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
                }
              ]}
            >
              <View style={styles.merchantContainer}>
                <View style={styles.merchantFrame}>
                  <Image
                    source={{ uri: 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                    style={styles.merchantImage}
                    resizeMode="cover"
                  />
                  <View style={styles.shawlOverlay}>
                    <LinearGradient
                      colors={['rgba(74, 112, 67, 0.4)', 'rgba(74, 112, 67, 0.7)']}
                      style={styles.shawlGradient}
                    />
                  </View>
                  <View style={styles.herbCart}>
                    <Text style={styles.cartIcon}>🛒🌿</Text>
                  </View>
                </View>
                <View style={styles.merchantInfo}>
                  <Text style={styles.merchantTitle}>草药商人</Text>
                  <Text style={styles.merchantSubtitle}>优质商品，诚信经营</Text>
                </View>
              </View>

              <View style={styles.titleContainer}>
                <Text style={styles.mainTitle}>集市商店</Text>
                <View style={styles.titleUnderline} />
                <Text style={styles.subtitle}>发现神奇的草药与珍宝</Text>
              </View>
            </Animated.View>

            {/* Category Filter */}
            <Animated.View 
              style={[
                styles.categoryContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}
            >
              <BlurView intensity={25} style={styles.categoryBar}>
                <TouchableOpacity
                  style={[
                    styles.categoryButton,
                    selectedCategory === 'all' && styles.categoryButtonActive
                  ]}
                  onPress={() => setSelectedCategory('all')}
                >
                  <Text style={[
                    styles.categoryText,
                    selectedCategory === 'all' && styles.categoryTextActive
                  ]}>
                    全部商品
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.categoryButton,
                    selectedCategory === 'herbs' && styles.categoryButtonActive
                  ]}
                  onPress={() => setSelectedCategory('herbs')}
                >
                  <View style={[styles.categoryFrame, { borderColor: '#4A7043' }]}>
                    <Leaf size={16} color={selectedCategory === 'herbs' ? '#6B4E71' : '#4A7043'} />
                  </View>
                  <Text style={[
                    styles.categoryText,
                    selectedCategory === 'herbs' && styles.categoryTextActive
                  ]}>
                    草本药剂
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.categoryButton,
                    selectedCategory === 'decorations' && styles.categoryButtonActive
                  ]}
                  onPress={() => setSelectedCategory('decorations')}
                >
                  <View style={[styles.categoryFrame, { borderColor: '#4A7043' }]}>
                    <Gem size={16} color={selectedCategory === 'decorations' ? '#6B4E71' : '#4A7043'} />
                  </View>
                  <Text style={[
                    styles.categoryText,
                    selectedCategory === 'decorations' && styles.categoryTextActive
                  ]}>
                    装饰品
                  </Text>
                </TouchableOpacity>
              </BlurView>
            </Animated.View>

            <ScrollView 
              style={styles.scrollView} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.scrollContent}
            >
              {/* Product Grid */}
              <Animated.View 
                style={[
                  styles.productsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                {filteredProducts.map((product, index) => (
                  <BlurView key={product.id} intensity={30} style={styles.productCard}>
                    <TouchableOpacity 
                      style={styles.productContent}
                      onPress={() => setSelectedProduct(product)}
                      activeOpacity={0.8}
                    >
                      {/* Product Image */}
                      <View style={styles.productImageContainer}>
                        <Image
                          source={{ uri: product.image }}
                          style={styles.productImage}
                          resizeMode="cover"
                        />
                        <View style={styles.productOverlay}>
                          <LinearGradient
                            colors={['transparent', 'rgba(0, 0, 0, 0.3)']}
                            style={styles.imageGradient}
                          />
                        </View>
                        
                        {/* Rarity Badge */}
                        <View style={[
                          styles.rarityBadge,
                          { backgroundColor: `${getRarityColor(product.rarity)}20` }
                        ]}>
                          <Text style={[
                            styles.rarityText,
                            { color: getRarityColor(product.rarity) }
                          ]}>
                            {getRarityText(product.rarity)}
                          </Text>
                        </View>

                        {/* Favorite Button */}
                        <TouchableOpacity
                          style={styles.favoriteButton}
                          onPress={() => toggleFavorite(product.id)}
                        >
                          <Heart 
                            size={20} 
                            color={favorites.includes(product.id) ? '#DC3545' : '#8B7355'}
                            fill={favorites.includes(product.id) ? '#DC3545' : 'transparent'}
                          />
                        </TouchableOpacity>

                        {/* Discount Badge */}
                        {product.originalPrice && (
                          <View style={styles.discountBadge}>
                            <Text style={styles.discountText}>
                              -{Math.round((1 - product.price / product.originalPrice) * 100)}%
                            </Text>
                          </View>
                        )}
                      </View>

                      {/* Product Info */}
                      <View style={styles.productInfo}>
                        <Text style={styles.productName}>{product.name}</Text>
                        <Text style={styles.productDescription} numberOfLines={2}>
                          {product.description}
                        </Text>

                        {/* Rating */}
                        <View style={styles.ratingContainer}>
                          <View style={styles.stars}>
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star
                                key={star}
                                size={12}
                                color="#D4A017"
                                fill={star <= Math.floor(product.rating) ? '#D4A017' : 'transparent'}
                              />
                            ))}
                          </View>
                          <Text style={styles.ratingText}>
                            {product.rating} ({product.reviews})
                          </Text>
                        </View>

                        {/* Effects */}
                        {product.effects && (
                          <View style={styles.effectsContainer}>
                            {product.effects.slice(0, 2).map((effect, idx) => (
                              <View key={idx} style={styles.effectBadge}>
                                <Sparkles size={10} color="#6B4E71" />
                                <Text style={styles.effectText}>{effect}</Text>
                              </View>
                            ))}
                          </View>
                        )}

                        {/* Price and Purchase */}
                        <View style={styles.priceContainer}>
                          <View style={styles.priceInfo}>
                            <View style={styles.priceRow}>
                              <Coins size={16} color="#D4A017" />
                              <Text style={styles.price}>{product.price}</Text>
                              {product.originalPrice && (
                                <Text style={styles.originalPrice}>{product.originalPrice}</Text>
                              )}
                            </View>
                            {!product.inStock && (
                              <Text style={styles.outOfStock}>缺货</Text>
                            )}
                          </View>
                          
                          <TouchableOpacity
                            style={[
                              styles.purchaseButton,
                              !product.inStock && styles.purchaseButtonDisabled
                            ]}
                            onPress={() => handlePurchase(product)}
                            disabled={!product.inStock}
                          >
                            <LinearGradient
                              colors={
                                product.inStock 
                                  ? ['#D4A017', '#E6B82A']
                                  : ['#B0B7A4', '#C0C7B4']
                              }
                              style={styles.purchaseButtonGradient}
                            >
                              <Text style={styles.purchaseButtonText}>
                                {product.inStock ? '购买' : '缺货'}
                              </Text>
                            </LinearGradient>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </TouchableOpacity>
                  </BlurView>
                ))}
              </Animated.View>

              {/* Featured Section */}
              <Animated.View 
                style={[
                  styles.featuredContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <BlurView intensity={30} style={styles.featuredCard}>
                  <View style={styles.featuredHeader}>
                    <Crown size={24} color="#D4A017" />
                    <Text style={styles.featuredTitle}>今日特惠</Text>
                  </View>
                  <Text style={styles.featuredDescription}>
                    精选商品限时优惠，数量有限，先到先得！
                  </Text>
                  <View style={styles.featuredStats}>
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>15%</Text>
                      <Text style={styles.statLabel}>平均折扣</Text>
                    </View>
                    <View style={styles.statDivider} />
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>24</Text>
                      <Text style={styles.statLabel}>特惠商品</Text>
                    </View>
                    <View style={styles.statDivider} />
                    <View style={styles.statItem}>
                      <Text style={styles.statNumber}>2小时</Text>
                      <Text style={styles.statLabel}>剩余时间</Text>
                    </View>
                  </View>
                </BlurView>
              </Animated.View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    width: width,
    height: height,
  },
  overlay: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 15,
  },
  merchantContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  merchantFrame: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 3,
    borderColor: '#4A7043',
    overflow: 'hidden',
    position: 'relative',
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  merchantImage: {
    width: '100%',
    height: '100%',
  },
  shawlOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  shawlGradient: {
    flex: 1,
  },
  herbCart: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    backgroundColor: '#4A7043',
    borderRadius: 12,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  cartIcon: {
    fontSize: 12,
  },
  merchantInfo: {
    flex: 1,
  },
  merchantTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 2,
  },
  merchantSubtitle: {
    fontSize: 14,
    color: '#8B7355',
  },
  titleContainer: {
    alignItems: 'center',
  },
  mainTitle: {
    fontSize: 28,
    color: '#4A7043',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  titleUnderline: {
    width: 80,
    height: 3,
    backgroundColor: '#4A7043',
    borderRadius: 2,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8B7355',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  categoryContainer: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  categoryBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(176, 183, 164, 0.3)',
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  categoryButtonActive: {
    backgroundColor: 'rgba(107, 78, 113, 0.15)',
  },
  categoryFrame: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4A7043',
  },
  categoryTextActive: {
    color: '#6B4E71',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  productsContainer: {
    paddingHorizontal: 20,
  },
  productCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  productContent: {
    padding: 16,
  },
  productImageContainer: {
    width: '100%',
    height: 180,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
    marginBottom: 12,
  },
  productImage: {
    width: '100%',
    height: '100%',
  },
  productOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  imageGradient: {
    flex: 1,
  },
  rarityBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  rarityText: {
    fontSize: 12,
    fontWeight: '600',
  },
  favoriteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(245, 232, 199, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  discountBadge: {
    position: 'absolute',
    top: 8,
    right: 48,
    backgroundColor: '#DC3545',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  discountText: {
    color: '#F5E8C7',
    fontSize: 10,
    fontWeight: '700',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 4,
  },
  productDescription: {
    fontSize: 14,
    color: '#8B7355',
    lineHeight: 20,
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  stars: {
    flexDirection: 'row',
    marginRight: 6,
  },
  ratingText: {
    fontSize: 12,
    color: '#8B7355',
  },
  effectsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
  },
  effectBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(107, 78, 113, 0.1)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    marginRight: 6,
    marginBottom: 4,
  },
  effectText: {
    fontSize: 10,
    color: '#6B4E71',
    marginLeft: 2,
    fontWeight: '500',
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceInfo: {
    flex: 1,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  price: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B4E71',
    marginLeft: 4,
  },
  originalPrice: {
    fontSize: 14,
    color: '#8B7355',
    textDecorationLine: 'line-through',
    marginLeft: 8,
  },
  outOfStock: {
    fontSize: 12,
    color: '#DC3545',
    fontWeight: '600',
    marginTop: 2,
  },
  purchaseButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  purchaseButtonDisabled: {
    opacity: 0.6,
  },
  purchaseButtonGradient: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  purchaseButtonText: {
    color: '#6B4E71',
    fontSize: 14,
    fontWeight: '700',
  },
  featuredContainer: {
    paddingHorizontal: 20,
    marginTop: 10,
  },
  featuredCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 78, 113, 0.2)',
  },
  featuredHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  featuredTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B4E71',
    marginLeft: 8,
  },
  featuredDescription: {
    fontSize: 14,
    color: '#8B7355',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
  },
  featuredStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 16,
    fontWeight: '700',
    color: '#6B4E71',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#8B7355',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(139, 115, 85, 0.3)',
  },
});