{"_id": "cmd-shim", "_rev": "76-d45abb90517f88411c17835af0defcea", "name": "cmd-shim", "dist-tags": {"latest": "7.0.0"}, "versions": {"1.0.0": {"name": "cmd-shim", "version": "1.0.0", "license": "MIT", "_id": "cmd-shim@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "8a126dd25d7656afa12b6de343699ccfa5924dca", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-1.0.0.tgz", "integrity": "sha512-+0HBwpuycH6oWFq/YhCm7l8CDh+nQOS9iJFdOnKIRHT/cu65MEu8KnyNJT+cKFptjkPov2um5CC9pg51PRrJKw==", "signatures": [{"sig": "MEYCIQDmOGo9ckmmgv6VwiM/YoqtnOV4Ycani9yghq9SGEC0igIhANSqckzU4Jt8o4WOerGff+ntU0rKBv5ioIRRxLA+YEyQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ForbesLindesay/cmd-shim.git", "type": "git"}, "_npmVersion": "1.2.10", "description": "Used in npm for command line application support", "directories": {}, "dependencies": {"slide": "1", "mkdirp": "~0.3.3", "npmlog": "0", "rimraf": "2", "graceful-fs": "~1.2.0"}}, "1.1.0": {"name": "cmd-shim", "version": "1.1.0", "license": "BSD", "_id": "cmd-shim@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e69fe26e9a8b9040c7b61dc8ad6b04d7dbabe767", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-1.1.0.tgz", "integrity": "sha512-K9heLLKmdwstkHM2g7KnLckB5hmj9ow1qGlbcXZHJUJMs5RQGszmHHrT+w2ailhK5u1AscVEI5/GrHkyng7ntQ==", "signatures": [{"sig": "MEQCICZ3IMl/UXZJS6UL5Iy0pr4W5/OSv7Yk0GVZE37SYApiAiBzRCFrIGbYn77VAHAC2onACfi5jkU32XecnGJK6t1Ntg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ForbesLindesay/cmd-shim.git", "type": "git"}, "_npmVersion": "1.2.10", "description": "Used in npm for command line application support", "directories": {}, "dependencies": {"mkdirp": "~0.3.3", "graceful-fs": "1.2"}, "devDependencies": {"tap": "~0.4.1", "rimraf": "~2.1.4"}, "optionalDependencies": {"graceful-fs": "1.2"}}, "1.0.1": {"name": "cmd-shim", "version": "1.0.1", "license": "BSD", "_id": "cmd-shim@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "dist": {"shasum": "75e917c2185240854718c686346770640083d7bc", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-1.0.1.tgz", "integrity": "sha512-tBFdo1ZLoJpak+/YFfBr1wp/scQmh3EwcqWV6/CoCvfoBlA+QzVGgaAFvQb34Nv+Bd+8JxrByulIa1D2J6+p5w==", "signatures": [{"sig": "MEUCIQD6QBAKjTWgeasnhkao4d9ZGDa+iwgc9miZeiHSfrbO5gIgC0Xs+Aym3ZbLzfKeX8BiqfdltPIVCMfS4GENDytzH4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ForbesLindesay/cmd-shim.git", "type": "git"}, "_npmVersion": "1.3.4", "description": "Used in npm for command line application support", "directories": {}, "dependencies": {"mkdirp": "~0.3.3", "graceful-fs": "2"}, "devDependencies": {"tap": "~0.4.1", "rimraf": "~2.1.4"}, "optionalDependencies": {"graceful-fs": "2"}}, "1.1.1": {"name": "cmd-shim", "version": "1.1.1", "license": "BSD", "_id": "cmd-shim@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "dist": {"shasum": "348b292db32ed74c8283fcf6c48549b84c6658a7", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-1.1.1.tgz", "integrity": "sha512-SNnCQQ8PntplOH5uN8Mtzd2at6OolhLOwCfC3XfJ8oEr3Nk4vzOIIU9HJa+F34hz1c2C4a9T6nmxGdG1+l/TkA==", "signatures": [{"sig": "MEUCIQCvZpaQ8kNE4vDraKnG9z+9GOXl0EPuQi53vvzDPVLhEQIgCku2jE9WB4rHgdK5XZsaKRD69AmxZf04TjjRu2RqNMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ForbesLindesay/cmd-shim.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "Used in npm for command line application support", "directories": {}, "dependencies": {"mkdirp": "~0.3.3", "graceful-fs": "2"}, "devDependencies": {"tap": "~0.4.1", "rimraf": "~2.1.4"}, "optionalDependencies": {"graceful-fs": "2"}}, "1.1.2": {"name": "cmd-shim", "version": "1.1.2", "license": "BSD", "_id": "cmd-shim@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ForbesLindesay/cmd-shim", "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "dist": {"shasum": "e4f9198802e361e8eb43b591959ef4dc6cdb6754", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-1.1.2.tgz", "integrity": "sha512-ik9TmR1DTtmh30JdCqJ/KeuA+Dib47dVLGCAZTgB/1l8fLxrTSDWxdB6yPIeIk6fEDEny4Pn8ZfEckfMJHivmQ==", "signatures": [{"sig": "MEUCIQDMUT9il8Ns+r/Q4d0Kcg4LHHCa+1kjaIK2OHSl50oR9gIgOGaz5HX4HHKz6TK21LiIFKGqGmFbFayxgOglJibtOMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ForbesLindesay/cmd-shim.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Used in npm for command line application support", "directories": {}, "dependencies": {"mkdirp": "~0.5.0", "graceful-fs": "2"}, "devDependencies": {"tap": "~0.4.11", "rimraf": "~2.2.8"}, "optionalDependencies": {"graceful-fs": "2"}}, "2.0.0": {"name": "cmd-shim", "version": "2.0.0", "license": "BSD", "_id": "cmd-shim@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ForbesLindesay/cmd-shim", "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "dist": {"shasum": "34e0cd2ede0505cd8b154667eee9054ee24006b4", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-2.0.0.tgz", "integrity": "sha512-YeFz1Zc8GrE+gvED3s7PUjOy5eZfC+fkyocHO3i3x3pGxGh+rlB4FcB2PwxAGzM/TeKNAmkRV/64CCIFzMBvgA==", "signatures": [{"sig": "MEYCIQDEeAOUjptK6IW5MgcnUPsUkTS3g6YxcHJr9bWxuVriBAIhAODOwAu73JojbV/yLivpJqITM8HyUtSZxbEPB1MkHdni", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "34e0cd2ede0505cd8b154667eee9054ee24006b4", "gitHead": "10bd1362edd0dddec30168b2c3cd915d89d4c0ef", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ForbesLindesay/cmd-shim.git", "type": "git"}, "_npmVersion": "1.5.0-alpha-4", "description": "Used in npm for command line application support", "directories": {}, "dependencies": {"mkdirp": "~0.5.0", "graceful-fs": "2"}, "devDependencies": {"tap": "~0.4.11", "rimraf": "~2.2.8"}, "optionalDependencies": {"graceful-fs": "2"}}, "2.0.1": {"name": "cmd-shim", "version": "2.0.1", "license": "BSD", "_id": "cmd-shim@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ForbesLindesay/cmd-shim", "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "dist": {"shasum": "4512a373d2391679aec51ad1d4733559e9b85d4a", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-2.0.1.tgz", "integrity": "sha512-jMTDyjr2QTbQ2d7JPjHgG9qsIJd4Qh4EAjfb70CaRD3AIRgd3vpAmnYRnkyHJC+t/ItP3l3IhAOwJOfp2hmLxA==", "signatures": [{"sig": "MEUCICiBILeXveg9UBZGVjmdoXUykFN4ir8OhG4bDW8f6CQkAiEA8VTLLqvIIDLPY2FvaBGD1EGlfEkmPRlS/nsLMuiRYAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "4512a373d2391679aec51ad1d4733559e9b85d4a", "gitHead": "6f53d506be590fe9ac20c9801512cd1a3aad5974", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ForbesLindesay/cmd-shim.git", "type": "git"}, "_npmVersion": "1.5.0-alpha-4", "description": "Used in npm for command line application support", "directories": {}, "dependencies": {"mkdirp": "~0.5.0", "graceful-fs": ">3.0.1 <4.0.0-0"}, "devDependencies": {"tap": "~0.4.11", "rimraf": "~2.2.8"}}, "2.0.2": {"name": "cmd-shim", "version": "2.0.2", "license": "BSD-2-<PERSON><PERSON>", "_id": "cmd-shim@2.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/ForbesLindesay/cmd-shim", "bugs": {"url": "https://github.com/ForbesLindesay/cmd-shim/issues"}, "dist": {"shasum": "6fcbda99483a8fd15d7d30a196ca69d688a2efdb", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-2.0.2.tgz", "integrity": "sha512-NLt0ntM0kvuSNrToO0RTFiNRHdioWsLW+OgDAEVDvIivsYwR+AjlzvLaMJ2Z+SNRpV3vdsDrHp1WI00eetDYzw==", "signatures": [{"sig": "MEUCIQD5DBGJemy+a5o+U0td3vKZIoaZnWVVvHETjnr1OeGDiwIgT9zpt1j3HkVMhtgznunR9q4MWhnycqFL18zomsS0/Ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "6fcbda99483a8fd15d7d30a196ca69d688a2efdb", "gitHead": "8492e2a92b5062bb02a9eec509e57eea94b110a7", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/ForbesLindesay/cmd-shim.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "Used in npm for command line application support", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"mkdirp": "~0.5.0", "graceful-fs": "^4.1.2"}, "devDependencies": {"tap": "~0.4.11", "rimraf": "~2.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim-2.0.2.tgz_1455116566936_0.7582207734230906", "host": "packages-6-west.internal.npmjs.com"}}, "2.1.0": {"name": "cmd-shim", "version": "2.1.0", "license": "ISC", "_id": "cmd-shim@2.1.0", "maintainers": [], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "dist": {"shasum": "e59a08d4248dda3bb502044083a4db4ac890579a", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-2.1.0.tgz", "fileCount": 11, "integrity": "sha512-A5C0Cyf2H8sKsHqX0tvIWRXw5/PK++3Dc0lDbsugr90nOECLLuSPahVQBG8pgmgiXgm/TzBWMqI2rWdZwHduAw==", "signatures": [{"sig": "MEYCIQDqx7DYuT86VKv4610DP/YZbqNC8MwhihRe/GYjdlwX/wIhAIBBaO8QyUT/qvtq+P/eSCdouP6eDUIuaJw/m3JVObrM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUblQCRA9TVsSAnZWagAA6z8P/RdL9Y1bSkf3l4EDG1zP\ndqSD874TvGd6HjxEtz6YsQPBXWgQbWHGRdvHKLvFDWZEuZk42n7KcxKy6B94\n8tYTZbAYbvEZkJoZbFwP02Nn1f9L7oYgEssDEQLTvfq5+wJDZg/E/9gnBq5o\not0rZ5rDtQjRfcgRMlC1p4k0ZbzkCoT+scSdsL9u3O9HAwYTA2fJM7Ivzyxu\nyvmtOd5OkT5F7xXwvs6FMUFT5EKrslNui+hrP1OiHO82W2y0wxaq3627JIj7\nx3v4wDjMHJhxZYpoIWwCRYDLFO85y9nCGTa4c7rIDanEt5VO2/Thc6QLwaE/\nzkfDqtHJ0R4tIDeskKlo3FBKO/Hd0nIR6tXMXJRSDy+XnL44fGPDtECy0siN\nbuDixmbUW64P9gZntO4G3kc/WcCGDVy8C4CI9rbP+uzKTFJ9i0iOufHG6uJ+\nVsSqhTDh7r0i4SYp8MjdOgb0gVI5KD7wnyrmmE2jexEfjSDJcMiU8ah/4yGa\nV9gRgFECKaV1Euqnjhz5M2rzdbDAdWF8ZToNUKFb0O8oX5sPIUszG9J8anTm\noeXJi2DavmVPaTlwavvlEZc3i7Uzg+ALUb5JXAhUdu5/M7dYRQ1KNdVFYc4V\n2GXXi8Xl/QDxhAlIPm1hHcPQTyU0unDrJRQSYYW8Csjv2xowatZ3W9cwfcdb\nyi7m\r\n=J/Kx\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "37dd1e4681bcf75fd686e9f0665448f162f74790", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "Used in npm for command line application support", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"mkdirp": "~0.5.0", "graceful-fs": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^1.2.0", "rimraf": "~2.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_2.1.0_1565636944140_0.3777399950082776", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "cmd-shim", "version": "3.0.0", "license": "ISC", "_id": "cmd-shim@3.0.0", "maintainers": [], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "dist": {"shasum": "738b3532187238c4b69682345c2ad930e99b7750", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-3.0.0.tgz", "fileCount": 12, "integrity": "sha512-9cl9KcmjzrOZhA/mLl2qwhFaPLr6HpbbuPjMWPdDQ7EVrIOQJsI1JvzMur87mgaeCAtwRb5WNS2SBnC9uZDWwg==", "signatures": [{"sig": "MEUCIQCJyjlfBVWVy89F9+Z206WacCW1bZkB5xRU6Nha2ea8tAIgZbyFV2WdWDvDQHbUW1i98pK7hQObuP4ItAKAE1tR9/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVGPeCRA9TVsSAnZWagAAzDYP/RDFNUkpBnkyTipDyVPU\nfTvR/IZh8ANTqksogq8vo6w/ngl3Og4Va/FPwthgAwm6eS7GPURVp2qZINbx\nUtOvqZHvdk5Dur6fNNXTACZ1zwjmrhYJzQPA+Zdorsl6H8LJnX8GqE0RgN71\nTgJp0IpTJgfNIYVYK6O2OjhIL2XshEfHl2AKkQciV1WyWF/yBw+eVKLQTirg\nBEaL6BuvCUAp+Km/JU/QMFXaptM1BttiGWGOBs+adXQAqJzUP6BRngjsVucK\nCV82T1gQSWmThKKg/opNUagP5mQeo2SkKaC8KMPif8Ose744C0C0f+G/ttGI\nZL+TRLLa+2TiCZ2KOjUDrVtuh6IGoX9w+UngU50xc8bOggqYkoiwCke5BD2H\nBL9eBSRi9AJe+yrq5yfxvuH9EF8dg2lK32vuA3w+85MIiKLpMG+WlhQgLDsk\nu2GCbEMq/HKIGv3iNedNNLdgYZtvz+K5NoX+zIZzfFW3B10eC759DtAv78+X\ntiadd0FzsITVVYu8wO7/uaM0BzmDTVjfVintwXbX13huo0031gEqxJ2qcUWc\nvU3w9x8bQA1a7iT8S0ef4O5nUb8+H08lEwl2DzuU5aVijF77S7blpmOMHR5Y\nCqi/M7RGZuckw7tXeJ/FkB3MrA3g59fuazA2RB3YGz1dq4og2kLnFxWD+gh6\nubi4\r\n=2CQn\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "a8247738b11e27f5668a07374a9928445b00ebf4", "scripts": {"snap": "TAP_SNAPSHOT=1 tap test/*.js --100", "test": "tap test/*.js --100", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "Used in npm for command line application support", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"mkdirp": "~0.5.0", "graceful-fs": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.7.0", "rimraf": "~2.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_3.0.0_1565811677907_0.4344263968534181", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "cmd-shim", "version": "3.0.1", "license": "ISC", "_id": "cmd-shim@3.0.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "dist": {"shasum": "7b34dfbd503898b9953928a9eed1ef251fe19f88", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-C5t8o5glT2rDJkfRXZXweu5+obqcCjA5OSKSFQeYBDGZo+crirMh35Uhm9Lmb1wRSinKBXOFsCN7XNyLGTve8A==", "signatures": [{"sig": "MEUCIE7vO0YPC5RzK3qgkjYiRHkt9lurfZ5v6/gmXrpdVjg3AiEAi/qWQLqQ1pJQgY/Jg1uBbYfF7YBOsiTQtDL4d4TI01M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXE87CRA9TVsSAnZWagAAjN0P/RR/lbzgvxyjiQwPYcwQ\nOIGlAP76x7ngiaS9eDkvQETwjYPIzrjS//tfJRJ2GqZpPyA2Kw/FhlOecFxx\nvd58Ejwtlx++HmcoOgFZWDiqFHfn1cEDJGRGVMoUuRV3KQs1SAOOzjKX5Ze2\n6ADUEom2cwnUBnLiYujiQhR1TTTZ85oK1RYvYG4rqi12LT/q4f480zh0h6Xf\npzBtKV68JFDxlR7+XzsofMKfdHaVZgXHPsq3n+pEo88BpSrAODPyI+UQ3617\n2GwHa9mufuHW3xIKP96WF9AEV0QFQLmEdChPOpJRqwDD2Ls/31tq4Mg+aVwa\n/yxGDtOAKnKKORJHDxJzN77N3wdru4FpI+VuH00I26BORYVe2RGezrvScsgW\niNk/lGVth7jTMDL21vy8DPun0zGFJvzhfrJ88xEetNZFr/F1im5AKOMZH7uz\nP+eSWo6HHOBsV1t1CVEZIrFvyFxrMJsEl/AA4V6QCl3qM4ApnWGVjpeEAtzE\nOnxbv6/VTqrfYg7wM/ru06VRgNja61g69ldulbx5HOhoLcKVDtABA0va/qCs\noN1YELk9HWwxWBK0fhpT5aMTbjrGPV3FYKmKkPqVrjuEy2DtpHZOdGxxRvds\nDLBjsht0Vrpyi6f4B7pe7Ant4OX5KfBveRxQYTJ3ufd/zZxlJAcwKaDLSnyo\ni3CV\r\n=eLYL\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "a9ea3f8e772da68b38ceb9d55e1374d3b1d43b03", "scripts": {"snap": "TAP_SNAPSHOT=1 tap test/*.js --100", "test": "tap test/*.js --100", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "6.11.0", "description": "Used in npm for command line application support", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"mkdirp": "~0.5.0", "graceful-fs": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.7.0", "rimraf": "~2.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_3.0.1_1566330683267_0.29721216609804335", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "cmd-shim", "version": "3.0.2", "license": "ISC", "_id": "cmd-shim@3.0.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "dist": {"shasum": "159a20602ac4295ec0997243a40ac9b2df54f5e3", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-x/2sXAaA1lUgUCZ9zaeOGzSkXOoN2DFxY61kgfhnIIUe/2QK8DFDnezT2drSZa/u8SKQT3vRFwTFPMMgQmH8kg==", "signatures": [{"sig": "MEUCIEFlC7zlsYvzagosIEyTkZAVAe3TgW673wEcjPdn2Pj8AiEA9EFeQqgIGJarLGXxWGUaUZ0sslcHfL8WUYYOdVk7d+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXIrSCRA9TVsSAnZWagAAz8wP/2gTTWFoZPuPKtVk5v2H\nSZpsiqjfrIJTg3kV3O7ciC+RdppFcYRifPJx517zBBfjiLTvdDWiy4vc7GhY\nqMVDoTFX2k1uRL5ZwRyaBOPBbdgs0UZGWuYiolqYNdVuOZk2T7DvuSdmMnbm\n1vGbo9CxZKT2MwrYTqadhCA5ZxSV/oRmjBvFaa8UV9LNCrwlxMV0+58tXIod\n71zwCbSKepRdQn5EkmKk93rdMJ4U1Jq9bZ+C/C8+QRkT32w4oRsjgNLn1L7W\nRrkc6vUsOfHuJ/WNY0Quns3BblapjmyDWsj31YBBHCkUdvaWLjf5rf6hWAyU\nmYLbT8woODrlF/gXjraivF8rLzB8Vvfv/z90zoH2YKRSzLrYv9EFbfAuInMM\ny3QBaQ+kaATe6RMyHET2ARIScFFmGgHUCgBOUTRDvLzmZVZq2soRXUZ1AAj9\nr7EYVdUjtI+tYddl0U+734FHo6PA3q9+9RqPwqDX5jdI1w8r4+NzHAR5NOzs\nXiCINJoYcu41P5988LlZnTD7nDeCUq9cH+POMGGHyZZeT/x9De7Qih7LrCzA\n/kYxfaSFnj0j9Ba2f0Gg2d25b6yArx5EAUFbXctgJfUjxZfv6Z/O2amACoi8\nUsnqCihliITIvFss7T8vDv4wGaiRVw05Kom1W51ztld1Y/botieYJuei8e4d\nFv9G\r\n=NY3k\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "c380c2ab26f1eaea1fd15cb868d5fc9ea3847076", "scripts": {"snap": "TAP_SNAPSHOT=1 tap test/*.js --100", "test": "tap test/*.js --100", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "6.11.0", "description": "Used in npm for command line application support", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"mkdirp": "~0.5.0", "graceful-fs": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.7.0", "rimraf": "~2.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_3.0.2_1566345938047_0.8904793888273503", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "cmd-shim", "version": "3.0.3", "license": "ISC", "_id": "cmd-shim@3.0.3", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "billatnpm", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "dist": {"shasum": "2c35238d3df37d98ecdd7d5f6b8dc6b21cadc7cb", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-3.0.3.tgz", "fileCount": 5, "integrity": "sha512-DtGg+0xiFhQIntSBRzL2fRQBnmtAVwXIDo4Qq46HPpObYquxMaZS4sb82U9nH91qJrlosC1wa9gwr0QyL/HypA==", "signatures": [{"sig": "MEYCIQCZclzsa7WUkq67Zi6mFqJr7TpdFMGuQmSBuMSFg+jaZAIhAK2uPiuawYRg6Oxr9MHlPcyps5DiVxGOOmmg+ymwkvbI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXbGTCRA9TVsSAnZWagAA49AQAJjvtSG/Om8OIFnc8t8b\ndTGoMcBQQKqL3dwERKQDzsI3iWq06iQCIEYZXJ9x+rcbpwu+qJkQe/uh/F8U\nE+Mo5YZLcGX7/plQ7DO+XnUqVZiiv8jDaQMXq3vkywmW3LoIsYX3UwHHC/Ne\n7Gdi4zpiR2sL4Qd9budRjSCCRuQKCKfxt7FrMibAdmsqZKgdv3qnYGG3O3eX\nvxThuBXgH8z8V53grDFud/QchfFVexPCBMkzP4nfyXlAbvIvMBez3kpQhaey\ncSuFQsxHBQJRvowcnfZ7TQFVJqlznQWOxeg6d6h5Yzbm9zYFiU+KaPIxK3Sk\nuM8oOQKFlw3aKTq5tJmw3iWWWuc/4ixI5mtMzVEPi2SmfLr37XrMb6xt9Jpz\n3KEJKENaOzEX3qkMqQrZP0cb+VCWE9A46bmAQ0Tu2Dc5yczuj9/GDN0yiXjD\n+vLfJ27fUtDnxR/P2gggumNiGiNlfIZvGANU4n4iNB9xuY91Xe0gUkuUwkRy\nQUC4wGszRYvhdmXaE+hZZV1X/r5EqOxaOPUSecZah4fGP8ab+5uCpXYKq6Yt\nlBVerzLbf0UnqRbx05ipkmsRsYBFt/oMzcn8Qsoi9N85dK6py9I8eImxW77N\n9I6Of+ufI8BB6Te/AeBJvX6x1gPzOIqkAbdBODvpbVcaBEf3dtBnZq36N6YT\nUtvy\r\n=Ewav\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "bf5df1156ff856571011d0b4b8ce78fe19441a57", "scripts": {"snap": "TAP_SNAPSHOT=1 tap test/*.js --100", "test": "tap test/*.js --100", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "6.11.1", "description": "Used in npm for command line application support", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"mkdirp": "~0.5.0", "graceful-fs": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.7.0", "rimraf": "~2.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_3.0.3_1566421394401_0.9316181722794517", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "cmd-shim", "version": "4.0.0", "license": "ISC", "_id": "cmd-shim@4.0.0", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "tap": {"after": "test/zz-cleanup.js", "before": "test/00-setup.js", "check-coverage": true}, "dist": {"shasum": "807b82c415d77bfe05bc1026fe417c2b33a45051", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-K3lQ3YlkNJTBcglJ9ubBqkk23WoaiLN0GytOUt7L/E3MzKTjaf33H3b2HHCiNA01SvTnDoamPpTmczrvOMPhWA==", "signatures": [{"sig": "MEQCIFOSYhYBx6OI1sb1XjgVKrI/k8vzRqvL0mgqohf/1MrtAiBV+blkVPWskYp3WXrfW5mvmSzVncCmEcarBbeea7li1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLo4QCRA9TVsSAnZWagAA2NAP/1F4P2DopUzb+mlebiE+\nFcSCfh97gyK1Mz5PxZ5hRB9Sm2xjR6B807FmFx9pZ4c2nouo9+o9QG1QzowK\nOxmIxe72Z0IOPVispejb6OlR0t8iJkh3B+lP9Qpe8XMANxSJFUY4eN1u5YlJ\n503vJQowqrS8sEERR75egozK7pP1YNeLXnRCNB33+06Ibh9VV6bMGqZ5EeC/\nKYmYr7JjDRazsiNXkXnARybKWEljZMfJz1PY0ZoEbZ1DU15XeI71WsWw51ch\nwvyI0xBZExhgncYOrgiNIHCIGTXkndFBDb6C9VPKkqu2DDPE1wAYisx2GQC+\nG1eFUAfqtivk1uhvmYopaXlwrVwWiIFEICiQKwsaT2NjfMiYKoMXK8/TkCHr\n3OiEDPnXALFlHv4WiEfRn5s6unzwc3nWo+mGs9WvHQiH4qwl2Lod1VojCLzZ\nGgTTXT3OnEQcMco92AVmkGqxhV3FA2jWZ0mnXY2E3RukhR7PRPVVM8K40CSf\nxBkC+XRJqcwRxwuFmX8WWvXjikq6j4jhqLcOekot3r1ERuAwkut3wXHf3xE1\nrrAK4TW2zQnLBjJ+HKyHAb8yjBkrwBDyUmgPNOK5A+MxBh/ZDz6JMPJhRXNF\nVZTd52AFjaqf/9LAwNDNIwR/jV0ScgZh34ifAFer7ymgh9rVj69SSG3sTV0k\nN89h\r\n=y6g5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "f35dcd50f8427e32e9622f3f64bb01d917f15ffa", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Used in npm for command line application support", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"mkdirp": "^1.0.3", "graceful-fs": "^4.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "rimraf": "~2.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_4.0.0_1580109327958_0.8777105251047086", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "cmd-shim", "version": "4.0.1", "license": "ISC", "_id": "cmd-shim@4.0.1", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "mike<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "tap": {"after": "test/zz-cleanup.js", "before": "test/00-setup.js", "check-coverage": true}, "dist": {"shasum": "8853812cd7033fae6bae2763d2721231fc784424", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-exU/B+ts37psdPUgBhYZsHTGZ6kmZuy0i3L6+TG1BzvrQCfgc4VPpjnY4WxCz7tdMtgtCwXUIu7wLsTZ1LsIRg==", "signatures": [{"sig": "MEQCIHerS4wBVmaU5GWANg6+4+3jW2emuAVYahE2TYmveo4VAiAzoTmCDgVDy2/OPhlZtmkyDuFk/YDmQMWzNYaMkgxwiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeL1fNCRA9TVsSAnZWagAAL+wP/jZneppRPch0V6BDWN3y\noWdSK84PNRL0Sc/Wh3s7SqlIWlAYw2FfAsHsmT+yAjGdNCoTJjrgL0djrcDr\n9F05Eaa3Iym68vCUn8Jpqz1rhmdcFKtqgQY3Bxa79I4UXAJB+gNprmOdyh9H\nL1a7gYwRt9QVcGhhrLm50BdJfTAFZ4LwC/LEMMer4Y1ocHViYEqy4L8uPv2B\nLfeLv1tDQM6JJHpyKHjjAuT/sTtlsnp/dvdDKeUBviqPEvVSEXbx3H/CKLzZ\nolbWOXvI6CPL7U/yv01oYGwOSmL1371nWx723CG4kJ6eXNZkrF7Akce7IMWI\npPOKyI6cvSdNcdFzwXoqb6OWhMY1GkTiRZbaYbXA6uMUwTeIIv7dy7Vijo17\nONpBsGXmW1rkiEQOFpVS0DNxGTZRz8a3FuOy4xctbybbS5yVNt1ppFYK8yNv\ncA6YSgC1tvGx5MRSYoAm/Ldl+KW63HMdqk7gWb6cvGVE3ldYDuODn7q2STOZ\njH86ZImpGj4/6iM6t3cy4UMae9letVeN+KyDif0PtXtlCwxuWS5Co/KXxMJy\nkGRi84t/kvnvtVbsT0TQGW1fdPCfBeBNjUwh6D3HUgr1wKQmcMLlLTbRUuLI\nYKTlKW3MC9jRFMJVIFdxc69WiS4CFZcSRPLN+nqYvGH4zmoZ3+nOprp5pUTw\n5COD\r\n=WKgE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "4a799fbd4358dd1ae73e1bb2aa51811654743949", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Used in npm for command line application support", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp-infer-owner": "^1.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "rimraf": "~2.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_4.0.1_1580160973210_0.05277634182343771", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "cmd-shim", "version": "4.0.2", "license": "ISC", "_id": "cmd-shim@4.0.2", "maintainers": [{"name": "adam_baldwin", "email": "<EMAIL>"}, {"name": "clau<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "tap": {"after": "test/zz-cleanup.js", "before": "test/00-setup.js", "check-coverage": true}, "dist": {"shasum": "600c8b5962eea0cfd8cb809826b9584a59380c01", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-4.0.2.tgz", "fileCount": 5, "integrity": "sha512-yuOHOon6oFX6kcxVl2jIkvPJsQ/yiKp9fd2dnuoBRZB9GEJ3USWAFCIqfB4xmFou93C3MjjhAprcDwrw+O29VA==", "signatures": [{"sig": "MEQCIEUjX/i8cvmQ66zGJsVQdL75V09ckF0Y0Gz0AI5q8uTUAiAcj2IXZ+5AR9w18oO7JebEgIa1RBx3iFYK5aLJaMs3Sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCQTrCRA9TVsSAnZWagAAMoAP/2yHQw6tVPQpVlRxjLgP\nh+Y4NLVsdNdWm6QM3dFPVheh7Ad1iF/2ySWTTNW2vUbUojxrqQJCQysvwcoG\nFxO1hrq2SzEguXW/R1k4R4CYuZH187lY++wgDomgaTi96BiZmrkZyqAfrdy2\naOFwfZcHOSJZDPbFM0lHxzNNCdKpvD2gpSeXqR8tEpgqqkPtPKogpyjqqJK6\nEpSv9tZTOVhIzv3/Uty2E9pz5kZHsbZYoIGcq8kBG3JWxhB7F+QguVmz+YPY\n/caui/WpspcyiEtD7zlrxVrRcD/2TRWf2wH2trIjmkUJ/2aO1MsLbi1RRfDE\nwY9SSvpdtFzC7/HQQldUjglFtRSBSoVwMhR2/uMjWDIvdjSVYL39VqIWRDA6\npeGmEZC27HPhpZKiB/Pa6Rs4i7E/d7UnFV4AWM7JKAEHj3vTndz1Ml2Fm558\nxXMCJL1fXELvkey6NSgwbspEYZmPiG0D37YWyI+XPkHCf2cW7IuE1AaBdDgt\n7ZCrJaPZ9R8GXVV516nN9a71uFMj9jO+/1F/IaRiiDPP2qqKeLhscilmiZvy\nORbHsKyQLuJWX7hqdTDICe7J5rZrZtlEZ59edNItZ0/0V1xNO6WF170lvhg4\nMYztEucN6n7SZ8H6O/bvBDYOv9QWHf2aombo9/ZglyyCNFtmRPFkFb76vXD1\nfwzF\r\n=GOI0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "92c0a16d3c8a8c53ade76b5eae8efae01dd7f41f", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Used in npm for command line application support", "directories": {}, "_nodeVersion": "14.2.0", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp-infer-owner": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "rimraf": "~2.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_4.0.2_1594426603241_0.30368722643976276", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "cmd-shim", "version": "4.1.0", "license": "ISC", "_id": "cmd-shim@4.1.0", "maintainers": [{"name": "gar", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "tap": {"after": "test/zz-cleanup.js", "before": "test/00-setup.js", "check-coverage": true}, "dist": {"shasum": "b3a904a6743e9fede4148c6f3800bf2a08135bdd", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-lb9L7EM4I/ZRVuljLPEtUJOP+xiQVknZ4ZMpMgEp4JzNldPb27HU03hi6K1/6CoIuit/Zm/LQXySErFeXxDprw==", "signatures": [{"sig": "MEQCIAKiuFQZX/TN6BM+LpdoVYG4QzHh7uqU7NyGHVQh5b4CAiAaasp4ilVOZ6lR8zfnFB0vbmtwW4HaBTDadDSRBpV/Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHa6GCRA9TVsSAnZWagAAupYP+wQo1PhlgS4AmfLes9Ou\ngfL8lJqlZIb0aiB921OHSalz+DgHW9bpUipWN0ymVUh/3EKxNxzktchciPLl\nSbE7xAmb2cvwo0O8h7bVIRKbU67B+KC34Gl/DLZ1HxMJwOVCHBJ5LKWMh+rO\nVeflc8yE+xuSp9S9oF5guAFuAu+5DeIrfJ96LNRK86k9wUi/FiyD134QtQJ6\n7lG2bEKJYwXDMZuRfP2lbSCIkJQsqEHUPZoa5yZLIKArAiHpLUaG61tVyWR5\niPSQ9nLbMubcBkH8mvmM/JSh2r4XMGd0aJBN2APDxF3pCEF+gsh9orSLEP3t\nS/Ja7s9WL/+C7DK8otW+TYBKq3DJgqAMZrod60nFYYe3DNTap9LNaWNlHaPo\nkGiub+3oTZc9bMMCd7ijollU9XYAC/BmpRAEcuogyyc16orx9EEIw71Qy7Wh\nEsF7RUB6koDI1k6rGviL18Bw7efnuREX+MHEQ820F8u05QIqFkDa6kQdttyD\n8Zuf9RIQ2NstIBqxM9OnPw2e8YqkLRN96Zjla+TNoKWFLpdjQkVKPQ9fUKcq\nl0aQylldTWrlIztp0cqxNdkFTur8GVa1ZCKgSOH2P5suQ0XMD6Z2HpLyM/o9\nLack0VWT9EJRWH71GbhtIQ/mRUWvB7lOLJQfF+56jnl/9VeBPG3EMi8cF2A1\nIAFa\r\n=8z3q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "gitHead": "c5118da34126e6639361fe9706a5ff07e726ed45", "scripts": {"snap": "tap", "test": "tap", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "7.5.2", "description": "Used in npm for command line application support", "directories": {}, "_nodeVersion": "15.3.0", "dependencies": {"mkdirp-infer-owner": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.10.6", "rimraf": "~2.2.8"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_4.1.0_1612557958077_0.5882411286379068", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "cmd-shim", "version": "5.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "cmd-shim@5.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ruyadorno", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "tap": {"after": "test/zz-cleanup.js", "before": "test/00-setup.js", "check-coverage": true}, "dist": {"shasum": "8d0aaa1a6b0708630694c4dbde070ed94c707724", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-qkCtZ59BidfEwHltnJwkyVZn+XQojdAySM1D1gSeh11Z4pW1Kpolkyo53L5noc0nrxmIvyFwTmJRo4xs7FFLPw==", "signatures": [{"sig": "MEUCIQCVxNILfg0aAetw0qcMd0+qoN3s5P35UPjypGBCMyxbbwIgKa0AaNUDNIOqUtvEi7Q+MHzr+8V1p5yTHSSS7Nbxg40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTIG0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUhQ//YoDMAPPuRtd3ih0Onr9ERPltuLgGjMtnsDfQ1pK3MhF0TAPF\r\n72KVyqHl0IJExjfBOPSxUwU4LWErUso5RDfpU6MfyyYWaB4r33DOIcn80mvR\r\nPxYTJ5NR29DRg0d7bXl77FOIyJcMCVw33pv8EOQfMLqcpLQXqZiv8Sq5Bk5F\r\nYKnkUF5Ky/qncYmamaDJkoGABcj33ItXlWWgBD4nu6dCLUPtvkNj7iNWRi3w\r\nGAadR+Jh/8UzJdn7n+NHtMjUeau68vKR/BG+GwnjKGjXr6GgbA5kYICBZ7H/\r\nmw6SmoL0TdUM6bTO5r+YNWycg8bFFFDyM5WUpimNlqrK3V1sWpAiNtkJ4JtC\r\nxIcGVaXni1rGKYR3L7e4wUft0yh8ByA421Agxf23FV0fkTV9xLIaIvsKw0+C\r\nPaioaDGQBQ+rHT0QfMkrkHc84n48UhPoxK0ToSo/Sjsw1aEDi7VE+eohsJ9L\r\nixgcnpoy9a9hqXORUl33lMQ3YINXU32znLAawiEoZ/irz2O/coGBN7K6yv/9\r\nYuJ5NN2Zg03o6MDc6e2vbdUXK2NdcElWXbJBtZ0jzL9I/4NxpGU2L5gMA/Vu\r\nT8DUZJIfTTp97FRYh3vPz8xzloUc3WXBinKqje/77uDn4SezRHGmQ+XaBD5H\r\noiVlMjpW6U+MXvjRXcEt8Rcm/pAGLUAen+A=\r\n=QaBj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "gitHead": "c55b9a9a4cb3f321a9abad7d6d45e2aac2f82b08", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "gar", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Used in npm for command line application support", "directories": {}, "templateOSS": {"version": "3.2.2", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.14.2", "dependencies": {"mkdirp-infer-owner": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "rimraf": "^3.0.2", "@npmcli/template-oss": "3.2.2", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_5.0.0_1649181108588_0.1611595224579978", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "cmd-shim", "version": "6.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "cmd-shim@6.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "tap": {"after": "test/zz-cleanup.js", "before": "test/00-setup.js", "nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "6c4eb6437defacf0ae8e9d281d5b06749730a65b", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-wx+RWLgiSU6SCDzMtxG0Dv1lsuOcEfqq5SbqAViezaJIkR5sbveKzFU31YnWhqrJx3o3Iu3H0Rq8R00OS3oI+Q==", "signatures": [{"sig": "MEYCIQDl4/hVp/AHazDbS3FNPmvQBxvW/wmTSRou5VCVPRyG5AIhAI0Psqi/vkXpbwg/CW6Mh6HDer+14bY9E6FFWj0EKMjH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRbqNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqs5A/+NkuYyQB2DkdBJ1pjPg6WfqYUf6TzJquW2FunGCF8D6fEonmK\r\n83t3OYhfRm/HlQ0zEI3JKb6/j0ozppIeAKWHnZWvVEEwBF5uSftcaOqm+IB6\r\nzMVqDTaEk5mdOJbU/zsXSVYRYBHp8bmkpoJZUYSsel2fHmMdrkSJJLOyZv2L\r\nI8OEtdFVJuxGOj82YvusfIkv8qeU052dc0CSX0qrx0BFNIYzfe02RYBC8UsT\r\nrGRsPxqaYZZOBT077EoC+qT49hD753i4CbFdtSN3MDa4FNdOCq1+FtA80t5H\r\nacy15NXqzq5WfFb3RAQYpMQaKQzNpUlYjRjQc4smtbxnZ8GN/tIxbpju5Ydg\r\ns4ENleufVKdkYsXQiCa6iELUiZhbGDvq/oi8dUoABkVAiaF2OOdsXpf38zP9\r\nA8FB54AqrZdiqLzIbSt2+qdDk/NH5xsYmMJ2feWPjYZH38u/MpspfuPhMC+V\r\n/D3zWb9uEBKKatAwyqq7ogu2VVuqXV6TJIot8w0A4NllzNhBCXnlVt5XSD6X\r\n30TC8vRnmv34h2/jAX3A5ffxVqUhfJmhf0uAtXhNIFFxQ1v86IcbNY1WMNTQ\r\nhgA5/Q6oDiJgTznAyHLYKHZoERijF5/w9DPC1m34nwoj0Typ8Go3fXawtMFh\r\n0f3BxDhgLNkfVBvHmfltma2ynu2Wo4Wk6zs=\r\n=PRZJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "25724bfea794ebec14432278b1a4d223cc158f83", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "9.0.0-pre.4", "description": "Used in npm for command line application support", "directories": {}, "templateOSS": {"version": "4.5.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.5.1", "@npmcli/eslint-config": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_6.0.0_1665514125294_0.24720610067870985", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "cmd-shim", "version": "6.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "cmd-shim@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "tap": {"after": "test/zz-cleanup.js", "before": "test/00-setup.js", "nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "a65878080548e1dca760b3aea1e21ed05194da9d", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-6.0.1.tgz", "fileCount": 5, "integrity": "sha512-S9iI9y0nKR4hwEQsVWpyxld/6kRfGepGfzff83FcaiEBpmvlbA2nnGe7Cylgrx2f/p1P5S5wpRm9oL8z1PbS3Q==", "signatures": [{"sig": "MEUCIQD2xMeuWK/xm9Ew2UVIBiX2FTuWGfIjA7GGdfvuqDvbrgIgIfGTrJypvvfhAE8isJ+gMUQJz7PJEB6XUYxIg10wrXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmP0CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5lQ/+NHDPk8KAioVKH06Uy2LodvcjZxA27vkibYw2QV/gSLWNODM9\r\nzBbaKN8WkjtbBUedWijIYnHzOY/TKQRZ7tNON0PPmhgwl2k/KGqVhR0Z4ZW9\r\nyCgZe93J6oKicf2TkinBHojd8PN2fK2Q9euIVHYbtv10G+7geURG43leeWjv\r\n+1mCh89awPN+Vc5+t+FfTiyWqO7C/ODM/YdJLVKxrF6+PPd5+lkuWGup+OMr\r\nyUtzuBhviPSyQQzxmqcx4+cWADiQn5YX+Dg5AUHtmMtFm/entboH88D7Wsb5\r\nTYYAlN7s0+K4SujZbQOsxSyCQPVnMIzYSCw24KFlZY1eMr5KoT6mhzD75rRz\r\nRkSaXuxph0qSVXcJXM51jueaOdnZ3TGKLnJwtYJb9j7DB7thDdsQXlwJNI1t\r\nzGxXQ1FXh0YD3MOoUni6P34qTXVDf0Jp4sJFUT3drcqMM++ETA9QX5W1GwD4\r\nmB+z8ooSlT38mDffseRlap42B5iX5L42ojO2qTj6eumwBfiozqwNVFm4CxqD\r\nJQodYorJksspzPsIcvbvS8G9A0fuE2JofvTxxcZpHSPNPfu4x95mKVoq/BPQ\r\nB7mBdbYzoc00eO3/Gk/CZZCxvt2LThk0SG2mC4nvHeqyGAAXWotKqKfX1cUI\r\nDMQkDCgHyMuoDASr6lECA7FuZuI/O1Bic0s=\r\n=dyNH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Used in npm for command line application support", "directories": {}, "templateOSS": {"version": "4.11.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "16.16.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.11.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_6.0.1_1670970626685_0.3301687872715562", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "cmd-shim", "version": "6.0.2", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "cmd-shim@6.0.2", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "tap": {"after": "test/zz-cleanup.js", "before": "test/00-setup.js", "nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "435fd9e5c95340e61715e19f90209ed6fcd9e0a4", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-6.0.2.tgz", "fileCount": 5, "integrity": "sha512-+FFYbB0YLaAkhkcrjkyNLYDiOsFSfRjwjY19LXk/psmMx1z00xlCv7hhQoTGXXIKi+YXHL/iiFo8NqMVQX9nOw==", "signatures": [{"sig": "MEYCIQC+EY/0UXnXRf2Dy4YOntzlfIi5PPuF+4cqXGmWhdZQcAIhANd7Y93v8GpCt7pc7KDk6T4ByaNb5ufpWTsHs60eiq3E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cmd-shim@6.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 12087}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "e5327c0db02e434228e3f14b01adca5e48c7ecfb", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "10.2.0", "description": "Used in npm for command line application support", "directories": {}, "templateOSS": {"publish": true, "version": "4.19.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.19.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_6.0.2_1697653533209_0.900791846305252", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "cmd-shim", "version": "6.0.3", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "cmd-shim@6.0.3", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "tap": {"after": "test/zz-cleanup.js", "before": "test/00-setup.js", "nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "c491e9656594ba17ac83c4bd931590a9d6e26033", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-6.0.3.tgz", "fileCount": 5, "integrity": "sha512-FMabTRlc5t5zjdenF6mS0MBeFZm0XqHqeOkcskKFb/LYCcRQ5fVgLOHVc4Lq9CqABd9zhjwPjMBCJvMCziSVtA==", "signatures": [{"sig": "MEYCIQDwvId1BlBKaKRHKCau3S2wGTAAKvJ2fmzcOXaBH3JQRQIhAO3HF9nzvkK6nu/U8vKJcIevmkKGoiC39wnxb5Nov+mF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cmd-shim@6.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 12150}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "6283211cbd16527bbd575f6d1ce12b92333d696b", "scripts": {"lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Used in npm for command line application support", "directories": {}, "templateOSS": {"publish": true, "version": "4.22.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.22.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_6.0.3_1714784988262_0.9440454992305478", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "cmd-shim", "version": "7.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "cmd-shim@7.0.0", "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/cmd-shim#readme", "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "tap": {"after": "test/zz-cleanup.js", "before": "test/00-setup.js", "nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "23bcbf69fff52172f7e7c02374e18fb215826d95", "tarball": "https://registry.npmjs.org/cmd-shim/-/cmd-shim-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-rtpaCbr164TPPh+zFdkWpCyZuKkjpAzODfaZCf/SVJZzJN+4bHQb/LP3Jzq5/+84um3XXY8r548XiWKSborwVw==", "signatures": [{"sig": "MEQCIC1x4Ogdscd7KH7Gy3M/jJNC9umewuYs0Y8OIBjU3uuJAiA40ozBaWwaP2dJnWD7bDsMtfKgZkC5J8/3KGjtYSOF+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/cmd-shim@7.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 12172}, "main": "lib/index.js", "engines": {"node": "^18.17.0 || >=20.5.0"}, "gitHead": "693079b3c46fb7fec87cb5d976e21d266462fcfd", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Used in npm for command line application support", "directories": {}, "templateOSS": {"publish": true, "version": "4.23.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.7.0", "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.0.1", "@npmcli/template-oss": "4.23.1", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/cmd-shim_7.0.0_1724710409275_0.9781473268099024", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-03-31T18:09:50.236Z", "modified": "2025-05-14T20:03:30.575Z", "1.0.0": "2013-03-31T18:09:52.437Z", "1.1.0": "2013-04-03T10:48:28.936Z", "1.0.1": "2013-07-22T16:26:42.053Z", "1.1.1": "2013-08-25T16:27:13.538Z", "1.1.2": "2014-07-11T13:26:43.248Z", "2.0.0": "2014-08-02T01:42:19.433Z", "2.0.1": "2014-09-05T16:55:49.181Z", "2.0.2": "2016-02-10T15:02:50.083Z", "2.1.0": "2019-08-12T19:09:04.312Z", "3.0.0": "2019-08-14T19:41:18.055Z", "3.0.1": "2019-08-20T19:51:23.346Z", "3.0.2": "2019-08-21T00:05:38.181Z", "3.0.3": "2019-08-21T21:03:14.693Z", "4.0.0": "2020-01-27T07:15:28.093Z", "4.0.1": "2020-01-27T21:36:13.315Z", "4.0.2": "2020-07-11T00:16:43.346Z", "4.1.0": "2021-02-05T20:45:58.188Z", "5.0.0": "2022-04-05T17:51:48.706Z", "6.0.0": "2022-10-11T18:48:45.520Z", "6.0.1": "2022-12-13T22:30:26.898Z", "6.0.2": "2023-10-18T18:25:33.406Z", "6.0.3": "2024-05-04T01:09:48.406Z", "7.0.0": "2024-08-26T22:13:29.463Z"}, "bugs": {"url": "https://github.com/npm/cmd-shim/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/cmd-shim#readme", "repository": {"url": "git+https://github.com/npm/cmd-shim.git", "type": "git"}, "description": "Used in npm for command line application support", "maintainers": [{"email": "<EMAIL>", "name": "gar"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "owlstronaut"}], "readme": "# cmd-shim\n\nThe cmd-shim used in npm to create executable scripts on Windows,\nsince symlinks are not suitable for this purpose there.\n\nOn Unix systems, you should use a symbolic link instead.\n\n[![Build Status](https://img.shields.io/github/actions/workflow/status/npm/cmd-shim/ci.yml?branch=main)](https://github.com/npm/cmd-shim)\n[![Dependency Status](https://img.shields.io/librariesio/github/npm/cmd-shim)](https://libraries.io/npm/cmd-shim)\n[![npm version](https://img.shields.io/npm/v/cmd-shim.svg)](https://www.npmjs.com/package/cmd-shim)\n\n## Installation\n\n```\nnpm install cmd-shim\n```\n\n## API\n\n### cmdShim(from, to) -> Promise\n\nCreate a cmd shim at `to` for the command line program at `from`.\ne.g.\n\n```javascript\nvar cmdShim = require('cmd-shim');\ncmdShim(__dirname + '/cli.js', '/usr/bin/command-name').then(() => {\n  // shims are created!\n})\n```\n\n### cmdShim.ifExists(from, to) -> Promise\n\nThe same as above, but will just continue if the file does not exist.\n", "readmeFilename": "README.md", "users": {"maoizm": true, "heartnett": true, "mrvicadai": true, "domjtalbot": true}}