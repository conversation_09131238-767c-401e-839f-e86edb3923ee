# 数据库表结构修复指南

## 问题描述
当前应用在保存用户资料时出现错误：
```
Could not find the 'bio' column of 'profiles' in the schema cache
```

这是因为 Supabase 数据库中的 `profiles` 表缺少一些字段。

## 解决方案

### 方法 1：在 Supabase Dashboard 中执行 SQL（推荐）

1. 打开 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择你的项目
3. 点击左侧菜单的 "SQL Editor"
4. 复制并执行以下 SQL 语句：

```sql
-- 添加缺失的字段
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bio TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS location TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS avatar_url TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- 添加字段注释
COMMENT ON COLUMN profiles.bio IS '用户个人简介';
COMMENT ON COLUMN profiles.location IS '用户所在地';
COMMENT ON COLUMN profiles.avatar_url IS '用户头像URL';
COMMENT ON COLUMN profiles.updated_at IS '最后更新时间';
```

### 方法 2：使用完整的迁移脚本

如果你想要更完整的设置，可以执行项目根目录下的 `database-migration.sql` 文件中的所有语句。

### 方法 3：逐个添加字段

如果你只想添加特定字段，可以逐个执行：

```sql
-- 只添加 bio 字段
ALTER TABLE profiles ADD COLUMN bio TEXT;

-- 只添加 location 字段  
ALTER TABLE profiles ADD COLUMN location TEXT;

-- 只添加 avatar_url 字段
ALTER TABLE profiles ADD COLUMN avatar_url TEXT;
```

## 验证修复

执行以下 SQL 来验证字段是否添加成功：

```sql
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'profiles'
ORDER BY ordinal_position;
```

你应该看到类似这样的结果：
```
column_name  | data_type | is_nullable
-------------|-----------|------------
id           | uuid      | NO
username     | text      | YES
email        | text      | YES
bio          | text      | YES
location     | text      | YES
avatar_url   | text      | YES
created_at   | timestamp | YES
updated_at   | timestamp | YES
```

## 应用代码改进

我已经更新了 `AuthContext.tsx` 中的 `updateProfile` 函数，现在它会：

1. ✅ **智能字段过滤**：只更新存在的字段
2. ✅ **错误处理**：如果字段不存在，会提供具体的 SQL 修复语句
3. ✅ **降级处理**：如果某些字段失败，会尝试只更新基本字段
4. ✅ **详细日志**：在控制台显示详细的操作信息

## 测试

修复数据库后，尝试在个人资料页面保存信息，应该不再出现字段不存在的错误。

## 注意事项

- 使用 `IF NOT EXISTS` 确保重复执行不会出错
- 所有新字段都设置为 `TEXT` 类型，可以存储任意长度的文本
- `updated_at` 字段会自动设置当前时间作为默认值
- 建议在生产环境执行前先在开发环境测试
