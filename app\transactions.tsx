import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Receipt, CircleCheck as CheckCircle, X, Clock, Coins, User, Package, Calendar, TrendingUp, TrendingDown, Star } from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { useToast } from '@/contexts/ToastContext';

const { width, height } = Dimensions.get('window');

interface Transaction {
  id: string;
  type: 'purchase' | 'sale';
  buyerId: string;
  buyerName: string;
  sellerId: string;
  sellerName: string;
  productId: string;
  productName: string;
  productImage: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  status: 'pending' | 'completed' | 'cancelled';
  createdAt: string;
  shopName: string;
}

function TransactionsContent() {
  const { isDark } = useTheme();
  const { user, profile } = useAuth();
  const { showToast } = useToast();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  const [selectedFilter, setSelectedFilter] = useState<'all' | 'purchases' | 'sales'>('all');
  const [transactions, setTransactions] = useState<Transaction[]>([]);

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Load mock transactions
    loadTransactions();
  }, []);

  const loadTransactions = () => {
    // Mock transaction data
    const mockTransactions: Transaction[] = [
      {
        id: '1',
        type: 'purchase',
        buyerId: user?.id || '',
        buyerName: profile?.username || '当前用户',
        sellerId: 'seller1',
        sellerName: '草药师艾琳',
        productId: '1',
        productName: '治愈药剂',
        productImage: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
        quantity: 2,
        unitPrice: 150,
        totalAmount: 300,
        status: 'pending',
        createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        shopName: '香草药铺',
      },
      {
        id: '2',
        type: 'sale',
        buyerId: 'buyer1',
        buyerName: '冒险者约翰',
        sellerId: user?.id || '',
        sellerName: profile?.username || '当前用户',
        productId: '2',
        productName: '百里香精华',
        productImage: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
        quantity: 1,
        unitPrice: 300,
        totalAmount: 300,
        status: 'completed',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        shopName: '我的商店',
      },
      {
        id: '3',
        type: 'purchase',
        buyerId: user?.id || '',
        buyerName: profile?.username || '当前用户',
        sellerId: 'seller2',
        sellerName: '铁匠托马斯',
        productId: '3',
        productName: '精钢长剑',
        productImage: 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg',
        quantity: 1,
        unitPrice: 500,
        totalAmount: 500,
        status: 'completed',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
        shopName: '铁匠铺',
      },
      {
        id: '4',
        type: 'sale',
        buyerId: 'buyer2',
        buyerName: '法师艾米丽',
        sellerId: user?.id || '',
        sellerName: profile?.username || '当前用户',
        productId: '4',
        productName: '魔法卷轴',
        productImage: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
        quantity: 3,
        unitPrice: 80,
        totalAmount: 240,
        status: 'cancelled',
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(), // 2 days ago
        shopName: '我的商店',
      },
    ];

    setTransactions(mockTransactions);
  };

  const filteredTransactions = transactions.filter(transaction => {
    if (selectedFilter === 'purchases') {
      return transaction.type === 'purchase';
    }
    if (selectedFilter === 'sales') {
      return transaction.type === 'sale';
    }
    return true;
  });

  const handleUpdateStatus = (transactionId: string, newStatus: 'completed' | 'cancelled') => {
    setTransactions(prev => 
      prev.map(transaction => 
        transaction.id === transactionId 
          ? { ...transaction, status: newStatus }
          : transaction
      )
    );

    const statusText = newStatus === 'completed' ? '已完成' : '已取消';
    showToast(`订单${statusText}`, newStatus === 'completed' ? 'success' : 'error');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return colors.success;
      case 'pending': return colors.warning;
      case 'cancelled': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'pending': return '待处理';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return CheckCircle;
      case 'pending': return Clock;
      case 'cancelled': return X;
      default: return Clock;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `${diffInMinutes}分钟前`;
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}天前`;
    }
  };

  const getTotalStats = () => {
    const purchases = transactions.filter(t => t.type === 'purchase' && t.status === 'completed');
    const sales = transactions.filter(t => t.type === 'sale' && t.status === 'completed');
    
    const totalSpent = purchases.reduce((sum, t) => sum + t.totalAmount, 0);
    const totalEarned = sales.reduce((sum, t) => sum + t.totalAmount, 0);
    const netProfit = totalEarned - totalSpent;
    
    return { totalSpent, totalEarned, netProfit, totalTransactions: transactions.length };
  };

  const stats = getTotalStats();

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    header: {
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    merchantContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 20,
      width: '100%',
    },
    merchantFrame: {
      width: 70,
      height: 70,
      borderRadius: 35,
      borderWidth: 3,
      borderColor: colors.accent,
      overflow: 'hidden',
      position: 'relative',
      marginRight: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.5 : 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    merchantImage: {
      width: '100%',
      height: '100%',
    },
    receiptBadge: {
      position: 'absolute',
      bottom: -5,
      right: -5,
      backgroundColor: colors.accent,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.background,
    },
    merchantInfo: {
      flex: 1,
    },
    merchantTitle: {
      fontSize: 18,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 2,
    },
    merchantSubtitle: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    titleContainer: {
      alignItems: 'center',
    },
    mainTitle: {
      fontSize: 28,
      color: colors.text,
      textAlign: 'center',
      marginBottom: 8,
      fontWeight: '700',
      textShadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.2)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    titleUnderline: {
      width: 80,
      height: 3,
      backgroundColor: colors.accent,
      borderRadius: 2,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    filterContainer: {
      paddingHorizontal: 20,
      marginBottom: 15,
    },
    filterBar: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 12,
      backgroundColor: 'transparent',
    },
    filterButtonActive: {
      backgroundColor: colors.accent + '20',
    },
    filterText: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.textSecondary,
      marginLeft: 4,
    },
    filterTextActive: {
      color: colors.accent,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 30,
    },
    statsContainer: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    statsCard: {
      padding: 20,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
    },
    statsHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 16,
    },
    statsTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
      marginLeft: 8,
    },
    statsGrid: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
    },
    statItem: {
      alignItems: 'center',
    },
    statNumber: {
      fontSize: 16,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    statDivider: {
      width: 1,
      height: 30,
      backgroundColor: colors.border,
    },
    transactionsContainer: {
      paddingHorizontal: 20,
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    transactionCard: {
      marginBottom: 12,
      borderRadius: 16,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    transactionContent: {
      padding: 16,
    },
    transactionHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    transactionLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    transactionImage: {
      width: 50,
      height: 50,
      borderRadius: 8,
      marginRight: 12,
    },
    transactionInfo: {
      flex: 1,
    },
    transactionType: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4,
    },
    typeText: {
      fontSize: 12,
      fontWeight: '600',
      marginLeft: 4,
    },
    productName: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 2,
    },
    shopName: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    statusText: {
      fontSize: 12,
      fontWeight: '600',
      marginLeft: 4,
    },
    transactionDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    quantityPrice: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    detailText: {
      fontSize: 14,
      color: colors.textSecondary,
      marginRight: 16,
    },
    totalAmount: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    amountText: {
      fontSize: 16,
      fontWeight: '700',
      color: colors.text,
      marginLeft: 4,
    },
    transactionFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    participantInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    participantText: {
      fontSize: 12,
      color: colors.textSecondary,
      marginLeft: 4,
    },
    dateText: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    actionButtons: {
      flexDirection: 'row',
      marginTop: 12,
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    actionButton: {
      flex: 1,
      borderRadius: 8,
      overflow: 'hidden',
      marginHorizontal: 4,
    },
    actionButtonGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
    },
    actionButtonText: {
      color: colors.background,
      fontSize: 12,
      fontWeight: '600',
      marginLeft: 4,
    },
    emptyContainer: {
      alignItems: 'center',
      paddingVertical: 40,
    },
    emptyText: {
      fontSize: 16,
      color: colors.textSecondary,
      marginTop: 12,
      textAlign: 'center',
    },
  });

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{ 
          uri: isDark 
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary
          ]}
          style={dynamicStyles.overlay}
        >
          <SafeAreaView style={dynamicStyles.safeArea}>
            {/* Header */}
            <Animated.View 
              style={[
                dynamicStyles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
                }
              ]}
            >
              <View style={dynamicStyles.merchantContainer}>
                <View style={dynamicStyles.merchantFrame}>
                  <Image
                    source={{ uri: profile?.avatar_url || 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                    style={dynamicStyles.merchantImage}
                    resizeMode="cover"
                  />
                  <View style={dynamicStyles.receiptBadge}>
                    <Receipt size={16} color={colors.background} />
                  </View>
                </View>
                <View style={dynamicStyles.merchantInfo}>
                  <Text style={dynamicStyles.merchantTitle}>交易记录</Text>
                  <Text style={dynamicStyles.merchantSubtitle}>管理您的买卖记录</Text>
                </View>
              </View>

              <View style={dynamicStyles.titleContainer}>
                <Text style={dynamicStyles.mainTitle}>交易中心</Text>
                <View style={dynamicStyles.titleUnderline} />
                <Text style={dynamicStyles.subtitle}>查看和管理所有交易</Text>
              </View>
            </Animated.View>

            {/* Filter */}
            <Animated.View 
              style={[
                dynamicStyles.filterContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }]
                }
              ]}
            >
              <BlurView intensity={25} style={dynamicStyles.filterBar}>
                <TouchableOpacity
                  style={[
                    dynamicStyles.filterButton,
                    selectedFilter === 'all' && dynamicStyles.filterButtonActive
                  ]}
                  onPress={() => setSelectedFilter('all')}
                >
                  <Receipt size={16} color={selectedFilter === 'all' ? colors.accent : colors.textSecondary} />
                  <Text style={[
                    dynamicStyles.filterText,
                    selectedFilter === 'all' && dynamicStyles.filterTextActive
                  ]}>
                    全部
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    dynamicStyles.filterButton,
                    selectedFilter === 'purchases' && dynamicStyles.filterButtonActive
                  ]}
                  onPress={() => setSelectedFilter('purchases')}
                >
                  <TrendingDown size={16} color={selectedFilter === 'purchases' ? colors.accent : colors.textSecondary} />
                  <Text style={[
                    dynamicStyles.filterText,
                    selectedFilter === 'purchases' && dynamicStyles.filterTextActive
                  ]}>
                    购买
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    dynamicStyles.filterButton,
                    selectedFilter === 'sales' && dynamicStyles.filterButtonActive
                  ]}
                  onPress={() => setSelectedFilter('sales')}
                >
                  <TrendingUp size={16} color={selectedFilter === 'sales' ? colors.accent : colors.textSecondary} />
                  <Text style={[
                    dynamicStyles.filterText,
                    selectedFilter === 'sales' && dynamicStyles.filterTextActive
                  ]}>
                    销售
                  </Text>
                </TouchableOpacity>
              </BlurView>
            </Animated.View>

            <ScrollView 
              style={dynamicStyles.scrollView} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={dynamicStyles.scrollContent}
            >
              {/* Stats */}
              <Animated.View 
                style={[
                  dynamicStyles.statsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <BlurView intensity={30} style={dynamicStyles.statsCard}>
                  <View style={dynamicStyles.statsHeader}>
                    <Star size={24} color={colors.accent} />
                    <Text style={dynamicStyles.statsTitle}>交易统计</Text>
                  </View>
                  <View style={dynamicStyles.statsGrid}>
                    <View style={dynamicStyles.statItem}>
                      <Text style={[dynamicStyles.statNumber, { color: colors.error }]}>
                        {stats.totalSpent}
                      </Text>
                      <Text style={dynamicStyles.statLabel}>总支出</Text>
                    </View>
                    <View style={dynamicStyles.statDivider} />
                    <View style={dynamicStyles.statItem}>
                      <Text style={[dynamicStyles.statNumber, { color: colors.success }]}>
                        {stats.totalEarned}
                      </Text>
                      <Text style={dynamicStyles.statLabel}>总收入</Text>
                    </View>
                    <View style={dynamicStyles.statDivider} />
                    <View style={dynamicStyles.statItem}>
                      <Text style={[
                        dynamicStyles.statNumber, 
                        { color: stats.netProfit >= 0 ? colors.success : colors.error }
                      ]}>
                        {stats.netProfit >= 0 ? '+' : ''}{stats.netProfit}
                      </Text>
                      <Text style={dynamicStyles.statLabel}>净利润</Text>
                    </View>
                  </View>
                </BlurView>
              </Animated.View>

              {/* Transactions List */}
              <Animated.View 
                style={[
                  dynamicStyles.transactionsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <Text style={dynamicStyles.sectionTitle}>交易记录</Text>
                
                {filteredTransactions.length === 0 ? (
                  <BlurView intensity={30} style={dynamicStyles.transactionCard}>
                    <View style={dynamicStyles.emptyContainer}>
                      <Receipt size={60} color={colors.textSecondary} />
                      <Text style={dynamicStyles.emptyText}>暂无交易记录</Text>
                    </View>
                  </BlurView>
                ) : (
                  filteredTransactions.map((transaction) => {
                    const StatusIcon = getStatusIcon(transaction.status);
                    const isUserBuyer = transaction.type === 'purchase';
                    const otherParty = isUserBuyer ? transaction.sellerName : transaction.buyerName;
                    
                    return (
                      <BlurView key={transaction.id} intensity={30} style={dynamicStyles.transactionCard}>
                        <View style={dynamicStyles.transactionContent}>
                          {/* Header */}
                          <View style={dynamicStyles.transactionHeader}>
                            <View style={dynamicStyles.transactionLeft}>
                              <Image
                                source={{ uri: transaction.productImage }}
                                style={dynamicStyles.transactionImage}
                                resizeMode="cover"
                              />
                              <View style={dynamicStyles.transactionInfo}>
                                <View style={dynamicStyles.transactionType}>
                                  {transaction.type === 'purchase' ? 
                                    <TrendingDown size={14} color={colors.error} /> :
                                    <TrendingUp size={14} color={colors.success} />
                                  }
                                  <Text style={[
                                    dynamicStyles.typeText,
                                    { color: transaction.type === 'purchase' ? colors.error : colors.success }
                                  ]}>
                                    {transaction.type === 'purchase' ? '购买' : '销售'}
                                  </Text>
                                </View>
                                <Text style={dynamicStyles.productName}>{transaction.productName}</Text>
                                <Text style={dynamicStyles.shopName}>{transaction.shopName}</Text>
                              </View>
                            </View>
                            <View style={[
                              dynamicStyles.statusBadge,
                              { backgroundColor: `${getStatusColor(transaction.status)}20` }
                            ]}>
                              <StatusIcon size={14} color={getStatusColor(transaction.status)} />
                              <Text style={[
                                dynamicStyles.statusText,
                                { color: getStatusColor(transaction.status) }
                              ]}>
                                {getStatusText(transaction.status)}
                              </Text>
                            </View>
                          </View>

                          {/* Details */}
                          <View style={dynamicStyles.transactionDetails}>
                            <View style={dynamicStyles.quantityPrice}>
                              <Text style={dynamicStyles.detailText}>
                                数量: {transaction.quantity}
                              </Text>
                              <Text style={dynamicStyles.detailText}>
                                单价: {transaction.unitPrice}
                              </Text>
                            </View>
                            <View style={dynamicStyles.totalAmount}>
                              <Coins size={16} color={colors.warning} />
                              <Text style={dynamicStyles.amountText}>{transaction.totalAmount}</Text>
                            </View>
                          </View>

                          {/* Footer */}
                          <View style={dynamicStyles.transactionFooter}>
                            <View style={dynamicStyles.participantInfo}>
                              <User size={14} color={colors.textSecondary} />
                              <Text style={dynamicStyles.participantText}>
                                {isUserBuyer ? `卖家: ${otherParty}` : `买家: ${otherParty}`}
                              </Text>
                            </View>
                            <Text style={dynamicStyles.dateText}>
                              {formatDate(transaction.createdAt)}
                            </Text>
                          </View>

                          {/* Action Buttons for pending transactions */}
                          {transaction.status === 'pending' && transaction.type === 'sale' && (
                            <View style={dynamicStyles.actionButtons}>
                              <TouchableOpacity
                                style={dynamicStyles.actionButton}
                                onPress={() => handleUpdateStatus(transaction.id, 'completed')}
                                activeOpacity={0.8}
                              >
                                <LinearGradient
                                  colors={[colors.success, colors.secondary]}
                                  style={dynamicStyles.actionButtonGradient}
                                >
                                  <CheckCircle size={14} color={colors.background} />
                                  <Text style={dynamicStyles.actionButtonText}>完成</Text>
                                </LinearGradient>
                              </TouchableOpacity>
                              <TouchableOpacity
                                style={dynamicStyles.actionButton}
                                onPress={() => handleUpdateStatus(transaction.id, 'cancelled')}
                                activeOpacity={0.8}
                              >
                                <LinearGradient
                                  colors={[colors.error, '#FF6B6B']}
                                  style={dynamicStyles.actionButtonGradient}
                                >
                                  <X size={14} color={colors.background} />
                                  <Text style={dynamicStyles.actionButtonText}>取消</Text>
                                </LinearGradient>
                              </TouchableOpacity>
                            </View>
                          )}
                        </View>
                      </BlurView>
                    );
                  })
                )}
              </Animated.View>
            </ScrollView>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

export default function TransactionsScreen() {
  return (
    <ProtectedRoute>
      <TransactionsContent />
    </ProtectedRoute>
  );
}