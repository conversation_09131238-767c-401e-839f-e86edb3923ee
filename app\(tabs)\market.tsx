import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ImageBackground,
  TouchableOpacity,
  Dimensions,
  Animated,
  Image,
  SafeAreaView,
  Platform,
  Modal,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { MapPin, Navigation, Store, Eye, EyeOff, Clock, Star, Coins, ShoppingCart, X, Plus, Minus, CircleCheck as CheckCircle } from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';

const { width, height } = Dimensions.get('window');

// Web-only imports
let MapContainer: any = null;
let TileLayer: any = null;
let Marker: any = null;
let Popup: any = null;
let L: any = null;

if (Platform.OS === 'web') {
  try {
    const leaflet = require('react-leaflet');
    MapContainer = leaflet.MapContainer;
    TileLayer = leaflet.TileLayer;
    Marker = leaflet.Marker;
    Popup = leaflet.Popup;
    L = require('leaflet');
    
    // Fix for default markers in Leaflet
    delete (L.Icon.Default.prototype as any)._getIconUrl;
    L.Icon.Default.mergeOptions({
      iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
      iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
    });
  } catch (error) {
    console.warn('Leaflet not available:', error);
  }
}

interface Shop {
  id: string;
  name: string;
  description: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  isOpen: boolean;
  ownerId: string;
  ownerName: string;
  rating: number;
  products: Product[];
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  imageUrl: string;
  category: string;
  inStock: boolean;
  shopId: string;
}

interface CartItem {
  product: Product;
  quantity: number;
  shopName: string;
}

interface Order {
  id: string;
  buyerId: string;
  sellerId: string;
  items: CartItem[];
  totalAmount: number;
  status: 'pending' | 'completed' | 'cancelled';
  createdAt: string;
}

function MarketContent() {
  const { isDark } = useTheme();
  const { user, profile } = useAuth();
  const colors = getColors(isDark);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  const [selectedShop, setSelectedShop] = useState<Shop | null>(null);
  const [showShopModal, setShowShopModal] = useState(false);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [showCartModal, setShowCartModal] = useState(false);
  const [orders, setOrders] = useState<Order[]>([]);

  useEffect(() => {
    // Animation sequence
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Mock shops data
  const shops: Shop[] = [
    {
      id: '1',
      name: '香草药铺',
      description: '专营各种珍贵草药和治愈药剂',
      location: {
        latitude: 54.2781,
        longitude: -0.4053,
        address: '斯卡布罗集市东区第三街',
      },
      isOpen: true,
      ownerId: 'owner1',
      ownerName: '草药师艾琳',
      rating: 4.8,
      products: [
        {
          id: '1',
          name: '治愈药剂',
          description: '由新鲜香芹制成的强效治愈药剂',
          price: 150,
          imageUrl: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
          category: 'potions',
          inStock: true,
          shopId: '1',
        },
        {
          id: '2',
          name: '百里香精华',
          description: '珍贵的百里香精华，提升魔法抗性',
          price: 300,
          imageUrl: 'https://images.pexels.com/photos/4021775/pexels-photo-4021775.jpeg',
          category: 'herbs',
          inStock: true,
          shopId: '1',
        },
      ],
    },
    {
      id: '2',
      name: '铁匠铺',
      description: '制作精良的武器和护甲',
      location: {
        latitude: 54.2891,
        longitude: -0.3721,
        address: '斯卡布罗集市西区铁匠街',
      },
      isOpen: true,
      ownerId: 'owner2',
      ownerName: '铁匠托马斯',
      rating: 4.6,
      products: [
        {
          id: '3',
          name: '精钢长剑',
          description: '锋利的精钢长剑，适合战士使用',
          price: 500,
          imageUrl: 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg',
          category: 'weapons',
          inStock: true,
          shopId: '2',
        },
        {
          id: '4',
          name: '皮甲护胸',
          description: '柔软的皮制护甲，提供基础防护',
          price: 220,
          imageUrl: 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg',
          category: 'armor',
          inStock: true,
          shopId: '2',
        },
      ],
    },
    {
      id: '3',
      name: '珍宝阁',
      description: '收藏各种稀有宝石和装饰品',
      location: {
        latitude: 54.2567,
        longitude: -0.3945,
        address: '斯卡布罗集市中央广场',
      },
      isOpen: false, // Closed shop
      ownerId: 'owner3',
      ownerName: '珠宝商莉莉安',
      rating: 4.9,
      products: [
        {
          id: '5',
          name: '水晶项链',
          description: '闪闪发光的水晶项链',
          price: 250,
          imageUrl: 'https://images.pexels.com/photos/1191710/pexels-photo-1191710.jpeg',
          category: 'jewelry',
          inStock: true,
          shopId: '3',
        },
      ],
    },
  ];

  const openShops = shops.filter(shop => shop.isOpen);

  const createCustomIcon = (shopName: string) => {
    if (Platform.OS !== 'web' || !L) return null;
    
    const iconHtml = `
      <div style="
        background-color: #4A7043;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 3px solid #F5E8C7;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: white;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        cursor: pointer;
      ">
        🏪
      </div>
    `;
    
    return L.divIcon({
      html: iconHtml,
      className: 'custom-shop-marker',
      iconSize: [40, 40],
      iconAnchor: [20, 20],
    });
  };

  const handleShopSelect = (shop: Shop) => {
    setSelectedShop(shop);
    setShowShopModal(true);
  };

  const addToCart = (product: Product, shopName: string) => {
    const existingItem = cart.find(item => item.product.id === product.id);
    
    if (existingItem) {
      setCart(cart.map(item => 
        item.product.id === product.id 
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { product, quantity: 1, shopName }]);
    }
    
    Alert.alert('成功', `${product.name} 已添加到购物车！`);
  };

  const updateCartQuantity = (productId: string, change: number) => {
    setCart(cart.map(item => {
      if (item.product.id === productId) {
        const newQuantity = item.quantity + change;
        return newQuantity > 0 ? { ...item, quantity: newQuantity } : item;
      }
      return item;
    }).filter(item => item.quantity > 0));
  };

  const removeFromCart = (productId: string) => {
    setCart(cart.filter(item => item.product.id !== productId));
  };

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + (item.product.price * item.quantity), 0);
  };

  const handleCheckout = () => {
    if (cart.length === 0) {
      Alert.alert('错误', '购物车为空！');
      return;
    }

    const newOrder: Order = {
      id: Date.now().toString(),
      buyerId: user?.id || '',
      sellerId: cart[0]?.product.shopId || '',
      items: [...cart],
      totalAmount: getTotalAmount(),
      status: 'pending',
      createdAt: new Date().toISOString(),
    };

    setOrders([...orders, newOrder]);
    setCart([]);
    setShowCartModal(false);
    Alert.alert('成功', '订单已提交！请等待商家确认。');
  };

  const renderMap = () => {
    if (Platform.OS !== 'web' || !MapContainer) {
      return (
        <View style={styles.mapFallback}>
          <Navigation size={40} color={colors.secondary} />
          <Text style={styles.mapFallbackText}>地图功能仅在网页版可用</Text>
          <Text style={styles.mapFallbackSubtext}>请在浏览器中查看完整地图体验</Text>
        </View>
      );
    }

    return (
      <MapContainer
        center={[54.2781, -0.4053]} // Scarborough area
        zoom={13}
        style={{ height: '100%', width: '100%', borderRadius: '12px' }}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />
        {openShops.map((shop) => (
          <Marker
            key={shop.id}
            position={[shop.location.latitude, shop.location.longitude]}
            icon={createCustomIcon(shop.name)}
            eventHandlers={{
              click: () => handleShopSelect(shop),
            }}
          >
            <Popup>
              <div style={{ textAlign: 'center', minWidth: '200px' }}>
                <h3 style={{ margin: '0 0 8px 0', color: '#4A7043' }}>{shop.name}</h3>
                <p style={{ margin: '0 0 8px 0', fontSize: '12px', color: '#8B7355' }}>
                  {shop.description}
                </p>
                <p style={{ margin: '0 0 8px 0', fontSize: '11px', color: '#8B7355' }}>
                  店主: {shop.ownerName}
                </p>
                <div style={{ 
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '4px',
                  marginBottom: '8px'
                }}>
                  <span style={{ color: '#D4A017' }}>⭐</span>
                  <span style={{ fontSize: '12px', color: '#8B7355' }}>{shop.rating}</span>
                </div>
                <div style={{ 
                  display: 'inline-block',
                  padding: '4px 8px',
                  backgroundColor: shop.isOpen ? '#4A704320' : '#DC354520',
                  borderRadius: '6px',
                  fontSize: '10px',
                  color: shop.isOpen ? '#4A7043' : '#DC3545',
                  fontWeight: 'bold'
                }}>
                  {shop.isOpen ? '营业中' : '已关闭'}
                </div>
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    );
  };

  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
    },
    backgroundImage: {
      flex: 1,
      width: width,
      height: height,
    },
    overlay: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    header: {
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    merchantContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 20,
      width: '100%',
    },
    merchantFrame: {
      width: 70,
      height: 70,
      borderRadius: 35,
      borderWidth: 3,
      borderColor: colors.secondary,
      overflow: 'hidden',
      position: 'relative',
      marginRight: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.5 : 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    merchantImage: {
      width: '100%',
      height: '100%',
    },
    mapBadge: {
      position: 'absolute',
      bottom: -5,
      right: -5,
      backgroundColor: colors.secondary,
      borderRadius: 12,
      width: 24,
      height: 24,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: colors.background,
    },
    merchantInfo: {
      flex: 1,
    },
    merchantTitle: {
      fontSize: 18,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 2,
    },
    merchantSubtitle: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    titleContainer: {
      alignItems: 'center',
    },
    mainTitle: {
      fontSize: 28,
      color: colors.text,
      textAlign: 'center',
      marginBottom: 8,
      fontWeight: '700',
      textShadowColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.2)',
      textShadowOffset: { width: 1, height: 1 },
      textShadowRadius: 3,
    },
    titleUnderline: {
      width: 80,
      height: 3,
      backgroundColor: colors.secondary,
      borderRadius: 2,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: colors.textSecondary,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    cartButton: {
      position: 'absolute',
      top: 20,
      right: 20,
      borderRadius: 25,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    cartButtonGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
    },
    cartBadge: {
      position: 'absolute',
      top: -5,
      right: -5,
      backgroundColor: colors.error,
      borderRadius: 10,
      minWidth: 20,
      height: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    cartBadgeText: {
      color: colors.background,
      fontSize: 12,
      fontWeight: '700',
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: 30,
    },
    mapContainer: {
      paddingHorizontal: 20,
      marginBottom: 20,
    },
    mapCard: {
      height: height * 0.4,
      borderRadius: 16,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    mapFallback: {
      height: '100%',
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 12,
    },
    mapFallbackText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginTop: 12,
      textAlign: 'center',
    },
    mapFallbackSubtext: {
      fontSize: 14,
      color: colors.textSecondary,
      marginTop: 4,
      textAlign: 'center',
    },
    shopsContainer: {
      paddingHorizontal: 20,
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    shopCard: {
      marginBottom: 12,
      borderRadius: 16,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    shopContent: {
      flexDirection: 'row',
      padding: 16,
    },
    shopIcon: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: colors.secondary + '20',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    shopInfo: {
      flex: 1,
    },
    shopHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 4,
    },
    shopName: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      flex: 1,
    },
    statusBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginLeft: 8,
    },
    statusText: {
      fontSize: 10,
      fontWeight: '600',
      marginLeft: 4,
    },
    shopDescription: {
      fontSize: 14,
      color: colors.textSecondary,
      marginBottom: 8,
    },
    shopFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    shopOwner: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    shopRating: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    ratingText: {
      fontSize: 12,
      color: colors.textSecondary,
      marginLeft: 4,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      width: width * 0.9,
      maxHeight: height * 0.8,
      borderRadius: 20,
      overflow: 'hidden',
    },
    modalContent: {
      padding: 20,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    modalTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: colors.text,
    },
    closeButton: {
      padding: 8,
    },
    shopModalInfo: {
      marginBottom: 20,
    },
    shopModalName: {
      fontSize: 24,
      fontWeight: '700',
      color: colors.text,
      marginBottom: 8,
    },
    shopModalDescription: {
      fontSize: 16,
      color: colors.textSecondary,
      marginBottom: 12,
      lineHeight: 22,
    },
    shopModalDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    shopModalOwner: {
      fontSize: 14,
      color: colors.textSecondary,
    },
    shopModalRating: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    productsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    productCard: {
      width: '48%',
      marginBottom: 12,
      borderRadius: 12,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: colors.border,
    },
    productImage: {
      width: '100%',
      height: 100,
    },
    productInfo: {
      padding: 12,
    },
    productName: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 4,
    },
    productPrice: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    priceText: {
      fontSize: 14,
      fontWeight: '700',
      color: colors.text,
      marginLeft: 4,
    },
    addToCartButton: {
      borderRadius: 8,
      overflow: 'hidden',
    },
    addToCartGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
    },
    addToCartText: {
      color: colors.background,
      fontSize: 12,
      fontWeight: '600',
      marginLeft: 4,
    },
    cartModalContent: {
      maxHeight: height * 0.6,
    },
    cartItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    cartItemImage: {
      width: 50,
      height: 50,
      borderRadius: 8,
      marginRight: 12,
    },
    cartItemInfo: {
      flex: 1,
    },
    cartItemName: {
      fontSize: 14,
      fontWeight: '600',
      color: colors.text,
      marginBottom: 4,
    },
    cartItemShop: {
      fontSize: 12,
      color: colors.textSecondary,
      marginBottom: 4,
    },
    cartItemPrice: {
      fontSize: 12,
      color: colors.textSecondary,
    },
    quantityControls: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    quantityButton: {
      width: 30,
      height: 30,
      borderRadius: 15,
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    quantityText: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
      marginHorizontal: 12,
    },
    cartTotal: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 16,
      borderTopWidth: 2,
      borderTopColor: colors.border,
      marginTop: 16,
    },
    totalLabel: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text,
    },
    totalAmount: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    totalText: {
      fontSize: 20,
      fontWeight: '700',
      color: colors.text,
      marginLeft: 8,
    },
    checkoutButton: {
      borderRadius: 12,
      overflow: 'hidden',
      marginTop: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 8,
    },
    checkoutGradient: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
      paddingHorizontal: 24,
    },
    checkoutText: {
      color: colors.background,
      fontSize: 18,
      fontWeight: '700',
      marginLeft: 8,
    },
    emptyCart: {
      alignItems: 'center',
      paddingVertical: 40,
    },
    emptyCartText: {
      fontSize: 16,
      color: colors.textSecondary,
      marginTop: 12,
    },
  });

  return (
    <View style={dynamicStyles.container}>
      <ImageBackground
        source={{ 
          uri: isDark 
            ? 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
            : 'https://images.pexels.com/photos/1666021/pexels-photo-1666021.jpeg' 
        }}
        style={dynamicStyles.backgroundImage}
        resizeMode="cover"
      >
        <LinearGradient
          colors={[
            colors.backgroundSecondary,
            colors.backgroundTertiary,
            colors.backgroundSecondary
          ]}
          style={dynamicStyles.overlay}
        >
          <SafeAreaView style={dynamicStyles.safeArea}>
            {/* Header */}
            <Animated.View 
              style={[
                dynamicStyles.header,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideAnim }, { scale: scaleAnim }]
                }
              ]}
            >
              <View style={dynamicStyles.merchantContainer}>
                <View style={dynamicStyles.merchantFrame}>
                  <Image
                    source={{ uri: profile?.avatar_url || 'https://images.pexels.com/photos/8728380/pexels-photo-8728380.jpeg' }}
                    style={dynamicStyles.merchantImage}
                    resizeMode="cover"
                  />
                  <View style={dynamicStyles.mapBadge}>
                    <MapPin size={16} color={colors.background} />
                  </View>
                </View>
                <View style={dynamicStyles.merchantInfo}>
                  <Text style={dynamicStyles.merchantTitle}>集市地图</Text>
                  <Text style={dynamicStyles.merchantSubtitle}>探索开放的商店</Text>
                </View>
              </View>

              <View style={dynamicStyles.titleContainer}>
                <Text style={dynamicStyles.mainTitle}>斯卡布罗市场</Text>
                <View style={dynamicStyles.titleUnderline} />
                <Text style={dynamicStyles.subtitle}>发现营业中的商店和商品</Text>
              </View>

              {/* Cart Button */}
              <TouchableOpacity
                style={dynamicStyles.cartButton}
                onPress={() => setShowCartModal(true)}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={[colors.accent, colors.warning]}
                  style={dynamicStyles.cartButtonGradient}
                >
                  <ShoppingCart size={20} color={colors.background} />
                  {cart.length > 0 && (
                    <View style={dynamicStyles.cartBadge}>
                      <Text style={dynamicStyles.cartBadgeText}>{cart.length}</Text>
                    </View>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            </Animated.View>

            <ScrollView 
              style={dynamicStyles.scrollView} 
              showsVerticalScrollIndicator={false}
              contentContainerStyle={dynamicStyles.scrollContent}
            >
              {/* Map */}
              <Animated.View 
                style={[
                  dynamicStyles.mapContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <BlurView intensity={30} style={dynamicStyles.mapCard}>
                  {renderMap()}
                </BlurView>
              </Animated.View>

              {/* Open Shops List */}
              <Animated.View 
                style={[
                  dynamicStyles.shopsContainer,
                  {
                    opacity: fadeAnim,
                    transform: [{ translateY: slideAnim }]
                  }
                ]}
              >
                <Text style={dynamicStyles.sectionTitle}>营业中的商店</Text>
                
                {openShops.map((shop) => (
                  <BlurView key={shop.id} intensity={30} style={dynamicStyles.shopCard}>
                    <TouchableOpacity 
                      style={dynamicStyles.shopContent}
                      onPress={() => handleShopSelect(shop)}
                      activeOpacity={0.8}
                    >
                      <View style={dynamicStyles.shopIcon}>
                        <Store size={30} color={colors.secondary} />
                      </View>
                      <View style={dynamicStyles.shopInfo}>
                        <View style={dynamicStyles.shopHeader}>
                          <Text style={dynamicStyles.shopName}>{shop.name}</Text>
                          <View style={[
                            dynamicStyles.statusBadge,
                            { backgroundColor: shop.isOpen ? colors.success + '20' : colors.error + '20' }
                          ]}>
                            {shop.isOpen ? <Eye size={12} color={colors.success} /> : <EyeOff size={12} color={colors.error} />}
                            <Text style={[
                              dynamicStyles.statusText,
                              { color: shop.isOpen ? colors.success : colors.error }
                            ]}>
                              {shop.isOpen ? '营业中' : '已关闭'}
                            </Text>
                          </View>
                        </View>
                        <Text style={dynamicStyles.shopDescription}>{shop.description}</Text>
                        <View style={dynamicStyles.shopFooter}>
                          <Text style={dynamicStyles.shopOwner}>店主: {shop.ownerName}</Text>
                          <View style={dynamicStyles.shopRating}>
                            <Star size={14} color={colors.warning} fill={colors.warning} />
                            <Text style={dynamicStyles.ratingText}>{shop.rating}</Text>
                          </View>
                        </View>
                      </View>
                    </TouchableOpacity>
                  </BlurView>
                ))}
              </Animated.View>
            </ScrollView>

            {/* Shop Details Modal */}
            <Modal
              visible={showShopModal}
              animationType="slide"
              transparent={true}
              onRequestClose={() => setShowShopModal(false)}
            >
              <View style={dynamicStyles.modalOverlay}>
                <BlurView intensity={50} style={dynamicStyles.modalContainer}>
                  <ScrollView style={dynamicStyles.modalContent} showsVerticalScrollIndicator={false}>
                    {selectedShop && (
                      <>
                        <View style={dynamicStyles.modalHeader}>
                          <Text style={dynamicStyles.modalTitle}>商店详情</Text>
                          <TouchableOpacity
                            style={dynamicStyles.closeButton}
                            onPress={() => setShowShopModal(false)}
                          >
                            <X size={24} color={colors.text} />
                          </TouchableOpacity>
                        </View>

                        <View style={dynamicStyles.shopModalInfo}>
                          <Text style={dynamicStyles.shopModalName}>{selectedShop.name}</Text>
                          <Text style={dynamicStyles.shopModalDescription}>{selectedShop.description}</Text>
                          <View style={dynamicStyles.shopModalDetails}>
                            <Text style={dynamicStyles.shopModalOwner}>店主: {selectedShop.ownerName}</Text>
                            <View style={dynamicStyles.shopModalRating}>
                              <Star size={16} color={colors.warning} fill={colors.warning} />
                              <Text style={dynamicStyles.ratingText}>{selectedShop.rating}</Text>
                            </View>
                          </View>
                        </View>

                        <Text style={dynamicStyles.sectionTitle}>商品列表</Text>
                        <View style={dynamicStyles.productsGrid}>
                          {selectedShop.products.map((product) => (
                            <BlurView key={product.id} intensity={20} style={dynamicStyles.productCard}>
                              <Image
                                source={{ uri: product.imageUrl }}
                                style={dynamicStyles.productImage}
                                resizeMode="cover"
                              />
                              <View style={dynamicStyles.productInfo}>
                                <Text style={dynamicStyles.productName}>{product.name}</Text>
                                <View style={dynamicStyles.productPrice}>
                                  <Coins size={14} color={colors.warning} />
                                  <Text style={dynamicStyles.priceText}>{product.price}</Text>
                                </View>
                                <TouchableOpacity
                                  style={dynamicStyles.addToCartButton}
                                  onPress={() => addToCart(product, selectedShop.name)}
                                  activeOpacity={0.8}
                                >
                                  <LinearGradient
                                    colors={[colors.secondary, colors.accent]}
                                    style={dynamicStyles.addToCartGradient}
                                  >
                                    <ShoppingCart size={14} color={colors.background} />
                                    <Text style={dynamicStyles.addToCartText}>加入购物车</Text>
                                  </LinearGradient>
                                </TouchableOpacity>
                              </View>
                            </BlurView>
                          ))}
                        </View>
                      </>
                    )}
                  </ScrollView>
                </BlurView>
              </View>
            </Modal>

            {/* Cart Modal */}
            <Modal
              visible={showCartModal}
              animationType="slide"
              transparent={true}
              onRequestClose={() => setShowCartModal(false)}
            >
              <View style={dynamicStyles.modalOverlay}>
                <BlurView intensity={50} style={dynamicStyles.modalContainer}>
                  <View style={dynamicStyles.modalContent}>
                    <View style={dynamicStyles.modalHeader}>
                      <Text style={dynamicStyles.modalTitle}>购物车</Text>
                      <TouchableOpacity
                        style={dynamicStyles.closeButton}
                        onPress={() => setShowCartModal(false)}
                      >
                        <X size={24} color={colors.text} />
                      </TouchableOpacity>
                    </View>

                    {cart.length === 0 ? (
                      <View style={dynamicStyles.emptyCart}>
                        <ShoppingCart size={60} color={colors.textSecondary} />
                        <Text style={dynamicStyles.emptyCartText}>购物车为空</Text>
                      </View>
                    ) : (
                      <>
                        <ScrollView style={dynamicStyles.cartModalContent} showsVerticalScrollIndicator={false}>
                          {cart.map((item) => (
                            <View key={item.product.id} style={dynamicStyles.cartItem}>
                              <Image
                                source={{ uri: item.product.imageUrl }}
                                style={dynamicStyles.cartItemImage}
                                resizeMode="cover"
                              />
                              <View style={dynamicStyles.cartItemInfo}>
                                <Text style={dynamicStyles.cartItemName}>{item.product.name}</Text>
                                <Text style={dynamicStyles.cartItemShop}>{item.shopName}</Text>
                                <Text style={dynamicStyles.cartItemPrice}>
                                  {item.product.price} × {item.quantity} = {item.product.price * item.quantity} 金币
                                </Text>
                              </View>
                              <View style={dynamicStyles.quantityControls}>
                                <TouchableOpacity
                                  style={dynamicStyles.quantityButton}
                                  onPress={() => updateCartQuantity(item.product.id, -1)}
                                >
                                  <Minus size={16} color={colors.text} />
                                </TouchableOpacity>
                                <Text style={dynamicStyles.quantityText}>{item.quantity}</Text>
                                <TouchableOpacity
                                  style={dynamicStyles.quantityButton}
                                  onPress={() => updateCartQuantity(item.product.id, 1)}
                                >
                                  <Plus size={16} color={colors.text} />
                                </TouchableOpacity>
                              </View>
                            </View>
                          ))}
                        </ScrollView>

                        <View style={dynamicStyles.cartTotal}>
                          <Text style={dynamicStyles.totalLabel}>总计:</Text>
                          <View style={dynamicStyles.totalAmount}>
                            <Coins size={20} color={colors.warning} />
                            <Text style={dynamicStyles.totalText}>{getTotalAmount()}</Text>
                          </View>
                        </View>

                        <TouchableOpacity
                          style={dynamicStyles.checkoutButton}
                          onPress={handleCheckout}
                          activeOpacity={0.8}
                        >
                          <LinearGradient
                            colors={[colors.success, colors.secondary]}
                            style={dynamicStyles.checkoutGradient}
                          >
                            <CheckCircle size={20} color={colors.background} />
                            <Text style={dynamicStyles.checkoutText}>结算订单</Text>
                          </LinearGradient>
                        </TouchableOpacity>
                      </>
                    )}
                  </View>
                </BlurView>
              </View>
            </Modal>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

const styles = StyleSheet.create({
  mapFallback: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapFallbackText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 12,
    textAlign: 'center',
  },
  mapFallbackSubtext: {
    fontSize: 14,
    marginTop: 4,
    textAlign: 'center',
  },
});

export default function MarketScreen() {
  return (
    <ProtectedRoute>
      <MarketContent />
    </ProtectedRoute>
  );
}