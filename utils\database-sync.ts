import { supabase } from '@/lib/supabase';

/**
 * 自动同步数据库表结构
 * 检查并添加缺失的字段
 */
export async function syncDatabaseSchema() {
  console.log('🔄 开始同步数据库表结构...');

  try {
    // 检查 profiles 表的字段
    await checkAndAddProfileFields();
    
    console.log('✅ 数据库表结构同步完成');
    return true;
  } catch (error) {
    console.error('❌ 数据库表结构同步失败:', error);
    return false;
  }
}

/**
 * 检查并添加 profiles 表的字段
 */
async function checkAndAddProfileFields() {
  const requiredFields = [
    { name: 'bio', type: 'TEXT', comment: '用户个人简介' },
    { name: 'location', type: 'TEXT', comment: '用户所在地' },
    { name: 'avatar_url', type: 'TEXT', comment: '用户头像URL' },
  ];

  for (const field of requiredFields) {
    await checkAndAddField('profiles', field);
  }
}

/**
 * 检查并添加单个字段
 */
async function checkAndAddField(
  tableName: string, 
  field: { name: string; type: string; comment: string }
) {
  try {
    // 尝试查询该字段，如果不存在会报错
    const { error } = await supabase
      .from(tableName)
      .select(field.name)
      .limit(1);

    if (error && error.message.includes(`column "${field.name}" does not exist`)) {
      console.log(`➕ 字段 ${field.name} 不存在，需要添加`);
      
      // 提供 SQL 语句供手动执行
      const sql = `ALTER TABLE ${tableName} ADD COLUMN ${field.name} ${field.type};`;
      console.log(`📝 请在 Supabase Dashboard 的 SQL Editor 中执行:`);
      console.log(`   ${sql}`);
      console.log(`   COMMENT ON COLUMN ${tableName}.${field.name} IS '${field.comment}';`);
      
      return false;
    } else if (error) {
      console.log(`⚠️  检查字段 ${field.name} 时出错:`, error.message);
      return false;
    } else {
      console.log(`✅ 字段 ${field.name} 已存在`);
      return true;
    }
  } catch (error) {
    console.error(`❌ 检查字段 ${field.name} 失败:`, error);
    return false;
  }
}

/**
 * 获取表的所有字段信息
 */
export async function getTableSchema(tableName: string) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);

    if (error) {
      console.error(`❌ 获取表 ${tableName} 结构失败:`, error);
      return null;
    }

    if (data && data.length > 0) {
      const fields = Object.keys(data[0]);
      console.log(`📊 表 ${tableName} 的字段:`, fields);
      return fields;
    } else {
      console.log(`📊 表 ${tableName} 为空，无法获取字段信息`);
      return [];
    }
  } catch (error) {
    console.error(`❌ 获取表 ${tableName} 结构失败:`, error);
    return null;
  }
}

/**
 * 验证用户数据是否符合表结构
 */
export function validateProfileData(data: any) {
  const requiredFields = ['username', 'email'];
  const optionalFields = ['bio', 'location', 'avatar_url'];
  const allFields = [...requiredFields, ...optionalFields];

  const errors: string[] = [];

  // 检查必需字段
  for (const field of requiredFields) {
    if (!data[field]) {
      errors.push(`缺少必需字段: ${field}`);
    }
  }

  // 检查是否有未知字段
  for (const key in data) {
    if (!allFields.includes(key) && key !== 'updated_at' && key !== 'id') {
      console.warn(`⚠️  未知字段: ${key}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    validData: Object.fromEntries(
      Object.entries(data).filter(([key]) => 
        allFields.includes(key) || key === 'updated_at' || key === 'id'
      )
    )
  };
}
