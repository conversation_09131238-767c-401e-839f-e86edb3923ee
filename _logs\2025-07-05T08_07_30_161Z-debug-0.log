0 verbose cli E:\Node\node.exe e:\Node\node_modules\npm\bin\npm-cli.js
1 info using npm@10.7.0
2 info using node@v20.15.1
3 silly config:load:file:E:\Nvm\nvm\v20.15.1\node_modules\npm\npmrc
4 silly config:load:file:C:\Users\<USER>\.npmrc
5 silly config:load:file:e:\Node\etc\npmrc
6 verbose title npm install supabase
7 verbose argv "install" "--global" "supabase"
8 verbose logfile logs-max:10 dir:d:\.0000\scarboroughfair\_logs\2025-07-05T08_07_30_161Z-
9 verbose logfile d:\.0000\scarboroughfair\_logs\2025-07-05T08_07_30_161Z-debug-0.log
10 silly logfile start cleaning logs, removing 1 files
11 silly logfile done cleaning log files
12 silly idealTree buildDeps
13 silly fetch manifest supabase@*
14 http fetch GET 200 https://registry.npmjs.org/supabase 5951ms (cache miss)
15 silly placeDep ROOT supabase@2.30.4 OK for:  want: *
16 silly fetch manifest tar@7.4.3
17 silly fetch manifest bin-links@^5.0.0
18 silly fetch manifest node-fetch@^3.3.2
19 silly fetch manifest https-proxy-agent@^7.0.2
20 http fetch GET 200 https://registry.npmjs.org/tar 1655ms (cache miss)
21 http fetch GET 200 https://registry.npmjs.org/https-proxy-agent 4344ms (cache miss)
22 http fetch GET 200 https://registry.npmjs.org/node-fetch 4735ms (cache miss)
23 http fetch GET 200 https://registry.npmjs.org/bin-links 5896ms (cache miss)
24 silly placeDep node_modules/supabase bin-links@5.0.0 OK for: supabase@2.30.4 want: ^5.0.0
25 silly placeDep node_modules/supabase https-proxy-agent@7.0.6 OK for: supabase@2.30.4 want: ^7.0.2
26 silly placeDep node_modules/supabase node-fetch@3.3.2 OK for: supabase@2.30.4 want: ^3.3.2
27 silly placeDep node_modules/supabase tar@7.4.3 OK for: supabase@2.30.4 want: 7.4.3
28 silly fetch manifest cmd-shim@^7.0.0
29 silly fetch manifest proc-log@^5.0.0
30 silly fetch manifest read-cmd-shim@^5.0.0
31 silly fetch manifest write-file-atomic@^6.0.0
32 silly fetch manifest npm-normalize-package-bin@^4.0.0
33 silly fetch manifest agent-base@^7.1.2
34 silly fetch manifest debug@4
35 silly fetch manifest data-uri-to-buffer@^4.0.0
36 silly fetch manifest fetch-blob@^3.1.4
37 silly fetch manifest formdata-polyfill@^4.0.10
38 silly fetch manifest @isaacs/fs-minipass@^4.0.0
39 http fetch GET 200 https://registry.npmjs.org/read-cmd-shim 616ms (cache miss)
40 silly fetch manifest chownr@^3.0.0
41 http fetch GET 200 https://registry.npmjs.org/write-file-atomic 623ms (cache miss)
42 silly fetch manifest minipass@^7.1.2
43 http fetch GET 200 https://registry.npmjs.org/proc-log 632ms (cache miss)
44 silly fetch manifest minizlib@^3.0.1
45 http fetch GET 200 https://registry.npmjs.org/chownr 615ms (cache miss)
46 silly fetch manifest mkdirp@^3.0.1
47 http fetch GET 200 https://registry.npmjs.org/minizlib 894ms (cache miss)
48 silly fetch manifest yallist@^5.0.0
49 http fetch GET 200 https://registry.npmjs.org/mkdirp 596ms (cache miss)
50 http fetch GET 200 https://registry.npmjs.org/yallist 617ms (cache miss)
51 http fetch GET 200 https://registry.npmjs.org/cmd-shim 2977ms (cache miss)
52 http fetch GET 200 https://registry.npmjs.org/minipass 2376ms (cache miss)
53 http fetch GET 200 https://registry.npmjs.org/@isaacs%2ffs-minipass 4421ms (cache miss)
54 http fetch GET 200 https://registry.npmjs.org/debug 4593ms (cache miss)
55 http fetch GET 200 https://registry.npmjs.org/npm-normalize-package-bin 4609ms (cache miss)
56 http fetch GET 200 https://registry.npmjs.org/fetch-blob 4991ms (cache miss)
57 http fetch GET 200 https://registry.npmjs.org/formdata-polyfill 5967ms (cache miss)
58 http fetch GET 200 https://registry.npmjs.org/agent-base 6096ms (cache miss)
59 http fetch GET 200 https://registry.npmjs.org/data-uri-to-buffer 6108ms (cache miss)
60 silly placeDep node_modules/supabase cmd-shim@7.0.0 OK for: bin-links@5.0.0 want: ^7.0.0
61 silly placeDep node_modules/supabase npm-normalize-package-bin@4.0.0 OK for: bin-links@5.0.0 want: ^4.0.0
62 silly placeDep node_modules/supabase proc-log@5.0.0 OK for: bin-links@5.0.0 want: ^5.0.0
63 silly placeDep node_modules/supabase read-cmd-shim@5.0.0 OK for: bin-links@5.0.0 want: ^5.0.0
64 silly placeDep node_modules/supabase write-file-atomic@6.0.0 OK for: bin-links@5.0.0 want: ^6.0.0
65 silly fetch manifest imurmurhash@^0.1.4
66 silly fetch manifest signal-exit@^4.0.1
67 http fetch GET 200 https://registry.npmjs.org/imurmurhash 678ms (cache miss)
68 http fetch GET 200 https://registry.npmjs.org/signal-exit 1608ms (cache miss)
69 silly placeDep node_modules/supabase agent-base@7.1.3 OK for: https-proxy-agent@7.0.6 want: ^7.1.2
70 silly placeDep node_modules/supabase debug@4.4.1 OK for: https-proxy-agent@7.0.6 want: 4
71 silly fetch manifest ms@^2.1.3
72 http fetch GET 200 https://registry.npmjs.org/ms 637ms (cache miss)
73 silly placeDep node_modules/supabase ms@2.1.3 OK for: debug@4.4.1 want: ^2.1.3
74 silly placeDep node_modules/supabase data-uri-to-buffer@4.0.1 OK for: node-fetch@3.3.2 want: ^4.0.0
75 silly placeDep node_modules/supabase fetch-blob@3.2.0 OK for: node-fetch@3.3.2 want: ^3.1.4
76 silly placeDep node_modules/supabase formdata-polyfill@4.0.10 OK for: node-fetch@3.3.2 want: ^4.0.10
77 silly fetch manifest node-domexception@^1.0.0
78 silly fetch manifest web-streams-polyfill@^3.0.3
79 http fetch GET 200 https://registry.npmjs.org/web-streams-polyfill 896ms (cache miss)
80 http fetch GET 200 https://registry.npmjs.org/node-domexception 1589ms (cache miss)
81 silly placeDep node_modules/supabase node-domexception@1.0.0 OK for: fetch-blob@3.2.0 want: ^1.0.0
82 silly placeDep node_modules/supabase web-streams-polyfill@3.3.3 OK for: fetch-blob@3.2.0 want: ^3.0.3
83 silly placeDep node_modules/supabase @isaacs/fs-minipass@4.0.1 OK for: tar@7.4.3 want: ^4.0.0
84 silly placeDep node_modules/supabase chownr@3.0.0 OK for: tar@7.4.3 want: ^3.0.0
85 silly placeDep node_modules/supabase minipass@7.1.2 OK for: tar@7.4.3 want: ^7.1.2
86 silly placeDep node_modules/supabase minizlib@3.0.2 OK for: tar@7.4.3 want: ^3.0.1
87 silly placeDep node_modules/supabase mkdirp@3.0.1 OK for: tar@7.4.3 want: ^3.0.1
88 silly placeDep node_modules/supabase yallist@5.0.0 OK for: tar@7.4.3 want: ^5.0.0
89 silly fetch manifest minipass@^7.0.4
90 silly placeDep node_modules/supabase imurmurhash@0.1.4 OK for: write-file-atomic@6.0.0 want: ^0.1.4
91 silly placeDep node_modules/supabase signal-exit@4.1.0 OK for: write-file-atomic@6.0.0 want: ^4.0.1
92 silly reify moves {}
93 silly tarball no local data for web-streams-polyfill@https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz. Extracting by manifest.
94 silly tarball no local data for node-domexception@https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz. Extracting by manifest.
95 silly tarball no local data for data-uri-to-buffer@https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz. Extracting by manifest.
96 silly tarball no local data for fetch-blob@https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz. Extracting by manifest.
97 silly tarball no local data for formdata-polyfill@https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz. Extracting by manifest.
98 silly tarball no local data for write-file-atomic@https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-6.0.0.tgz. Extracting by manifest.
99 silly tarball no local data for read-cmd-shim@https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-5.0.0.tgz. Extracting by manifest.
100 silly tarball no local data for proc-log@https://registry.npmjs.org/proc-log/-/proc-log-5.0.0.tgz. Extracting by manifest.
101 silly tarball no local data for cmd-shim@https://registry.npmjs.org/cmd-shim/-/cmd-shim-7.0.0.tgz. Extracting by manifest.
102 silly tarball no local data for npm-normalize-package-bin@https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-4.0.0.tgz. Extracting by manifest.
103 silly tarball no local data for node-fetch@https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz. Extracting by manifest.
104 silly tarball no local data for bin-links@https://registry.npmjs.org/bin-links/-/bin-links-5.0.0.tgz. Extracting by manifest.
105 silly tarball no local data for supabase@https://registry.npmjs.org/supabase/-/supabase-2.30.4.tgz. Extracting by manifest.
106 http fetch GET 200 https://registry.npmjs.org/cmd-shim/-/cmd-shim-7.0.0.tgz 338ms (cache miss)
107 http fetch GET 200 https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-6.0.0.tgz 342ms (cache miss)
108 http fetch GET 200 https://registry.npmjs.org/npm-normalize-package-bin/-/npm-normalize-package-bin-4.0.0.tgz 339ms (cache miss)
109 http fetch GET 200 https://registry.npmjs.org/proc-log/-/proc-log-5.0.0.tgz 399ms (cache miss)
110 http fetch GET 200 https://registry.npmjs.org/read-cmd-shim/-/read-cmd-shim-5.0.0.tgz 691ms (cache miss)
111 http fetch GET 200 https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz 708ms (cache miss)
112 warn deprecated node-domexception@1.0.0: Use your platform's native DOMException instead
113 http fetch GET 200 https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz 1037ms (cache miss)
114 http fetch GET 200 https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz 1193ms (cache miss)
115 http fetch GET 200 https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz 1428ms (cache miss)
116 http fetch GET 200 https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz 3032ms (cache miss)
117 http fetch GET 200 https://registry.npmjs.org/supabase/-/supabase-2.30.4.tgz 5172ms (cache miss)
118 http fetch GET 200 https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz 6091ms (cache miss)
119 http fetch GET 200 https://registry.npmjs.org/bin-links/-/bin-links-5.0.0.tgz 6364ms (cache miss)
120 info run supabase@2.30.4 postinstall node_modules/supabase node scripts/postinstall.js
121 info run supabase@2.30.4 postinstall { code: 1, signal: null }
122 warn cleanup Failed to remove some directories [
122 warn cleanup   [
122 warn cleanup     'E:\\Nvm\\nvm\\v20.15.1\\node_modules\\supabase',
122 warn cleanup     [Error: EPERM: operation not permitted, rmdir 'E:\Nvm\nvm\v20.15.1\node_modules\supabase\node_modules\minizlib\dist'] {
122 warn cleanup       errno: -4048,
122 warn cleanup       code: 'EPERM',
122 warn cleanup       syscall: 'rmdir',
122 warn cleanup       path: 'E:\\Nvm\\nvm\\v20.15.1\\node_modules\\supabase\\node_modules\\minizlib\\dist'
122 warn cleanup     }
122 warn cleanup   ]
122 warn cleanup ]
123 verbose stack Error: command failed
123 verbose stack     at ChildProcess.<anonymous> (E:\Nvm\nvm\v20.15.1\node_modules\npm\node_modules\@npmcli\promise-spawn\lib\index.js:53:27)
123 verbose stack     at ChildProcess.emit (node:events:519:28)
123 verbose stack     at maybeClose (node:internal/child_process:1105:16)
123 verbose stack     at ChildProcess._handle.onexit (node:internal/child_process:305:5)
124 verbose pkgid supabase@2.30.4
125 verbose cwd D:\.0000\scarboroughfair
126 verbose Windows_NT 10.0.19044
127 verbose node v20.15.1
128 verbose npm  v10.7.0
129 error code 1
130 error path E:\Nvm\nvm\v20.15.1\node_modules\supabase
131 error command failed
132 error command C:\WINDOWS\system32\cmd.exe /d /s /c node scripts/postinstall.js
133 error node:internal/modules/run_main:129
133 error     triggerUncaughtException(
133 error     ^
133 error Installing Supabase CLI as a global module is not supported.
133 error Please use one of the supported package managers: https://github.com/supabase/cli#install-the-cli
133 error
133 error (Use `node --trace-uncaught ...` to show where the exception was thrown)
133 error
133 error Node.js v20.15.1
134 verbose exit 1
135 verbose code 1
136 silly unfinished npm timer reify 1751702850739
137 silly unfinished npm timer reify:build 1751702879020
138 silly unfinished npm timer build 1751702879020
139 silly unfinished npm timer build:deps 1751702879020
140 silly unfinished npm timer build:run:postinstall 1751702879033
141 silly unfinished npm timer build:run:postinstall:node_modules/supabase 1751702879033
142 error A complete log of this run can be found in: d:\.0000\scarboroughfair\_logs\2025-07-05T08_07_30_161Z-debug-0.log
