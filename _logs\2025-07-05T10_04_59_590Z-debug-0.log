0 verbose cli E:\Node\node.exe e:\Node\node_modules\npm\bin\npm-cli.js
1 info using npm@10.7.0
2 info using node@v20.15.1
3 silly config:load:file:E:\Nvm\nvm\v20.15.1\node_modules\npm\npmrc
4 silly config:load:file:D:\.0000\scarboroughfair\.npmrc
5 silly config:load:file:C:\Users\<USER>\.npmrc
6 silly config:load:file:e:\Node\etc\npmrc
7 verbose title npm create vite@latest scarborough-market-web --template react-ts
8 verbose argv "create" "vite@latest" "scarborough-market-web" "--" "--template" "react-ts"
9 verbose logfile logs-max:10 dir:d:\.0000\scarboroughfair\_logs\2025-07-05T10_04_59_590Z-
10 verbose logfile d:\.0000\scarboroughfair\_logs\2025-07-05T10_04_59_590Z-debug-0.log
11 silly logfile start cleaning logs, removing 1 files
12 silly logfile done cleaning log files
13 http fetch GET 200 https://registry.npmjs.org/create-vite 4388ms (cache miss)
14 verbose stack Error: canceled
14 verbose stack     at Interface.<anonymous> (E:\Nvm\nvm\v20.15.1\node_modules\npm\node_modules\read\dist\commonjs\read.js:89:21)
14 verbose stack     at Interface.emit (node:events:519:28)
14 verbose stack     at [_ttyWrite] [as _ttyWrite] (node:internal/readline/interface:1126:18)
14 verbose stack     at ReadStream.onkeypress (node:internal/readline/interface:265:20)
14 verbose stack     at ReadStream.emit (node:events:519:28)
14 verbose stack     at emitKeys (node:internal/readline/utils:371:14)
14 verbose stack     at emitKeys.next (<anonymous>)
14 verbose stack     at ReadStream.onData (node:internal/readline/emitKeypressEvents:64:36)
14 verbose stack     at ReadStream.emit (node:events:519:28)
14 verbose stack     at addChunk (node:internal/streams/readable:559:12)
15 verbose cwd D:\.0000\scarboroughfair
16 verbose Windows_NT 10.0.19044
17 verbose node v20.15.1
18 verbose npm  v10.7.0
19 error canceled
20 verbose exit 1
21 verbose code 1
22 error A complete log of this run can be found in: d:\.0000\scarboroughfair\_logs\2025-07-05T10_04_59_590Z-debug-0.log
