import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  SafeAreaView,
  Dimensions,
  Animated,
  Share,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import {
  ArrowLeft,
  Edit3,
  Share2,
  Trash2,
  MapPin,
  Calendar,
  Star,
  Leaf,
  Book,
  Sparkles,
} from 'lucide-react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { getColors } from '@/constants/Colors';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { router, useLocalSearchParams } from 'expo-router';
import { HerbEntry, RarityOptions } from '@/types/diary';
import { DiaryService } from '@/services/diaryService';

const { width, height } = Dimensions.get('window');

function HerbDetailContent() {
  const { isDark } = useTheme();
  const { user } = useAuth();
  const colors = getColors(isDark);
  const { id } = useLocalSearchParams<{ id: string }>();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  const [entry, setEntry] = useState<HerbEntry | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadEntry();
    }
    
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, [id]);

  const loadEntry = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const { data, error } = await DiaryService.getHerbEntry(id);
      if (error) {
        Alert.alert('错误', '加载草药记录失败');
        router.back();
      } else {
        setEntry(data);
      }
    } catch (error) {
      Alert.alert('错误', '加载草药记录失败');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    router.push(`/diary/herb/edit/${id}`);
  };

  const handleDelete = () => {
    Alert.alert(
      '确认删除',
      '确定要删除这条草药记录吗？此操作无法撤销。',
      [
        { text: '取消', style: 'cancel' },
        { text: '删除', style: 'destructive', onPress: confirmDelete },
      ]
    );
  };

  const confirmDelete = async () => {
    if (!id) return;
    
    try {
      const { error } = await DiaryService.deleteHerbEntry(id);
      if (error) {
        Alert.alert('错误', '删除失败，请重试');
      } else {
        Alert.alert('成功', '草药记录已删除', [
          { text: '确定', onPress: () => router.back() }
        ]);
      }
    } catch (error) {
      Alert.alert('错误', '删除失败，请重试');
    }
  };

  const handleShare = async () => {
    if (!entry) return;
    
    try {
      await Share.share({
        message: `${entry.name} (${entry.scientific_name})\n\n${entry.description}\n\n来自斯卡布罗集市草药图鉴`,
        title: entry.name,
      });
    } catch (error) {
      console.error('分享失败:', error);
    }
  };

  if (loading || !entry) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
        <Text style={{ color: colors.text }}>加载中...</Text>
      </View>
    );
  }

  const rarity = RarityOptions[entry.rarity];

  const dynamicStyles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'space-between' as const,
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 15,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    headerActions: {
      flexDirection: 'row' as const,
      gap: 12,
    },
    actionButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: colors.surfaceSecondary,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
    },
    contentContainer: {
      padding: 20,
    },
    heroSection: {
      alignItems: 'center' as const,
      marginBottom: 24,
    },
    herbIcon: {
      fontSize: 64,
      marginBottom: 16,
    },
    title: {
      fontSize: 28,
      fontWeight: '700' as const,
      color: colors.text,
      textAlign: 'center' as const,
      marginBottom: 8,
    },
    scientificName: {
      fontSize: 18,
      color: colors.textSecondary,
      fontStyle: 'italic' as const,
      textAlign: 'center' as const,
      marginBottom: 16,
    },
    rarityBadge: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      gap: 8,
    },
    rarityText: {
      fontSize: 16,
      fontWeight: '600' as const,
    },
    metaContainer: {
      flexDirection: 'row' as const,
      justifyContent: 'space-around' as const,
      marginBottom: 24,
      padding: 16,
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 16,
    },
    metaItem: {
      alignItems: 'center' as const,
      gap: 8,
    },
    metaLabel: {
      fontSize: 12,
      color: colors.textSecondary,
      textAlign: 'center' as const,
    },
    metaValue: {
      fontSize: 14,
      color: colors.text,
      fontWeight: '600' as const,
      textAlign: 'center' as const,
    },
    sectionCard: {
      borderRadius: 16,
      overflow: 'hidden' as const,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: 20,
    },
    sectionHeader: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      padding: 16,
      backgroundColor: colors.surfaceSecondary,
      gap: 12,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600' as const,
      color: colors.text,
    },
    sectionContent: {
      padding: 16,
    },
    description: {
      fontSize: 16,
      color: colors.text,
      lineHeight: 24,
    },
    tagsContainer: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 8,
    },
    tag: {
      backgroundColor: colors.accent + '20',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    tagText: {
      fontSize: 14,
      color: colors.accent,
      fontWeight: '600' as const,
    },
    locationContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginBottom: 16,
      padding: 16,
      backgroundColor: colors.surfaceSecondary,
      borderRadius: 12,
    },
    locationText: {
      fontSize: 16,
      color: colors.text,
      marginLeft: 8,
      fontWeight: '600' as const,
    },
    imagesContainer: {
      marginBottom: 20,
    },
    imagesGrid: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 12,
    },
    imageItem: {
      width: (width - 64) / 2,
      height: (width - 64) / 2,
      borderRadius: 12,
      overflow: 'hidden' as const,
    },
    image: {
      width: '100%',
      height: '100%',
    },
    singleImage: {
      width: width - 40,
      height: 240,
    },
    notesText: {
      fontSize: 16,
      color: colors.textSecondary,
      lineHeight: 24,
      fontStyle: 'italic' as const,
    },
  };

  return (
    <View style={dynamicStyles.container}>
      <LinearGradient
        colors={[colors.backgroundSecondary, colors.backgroundTertiary]}
        style={{ flex: 1 }}
      >
        <SafeAreaView style={{ flex: 1 }}>
          {/* Header */}
          <Animated.View style={[dynamicStyles.header, { opacity: fadeAnim }]}>
            <TouchableOpacity
              style={dynamicStyles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={20} color={colors.text} />
            </TouchableOpacity>
            
            <View style={dynamicStyles.headerActions}>
              <TouchableOpacity
                style={dynamicStyles.actionButton}
                onPress={handleShare}
              >
                <Share2 size={20} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity
                style={dynamicStyles.actionButton}
                onPress={handleEdit}
              >
                <Edit3 size={20} color={colors.text} />
              </TouchableOpacity>
              <TouchableOpacity
                style={dynamicStyles.actionButton}
                onPress={handleDelete}
              >
                <Trash2 size={20} color={colors.error} />
              </TouchableOpacity>
            </View>
          </Animated.View>

          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={dynamicStyles.contentContainer}
            showsVerticalScrollIndicator={false}
          >
            <Animated.View
              style={{
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              }}
            >
              {/* Hero Section */}
              <View style={dynamicStyles.heroSection}>
                <Text style={dynamicStyles.herbIcon}>{entry.illustration}</Text>
                <Text style={dynamicStyles.title}>{entry.name}</Text>
                {entry.scientific_name && (
                  <Text style={dynamicStyles.scientificName}>{entry.scientific_name}</Text>
                )}
                <View style={[dynamicStyles.rarityBadge, { backgroundColor: rarity.color + '20' }]}>
                  <Text style={dynamicStyles.rarityText}>{rarity.icon}</Text>
                  <Text style={[dynamicStyles.rarityText, { color: rarity.color }]}>
                    {rarity.label}
                  </Text>
                </View>
              </View>

              {/* Meta Information */}
              <View style={dynamicStyles.metaContainer}>
                <View style={dynamicStyles.metaItem}>
                  <Calendar size={20} color={colors.primary} />
                  <Text style={dynamicStyles.metaLabel}>发现日期</Text>
                  <Text style={dynamicStyles.metaValue}>
                    {entry.date_found || '未知'}
                  </Text>
                </View>
                <View style={dynamicStyles.metaItem}>
                  <Leaf size={20} color={colors.accent} />
                  <Text style={dynamicStyles.metaLabel}>属性数量</Text>
                  <Text style={dynamicStyles.metaValue}>
                    {entry.properties.length}
                  </Text>
                </View>
                <View style={dynamicStyles.metaItem}>
                  <Sparkles size={20} color={colors.secondary} />
                  <Text style={dynamicStyles.metaLabel}>用途数量</Text>
                  <Text style={dynamicStyles.metaValue}>
                    {entry.uses.length}
                  </Text>
                </View>
              </View>

              {/* Location */}
              {entry.location && (
                <View style={dynamicStyles.locationContainer}>
                  <MapPin size={20} color={colors.primary} />
                  <Text style={dynamicStyles.locationText}>{entry.location}</Text>
                </View>
              )}

              {/* Description */}
              <BlurView intensity={30} style={dynamicStyles.sectionCard}>
                <View style={dynamicStyles.sectionHeader}>
                  <Book size={20} color={colors.primary} />
                  <Text style={dynamicStyles.sectionTitle}>描述</Text>
                </View>
                <View style={dynamicStyles.sectionContent}>
                  <Text style={dynamicStyles.description}>{entry.description}</Text>
                </View>
              </BlurView>

              {/* Properties */}
              {entry.properties.length > 0 && (
                <BlurView intensity={30} style={dynamicStyles.sectionCard}>
                  <View style={dynamicStyles.sectionHeader}>
                    <Leaf size={20} color={colors.accent} />
                    <Text style={dynamicStyles.sectionTitle}>属性</Text>
                  </View>
                  <View style={dynamicStyles.sectionContent}>
                    <View style={dynamicStyles.tagsContainer}>
                      {entry.properties.map((property, index) => (
                        <View key={index} style={dynamicStyles.tag}>
                          <Text style={dynamicStyles.tagText}>{property}</Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </BlurView>
              )}

              {/* Uses */}
              {entry.uses.length > 0 && (
                <BlurView intensity={30} style={dynamicStyles.sectionCard}>
                  <View style={dynamicStyles.sectionHeader}>
                    <Sparkles size={20} color={colors.secondary} />
                    <Text style={dynamicStyles.sectionTitle}>用途</Text>
                  </View>
                  <View style={dynamicStyles.sectionContent}>
                    <View style={dynamicStyles.tagsContainer}>
                      {entry.uses.map((use, index) => (
                        <View key={index} style={dynamicStyles.tag}>
                          <Text style={dynamicStyles.tagText}>{use}</Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </BlurView>
              )}

              {/* Images */}
              {entry.images.length > 0 && (
                <BlurView intensity={30} style={dynamicStyles.sectionCard}>
                  <View style={dynamicStyles.sectionHeader}>
                    <Star size={20} color={colors.warning} />
                    <Text style={dynamicStyles.sectionTitle}>图片</Text>
                  </View>
                  <View style={dynamicStyles.sectionContent}>
                    <View style={dynamicStyles.imagesGrid}>
                      {entry.images.map((image, index) => (
                        <View
                          key={index}
                          style={[
                            dynamicStyles.imageItem,
                            entry.images.length === 1 && dynamicStyles.singleImage,
                          ]}
                        >
                          <Image source={{ uri: image }} style={dynamicStyles.image} />
                        </View>
                      ))}
                    </View>
                  </View>
                </BlurView>
              )}

              {/* Notes */}
              {entry.notes && (
                <BlurView intensity={30} style={dynamicStyles.sectionCard}>
                  <View style={dynamicStyles.sectionHeader}>
                    <Edit3 size={20} color={colors.textSecondary} />
                    <Text style={dynamicStyles.sectionTitle}>备注</Text>
                  </View>
                  <View style={dynamicStyles.sectionContent}>
                    <Text style={dynamicStyles.notesText}>{entry.notes}</Text>
                  </View>
                </BlurView>
              )}
            </Animated.View>
          </ScrollView>
        </SafeAreaView>
      </LinearGradient>
    </View>
  );
}

export default function HerbDetailScreen() {
  return (
    <ProtectedRoute>
      <HerbDetailContent />
    </ProtectedRoute>
  );
}
