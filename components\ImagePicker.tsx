import React from 'react';
import { Platform, Alert } from 'react-native';

// Web 兼容的图片选择器
export const pickImage = async (): Promise<{ uri: string } | null> => {
  if (Platform.OS === 'web') {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.onchange = (event: any) => {
        const file = event.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            resolve({ uri: e.target?.result as string });
          };
          reader.readAsDataURL(file);
        } else {
          resolve(null);
        }
      };
      input.click();
    });
  } else {
    // 移动端使用 expo-image-picker
    try {
      const { launchImageLibraryAsync, MediaTypeOptions, requestMediaLibraryPermissionsAsync } = await import('expo-image-picker');
      
      const { status } = await requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('权限', '需要相册权限来选择图片');
        return null;
      }

      const result = await launchImageLibraryAsync({
        mediaTypes: MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        return { uri: result.assets[0].uri };
      }
      return null;
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert('错误', '无法打开图片选择器');
      return null;
    }
  }
};

export default { pickImage };
