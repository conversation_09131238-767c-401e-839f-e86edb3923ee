export const Colors = {
  light: {
    // Primary colors
    primary: '#6B4E71',
    primaryLight: '#8B6F8B',
    secondary: '#4A7043',
    accent: '#D4A017',
    accentLight: '#E6B82A',
    
    // Background colors
    background: '#F5E8C7',
    backgroundSecondary: 'rgba(245, 232, 199, 0.95)',
    backgroundTertiary: 'rgba(245, 232, 199, 0.9)',
    
    // Text colors
    text: '#6B4E71',
    textSecondary: '#8B7355',
    textTertiary: '#B0B7A4',
    
    // Surface colors
    surface: 'rgba(245, 232, 199, 0.8)',
    surfaceSecondary: 'rgba(176, 183, 164, 0.3)',
    
    // Status colors
    success: '#4A7043',
    warning: '#D4A017',
    error: '#DC3545',
    
    // Border colors
    border: 'rgba(107, 78, 113, 0.2)',
    borderSecondary: 'rgba(176, 183, 164, 0.3)',
  },
  dark: {
    // Primary colors
    primary: '#9B7EA1',
    primaryLight: '#B598BB',
    secondary: '#6A9063',
    accent: '#F4C037',
    accentLight: '#F6D24A',
    
    // Background colors
    background: '#1A1A1A',
    backgroundSecondary: 'rgba(26, 26, 26, 0.95)',
    backgroundTertiary: 'rgba(26, 26, 26, 0.9)',
    
    // Text colors
    text: '#E8E8E8',
    textSecondary: '#B8B8B8',
    textTertiary: '#888888',
    
    // Surface colors
    surface: 'rgba(40, 40, 40, 0.8)',
    surfaceSecondary: 'rgba(60, 60, 60, 0.3)',
    
    // Status colors
    success: '#6A9063',
    warning: '#F4C037',
    error: '#FF6B6B',
    
    // Border colors
    border: 'rgba(155, 126, 161, 0.3)',
    borderSecondary: 'rgba(136, 136, 136, 0.3)',
  },
};

export function getColors(isDark: boolean) {
  return isDark ? Colors.dark : Colors.light;
}