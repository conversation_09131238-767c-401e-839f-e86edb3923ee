
ff8d4dba0ce6b3081a22d44e2a6ac94bec219481	{"key":"make-fetch-happen:request-cache:https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz","integrity":"sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==","time":1751696787040,"size":1528,"metadata":{"time":1751696780458,"url":"https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz","reqHeaders":{},"resHeaders":{"cache-control":"public, must-revalidate, max-age=31557600","content-type":"application/octet-stream","date":"Sat, 05 Jul 2025 06:25:48 GMT","etag":"\"f07933e3b37ddc954a8b88c9add93a0c\"","last-modified":"Tue, 30 Jun 2020 13:11:53 GMT","vary":"Accept-Encoding"},"options":{"compress":true}}}