{"_id": "@esbuild/android-arm", "_rev": "106-28e1b83dc1b4aebf41d7d7c8abdba0b3", "name": "@esbuild/android-arm", "dist-tags": {"latest": "0.25.5"}, "versions": {"0.15.7": {"name": "@esbuild/android-arm", "version": "0.15.7", "license": "MIT", "_id": "@esbuild/android-arm@0.15.7", "maintainers": [{"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "2b99f97a2c232c4f7962d17a0dc4d861a644e827", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.7.tgz", "fileCount": 3, "integrity": "sha512-ekgLiRNyKSOwi+D2Z/i7qXLwT1ObgjjxIB1jtqAoQfPvjr5k5rTTZATKva8J36nC12XnavLlihASkIUJ35CbPg==", "signatures": [{"sig": "MEQCIDqvzXeiuJxJt2mxRaLd7s61+Jj/4+HHpbSc0rRZexGoAiAL3OBxa4r21Jopg+Kcz5xOIDJ49GiWDfbfdPsLRIoV4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHovHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPcA//a7niZo9cwIixwpSvuRAguRVzU15PhQmmRY2VNwhrveM9RZeS\r\nZQaZFyV8NUxi6BRBIALc61yzNJZHp/3UctvXj+mTmxcNsYZp6I0AAtWEk4cf\r\nzbO74US9ejU7vZkeIXjeZ+7g0RNA9D0Rt07x5CXdesvx4Afa3ZwX4w4BWPPN\r\ntji6OGBzajOSeOAiz8g9PMsd5uqwK9JDbbWNpRyuA3ZJqlAhLn871T510QyK\r\nhvfnjZm5QBVf3Scked1LLE7kESzM/HAnZmiLU7QwU6f8FtqVU3t3SfdO49hO\r\nVO0Nu8jjSwFrpqEkk+rdmXUD4qnv6vGoOGr/qIczDsJhz4ieNyR/Od4O6Q9W\r\nX/NJXs9TrlxW70rW0GvkXml0QiX7/yDy65dheXh4SA+HB9S/FaW7nxMQWQsy\r\nMZa5QuHGj+oa+6M1X4bAsUJXfLMRBawy5EeC7mYJ8D7TWvoTzSo2T8hOoLhu\r\na3fWZh+QpbTGOVrCSp6JrOaMd1XhKddeaSdyQL2nKncBy5zIGoZp8EynFxed\r\n0v2ahgi7uFSzhk0ZLBlVdJVztJYEruu3hEhS9dFe7UMMRkmKFPQY4mvSzPGK\r\n6+Mh5akqr2uHNIrN2KrLRLsev30RnYosQPpX8hrZLtbMQSSMVUoTvvIbkF//\r\nLvWcMcOV8k36xKPZRDhKgPGyY0V3JbM1TyQ=\r\n=z3ji\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3e2374cb011a47482b415f84716afa13ea88f3ce", "_npmUser": {"name": "esbuild", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"esbuild-wasm": "0.15.7"}, "_hasShrinkwrap": false, "preferUnplugged": false, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.7_1662946247153_0.37842602777296186", "host": "s3://npm-registry-packages"}}, "0.15.8": {"name": "@esbuild/android-arm", "version": "0.15.8", "license": "MIT", "_id": "@esbuild/android-arm@0.15.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "52b094c98e415ec72fab39827c12f2051ac9c550", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.8.tgz", "fileCount": 3, "integrity": "sha512-CyEWALmn+no/lbgbAJsbuuhT8s2J19EJGHkeyAwjbFJMrj80KJ9zuYsoAvidPTU7BgBf87r/sgae8Tw0dbOc4Q==", "signatures": [{"sig": "MEQCIHfaOD1zQlnwla0RBr+nU9evPdHLjhTalXJT5ZXzmkqNAiALm87qozuFY364FEx6lWENhhaFWnO5Kk9GuFkTtencQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJ2F6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOxA/5Abm2o9lRthZqPMFJ+GkCMFlVYAG5lyPbfpACIHYHAeBfUmb2\r\nJizXCUTkSWVzdueixOXul1yVhH++uG2jdR45QjuEOJTMrPV9V7EKr6tSLAaV\r\ngZ1Dz/+wpDnQmgxtX7/K2Q2143eILLrFgdI58uPU67YWgRlz1McZHvAQqomC\r\n0vxa9xwhlM58LlIZkKepylbupGpzEohw4Q+z7w73u/k7vfF+jY+hIX1biY8t\r\nA3taBPu4y0hswutTuvyJwSspQlmntm2LDLZxftcKzS3I33nB5HARuVg/4+F4\r\n+J7PF8ra/vd8ltT3DUdEwOvX4YHrO0ELh0SrpN4ESGc9tT9LB3uyYss9uG1X\r\ntEROv+GWlCYM3JFmdNEsrSFSd2SlPSTBn+plfDjV6Jiqfc2HZH0HqZiZARXp\r\nKeXyGA0s6oHBkvUUPwmleFlHMHfUOi/ujTZEKrSLPqeb37mLES0OGGB3cwyS\r\nV2E2VOExZ9iR9O7aAjGYlx84fAwJibo9MEuYmHHM6m4wzEBImRsJU6Dnmu57\r\nUS7bZDpyRlP2HttqDSgf7OIdzaiUWKyUo3tyDmvXVMSF/tsH4O2AI0jP0qeY\r\nuSUCZJ036gBW3ZIRty4qHyIpv+m1lGvxatmXJVidZaViepxt8h5dW76T3JRG\r\nQqt5NH73ZzoW2KdSnzM5OvT27ovwtaz+lxg=\r\n=cIm+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "7553dbea555a7e8be42b90812e6c7f76446a04fd", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"esbuild-wasm": "0.15.8"}, "_hasShrinkwrap": false, "preferUnplugged": false, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.8_1663525242503_0.6423034103607472", "host": "s3://npm-registry-packages"}}, "0.15.9": {"name": "@esbuild/android-arm", "version": "0.15.9", "license": "MIT", "_id": "@esbuild/android-arm@0.15.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "7e1221604ab88ed5021ead74fa8cca4405e1e431", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.9.tgz", "fileCount": 7, "integrity": "sha512-VZPy/ETF3fBG5PiinIkA0W/tlsvlEgJccyN2DzWZEl0DlVKRbu91PvY2D6Lxgluj4w9QtYHjOWjAT44C+oQ+EQ==", "signatures": [{"sig": "MEYCIQC8fPBvIpXfmGqPS734LRg8VmrKkPGy9ZyqWtLi6oLuewIhALoCfVplh/J/m30ib/CDZZoZUYF+YbMh6fD/9bqMn1UR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10622808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLL0bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqW9Q//T4UMlBiVXd6uR/wbxQAjCCe2nG6uW+pp+gaDJEFBARBBhc56\r\nxW9CVGSmVNT8PKGPjvdtefks1S0Ckk/mPMhaPv3JUimhOu5SlUk7gBjB1g/P\r\nyhpyYQrLJotjCozN/xYvZh9jDEKW3K92fDepfj6gO5gr2CvnJJwk8oTs6Ink\r\n5vpUMjSdw7mGA3R87GkdZw5Z1uP4GY/ZV/l+zvMS0wGSLXlOEwFQMFQ4267D\r\nYoR8JxrMmQ6Fxw5DvT6bbAxyySJRCapFUgU2j88+djAzD9IZ8KaaiajiWrp3\r\nupSLmlXbSGFIyP8I2JzPOcippNBjCrYu3mtx90LwfI/Aqml+OcDPonI8QIs2\r\n53y5b9P/xMG3OZf1fmaL0zHnt758QQU5sAuyKRQKBbJ4pUtlC8RKTswv14d5\r\nw3Gav7fC8F81hGmtRyxN1i7okoY+qOdsBOz5Zns7XebXcB0+SKcnXu5nImke\r\nDT2kBMgCg17nIPx+dS6vCnud6gcrCfYJgEw6Prqia9BFLpYur0NG0q/7BgJ+\r\nlLfSuYWjze1rt1n+jU3OGZ6AW6gCeI7DjZRofaClcmBnmHgKtn5q75bFoEyu\r\n/s3US3AYtObG2WE2bug5TeTwuRVy68AxVRm/lD2hHoXlFayJsBVvlFaFl3Y8\r\ndpqfQb5Op/ucO0VY2lRn+ENwxkaiJiA4Q5g=\r\n=hhJk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "085265a2660fc9bbf1e4ab47ba0778e272a0cce8", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": false, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.9_1663876379584_0.2754301437381572", "host": "s3://npm-registry-packages"}}, "0.15.10": {"name": "@esbuild/android-arm", "version": "0.15.10", "license": "MIT", "_id": "@esbuild/android-arm@0.15.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "a5f9432eb221afc243c321058ef25fe899886892", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.10.tgz", "fileCount": 7, "integrity": "sha512-FNONeQPy/ox+5NBkcSbYJxoXj9GWu8gVGJTVmUyoOCKQFDTrHVKgNSzChdNt0I8Aj/iKcsDf2r9BFwv+FSNUXg==", "signatures": [{"sig": "MEQCIFui2SyCwpOPDkkRAUtBkGZ4Yngq/uO01rGvk0vMel8EAiAN0AiezW24U/x52+AASkO6+pYJMgibF97LqQEsKO4rSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10627005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNcQlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6Xw/6A+K0hdpWn088RDWjanzs8Z6eyr3aYUG7Af9AV8rukpJuX4ge\r\n3reifW/U/vl9O7CgiuI3yBX4Ay/7GAonM9jaTxPX9F0fZIqeRKjql4q1NcQR\r\nck0OwErVLW7oWHWqwyXf2ZljBMNMeEld8S/Yta+QbHHPUZQDKo+OsADHyohl\r\nF0/Z/YqH99WMLvC4NvLYH0IlGXyFKrp3HJ0mgtA4fWx80+FFQmxWbcIpiZT4\r\nY7Rr1TSe4l2eRHwqexu3J5sNErcpFjbWhQ8y3USf5bPEPnlBSwHwQXJHoHTI\r\nq5JmuB4BG6hAMxqqZrR/wRCznuiTve2HGVZMrtBmIQCCueHlpY97O4xJo3K/\r\na8y0SJjyjfxeSeiEBPYGIuW9vnemC3JmgsO5SAqVr+36y/wUTpAfGWo5F75f\r\nAcGK8FdFYhoi0CbcqU8tS1sZiLcwUU+SFag3wFq+yCnSU6rwTFOXWMu/aMwY\r\nDqfadqaTkKlIVeMtAAnAiIOp8L5T70q1DaXLOEwAoKpFZ2ewUBAM9IpyE4FH\r\np4LluOzP8I3x2hej69+aIbJBUkYVJLeJVMmeRmeiPm9i7DojWnnR3jdAUTcV\r\nR15kIDLEeHhhWeEU3U0mCVyXJHi0ExOmai5MKlkdp+K32dkZg8r3/7bshmbQ\r\nlKXdxlXyQsY324nMhpeEmaSPqPWkd1KHPnI=\r\n=eBiG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "aaae34eb60632f9945c96379fb3fd22580815e9c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": false, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.10_1664468005386_0.5884930729955966", "host": "s3://npm-registry-packages"}}, "0.15.11": {"name": "@esbuild/android-arm", "version": "0.15.11", "license": "MIT", "_id": "@esbuild/android-arm@0.15.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "bdd9c3e098183bdca97075aa4c3e0152ed3e10ee", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.11.tgz", "fileCount": 7, "integrity": "sha512-PzMcQLazLBkwDEkrNPi9AbjFt6+3I7HKbiYF2XtWQ7wItrHvEOeO3T8Am434zAozWtVP7lrTue1bEfc2nYWeCA==", "signatures": [{"sig": "MEQCIB+Y16Q2pgxRKOVw7x8seALt1nR89HA9gwqhZlsg7QDCAiBcKmunSslhnijlt3SQ9CUx2z+FJm3oU5ME0eUkLTj/ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10642184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSXB1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5ChAAnHIc+PkUvxcPX4CQ2fHcbfW5bQIecCoZkSeOqMWQ4PHNxBRT\r\nZrKnSXHLWtVbZRwgWPPxyOLfn+QKeVbepeq+Y6SBoLoQFBjRHZK/8s3wUWde\r\nfhSM5jjTsWS1A3XkqHNqdLAuMD/rHGbgAAQApANWcA9SsGZl/hWDK7+n7cZy\r\nnpQQ+VlRMGuSngihdj8AY+PjgEo7CaMCbwh4GWeulzVztZ3dCrhqRSzATwmR\r\ncWJW20wH0JquSmqKhYoMXJqDEAdVPbXxXf+jAKaKKwO031SnWEY5CILflis7\r\naTW2ZeRk9IquYkCGB2wtCA1+8SJJTwv9/LVonRxaCYwTwEJqJdh07g8PfIvV\r\nIn7tIvbsMHDWIZ30QvbrD93S6bo1epGTknQtirwG0YTg3Nst/D0J2sx16yLX\r\ntVfCZcSEM2up8B3dAtsmmHxyG4jiUegRcqMmFypKOfOrME9ZWTIHsAWVYyRC\r\nVQV0UiSmeIZK5CElfvqZYFbOBokhdcMS8Ond5uKvODmwTr5kF72MBrPZELnj\r\n59ioC3jjK49mPwNUVFdb2KHnrkeo7b46l7WeRS+3dw/f6USskoOJ9pYyWEs8\r\n5jNOZnkB4Vgcc8s2LoBp0ltCU0W3dbXf00unSgzw15f0cBUe2QL9Xq3fKohM\r\ncMdcMUG+IHNFaLrigt73ZFGIbEho0HjbDpU=\r\n=j9EI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0bb62e8d1c10c304c6224008eed35afbf45367c2", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": false, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.11_1665757300870_0.9512694256354988", "host": "s3://npm-registry-packages"}}, "0.15.12": {"name": "@esbuild/android-arm", "version": "0.15.12", "license": "MIT", "_id": "@esbuild/android-arm@0.15.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e548b10a5e55b9e10537a049ebf0bc72c453b769", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.12.tgz", "fileCount": 7, "integrity": "sha512-IC7TqIqiyE0MmvAhWkl/8AEzpOtbhRNDo7aph47We1NbE5w2bt/Q+giAhe0YYeVpYnIhGMcuZY92qDK6dQauvA==", "signatures": [{"sig": "MEQCIAqcOJcFyua0pH3+tuVOJ8W/IBxyXNBTYw4eyvAO59iMAiAOl9CrJgPSN/6/sHFlvlOUFzr3yNs2dy6nkvvGcA6M8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10642544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUEDxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpm1Q/+INRDe9TTD+kW2CgRgwd+03ZkwVGueDNEMv+C5+HoYUtEjy45\r\nimBkaGUPSZ1DGUSKpmuJjjhJoEgQ/gUcofXlczqZETYamkVXhg219WxPqKu6\r\ne7adXbAlTqMgVEvFFm8ZQ4DUb9CwOvuz10YIU5U8jSM5CQI1ugcE9hzfN7Er\r\n4uIjVmx56I9LD65wK7T9XT49e1BLeBI1dirM7ZsxJLVPPqLTU43lsHIG6F2Y\r\ngAPXfRnimvkHr3j7FBOHgJkdEhbguxCZzB6P2Wk1sskPZqtDi6UUNIShfef5\r\neJQHjGCT7uvW4K099QAfO7ZjdlQwxlJgs/qjPs9lGxx1kJwrbdvxD0qWgxDE\r\niI7FLhkTRpkzVoRybafwbnTGKucBRn6KwCyQ9Vsv1mhY9FZ4F2DQnWRswA4s\r\nuKy0RP3K+a6Y0/xBieWlO6lb27jzwp347EZOmNWFNdgyZxGo1ydoe0v4jEFn\r\nsetnlPTETBi2ljNIrF3/6R4iWKF431b3sjTvxxRRKSzBEPcP4Il39n5u7HSI\r\nW4MuUYJWz+lLsLLm0ZjnVINTXFMb5Iu99zAe48VizChriX5JIPenQ+kpVPMu\r\nPKMX1NF0gmqKJaVQFnxPTl3Y92QQNfdNdYLi/v+BzoJlLUbkmjhoN3ZudDGQ\r\nXkJfMHTkELjZzvLndvezbpX4K+RRmy2VPfY=\r\n=+714\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "223e6eaa90c699d052737bd574c25dab7adca212", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": false, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.12_1666203888654_0.015422668753328317", "host": "s3://npm-registry-packages"}}, "0.15.13": {"name": "@esbuild/android-arm", "version": "0.15.13", "license": "MIT", "_id": "@esbuild/android-arm@0.15.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ce11237a13ee76d5eae3908e47ba4ddd380af86a", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.13.tgz", "fileCount": 7, "integrity": "sha512-RY2fVI8O0iFUNvZirXaQ1vMvK0xhCcl0gqRj74Z6yEiO1zAUa7hbsdwZM1kzqbxHK7LFyMizipfXT3JME+12Hw==", "signatures": [{"sig": "MEQCIDslhoBlsBq4vVRi8zQ27CI4fSnfhxg48XKkm48s9FGNAiAiyqqvFRjNiXIO4Waq1kXSNIGYJzS/bbHcMTEgVcT8LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10644979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjY1NmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6jA/5AJF/WzgHcKOoQm5ThWnyEy2ctgK6y0n797B1WGOXTgWCnRf/\r\noQYxdGcx+SJqnVjTMk0njaXxtz7YtAGFdIrZSXfzQhPcwl8kFbThxE4ULlhU\r\nS7cKiFNpQCUs1DrN5azQd/IXaPZgVojjGoR/6XGzwMmxCxctsDslUx+S1v6H\r\nvnqSZvdXAvgNiWKPQK0M98kffNHnVChY2njPKBmEGnbtCo8BRdUA0kPeuW/g\r\nO3WU7cnH3XAZiXJIW4V+4/RI06n58l2uuciW3O1sYeLlaydKtfuyLjA6ja0Q\r\nU2jS85hS8g7DS7b1P0QvZU30dT3MSR5rjjYGIFSryE4bsM5vv2P+SqnVsldF\r\n89H+TYYEvMUMcF1sz7qZ86qgRO0ztCAjYZVR5AgatFuXLVfr10+JN9wgLl0Z\r\nzt1nWaJq0hGMvPRn/Tos7YVKI4NodbLDcVLF/zygfOx1W3AllNf6RJ+FFv8P\r\np3k3HzOh83Wq3NvHcWxZawFOR9B07nyUqzoCR/Ua1rFU8fSUe4/gi74L311y\r\nmQCxINL+oMwyKV/Gkrs2ILger9T0uG3PYYieUzdmxMiidwHp77aVtpY2nsA9\r\nKgVKiRu4TMyvZfzMKH7FxaCKulNXlkotg9qr0A4+RdWOIgjN38rrtbYiMYgc\r\n1uS6QLgvjsu7xT5cUwRpO/RigPuAvOZekmQ=\r\n=DcDt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ba4771032805f950982ab81cc099783bc6cd90c7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": false, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.13_1667453798670_0.3988394330412939", "host": "s3://npm-registry-packages"}}, "0.15.14": {"name": "@esbuild/android-arm", "version": "0.15.14", "license": "MIT", "_id": "@esbuild/android-arm@0.15.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5d0027f920eeeac313c01fd6ecb8af50c306a466", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.14.tgz", "fileCount": 7, "integrity": "sha512-+Rb20XXxRGisNu2WmNKk+scpanb7nL5yhuI1KR9wQFiC43ddPj/V1fmNyzlFC9bKiG4mYzxW7egtoHVcynr+OA==", "signatures": [{"sig": "MEQCIGIYsdigjXWzslaB7rzL0tqEMzKk2ciIrgnhaSy/SeKjAiB77iB3rAoX4crR8hZVyLLhcIMwwXDQZ/SKhNgMcwXzvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10647128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcxmwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6ug/+M30FxqZLNBF30WU+9NzvzPQcBPWnc+BSLAwdaqD+xHR4oa1k\r\ne3oIsTDPfUuRUy8KXowCtver4XemYK7QId5iy8w5fWuiEeAILOI3vu/yelLG\r\nQU3guBTNT26qty2PES9+TJWqZ6oUADgcBYZabsfXq8galGVGhyfSVsSpT5Ar\r\nP9Eyz/13/E0il9pfYidhjjUyGhx8hpldExZe9cWei3A0D9ZakJoehfyRCanV\r\nED9XebpjveWTmvKZnR6cJtW1F0jVRMCffDI6D9XnJ9UYIBhbBuPz6U96TiX2\r\nqJvC/nIdZsjyaQ4dHghIADrNh3AP382rP0Qux3KlYSW6aYLIGNTNuzLkhln+\r\n8ixUZZxp+/ql9ElpASxCtxELEeUOo5Qrzgr1e8hjlXWAQdwkKz+BlW16EMyg\r\nd0TsX3VSpvRfrrrQTcb1xbmhu21iEzVFI+IFTbkbdMSzK0lONpdtoQnIpBAE\r\n5oW0S83c+UvMqwI53MshMefoM4H7IMqd8sP5zFm6UvyxRJN0jabQTWYBU970\r\nzy68YxIV6RaZBdNUAwohS0s8O3WIRyYWxMosa92vOawwAPJ55Vpli7OFrnwE\r\nYJMsYgoeGqP+tt/BxEnNSFkXPgCQqbBl5c10L7GWYGM2uaHGARqKrGg33xtS\r\ngNXaw24Ge9QrNroXHJCZ8Ip9E3HfYCVDguo=\r\n=dbEc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "d0f6b7f9ced548d530e91f20e4d7a206cb1582f7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": false, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.14_1668487600705_0.4614459881653954", "host": "s3://npm-registry-packages"}}, "0.15.15": {"name": "@esbuild/android-arm", "version": "0.15.15", "license": "MIT", "_id": "@esbuild/android-arm@0.15.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "35b3cc0f9e69cb53932d44f60b99dd440335d2f0", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.15.tgz", "fileCount": 7, "integrity": "sha512-<PERSON><PERSON>jZjJi2eBL01QJuWjfCdZxcIgot+VoK6Fq7eKF9w4YHm9hwl7nhBR1o2Wnt/WcANk5l9SkpvrldW1PLuXxcbw==", "signatures": [{"sig": "MEUCIDDr/rrCasXBHPvgtNqtjx0PPvtYFx/sm4M6T8K+dSIXAiEAtMcte5wFIUgBXUnV8EtxQuE4+diUB5m6Doqz01B4Mx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10668043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjewMoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKvhAAhA157e3mMQYMPlNeBr8YJYeNSzBNzD2/U+Jwjih5jMKDiEFw\r\n2VLC2iY6wVzspAxLz819t2YFFgLaUSzM+QVaRbuZhgdT5QzOmLC7+PFQwDN7\r\nmkvS1CoQ0prb2cAqdRPCTuh0fKjzBgwm6GV7KCv5ZOd79CpgpcHfUdcDb3Ia\r\noSIIASWz9AS68aoFwPeGKP+X+eIeeMOi9qDyT4tvFAilJumc5/lDJnPYJ3RA\r\njQFy64Id8DtmP4XcgMVlXFd+vN7ZRa1TiPumcSfW3cOmYuu06GxLW5+s7pPk\r\nip+ACOpjgaciRTKLbkdi+REXguMHTecN9LEiaHtTcU7lfMYU1fb8zsyZ5pYe\r\nv+bQqZ7+9x+miEyiB67okzCNTE1EiT5hvMmQGXrn7yci0gv7SY5zzzErb5+a\r\nLsT3lliRArjZfKc/DP4e3YslLdFFLJs2uBSpuxuR5D9g3/w8oqdhv+79yoAI\r\n+UGV0W9iyniBhrVX/7Y1a7lIzlh7Jmk8XHchqXINbQaCtg3Wzmc8X/q/6BpM\r\nqeDfIzIzw442J8ldZ0Bj3vxM/CuNOGwuHxy1QDwwzIbrUiNphewxdKitpbaI\r\nn7lbMYjbdg6se1SbT+Rg73BaHf0qLb9ul2hir6W4TNYqxQef/ZnjC+KQQlGM\r\nzlmVJe+leR9h1EsjgBoC32LVLxH4RNkqrHs=\r\n=DT9l\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "478062d5310b27f1a6a259602a79bf84e233cc1d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.15_1669006120630_0.11889468518088786", "host": "s3://npm-registry-packages"}}, "0.15.16": {"name": "@esbuild/android-arm", "version": "0.15.16", "license": "MIT", "_id": "@esbuild/android-arm@0.15.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "0642926178b15e3d1545efae6eee05c4f3451d15", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.16.tgz", "fileCount": 7, "integrity": "sha512-nyB6CH++2mSgx3GbnrJsZSxzne5K0HMyNIWafDHqYy7IwxFc4fd/CgHVZXr8Eh+Q3KbIAcAe3vGyqIPhGblvMQ==", "signatures": [{"sig": "MEUCIC3n0HoneW73UAmaJXYpy1c6vkNFxCnPSWgLrOvSSk1QAiEA3IMX62ESn2Wv7TSywlJwQnkOsaQY4jd7dRyPqzYHDG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10693986, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjg49kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpo5g/+LpDNiH7xEOBgEcjpTtOKi2f3IpZ6JdMFFKT1eJrZ58R0QB7P\r\n0hVZf+i/0eO+ii5gNiXrvzabVsCgf8ixb3z3TzVrcmCtHHlX+0z1iAR6a9s0\r\nEbkOOrzpHX3LzEYlc8vkzsEzmsk2yMAW9Bo3XO5HNiEJ1BPpac7ykCH/rxXA\r\nJFnE7vqxVeNa7Cp6v0idR1AEbnjo9CyoljQp77mOw18w9s09RfYVk8XH6Szx\r\njdFKK3NfYhC+jU/GT5O4WJDxVY09F2hP4YfFQ8sKyCbZ3Dt9v5U0FyiprLX4\r\nnVdQggFOI6QimihUmWKxIxw0EyDmfsjqVzCK1Xvq/6J3kH32ky1b5UInxVIF\r\n6SMKhDlQI+F+ry/MtsByi/2AFVy3CAi+aw7FODzrsZ+PvmdOexqIUFI0xWXM\r\n6rwWLbV3va7KGdbR5lPe/OzDMUYn1+boHSiINOTPHuZONCyzEmGtEJv/1Aft\r\n1ha5X5vaAfqFLmsLDnbR638U+LNdoHc/LsPg81HHukgnCsXr9ucdvo+UiDWO\r\nwDyyyoIAMlnzWR+ROsd6SkB8YKIWLqtIBDt3oIzPRamghA0mWVqjNtijOwJy\r\n62AtwveNHXYQkhZ/V0EBnvea4TJcvuVgO5IVZWnHST3Sv9VPvr6NBgC+Y72C\r\nq6DNzpAUUwLti3FF+noFGdHqw2M1Ybi4AnQ=\r\n=uN8I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "50ae05ba9433c86ac227d917f3b92cec484ccfc5", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.16_1669566308036_0.14127877134056321", "host": "s3://npm-registry-packages"}}, "0.15.17": {"name": "@esbuild/android-arm", "version": "0.15.17", "license": "MIT", "_id": "@esbuild/android-arm@0.15.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "05162390ed2b0f2ae9647a809efb71fba14ddfe7", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.17.tgz", "fileCount": 7, "integrity": "sha512-ay6Ken4u+JStjYmqIgh71jMT0bs/rXpCCDKaMfl78B20QYWJglT5P6Ejfm4hWf6Zi+uUWNe7ZmqakRs2BQYIeg==", "signatures": [{"sig": "MEQCIBmQJQQMyLEIDn1CLIh+DhPYBG4nbzDv6vNfhBHj9n7GAiA8txhd/yeUdh6vVuZZxNsKNN5Mlg5b/gRxPv7asiwQqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10700077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJji/eKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpK1A/+KidwhriuELtKJbFen1kpZTYN+MfkjdzCht4U9wMjNlTuqt/k\r\n+QyX3KmqimhZT7sGvldS+hvUHKf1lTCXAgONSsQdrQBXb0uHCgIsDYevzbpi\r\nWQa8GvW6+sYjk6SI9qA2t6Hz1YXr4Zgw8+ME1k8me1Cz1tPN2yzwRYYFkIkl\r\nHKUhmyFE+UZ/KzQLeWDTEhzcH7VTP72H4rzJ4cz3pX8TAgc8NjdiGh3PkkvY\r\ncjGiX1Jzpj95NT3htDgmFcwosc02azsdkfiQC9UlQN2FgXEQWhZ02jelN7MD\r\n5iuXB0EhV4tDS6sCiIWEfZJRZMPKsMaqtWEqmE8S3Dx6u852GWqqWPDncI3J\r\n977f+g2g6ocULrYAiBalWoH/PCSfPuLg5N6SCQGVt5VyVyFpGm08SPven3tU\r\n5mQnoNBv/zKuG3fV3Fkub3sLUv9sOMZL73HLvU2TThCRQHOhURn3vHLrU7WS\r\ndxv6CIyt7jhuX2gCLvy9Jc9XdHIsxx3SXe4HqWacrU3zBMV4ci4OcsrjzM2O\r\n1E4qeRniGq5Xmow8KuOiyUer1q30XJQUPeXGwkSjv1UzgVrM5vverJJj5e47\r\nyASIhpP/+p3C6bSIs8XS+SwkreunRPjItBLuwr/oTVIpA/VZcWKsP1hz2LAE\r\noz51wABZhGLpRPh3yS39pNbkLGqLopNTmys=\r\n=MtTd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8821fbe6551f6a63cf4e086c59b0cd5288dbf4ae", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.17_1670117258394_0.9477704830966296", "host": "s3://npm-registry-packages"}}, "0.15.18": {"name": "@esbuild/android-arm", "version": "0.15.18", "license": "MIT", "_id": "@esbuild/android-arm@0.15.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "266d40b8fdcf87962df8af05b76219bc786b4f80", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.15.18.tgz", "fileCount": 7, "integrity": "sha512-5GT+kcs2WVGjVs7+boataCkO5Fg0y4kCjzkB5bAip7H4jfnOS3dA6KPiww9W1OEKTKeAcUVhdZGvgI65OXmUnw==", "signatures": [{"sig": "MEYCIQDJL9baxuG81QMiMUQ97SYZnHo7ovvpntQkxvph6NMq0gIhAIQtfro7TmWIKmJvrpwSXusjXgjv9xvxGbVnYT4Z7XbM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10703132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjU+lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTKQ/+KFW/aG2j8sca3Y84BWMnXh7TFZAs0eQP1SaHInMOQNJRgC/O\r\nUzdqxbHzDV5rBm0jIlpmZ2ZAKRnXd0aEWBRIQlsmnWaFSNDsa7ikiyhIG9NX\r\nwfEfXnAVY5moDiL3gu+RsrDD34+85+VsrQfsyvpCgnLu3oycEI8P9I+TwMXu\r\n+FZyXmownUQsksDQYqcE5hcpJTuEY4K8NBYEuEOibtqZ1eZuxNYLHKoZcwEU\r\n7xop2Bdg3/mxfhQOZOUUXHAAQp2+a+E1MW+hVTztFzWf4XBqbYOgua4n2dQW\r\nLnj1YAvYOrT4lsi8uuU/hAA12xY6sbZvUXJ032TT1cXRw/DnM9+NATHc4yAF\r\n4/rxx2oTarG9Fy4Wn+qeY5GntnrRXysOzKKkltbsuD5UpS0/BVZARFLR47mm\r\n1bnlYKncGbSlJixET0BavNkfT8ce1lC9obAjYFhuThHXuKVAyEuzOFuSEtv2\r\nYTeeFmB+TgZicsoC2V6bwbSpOX76GFTcjm0XD3IFviL4E+JwhPMlE4MHxfjO\r\nE7UVQIm4/3vPVsZLBwoT4H5MD+J27zSy89SJhsqUX7aRvgi/RQD76mhiPl1C\r\ntI+lPs5AP98NTGXTvNRrh5UyIZ2zKqp0cUcPKbsrGPIqxVaIlt9Pnihg/WG4\r\n+gLBtZ096NRIaGPwUZFc791FVsLYUlKRoqs=\r\n=fIMB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "2953831c60ea7e76dd1372204e23bdf7ff4ea459", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.15.18_1670205349309_0.9479292331869449", "host": "s3://npm-registry-packages"}}, "0.16.0": {"name": "@esbuild/android-arm", "version": "0.16.0", "license": "MIT", "_id": "@esbuild/android-arm@0.16.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "92b7f71bd31316ae69cddeb465803a9e83dda89b", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.0.tgz", "fileCount": 6, "integrity": "sha512-KfK1wuIhw3mNdkxHrMds09SqAjb8f9RIH84Wlfx12nKIrkxlUdv09fkbQCdzpQT6UY2CyI759zw6S/j84tPD+Q==", "signatures": [{"sig": "MEUCIQCENa3KX6hqa3cHmTthSju9NGCQkirutgNrgRoUbdDO0gIgUDXeNgyTjNvfRMT11h529oznLqiCXs9LDFbNewOla54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10715704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkA6eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVlw/9EDmszcATo0bzDU9FEKLtUw2wtpf2sNSf3hlg8iUgD8e0vr02\r\nkrmFnE4Qs0XM5KjGpuUQmGjuZsP0TOIjS9WNE1BGCKscvRhiI4i1wpQ8QJgU\r\nPX3HFpuxRgS6zSd5oud33mzoxK1fEf6XOHLIYd03H6n+uOxqM8AI0rw6uqE1\r\nJOdmnivwX5xcgmYJSFHwf5MRqbTGVehlvrjydSQrFyOXEKlb9WFjSXoP5Hjd\r\nV9dHi/TCo6vQIujENqv1YN/S6PT5wdOEP62FvKBJs3k73DbcPdVG286N8PsS\r\nrjR63jvN00+jC2ef5qD8UKdXO71D/kkGTCoC6JBcgZBdGOU9eD+5fdM2wmjT\r\n2FVW0hpJOeda/HRng1Wtef+d3ylD0xdp9wODJzTMEjW8sQDnabjeLP9u1y8j\r\n2z3VgmU4BtxPww79WvFN0I6R8dqQP2+eIYUun4xmN2hb+Hif/iVg9hvn5E/b\r\nRpMglb3g1z/8SsbERJ2twxrkAOZYp1pZclALab2zilnRD3qwBtIkWWWDI68u\r\nEsglp6h1mWvSA4Oz6znbN7CcL8CZYNh7RLxYxiTCxTSGBiUbb6ruPFJePb/j\r\nuXBm50WYTt5kadEgGYsC39MaFzMWeG+ev5G+nZ5vuB4CoV8zbh5tEBMlqIlV\r\nq2ry8BANqHqu7n0v7xJxKraDsZEZygrvRWY=\r\n=74OK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "6c8d15d404874fd939d7f4062cc6a660dffdabcb", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.0_1670385309800_0.5868963780075567", "host": "s3://npm-registry-packages"}}, "0.16.1": {"name": "@esbuild/android-arm", "version": "0.16.1", "license": "MIT", "_id": "@esbuild/android-arm@0.16.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5cc3f277b0c853da9d6241f8024da6a7bf6964b9", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.1.tgz", "fileCount": 6, "integrity": "sha512-zkalq3i2M+l812fhSswRM9FSryXEmoz30bfDlPYOl1ij0hBZd+lU3rRUzHSenU8LpsN/SAgX1d/mwq2dvGO3Qw==", "signatures": [{"sig": "MEYCIQCDULweSEazZHDmnqrv+OdCe2Kl6qqhVq8k4FiWQ4fRxAIhAISB4EvJz0D8tSwfYpqsro2tF76LTXP+sIva4YihaaDn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10715716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkBsTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmobdA//bkmLHh8PmEJI7E+OfBNwvuKL3YdihlWYbCKdsgcSf+TuWzDc\r\ncUdgTao2LhzQffzsGrxVlF/tmbNrCSIhiOEPADqjodn2VvHYGBBQVPf8uc2y\r\nu7ULFK3HHNPFKgdAKJxjY0j12iC2gmCKpmb+ehkMdmmyJQZfIbEHTG+T06RZ\r\ng44ZJxLo6LMTGDDg+IWtkuYyqbrIwxjAu9NI5XdDN9Y0RHhkMYRyb36mddyf\r\n1GszS1/t+OPObeJgm/JnYjxOdZ36VQaYGtk2/61NoK++TUDb/oHvYeoIXNLg\r\nzGWlPeCcyZA9nkL2kCQghLvbmTbL5MkYMZdvr6zhgTd/000IvnvMfMBXg+/j\r\n0McS8Tw9Y4rCJ0ZA4TZ1MRIXwG/DkSjpyPD7IMBRuOd6ix2wnOgd3j4FmChg\r\nZ/aetW6lTLxHKqINYXjWDZY6LNcB8CMm+3CCedx2Jg00BXrJaXtsmpuRpv0Z\r\nCdiE0b1ap4jpQiRUs3SQI95IMXwM8SBpa5nU7n6VV3UVgk41eNs8LLSwMaVA\r\noSS2NeVZzjppZ293+Uz4X8TlHxVF0XZS6phnQPJ5EKOuiG/KAJhsT/4TmBYA\r\nxA8sClPfYhTJ7NR7Bkp0YGJV/1hsEHDTBvf2MNYpXC8I+UEzXtrjyKEPTR2i\r\nVRHP3OdmUbgafkDYB2YJU430DEYx+q06Epk=\r\n=soeQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3b62a3680cdd1c9d76bed3e2e60841e371670c35", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.1_1670388498761_0.961583966229792", "host": "s3://npm-registry-packages"}}, "0.16.2": {"name": "@esbuild/android-arm", "version": "0.16.2", "license": "MIT", "_id": "@esbuild/android-arm@0.16.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "585ac1f25abce4c7b996f0a84f33f0ae6df043cb", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.2.tgz", "fileCount": 6, "integrity": "sha512-t8zq/Ad8njye3tYkbdBYAEGBExCyqFuPnKmKgLBF9+nIwAS/V3FYck6BjAx813JCGXkNsR1iriS8jQFwydT+FA==", "signatures": [{"sig": "MEYCIQDtAcEL+Y9+LNAwgN4qddaG7+KVn3RMg04KcU2lwHIPRQIhAOtTEcwWYDYJJ+EG79JmzZSmnLoaQqdCunhUY3actntc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10727756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkYtsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqm7RAAkVygQ7bCMWi4w5fPbbk+9bNe7pshbIw4p+zaOHijwXRH+TuB\r\nMvYH8jXXrMbEMZH0Ek8JWRAt6wjI9xphzfOMprAJZtV6MFJtlwUvvOzxBCWr\r\niB9ixkaQbObZFcSPuh7gPtIixX4zDtYJuc1v5+xivPo37tMBSz3QnC/JfCfq\r\nHeeegW30feAh1hGqGRdO/z28c0gzIvloL3f7ZT3q9OWj/8LqzOuZfo4CpZaG\r\nmhOuW3IPuMyK3/WbdOL4qQdovRX3OVBUBPcvKik4mvbStZTR7ozYjUzCUqQ+\r\njfSw7fIF7urXyh16kuwRS/m1GsYSlCNFS3VoxtcE1/GcHg8uqyIPkmdag1x7\r\nmdzvVUyRcKihgwACPEeV5AFdbxkVeqmhb+H5tEcHNlnRYFjUGrhlW2O9oh++\r\nOg5Bm9e2uxVdRZ4yqtmts4S48S+vYHl/TqztqSHEk2bOyknnak9NVESNgxGG\r\nKLk7Ay0HnTgRfHmLXMj2vIHwgfKwexcPj+xrQ1e75+J8VdNH1/1Eq+SUiaL6\r\nmazxBdd33KcLXeHZCOL2Bz9pREZ8LnKjM/0943BaUAAvWWzhPcPTPKOZksYb\r\n4WgFbZa5rfdqjjnGGVGLitc2jY5IILCAGFLwHdEV+RFmA5rSLl0RnkwM9Slb\r\n9Uo0+LukSrDibVx+53xkznDDIzpoHX9gqzg=\r\n=wblA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0829d74c0b3a913c0cf6d3f59902871bf63e0d16", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.2_1670482796008_0.21071795469520094", "host": "s3://npm-registry-packages"}}, "0.16.3": {"name": "@esbuild/android-arm", "version": "0.16.3", "license": "MIT", "_id": "@esbuild/android-arm@0.16.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "2a091222f3b1928e3246fb3c5202eaca88baab67", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.3.tgz", "fileCount": 6, "integrity": "sha512-mueuEoh+s1eRbSJqq9KNBQwI4QhQV6sRXIfTyLXSHGMpyew61rOK4qY21uKbXl1iBoMb0AdL1deWFCQVlN2qHA==", "signatures": [{"sig": "MEUCIQCHdvHpDaGjrRGMQbUgvb4HhG95yww5d553issSUGW3cQIgLSQNJDP5LBE+Z0wetD+e51vUlsfJlH+UNcYit+RFSh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10729584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkkVWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocJw//cnBs+E6boD4yBoWRTGmn8uzBVYpYUvIKUDFZNLM52gfUIya/\r\nUjVkc77lBKVyqU1Gb+FR0byW3JDUk9219VquymYJtfOzfkHdBLWDa0r2k0Lp\r\nyH+/KotOciAfntvXw+sGDcH22ujOJ/LdqIo40JiQpu2hkYA/LnZzS8/1kFGR\r\njF1toHZye4Tthru+tpAzSeZju4jvJDKeD5E3ue8+Br1Io/xt77m0O7i0qvxt\r\nO3MUcHs5Q/fPjsN3JYXFg9aT2rInRQYzCHyVmNWaqWUETUqd68SMvKaivgWz\r\n+1ioaXMYQVn2QALMlpYd7hVQ5nahBl3K5nr+0pXhpMghMRkk1KB/nNgYwjx8\r\nqW58AiNgt0J8dRL3n+hx4AxFl/rqmgaOJWkkL02uIyOfjcVcUplX7rhI7uZD\r\nvtSwgFOQdhL8AXgQ+Jlz6X6xSmXqEALyu0pI8pEk9oq1dziDG+8xBi8IkEho\r\nkSeislnrzWMRAfFNQ+45S0GKTb9KnDi1rEIxRncH/nG4yXsB7WmrZV75vosI\r\nuka111WZZGz9T0cUuoAwAajE0e8dQHbG4AGHrQalBwuLRYvSILW5JMONOtPj\r\nF44LvfkowHrraOn1IV9GSRBzLKWfIwDCMm34crTZ8+q3vLiXYoUl9ndcMRI+\r\ndWx/TEidb7prdjN3OAUoQjymvCOYxNwe9JY=\r\n=XSAu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8ef19fefc9bfdd28fab95dec3783d3f100f25e3e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.3_1670530390674_0.4912141432717294", "host": "s3://npm-registry-packages"}}, "0.16.4": {"name": "@esbuild/android-arm", "version": "0.16.4", "license": "MIT", "_id": "@esbuild/android-arm@0.16.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "057d3e8b0ee41ff59386c33ba6dcf20f4bedd1f7", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.4.tgz", "fileCount": 6, "integrity": "sha512-rZzb7r22m20S1S7ufIc6DC6W659yxoOrl7sKP1nCYhuvUlnCFHVSbATG4keGUtV8rDz11sRRDbWkvQZpzPaHiw==", "signatures": [{"sig": "MEUCIQDmNrjX7UHIwy/kCqaEGT4gdeTeyz3dK0/xpcEZkpYIQQIgaW5JJkO5WWALkn2hVGFyPzhaxN4qarRt+SKg0s+mS2o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10730175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlAISACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqL8hAAmSmYNWPdnV2GpUV8BN0gJZKikgrrgiRSDj85Xl4qILyvPvCx\r\nipmqDWRlCI0GyX3wDpUoQclGbWz5JqjMKW2uQMIqWqdTXO6/EWVZKKZHx8TR\r\ndKMtW/Di8kimlnmExSU0iOyiFNzFfkSBP7PPHGJgWo6zUrm/oGpacQGrPF94\r\ngomaLU6ODLYqtd7E/T1JkQc9QKTvwdQORXFGTkYdoMs3euN3XYkBkvdWiiS+\r\nSP2YD2Khsa4cZbskVVvP/lJSmUvpITa3HSjDI3zdodmNwM6WODqNyd5nMaCY\r\nwcqGiefQDHNhM93SKdDuv4fKYZBOaagRqNAlM64GD4T4fwTZGAS5ZSoN4/uB\r\nAS00YUBfiiid6LB1HCSXM2UOWA2RKPw44dNbiC8yA4JRTRpl2n0CENe5LtVd\r\n2DhP6XO9624A7/nGm6nn7sgdjM/tT5Ly2Y3KE+P9bAPhalSDfKPiYAc6a4HE\r\nHLeOgjLMCzWAxNjCZ3CGDKXts8aYT42ZTvgOMCPl6VtX9H5mNR6XI3gYAWUo\r\nlCuMU1YDCjEikVUNApSBQB/+FMY1d76MtCnqLwEfKw/kaqqTI9ZxuHk+DCSa\r\ni1SPw39JL5x4OAg5qKSNg69SF1Kpwj4nwDvtMz3EuWy0Hp1NJuKqxuNWHiqH\r\nohvLC6kmGoeNg/dcBrtNyqHvIKQupsBsiiQ=\r\n=ojMf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "643af8ed12345b3a249f1d4c7643c261d95c098c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.4_1670644242086_0.7006711874544123", "host": "s3://npm-registry-packages"}}, "0.16.5": {"name": "@esbuild/android-arm", "version": "0.16.5", "license": "MIT", "_id": "@esbuild/android-arm@0.16.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e6dd899ab0727d31c04309b6db2fb70ca4331af1", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.5.tgz", "fileCount": 6, "integrity": "sha512-eNk<PERSON>uLSKpbZTH0BZklJ9B9Sml7fTIamhrQNBwftsEHCUuSLBVunzV3LfghryVGpE5lSkOwOfeX6gR6+3yLaEfQ==", "signatures": [{"sig": "MEQCIGgSg9C/8vbBHf9awTIXzzSoeAVAE4R/38rr2x+SwjDlAiB7cVot27CjCx4OKE+Fn1HpRngQgUEB4APqQ8HZrBgpJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10752212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmLrDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7Fg//fh0M5Ehff+HDfreNOIsOef9u0vo4rW7MwKnTiVSJm+iae7Uv\r\nTz0djFu3Y50e6+oEWIOiHGcYn55xzPyztaL4XyWFyICQ/3eHN2Qlr1qOpL+q\r\nAA4IFUEWtIVvZO1ZLjDJ/bnNK913kJGWNZ3q0UyFYnVsfKvAF+BxhS9Hy4wo\r\nU2UQu0lsPofB/yFliowcztok8ESFA9dVM4gotr4tn6VMm2gF5RV0Qft1fdUL\r\nS8AFQ/n42OJEWmMj4MGKcMAmD2c2pn85DjOlPx5DqZcxmsi3/jEQkdFdRt+W\r\n6e0CBJLkux+W8Uvuy5jWo5YJ8qunpMIbwdvU2LqR3dGaW5ZGshe1GfKulUgq\r\n9Md2KIVCH+jzSCw/zSfys/2UtthmpArVHAglEe6rvhvz77ij1WYjktUWH/kl\r\nNViNDWieIwpA05ZMmSaolI7tOXKMD69t8NtSUln2M3ZHAIfyXMUil9ZL5Ebu\r\nJLGlrIeQ8y5arheGpdQPP5Yae/xjnXLfGXsI3xplXd1b0uzURkO7u1E9FYwh\r\nRA5LjNqddo3JzWOkYvo6Ka4AfF7P3W70xDO+OcjTSpBZlUyPX5tweD2LJRId\r\njzdaJT6i0qd8eMuoeH86cB+UH6Dvy0o91IaRp6ilaWDJ29qIcBU7wMgLKjIq\r\n+5+dKIhEifjbt0Ub5UpbfGiB3/7VIdJGCrw=\r\n=6NSM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "bb9639c3e1f57a3fdfaadf073a35d87020253f70", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.5_1670953666861_0.18076060413520123", "host": "s3://npm-registry-packages"}}, "0.16.6": {"name": "@esbuild/android-arm", "version": "0.16.6", "license": "MIT", "_id": "@esbuild/android-arm@0.16.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "450a79c5b04f955106515b0d24d0a428f3b7b468", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.6.tgz", "fileCount": 6, "integrity": "sha512-wc1AyHlFS8eejfAdePn2wr8/5zEa+FvF3ipBeTo4Qm9Xl0A0miTUfphwzXa3xdxU2pHimRCzIAUhjlbSSts8JQ==", "signatures": [{"sig": "MEQCIBsTfoic4kwMDP5hl/Nrd7VusNxoTjutR0X8OB56yINwAiBrua2pHTWJyzIDVfEJsmN7NGxOwPFgF2hLthKNYbYY9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10761549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmV3KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr97Q//XLQU7Z4eXz1OpuSQ2bjNerAXAINbCK9hJRjXE0WyIxL1v7Vg\r\n6E4UjWK2Kb18u2wQCwwQ1NpGJtYLFaXxohCPTvn2vo6n7/T/OfOIjsde3zFL\r\npw6oJQ1v/FBpdp0IQyr2MROWnC5JQMJWgFr027UnhMqlAxM6GcQlkxeg0O8Z\r\ndKE7Imv5tDK07AYm+9kXRNAt2kR2YXA0hr3qcVDAhcTSOcQhbexasBV2juMd\r\nNIQEJfarjWJ8CVBVRohKWQQG05YC5cCFRsgyQGDfccbkwMZK7cPDy4UM7nE/\r\njo6uQjCcGpVBDbuwddk/lW/SqIjI2CQuNBM2t9ueZCobvOtY2N0qbFhldeuD\r\nfvZOhlG9Pp1gN35hzEzNY4I0XKd0vAKJ1g24m8MsiRpD3HLTCCKV8e2Y29pg\r\nyEgA9f8gsmT+ubB5czzc1ozWvUuYZyI9Zdm+SoA1vHYYapWVyLsxHI1c1skE\r\nJ0VNOIncJPkW92p1jtUK4T3xoAHE8GTmXkba0Xxi7JPhB27YBESC76EQ2rwA\r\n7nnlu1iGh/w2g5knOKkl9NrpYboBLxXm+fHLtXZUh2+ntrMsFpqS1uCwE67j\r\nX9Sm/SGMvC5O2JT+rz2oSLSHu3CwzYECK2uh8s2xNBdIk74p3lANVi0U7CnW\r\noO73kbEAMgiilRzP/KFPtTxTgAHDPTNj3v0=\r\n=Wz2o\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee8e0ddc78114b73836ee1c520d255fd28c1ab1a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.6_1670995402265_0.1539081649518177", "host": "s3://npm-registry-packages"}}, "0.16.7": {"name": "@esbuild/android-arm", "version": "0.16.7", "license": "MIT", "_id": "@esbuild/android-arm@0.16.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "15f1a9b27b1637c38377b3e1f2d90b9782cdc141", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.7.tgz", "fileCount": 6, "integrity": "sha512-yhzDbiVcmq6T1/XEvdcJIVcXHdLjDJ5cQ0Dp9R9p9ERMBTeO1dR5tc8YYv8zwDeBw1xZm+Eo3MRo8cwclhBS0g==", "signatures": [{"sig": "MEUCIDGyBMGx/aeSwdA0HwGNEd81Kpd30wk5abA8gkHWKsEFAiEA6f9EFEDfQtMDSLjyNLQChv5slX1LIsvqMvV+G0XeNMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10770610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmlJqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYVQ/+Lwwrr0ue99wNNINJA/HZYCh1anFPptSsNMMzCR0RXTwrpUKE\r\nYhmBP7+KWVvWjqzIuFV76UP1B47txYkTnCJoT70sg6efiI/pIgNjwOArgKR2\r\nPxD6pANK/VNe3WTsPWZbEDz8N3Pzeomtpo+k+DWliYrd2SRmcwYwAYHgZly9\r\nnmcG1QuuRWHrDHTdbaFtb2MbxqFBUXYF097WBRFPUGvGfAc/8NC68bGEc3w5\r\nBErlDb9Lagi5+/Z3dE6RSyETUnZhvZVsXTRl3C9nTlZSUMC0vjKSDjB5+Rew\r\nhodLqLr/YSHZvke0j8QE3P+wv+lWTrPJCs+1tL5ZCBRDCIRC7HpoPA+VfKS4\r\n4dKlKdWlbpJKB8JQUmXTz6Jkioom7RH6sjr5JCTufT1MdlxdAIAnXkaEdWWX\r\nJXKSP8GzNxS09BZVvPI0dDUo8J43lTg6zOi7CrbbFCU9XfH4mFXJ+Bbnvr/P\r\nrNgjr60BnMqDIlP1HUzVGvN3A8n9iguAnh0gjI3E73Rnrtx9NwDdZE1lU/ne\r\nyZ7HPr68cDOyqFPwbT9f3C6Q9JA1HqlKG2LJzkrJ/mRzUpP+YFyd9XpPbiiO\r\nS7O0QqKg8i9lG7QCNonhezSpcBS/NnBQxtgFP2goNPbcoJskH5JSnLJxzff3\r\n/wPIwbw0NSFFaxT6uOyn9KPXJwK0zAHSuC0=\r\n=L4FT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "be16d813dfaca257af7ba99e458f54d1abdc31a4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.7_1671058025830_0.2421589570248983", "host": "s3://npm-registry-packages"}}, "0.16.8": {"name": "@esbuild/android-arm", "version": "0.16.8", "license": "MIT", "_id": "@esbuild/android-arm@0.16.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "bfe7e05ea51517948e93b190b3d8d8b7f1ce13f5", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.8.tgz", "fileCount": 6, "integrity": "sha512-r/qxYWkC3gY+Uq24wZacAUevGGb6d7d8VpyO8R0HGg31LXVi+eUr8XxHLCcmVzAjRjlZsZfzPelGpAKP/DafKg==", "signatures": [{"sig": "MEYCIQCeFyExPH1e7iB3JfORLJGBuQ3W9HaYTI2fG6fgejn3yQIhAJW1VI6/LKlFe9SkpTPzWOKDEf29cpoQXoaIPMaHkEQP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10775508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnQGKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqjmg/9GkFrwtmrg+1Cf8/xbEayhr/rMW2/JvkAF3t8ftCROHDaJ7yn\r\nt7BIv+XPwwK6l9lEEIvOA5AkiXFDDIs2LfxE2UJIAR5JzujYivO4X1H12HXd\r\n8BP0Nu/o1jxz4UmBg04yvsMPh31LBvCqzkSlpE5tHLaO3A0EAN1/9JIzaf5M\r\n1zpYXcWM+49yh8nHYxKFseB/T6+FK4rUnifQITf6mc4aW7DdGMiccsGv7jYB\r\nePxUPo20QcZUREQoaehxm3MK+UblkgFpEh3k401t7UAalOMtifZc28KUl8hf\r\nYJGal6Omsp3enE8PV4ub6RNy2VmUp9Ui9w+aPIuYvTI1Om1ZgJyaKACkzty/\r\nOIUeoXZBDEAF9MXM92IjaGdDXuQOEraIiIYRKCMsHW5domY5Wt8IVIk3APmL\r\npz+iv4/a15z+b757DW/hq7SF5drg4G+SmtGqKLT/2KX25Cl3ePY9gdDY+w2s\r\nfEmuxHOB8UUS5/H4ZOMIecx/0kfjtUSGcZ0Q+Pj9DRSJhIsWQTRudwJT4Qgi\r\ngVehN25+888xp10vCgjEXvhWA1fhRDJX3NzIMou1AD2TgPiZjDfl0jmIqQkS\r\nTH9spoVe1+M9S7hEzpZWUxPKdm/4H3ZwCeZH80JZ6f2HqoJcxq59uaSZV7Qp\r\n/tlDqtYNGAQs14JncyABzDBIqubolc4NkTQ=\r\n=H7bc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0ddb995d7d9bbcefa8e74c5a29c700111427bf18", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.8_1671233930294_0.8690859572728984", "host": "s3://npm-registry-packages"}}, "0.16.9": {"name": "@esbuild/android-arm", "version": "0.16.9", "license": "MIT", "_id": "@esbuild/android-arm@0.16.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "b0747ec074bba3ca652bfa8de3f55acfbb2d259e", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.9.tgz", "fileCount": 6, "integrity": "sha512-kW5ccqWHVOOTGUkkJbtfoImtqu3kA1PFkivM+9QPFSHphPfPBlBalX9eDRqPK+wHCqKhU48/78T791qPgC9e9A==", "signatures": [{"sig": "MEUCIFbZNDlRU25jcJzFIMTH0E3tqCuVjrla9/fL3T+HpK89AiEAk5qhNU0tb9Lc3vtWqOPp3l28TcDdEIdekay/Uq8XTOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10780987, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnpehACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgJw/8D3HM7bIesAlv/mC0+EnO8pLInjJkZ2MoDI5vbiQdA9WU0W34\r\n6Rkg2XGiKSApB/DgMnIL7OekfFoEp+VBHIyfGJRlNKmQg4JdgXdzYTGOQBqU\r\nCArCRsfFfTP/cCwzEAIR2WYiB1kRCNrpiawcSRxGY4H2LdJKT1zbU1wjB7bX\r\nI9BDEItQESbsAuCxSRtTomIMZmKYCYK1j7CMCehMkY/NSMcx3uytrJSuqnWw\r\nB3cENOdQjEQx/N+inVbr8frWvzzvX9thE4pyFbQuLehIcMPdXV3KGCJCjRg9\r\nE3CwMx3vv75uoabIs6oki59nfwaNctAYCC0LNAGivHJ7BxxkMbE8anwTa9tn\r\nre8G2HEmlb4TV/dXJSOJSVt4dWH0KkrcUZ1lqYALq7eTBUz3MOg99jvbDLK2\r\nnXh8PGiNvvaDgLBntZcrzbZdSaqMXwpMGpQ1hfLCfZ/XgC46AJBsGqKyCVO9\r\ndOktW1u/7CL8QAZVSRyLkE+2rheVInu5dJAWZnsDUmkvN2DIJxv00XJN1M0v\r\nRlaPVKNvq68V+JqOT0zuZ1UsmmhLqGZNl06Oi4e742ZGzOBDZDf8YhbB4UAE\r\n7Lq5CyACj/wdGj8dVPlSrpk7IQzB3hN77wgE/7nDl16I+Ly+Mp7Zj0u+fu65\r\nka0zNl9CswrQ4Rf0WEiVOEJVycm6LRUbVPU=\r\n=enG0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "29ae56a2ca081ed980ac9c73fcced1fdbc479f90", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.9_1671337888744_0.688322541018316", "host": "s3://npm-registry-packages"}}, "0.16.10": {"name": "@esbuild/android-arm", "version": "0.16.10", "license": "MIT", "_id": "@esbuild/android-arm@0.16.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "becf6b5647c091b039121db8c17300a7dfd1ab4a", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.10.tgz", "fileCount": 6, "integrity": "sha512-RmJjQTRrO6VwUWDrzTBLmV4OJZTarYsiepLGlF2rYTVB701hSorPywPGvP6d8HCuuRibyXa5JX4s3jN2kHEtjQ==", "signatures": [{"sig": "MEYCIQDmBjfmKKQHaHQ7NVuZKOKll7PKPR7/KrNAoV1gJeFwRAIhAMUtR6A4K0byggYCTp+U5ygzAu7b2bPBuWou+CrO9xi5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10797871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoPM1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoE+hAAkTefThkUnCy30Q0Mt+c4GKRCZx3ovNuF6m0JGX/24Otkb8QT\r\nhfkw9Xk8gjqlAS5YP3rUd2bfuR8u0CTLUXUSCUbBurbtazxY7+j9pz5sPMAk\r\nqZSpr5evTpnqV6y9e/x2/RkZr0O5ffevGz3+LdyRYnst3/zjzZqiSAvwWTpU\r\nS1yQQd6yAYTJ5pmyVr9yBAq8AOtMsDPNHAmo9sMgXG9kcWkSGz5ojgb89ps5\r\nIjtJl5WLEvfy9PhQkh2/WGR9A7rn6j8rDH/1lnK83jBXdi2YirZFcKOxCwVH\r\nUvIP/QjuTiAlGzT+W42O0egg3C/v/K3c95RWhyn5ZhPeC8I3/EyRKnA8y6D+\r\nbZJrxMXd1MZFa5jXnSu0lDSutbKnzGBpH0EY0cKIp4VQ8lymLAMMdKuEwp+d\r\nsruvInUW59mS+BWxkI/tvgoRCrjxtiJ7QioqOIax7c850oHrO2AJu9bzi+Jt\r\na6kRHKmpNwLJamnMQ5DoVQuJ9OUDWHvjzO+QRASfpu6ElHw8Y+EYViuDPgy+\r\nRdUf3s/EhcFJbp6Mjh8zQ+HalhdvdpbNXi+sy/BoUcQGY1v54PfEjjAu4eY9\r\no4CnvspOkhbeXGDtHG0oTJbrFnBy4TcH4RcPGhXEE711ExXFOiXGhqivX/sd\r\n9QvywvXjx0hVG9FIhLzS0fhEaZTQcgKkS9M=\r\n=ppmk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0fea6aea59845d1c0bef9dc16dfff636c3f721d4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.10_1671492405562_0.16543728314277883", "host": "s3://npm-registry-packages"}}, "0.16.11": {"name": "@esbuild/android-arm", "version": "0.16.11", "license": "MIT", "_id": "@esbuild/android-arm@0.16.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "842918ad32005d315b946fcf1c7752ea47b5a664", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.11.tgz", "fileCount": 6, "integrity": "sha512-j2xsG1OETgCe+OBA54DG5vLuGjmMZtQvyxt+rTw2aYK/RqjcG/F+UDdj43uoUOv8lSRC3lM4XpKLOVZfY/82yA==", "signatures": [{"sig": "MEQCIHVshNDE0bRkTHVmWOFrR1qflWbppVFH1R1FGNqyYqSRAiA55Jv0P7sTw/KA8YnhQ79omBRWGJoqU+R88zcl7AfRAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10799229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqkzDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGXA/5ARtehjDOlxCl2EyqoRdy+M+3ziNqw5a7qIce1sJgOB0dUiI4\r\nk0VjTxTtypvxWhcUIpgU80BoJAACmZ+glj/vL3wGm07TftTRr09N1o2XA1BZ\r\nVBCwmHND9aang5K/LVYBDWVFHZ0HekJgCT58Skoxm3ROOsC8mD08HB3n9JNk\r\nAXsrTAc4VxVQEGedS/D1g+Gl+Yi+gpKt4Lb+FNu3Cz1Zpz2VjL2fIIW5ETOI\r\nYLkmn6ZE/Qlhu5QMWgPK4qiwOiRxYt/rdXzvvr90xbeZLfuJ4e4VYsTI4Kp2\r\n/7F7UhfCecbwUxsphR8TIJ+jkzyeUwxA/uweHGRM+O3qfO1DDDc3+IrWqFPl\r\nmHHK9rIcUaHHoS+dqYCtASbYspBT5SUj8wh6mbsG14Af19tT5LZfM1A5Na1d\r\njVwtVULV2eS3QtZz9oGuHLTJxplAlbkNFh03j3xNzGjQkEWHHnFEVitJfbpH\r\nuAAQl2MYDj//QqmBaqW0o5ZWFZtqyVw+VYdfcpFfhiY6cRZ5dhJe+c7WaQyE\r\nyR2TnUCF0mxhhgEEvD6vzPOWCRR0tZWtifHhKaZMMkmXk2iq4VLhRCzcPyP8\r\n7Bzo78khGBMScUeg4CzOP9kDIIRUOiSSy6mt8IUmY7CZM0/ZComrGbHPlr9n\r\nE5b0gSTeSu2qLqC1xt0CjSA5p+8/sKbanXc=\r\n=hPXl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "17555921cbe672f6327f49a2436df1a69124623b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.11_1672105154764_0.2476839144058296", "host": "s3://npm-registry-packages"}}, "0.16.12": {"name": "@esbuild/android-arm", "version": "0.16.12", "license": "MIT", "_id": "@esbuild/android-arm@0.16.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "15e33bb1c8c2f560fbb27cda227c0fa22d83d0ef", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.12.tgz", "fileCount": 6, "integrity": "sha512-CTWgMJtpCyCltrvipZrrcjjRu+rzm6pf9V8muCsJqtKujR3kPmU4ffbckvugNNaRmhxAF1ZI3J+0FUIFLFg8KA==", "signatures": [{"sig": "MEQCIFVkuAJLr8a5g8xh9Eem6+I2jbCqc3LszF1sY4HdOUEAAiAF8C2VICoy4/ZTdx/WliHXX+AWg02ONQuwu9nK3FlZqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10800794, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq6T3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojMg//Yspt8XnfXRCeuDQjrdwLMnb55GT9tp/09EE45BLj6N8VyXZ7\r\n9ZFXcoUgot/tdQ05WT28tESxY5kpx3ExdPJw/GWlsgpsF2C4VxXFK5BLF0F1\r\n0XIkojn1SZygPGCkZAr5hVbEQYjkbToOF6sveo6QASyS6tuarhHjMSea/Ha4\r\nTXKxfMqxTicQ1GMQmkfnYhVvOiyD/n0EaOJcOayY3UF1CiTcnktpSMxau6/F\r\niUtKaDNAfkzfR6OmtIt+C/DW7IPdnKFwVHQF6W2NuMxKx3coM0xL73rCsyPX\r\nuoz4w48UsjjSjjwna+uPEGQkdiKS+JQsEo4xO3Ioje4l7VXCgzPWfo+hb0F/\r\nPLvNzKdGnNqxjADm9ihTLJO8TOyDKiQZOpBWYtE+Nn2ipOFwbUCdWoVNvQua\r\nXxXVOw8VsIcfwErOxSvMI9RPVWQb+x8zgBim5W0jgSuc6Rtkm8shQ350XdYt\r\nF7mGWG85NNShUyxsL/oiAqOR/LHRnKIbQSD5SSinbP2p65pdsU9Ob3lpeLeW\r\nnPEtWlfKIWo0EZX/CT3xNCJKdNCJICxySBOReKEt9/Rg8hIu3PwJgkQ96Y2p\r\nfVQaMtB00ilRoBeUZNfykk/t421JqL6TpawlaE4gRJGPr5XLjVJgopYVsTbt\r\n33pr227Q8JF41DGxVFBh0w5BAMOxrBtQUcw=\r\n=sKVu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ddda86edaae10abdc759601da6198b33e61c1220", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.12_1672193270791_0.8103727838950472", "host": "s3://npm-registry-packages"}}, "0.16.13": {"name": "@esbuild/android-arm", "version": "0.16.13", "license": "MIT", "_id": "@esbuild/android-arm@0.16.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "df3317286eed68c727daf39c2d585625f9c2f170", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.13.tgz", "fileCount": 6, "integrity": "sha512-JmtqThupn9Yf+FzANE+GG73ASUkssnPwOsndUElhp23685QzRK+MO1UompOlBaXV9D5FTuYcPnw7p4mCq2YbZQ==", "signatures": [{"sig": "MEUCIQDh5RnVgWAYZQFZLZ3p+rzI47BxX1UZjhGGSGzDcJGQegIgSg8/HUt7m+oyvBCjxkfW6b3LH2uPxWGRLOszD1cJUKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10808111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs2FVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoMw//XvEcBCU/FlH984iiGXjcAJWT9TYCa8JuPwntokh4ZR9yAKra\r\nmodKrPPt28DVDifh0VfHZKYXmzNgv+584UF46S1ZA/ZX6u9vm0TtRBzPyk+O\r\nhoqDO9y81IhPSsfl2Jz1QXalp/zIt0vwDauo4/FxUBiMvib1LnZjmztNh6s6\r\nTVarMOuKKk+okpgfTKJ7E0W6ZiFnRY+N51S8VvI2j/yeeXgoSTe53WCSAQVq\r\nhKKfFYynYIfjH9uIE8NLzVJGQayehk/EFoIOXrtHAbfty5w0hIFlmmMzX4E2\r\n2Bjgt4QOZNdfKAhBl5k2CnIjYgFmAs563t7N4PLowAYln5EEXqEcHysDiKHv\r\nQnGMqQcSgSfqaEh3JNy8ejYpk9Lq3VJFoPxs6QNhCBuYiqNmAyR3aATl+Ery\r\nf41h2vIqxk3oxLEQ6hfgnEaIpogJuT3jCD81k3Zv1dVYN3OurLWDMeT7woIO\r\nsZdpjum3aspZddP3acCXMsu8jk6PwJRtT/uDU/507LwKaz/vqUlwJuLaV98p\r\nNNHiCAOx/+ZAeMK5ZpLwehQRVToliKIkxOxbHObjgdvMm31+rfHP6uv22AZO\r\nUUdkziGNck55TxUnTjaLASJ+dwEgp0xMOjrNFyhua/PlvePbgXku3tDYdgfh\r\n351pOqrnc4yW5WnhEqw3CCUYaksWUdCuOL8=\r\n=cHGH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0db0b46399de81fb29f6fcb65dfb5fad3638b6d8", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.13_1672700245594_0.16447437521059727", "host": "s3://npm-registry-packages"}}, "0.16.14": {"name": "@esbuild/android-arm", "version": "0.16.14", "license": "MIT", "_id": "@esbuild/android-arm@0.16.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "24e4faf569d0d6bbf9ed46f6ed395d68eb7f04fc", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.14.tgz", "fileCount": 6, "integrity": "sha512-u0rITLxFIeYAvtJXBQNhNuV4YZe+MD1YvIWT7Nicj8hZAtRVZk2PgNH6KclcKDVHz1ChLKXRfX7d7tkbQBUfrg==", "signatures": [{"sig": "MEYCIQCcx4ZIL9FakX9tgJSoJkx1RxzYk/gE/riWApk769lw6gIhALhYXsbn5NyOKCE4GMTMWdNZB4MdgOK/L7DpBR+9CSZ2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10876020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtd3VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDzRAAgWhkMEygEXBIiCnNPGaxTdfr6ZjAKn2vM3qwyarL1MXVuwn/\r\n43GkGTOymUMzSEAscrclw5f6vxCfH1E8qjC7ttfv9edqszP2qBwXHJhX1LUi\r\nuEm7t0jfJfuH2WhvyizuOGNeW6DzG+QaBGZWFmq0mATVdnDpg0l6rub7ygSu\r\nc2+m5BdQLeFo2eo48EJoSRqlj1O5LNxyRVzu2xbes5IxgxtYTo/nL+hTqVcr\r\nctgZ2YC4LDP3nkAPYKsTHHew0OGxZ3FQ+AjUsKRyDg6qfiRFTerStwAXH1Rm\r\nDvdFzXoIuyybSCceyBD9vJhbm2dRG5AXenBSKE2/8Zz6QEd1emVYyGQPU44c\r\njAdC6Om8AbgEijBiygYHDSjDizAKHAY7NimKg4cBONdXe4627REs5uFdhJWJ\r\nR4rHTQhXhma51B5l1vWrK3EnhCen5liiggrZXxubfGMv5a4OAa17jcyxSbNB\r\nwVaY3A6bz9wpuJzWFjtVtpPNQZQJAOYQSAsBt9i2d2noU78MSg0PbhfdMpHV\r\nVMdTM+MG+8EFetzFg2FIWGuakyqDWENLUTfH2MA2RKcQdnAif1ceBctZQtcr\r\nM823U4g9IyGhfHk3sEII5TTNoLkAurKnY7+Q7Azk0A8rk+UTBxeX0odrq443\r\nb8CPuH1TYVqJ6xpMY7IpQVsKcU+5kppDVh0=\r\n=9weK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "93328af7dcf842f750c3e782bd83997e4f817e8d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.14_1672863189350_0.14014915565023123", "host": "s3://npm-registry-packages"}}, "0.16.15": {"name": "@esbuild/android-arm", "version": "0.16.15", "license": "MIT", "_id": "@esbuild/android-arm@0.16.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "6a8ad3016fd9c89bb419bd21605fba242c051809", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.15.tgz", "fileCount": 6, "integrity": "sha512-JsJtmadyWcR+DEtHLixM7bAQsfi1s0Xotv9kVOoXbCLyhKPOHvMEyh3kJBuTbCPSE4c2jQkQVmarwc9Mg9k3bA==", "signatures": [{"sig": "MEUCIQCiVLLa/DvJUgzLwKRwVUsWXNhLy4f2FmD8YxpqJ3ktSgIgE/XvQZhQeE0xoDEUzHGS/zQARcisvXfaWZDAI7LTF2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10877476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuPK+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpn3BAAhmOlRnwEkL4YRPfl7kjl2Tr87HuznM24pDuXjVm6r5XE76bU\r\nICsAfYnZmpts6bZuSPKx0mRPbAHf7SITabBn+BuiCf05TmhAZwVqVNBPlXbA\r\n4cXQ6vNsOtQhIRQyjc6/v0LmCRNKDShwpFkEJnEdhzjOC+V8upXAJty8s0hd\r\nHXNeCZFfNQEI1Ud12I56RzFSXkE/voEu48pNDfqPAVJVrTU9Nde/tGEX62kl\r\nloy9KwvX1ooKMbH+Nip88sZtP6uwR2F/AdXLkmQSFnnI/gkSn1iURh6it1FQ\r\n/qNCNiDcguSaFiPxq3HlZPE94XU75LcBYbOJLZXChjEbETs4RBOMQshNMq/I\r\nAHsHmFMJB20WWpf9qPFIyIS2LE478uVT94+R4r5S5X7aa5LZfFC+IOwgMfbf\r\nJpcT+5TXbHZw4oJipCLwr4X3txkpwvI40eIO1ZCg5dA/7d5wPDSm3a7fpwBv\r\n+ti0yZ4V+Dg2R5AuQsKNphveFLbKdNZ6bQCLH8wdSs8Xpm3yBPca23+lefLK\r\n9zZY3eeExacHh1BFeJT2vLsgwPPKYBdocaXQmp1k/kCtUIbnh6xcancABFfU\r\nzf5TR6sauaUz0f2V/AL0VZfojoYNYZ9nCuYdocCrDpf2q2w6ZvpjfHf31m3o\r\nChPPS40zhhhFZjys8iIdkGYOZhh/UZgTAkY=\r\n=12KK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "33a515951c626e56addc1dd4c6561a1514559fd0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.15_1673065150282_0.42981958512680607", "host": "s3://npm-registry-packages"}}, "0.16.16": {"name": "@esbuild/android-arm", "version": "0.16.16", "license": "MIT", "_id": "@esbuild/android-arm@0.16.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "23761b2fd1dfa4806161dbfc9e0824f04061cb95", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.16.tgz", "fileCount": 6, "integrity": "sha512-BUuWMlt4WSXod1HSl7aGK8fJOsi+Tab/M0IDK1V1/GstzoOpqc/v3DqmN8MkuapPKQ9Br1WtLAN4uEgWR8x64A==", "signatures": [{"sig": "MEYCIQDrUyrVmsi/nqODPxOwl8LW5nRqVWVWJQudBAns4U070QIhAPyL1Oeuf46ENKRwfEpavhsgs23vnIk344UBirFc2Op7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10878379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju0ctACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpq2Q//Veu67fiIIKbxbQp8vnU5rtAVh03E3d6NNMXG3hcMtWJSJSPo\r\n+yhKjrSFWIsB/AimFKKdLzejsDvCOejPF9/BxYl7pFa+IF9iyzhG6sykqa0+\r\nNBLL7rEGD6Mp20wuM46uYoqUfNb7zonNnRoTWsCIiTNWZIDD8bpQmybDGW8e\r\njDsohD0jL+gsdSLSuS/xJ0M63swwOeno8DagaJNRnbQusRaxD46G0HI5CRI7\r\nWrbxD946kjfzO8BPTvdvO3yIhqtO5PUs/JuUZADx+oinR3aRtY3G8rBTEnne\r\nE8/OCutSJ99ZT2QWgjcVcPsblJBmBoG7MGl7otIB2g+kxzfcr7RZUXbXpW6e\r\nOa3jCMKV3LLNN+CBlA5lCHD5d6Juwsc8vcnDZxIGqXN6shdenbV6t1+1W1+v\r\nobQntJ2mrRJnTzHio4ze6y25E92jg81PfCwYnKCVsnWtCB3oM0c+mqYj4RWw\r\nkWaLu1z1mzs6lWLFDsdTaWgO2CkKXN0l0bQGY1tE/PYjpM7ndbZ4XAZ1usnl\r\nh0OL4z36g2VxxFolbjnuQ/BEQILLLYxGVPeYJCCBXF0SM29TFPM1IzYInLWg\r\nwDA/YpdsgngO0gHdIobp6Qk1GhiZJ3C8wztDmlE7qyjTavAKEG9mIdXqiijX\r\n/IjWP0N91/KZ+gzAjUM330ltH6ywo20L4h4=\r\n=IWlZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "edede3c49ad6adddc6ea5b3c78c6ea7507e03020", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.16_1673217837583_0.2101402969284416", "host": "s3://npm-registry-packages"}}, "0.16.17": {"name": "@esbuild/android-arm", "version": "0.16.17", "license": "MIT", "_id": "@esbuild/android-arm@0.16.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "025b6246d3f68b7bbaa97069144fb5fb70f2fff2", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.16.17.tgz", "fileCount": 6, "integrity": "sha512-N9x1CMXVhtWEAMS7pNNONyA14f71VPQN9Cnavj1XQh6T7bskqiLLrSca4O0Vr8Wdcga943eThxnVp3JLnBMYtw==", "signatures": [{"sig": "MEUCIQDv1zdwiyTQZH0MirAPHFfDaZA1BsiL9A9rnjxhewLTLgIgM34f6Lk9nykyvnk2a+/MecB7avtLoeXU4eOLtgJ9GGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10915212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzDvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqfww/8CdD6GtM0uB9VFPYZGnv9UpcGOC+4+R7bWJop83tF2H5V0bnU\r\nqZaw/KVoIvjmoDNlBGK1wcX3rluq1XupH/PWhW6Hig5SNQ1UImaXjV7q/Tzv\r\noq1HzDqZlTKYU34DrYXAX5fB/D0ELfiyFRZB4WtamYcKU3nJ01gtdtKiuuJi\r\nc3psC6jFz3t7DxdVsT5Ot316ze5CIBIhko4rvN9ZAtRtf5zjQFd8DJYbZ3Nd\r\nLPEdFpjuq4EQxjjqACflWSZErFDeCKWP+YakJ5JkzSWE4BsVSaTUiM8KN66O\r\nhhXwpjom/VONJVAkdC+mXoa8JMwFuTLo9IDUOffZeCW2taNmfC1tRXqa43oE\r\nO51l3zSFollaxnc7p7irlrBCWVIbSRWqBYOsZYjBLu2oCPnOqGXrQUO/bxut\r\n2FiGRrw2sfy5XIqmxNaMSi9OLs7j87Er0RKRfsmJbJ/i/fi/qZzw0PoNaXK9\r\nH64fuOupCsf78iGmUsoWPOczYa0Xxbv7HYT0X45mYE/PyIvWsp2Wd9kqX4Sd\r\ncxU/OJsv6DkEnjkrGiJJlWYaaf/GXoAb708h3fRbN3BVbWYjEa5fBtdodd3q\r\nflJypxDldDRx0+jh0YKi6g26EqgnrVwJhtRDWS4kV0Kuw71ZF83UMW3v1KnQ\r\nEeFb+M0AiOjFOIiHerrEP1mIE7mG8q8KEVs=\r\n=bzLZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "d751dfb82002d332aa4dbfa89c74d25203d28123", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.16.17_1673474287259_0.24368158055322953", "host": "s3://npm-registry-packages"}}, "0.17.0": {"name": "@esbuild/android-arm", "version": "0.17.0", "license": "MIT", "_id": "@esbuild/android-arm@0.17.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "10d289617902f877a28f9f7913f4f54a4e699875", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.0.tgz", "fileCount": 6, "integrity": "sha512-hlbX5ym1V5kIKvnwFhm6rhar7MNqfJrZyYTNfk6+WS1uQfQmszFgXeyPH2beP3lSCumZyqX0zMBfOqftOpZ7GA==", "signatures": [{"sig": "MEQCICCuxVjdnYNxqV0cQnHlj8i9qO0Mu+QK2uEVmncEQQ2mAiADMeD+FYZ8caqmYwcSLnzM0CKlnKlrvENh6b1fx7TVtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10971161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwjE6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1Iw//fUDWwHpBWL6kI2J2M1k53k9t6Fbc/iOJCQJyZUcCNo47fXyl\r\nkMNxKXoQkjvmX7HpeLyt/CUQrF/c/cQjo9t2Aa4sUS444tz5n1vXs6cwH+r7\r\ncF3LIyiKv7uI8MUILq3Kurd+SPOx58dRIpBcZE0PHFBQWPAlyIadrSRzjkei\r\nEoqSgEdSqSaSxV3/Oskx1kKcjB2jYonVRCSOcnbz7/iPVegmSABAeS9n2oya\r\nLMd2Gx0P0KZmVKwipEhSI5ujhqDAwtWgTwZ3FM7Ru/dUoMidJ4HiJUWNkilp\r\np5RIFeULGU8/JJwTE+xl682B+XrFwKrUnyQRDjtPSq1YH0FwSOxYwbjAjJoT\r\nWFlk6hzRIbYW6/5aGAbpcFOSAVQAtdl5ILPU6kMxza00uKBLcObsHuwoxusG\r\nQnxk/kQYy5xsdmc/8SU0VR0PXjjfO+Owoa0Oat5wOb3skKFeSKpZBXvRrHSV\r\ncEh7iv5NG4j1idf+9kfZqRHXooD4XdEcE/rMAG4f1W+ezdK2r7WKcJ4JwagL\r\nsLdsMXdub3I6cmH3SeaZZxSXOpeIk5um+0TEqD6NZkmGNvSXXzkoVAf90cHR\r\npjjDBs1Ulg7SyUIl/LshcRpph85De6qKrB3YuIHOsa4XIm/b5RgvMPf35Gnj\r\n/h+I/bndEM3tfH0IEewze9Cjm50QyI/+YQA=\r\n=694z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "2a18b3d6ab3d1003afbcfcb4a6ffca61e04fcb57", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.0_1673670970414_0.02263805891127313", "host": "s3://npm-registry-packages"}}, "0.17.1": {"name": "@esbuild/android-arm", "version": "0.17.1", "license": "MIT", "_id": "@esbuild/android-arm@0.17.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "6de8d65b56414adce73a385d92344d99981347cf", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.1.tgz", "fileCount": 6, "integrity": "sha512-8ygAO+wxS3XcTxY0iuitMsttSIBUCSyVewCROQ0cdnp6u62qG+BWBjUxosUZ1EzxbblBJ2CjZwmbqSXjDO1P/w==", "signatures": [{"sig": "MEUCIAGAZ2KloQvNH1nVcINxjDrWATVT6AmjBlfIXlztCaLMAiEAgYyyi/amdXwqpulHrejv5yxwEg5Gs9Dhg4eNVUHx2SE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10983158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxZH8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpaPA/+IY6ksebQ7TXYtV9aSJrchH2426hIEvpwq70v0BNOT1TXiq2/\r\nbuXhYjR3MCm2jH2WRKnVJhHNLd9DCUaBZa+12gy92e464vYfSAd3Mv2xGuww\r\nVCwcJPEt27C4Qxc2sBrrCLpZSNMGG41cFyqsf8efohHNHTNFAmcebRCnioKh\r\nUM5DMrTIFStMfxB+8gft0l24h6sjxbFsR0RVRlUuJ4HyLw5tssVfzHXv1OgB\r\nDim3hnO7k1C4QuEIjoKrpZDgS8AdEVLH1BSu+Hyjql51JvaPJjTXWe/DlLrR\r\ns3MtyH6i2QmYeLtY8xu+uu+ORf/RenxkvFXfv9O6D+GNTJZ1v7uedjYRkmTu\r\nSwJ69bY7cFJVRWxFlBF02epJv3t1+WMLuV3W6Zu4td7of8j5BUOERu1qtgOc\r\n9SwKuHKPdpK0OEqRBdqCD+WGp33N7TWTnanF/NteibZHzDoXqN/MO3NdU0g8\r\n2nhBJBwVQYJMWCfbYuagPBkaR25tEnSiaCiGpCB1YYgC8SOXBnUJhIznydYs\r\nYyAueLnBmFs6uKz7+h/L+eKzTb06McofufyFQ3z2LY9mJ9avzteWEganYEhp\r\na9XNveJFEkvKjdtpUhgTsx6ykNvWvynflJIKs53B6fK8/aih91QdtpBB1DzR\r\nJ4QRq9+eShV+gj9qfUxTzHJa7NzOnnx2cSQ=\r\n=tX2q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "002ef9522a4103132cefb075aee3e09b0e4fa3f1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.1_1673892347907_0.4340198779426858", "host": "s3://npm-registry-packages"}}, "0.17.2": {"name": "@esbuild/android-arm", "version": "0.17.2", "license": "MIT", "_id": "@esbuild/android-arm@0.17.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "7cdb67672350177edbaa1de1bedd71b295989fab", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.2.tgz", "fileCount": 6, "integrity": "sha512-Art7v3xYfqH1gEMUSP0Nx67pNAlC/Y3qSg3mOw8Wg7MP9bJLXL0DrmJaV1Qz1o4FwagtvDgkVOeBDpZgxdj13Q==", "signatures": [{"sig": "MEQCIBYgP3FoQEPQLaKuobkaIohh5q9ktKDZzJykXNIQ94+bAiA8O1BJs9zMON/UTGULqOjinZ7ABNNnuPpQ2QVHaAHPTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10987148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxkK1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrLw//Uotrh/huNRVmA9Y56Hp5YrTAUbM2QV+vfIoSyXaEfb4t1ih+\r\npm2oivTzhubnew0RfVB+sJJ/bfKZh6zpuWdR0LjXBlZVetvfE6fCON2G+FaM\r\nQjvSKZmg3ldFjyCVPIwTiVcZpmCl5mrryXsIsVmm60QZdeVx0hu0pkSzpDGj\r\n0P2LtiFbh+B057PS9cOknEbzUVffDl8XJDxKQJjxKc67qriFmQkSljFQI+X+\r\nMtMcvi8wpHuFPQGGco2xNv81en1TEE5hfCDknsKW8Tlblk0j5Zi4kBdeknxI\r\n7TvOZZttYli8JlI/dYAaEaIZDG9KzQvMTTVqrA6B+T7VOIE9WOXu3zSPRNPv\r\nLK2vlQ1IGGsh5jp57ZacrxbYsdKtxYC1SJWDgTxwpUpSiFQmuSmGov22ESy3\r\ny9KKbVHjiTai55VaKF2dX38l+bxRohZY4ZUt7LU7zDx3NVY/FmB3pLkGYbJ4\r\nR/mqlodVUfBivL+NiPlXFLjn/XrNxWk+dalwN+FIV/rrB2xA+bIQW0vuXMNS\r\nrwNEqJ0sqddLA1PWX3zlGQY6OVrN0A2QXtWIqtwyPSan/9HpXwKsskbOnoTP\r\nnnlInLUVPt2Ld9pNYWJMRdFf4gr5WK07dQ8gqylSTBH1guXmgipYflC8hF49\r\n6PunTHqMNzHKeASo+rrVTK6dJwDWhUgV4qM=\r\n=7ywB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a98870a2fa9f7af7024be24cb6833e638aa71da3", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.2_1673937589178_0.8039617081038308", "host": "s3://npm-registry-packages"}}, "0.17.3": {"name": "@esbuild/android-arm", "version": "0.17.3", "license": "MIT", "_id": "@esbuild/android-arm@0.17.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "4986d26306a7440078d42b3bf580d186ef714286", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.3.tgz", "fileCount": 6, "integrity": "sha512-1Mlz934GvbgdDmt26rTLmf03cAgLg5HyOgJN+ZGCeP3Q9ynYTNMn2/LQxIl7Uy+o4K6Rfi2OuLsr12JQQR8gNg==", "signatures": [{"sig": "MEUCIFIXx8lhVSc29llRcrHZ/MTGFD4o45IjPNOnxmJnjVi2AiEAhKxCK1xJym5eldBKbinJSy0mnrNThGB4sdGsSlFhz4g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10987792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEUgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDWA//Z6xsf8Qy2HTldGc2aVW2Sz3ZYZUy90QVpiRfPBfORgP3xamH\r\n5AA22PxExPOxFrhI270I+4+FkjEuK5AHXYg7cG+EsDNZfYvrERk7Ooifsril\r\nuoEudFLOCrjRlm214hrm+3ttHd0cOXjLzXRwobnIgvwCU4zAmqRPMt6DOZYK\r\nTmsPR8hCWL4gSLrgSUVb1s8aNFeAp3Lf4hHuKIdSRil9fb1eQ5X8vLlCA421\r\nrMcZAnkd7KP8L/O9WfobwdzYAooldfLNAchgU6v0qmtWmewfU4T/MwvUSNnG\r\nz2e0Z2y44Z0PAWJvyCymr9OcbeN72XQwWJhAFP9zzPxWDrCwXVP6WaRJmE3j\r\nkj631qBntw3M4h0jKVkQMfnQKsyZX1TQZ7ol6v91KDqrZ54J60CfGs75g5P/\r\nu5ugjunAOmaHiNk0QEFx6PVl3Le7DPlSK7ebgyUjAlfW6M/pjGOzYFTv7hA/\r\nmemgmYbRKDQmIPR6g7/InV/XEXiILlGqqHlNu20tUrdGpi+ZIakVFaaZoBSk\r\nwm/neCsXkmBzDU/5X95jTDI7C2RGrKOEqgDidqPmE7jAITKorLuYQCVR/txw\r\nAB0ztUEEhFZdiFxWwlshLAs3sHrBT1Dg2LnaVUS4jJH8niH7NZ6W/ou2NirW\r\nVuBogMkEkfcsR4Q18kkIPzag529uuv7501A=\r\n=vUV5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "acc5becf4d4e81473761091fc340efe16325da4a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.3_1674069280403_0.7036943651803003", "host": "s3://npm-registry-packages"}}, "0.17.4": {"name": "@esbuild/android-arm", "version": "0.17.4", "license": "MIT", "_id": "@esbuild/android-arm@0.17.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "fe32ce82eb6064d3dc13c0d8ca0e440bbc776c93", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.4.tgz", "fileCount": 6, "integrity": "sha512-R9GCe2xl2XDSc2XbQB63mFiFXHIVkOP+ltIxICKXqUPrFX97z6Z7vONCLQM1pSOLGqfLrGi3B7nbhxmFY/fomg==", "signatures": [{"sig": "MEUCIDL9gVVJTkLL6S30deWkxAsCp0TitIhji8d7g5Wy9EgbAiEAwE1yYkHtBUM8mCCKAuxatq8RGzAA5sGf8tSQ0urhV5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10999050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzNQaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0qxAAmzz9E84SWBqoSm49SGCHW53IXW1LdMdUiiN8B2BGOmhD45OI\r\n/oJty6jtXagxmXby/rGt0sbJV0AeTli9p3p986J+7IuyxwJdHXhnCSlgKu9y\r\nevVC8DlVJJh2kue6WxQQ/iZkZg/CokC4QDOJNDjuva/xBNcZoSCEcX+uny18\r\nTEuqusO/6PiZFYQJkSFzFln6LaKK+nAPQwisdGSabBckOFSzabnQayapBmkE\r\nAP5qkvTY0fDcS2k69d0A6wJcvs7rXdBgoF24NGvcpfXI+YVtgZtF5Cch5rHV\r\nUiaIDDbOO2ghwzT2JfcFcvaiYjugJ/MkshFuxbmktPWC+JXxX+QfkGDGYWvq\r\nnnGxhtaWzL3aMhVLWjyR6VmH1lIlwIjcIcQJTZo0jNkgoDVUot0NyqfIDaN9\r\n3val/5b8Q/9UWlqFTMrlVkXFYqkZrd+2915ku9zB5nzWqfRQ7+1nszieoOer\r\n1Q2b0rOTBx5U+sgijKWqBcNoJEJQccluosKCnxpmWV28ONQRvvN7caUDt6wH\r\nFFXsBrLOAR2l/u9a4Xha9uk7F2CEioEVHRVFVl5p1+dR81XeyJTn48G5cFyB\r\nChYglykAn7eOjAkrzFGAdFgeB1OOM+80bXH6PG5D+RP2oHagi3yHugOZusuv\r\nI34uUWNzI2Dx1fKJ9BnpD6I9Y1t8cRzUGXk=\r\n=u/x2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3c83a84d01e22664923b543998b5c03c0c5d8654", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.4_1674368026218_0.9611578391043605", "host": "s3://npm-registry-packages"}}, "0.17.5": {"name": "@esbuild/android-arm", "version": "0.17.5", "license": "MIT", "_id": "@esbuild/android-arm@0.17.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "9fa2deff7fc5d180bb4ecff70beea3a95ac44251", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.5.tgz", "fileCount": 6, "integrity": "sha512-crmPUzgCmF+qZXfl1YkiFoUta2XAfixR1tEnr/gXIixE+WL8Z0BGqfydP5oox0EUOgQMMRgtATtakyAcClQVqQ==", "signatures": [{"sig": "MEQCIGVyqTjBD/dIvmL44omFr6TRPBFMVCT80oC3bMQemiKnAiBb9AbE5SlALIaprPhJkS/0Ps8FmLmG5wNOV0bgSPZ+Qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11008538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0/3jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHABAAnJ7orC+1qrC6Qg6woHld44RljKC0lDQ5DIZUzlFcZDE25/0v\r\noR+fuEBeV+0xC///ZM3tvWrS1AKNDOy73aZxfN2UJdaknd817uhecBRmH1k0\r\n5qll0etUJEJzVfHI/Zqj3kSuWJsm0qsx5BPlFZ3uhmgOo5DIfiyX3N3HnrVW\r\nDhLncx3qZyYQu4JWmIHP2t9MbfpKHEmmMisE8yiS9qsNj7OD/HSSYkP1G1yX\r\non82iHlOsrcm157QGiNZej85nV0BT5wtKN9XZS9K6TzvJm7YL+gItMLyn2KT\r\nAsnavr0UVD+jAsIFUIFpx47PgDYoXpeYKBjLjCngiLd1ANfrnWA5rwn/1OTx\r\nvjBtOLTGEqZmFi3wpBmbfsAINB7AICzH6+k8Y2BPSuytM640jah650yecO00\r\nZ5gKJDM7eCp+EhsrswDZ/mP7w1Q9FubDxmtCdjsnZIzxIeYCdFISk3/NstbT\r\ny3+Zi5rDlvKAnSEI14dk5xbgtA+NuH+oZ4Nxi4UkJgQ+9rPLbtIvoZEfE5WZ\r\ni4JcSKAnhjxrm2gsSbN4IgbAPXZ8qF8VNd0itTQfC4hy5w0JKQAFMyYiqn2y\r\nb9dRscgIe9cmHNTVFkjmzw9ihAb2jdYq1ITKu83DPazO4viMHsLRAvjoo0iB\r\nsyhgsGS6da7bCVT4gqRWsnD99eAWcxCubGI=\r\n=r58e\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "a8b660d85a0a57087a0f188857519f194f52b84c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.5_1674837474881_0.5612496351728871", "host": "s3://npm-registry-packages"}}, "0.17.6": {"name": "@esbuild/android-arm", "version": "0.17.6", "license": "MIT", "_id": "@esbuild/android-arm@0.17.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ac6b5674da2149997f6306b3314dae59bbe0ac26", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.6.tgz", "fileCount": 6, "integrity": "sha512-bSC9YVUjADDy1gae8RrioINU6e1lCkg3VGVwm0QQ2E1CWcC4gnMce9+B6RpxuSsrsXsk1yojn7sp1fnG8erE2g==", "signatures": [{"sig": "MEUCIQCEI1Phlpj+K/uwXK+TDnlAQGpEqYMdxSDjhDjJ+pCoYAIgNt6rm3NoDlfqwt3ff+ck9h19YxrKhoRZOtHpZQdVIwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10222544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4TJFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo29A//UmBEqMVZGbWfXvutEvnw/3M1PgNFTmPXUDpaTMitrwASWo4N\r\nSCIMEL4HlZObMSXdBXw+tIAeTl106wJdeQUZe1bL+EflazDHW9UYPi2GTI0U\r\n+tcbtP5G6Nj69kIOa8NC3z27UD4ALtVrgTcUse3KAqy32oqZT9xlwlG8doyg\r\noGvz8wgDSfsb4hX58ckt8P2iK0bcbj9cCEd68UcnvPPTsZnbQpvt4qcxcviQ\r\nYDfFI10X7wqKi4B1LYeIpxZ9ZwagJStS1iY0dSyd14z5dg+HJ6aqVUiShLOE\r\nA+ydW9TAFRNjugQhlGVSv/JWJnB1YUTQMiZTglaLSdOcj1cPmjOA3E1yEeKp\r\n6k9utkgtOEuRm8PUpCJwJurweCfVfuNx+5QUwYlUbNW6f7jcXojhaSMGRalY\r\niLEmL+ImzCi2VJj/L59Dz+tHnfAErNfBAJ339KYzYRgHdplUSiw+95LfUlbW\r\nsBcxCyzShTIgDaFt7j08lFxOU90c9UH4H57OgdXuAhDCascsa+0W4JvpC8Qa\r\ncNykJOd3wg8aP/LnNoXrR0jfjcd2SAK0m7tAvNyxlR2R8ALX89sZu9dBlLc6\r\n/vQLEcYJb3v0jly/WzfjLNVFBlRrjTBdlmplRPE067dua7lu32UNHiOnvxcd\r\nvIPK5xWlXADNRs2XUfYUdb+DvPe0CqsktfA=\r\n=p4Ps\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e1143a75dd5e7d9fb8591096edfa123f6eedbe44", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.6_1675702853365_0.41140320096626404", "host": "s3://npm-registry-packages"}}, "0.17.7": {"name": "@esbuild/android-arm", "version": "0.17.7", "license": "MIT", "_id": "@esbuild/android-arm@0.17.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "fa30de0cfae8e8416c693dc449c415765542483b", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.7.tgz", "fileCount": 6, "integrity": "sha512-Np6Lg8VUiuzHP5XvHU7zfSVPN4ILdiOhxA1GQ1uvCK2T2l3nI8igQV0c9FJx4hTkq8WGqhGEvn5UuRH8jMkExg==", "signatures": [{"sig": "MEUCIHngswT1IFiqufz88kHjl+1RdDZmxIneG3VnlSco201BAiEA+I0vUMUR4g5qiwj9nzLEL/EmU2mTtU3/ZLQjeC3Elmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10225954, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XMqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMlA/9EHgkD4UeBxJbRiqwsQ1stRAoiSSPPqkmzQQ6TVCpnqXsVqn+\r\nxZWBeWVrujtR4oxUFGKMCCyDUFECHV69P+QL7UWgopCq1KtQmAnrSFCMZ8u+\r\n88KPRr01DdgEdutvAvwLWjZO9xniTlb6eHIFx4Cuj9Z+8SLsvZgVaI3zGdxp\r\nXUu4+Ap6ihHoW4NQ4suj5EnMBwKqTafjkA6TLxScBOPBU1IFwvqFAUxdJ7Ga\r\nXBNCtOBrbDV1uJKd9qdIeAQDSRYqVVJcpP7ry7/LzOPR5twHdr1wHx0Jc7e9\r\n8y0ymqZJbgsyCXSt9ZLVZpDuy4E3AvlnJGkZDwCdz27VbCOTYcfGeWa2igGP\r\nZycdQUqQx3RWwtszPGrY3EFxVkzlyo6jX12AS/NozZQBZcWvP+IratH7kGVh\r\ni3ecDnSH3ea+nHTuC7J9/0rFJ8V5acyO6r07FuL56rZlq+UocWKKR0ZzbUVc\r\nMzSMDQm8sqEPMBhjSkDWfp3md5rQL65cFfyA8SoEOSozPr9PBNqEe2qgDQ6t\r\nolG8g4u5NXq8aFc5o3bL4WEpWCJMsJFe/TYwA6+nQgs6xvjRWuCKrz3nIVMg\r\nGHhap8IAh2lxF93gq5mKeZR8JTi8mS01fohDElcC9wVGHGnKOcUl50Oa1Afl\r\nJaEyrN9lQI+pKssm5yzO0Dz3I+a+TRo8Xjc=\r\n=AR7T\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "e345b13687bc3ac86f18f4a266a162653544ad31", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.7_1675981609919_0.24477698318436136", "host": "s3://npm-registry-packages"}}, "0.17.8": {"name": "@esbuild/android-arm", "version": "0.17.8", "license": "MIT", "_id": "@esbuild/android-arm@0.17.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c41e496af541e175369d48164d0cf01a5f656cf6", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.8.tgz", "fileCount": 6, "integrity": "sha512-0/rb91GYKhrtbeglJXOhAv9RuYimgI8h623TplY2X+vA4EXnk3Zj1fXZreJ0J3OJJu1bwmb0W7g+2cT/d8/l/w==", "signatures": [{"sig": "MEYCIQDjioyZd2BSdJEB8t2+Nz1TP7Qr5v9yAIerB3i6Zg7HdwIhALi/FAA2n4CsLxxivr7GGdhl0Zhy4ksvwTVebIKOF8Of", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10236790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6dpDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmraHQ/+JH4HMSBIJYVZCVE97ucCoCWEzVyJIx05pXoXNFBuCoZkWYLz\r\nGR52VEII7V8M/gqu7qEojgzatEA4LLKOJ/BmV1dK0L5CtPossvVXNdUizKSS\r\ng5yXGFCyfwxssalQyeoZY+LtBwz2BJzZNnsYi+xhDwjvltMI7gOiekFSAXDd\r\nRqlegbQO0rG1fsuwgNso/qq3d3law6HLDbDSVwYxNAutOCCAYWrMRY1FxzwX\r\nBDXVrnCkVB57jqMQJz/l6Nz1uSynsMmF7nvvD0TFxAZgyr2Q9ebR5Uu14VXE\r\nDLtHlIPcCCFH9w0Ut5Cudks1mXv3yYmEEkH3ClUFzjg2ZQ9vX61ldGrhLmyP\r\ngEIYJlPTTZC6Zq5fRTAYyItxEe4RV6eiiegro1mMP0w3ITfRxflrF7+sjMn+\r\nMRjWi43IPkd6ZLgZQpz9D4AyEeUpU7Du0gc9CODmSof2szNagi/1oWcYWTQI\r\nYp5XY6a3MJpfAc9hfeI9QKNmKy25fjNsWuLqQSGBb8G/gIPzp/reC2UKVPXc\r\nn68mJTjcxLTgc/IERKtAd23ZdI7w4UsCDBhjlFkXWgvVkRlpwq7W/i6NCcUI\r\nRN6D+87HNJgyQy1eu1OcshKSr4a5bzCBZVRGmWUWhFFHXRCvDv2RICmnLAkn\r\nFSBML4WDHAv3zCqKAYiRlxA6ieS8sZFbSu4=\r\n=mHnU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "5e0b1cd2ce6297cf31b132e413134ffe2576c668", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.8_1676270146926_0.22029604368908662", "host": "s3://npm-registry-packages"}}, "0.17.9": {"name": "@esbuild/android-arm", "version": "0.17.9", "license": "MIT", "_id": "@esbuild/android-arm@0.17.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "7c2e3899715e190ace63291adc005ee8726d3ad9", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.9.tgz", "fileCount": 6, "integrity": "sha512-efHnZVJldh2e18fK40RYzYTTRDzZ0QgL9V/73PSsAH43BauvjVwkqSHPhbcn77H0EQOUM2JPuO/XCg7jcKt94A==", "signatures": [{"sig": "MEUCIAff6z6Z2MZj+nzgmjquL2UeE/zjtX/YX7acP4qBbfZ0AiEAjqRRI+TbcHBod7+RRAIErFsNyHLdyQsGWcQqosz5PM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10240336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8mA3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooEA//VRQUUw+BMNk3FoiW9qlidM2rH9glwBFT8cKqCcwU4AAfCHEt\r\nqEng3ir2KDaSj6a7D39jsXQ5NJ6oJN0EQbwSdwx6TWKuawB0ZQihOKPePQf/\r\nZDV6gnLqxWcW0fCLR9VcawZj7x3/fGUmluubKVdFXYtTEVOmZTx4wzsTICNh\r\ntX8nYIrwIfjmYQeeItgl2XnHQYvMTyQ5CijUB+28LiqNKt0qBX2WOvwS9fPn\r\n7hMXQpp979rhAUiCPr/BfIsDZ8Innqw/nQWi60JOEou51w5kahadPB5CAXRM\r\nVYDHfKt2dreNATbkMe1/NKve+HqnHA/1jv8xSSs6L16D2QLh4Jh9Y1hLz1TQ\r\nPpvoYMrEKx4lB7FEhpVS1ZpK0QQkxySuLm/uPAPn/oUnGlJjlQwqtSflnoms\r\n3S9YAhqxtnM5OX4X+Q3XCZB0CPnUFG+LjgLItaY2KFx/FqjBZbcSG5Qlx6Tl\r\nmIgoCfvsWemDNVlBKhmaNWz4U2QvoQwi7lfd88t1jGqsIWqQaidhK7UBXPLS\r\nSsCN65ZMUg2D8nv+g8YfHnnQL1afB1VoqDHrJ+CUNKhtNTEW+zgNoXxsCkc6\r\n1gfANyr+b8YQKoizBC4x7/FazaFv36rHEPScMUI4xSqIg+/pmYbYdFGY+jxT\r\nwFffuX237sNEDC26ctsVFAbH+KUn+G4BhG8=\r\n=+fkA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "3765e880ed0a55b8e62bfa17b7e004e656eaf914", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.9_1676828727014_0.404189256159474", "host": "s3://npm-registry-packages"}}, "0.17.10": {"name": "@esbuild/android-arm", "version": "0.17.10", "license": "MIT", "_id": "@esbuild/android-arm@0.17.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "bb5a68af8adeb94b30eadee7307404dc5237d076", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.10.tgz", "fileCount": 6, "integrity": "sha512-7YEBfZ5lSem9Tqpsz+tjbdsEshlO9j/REJrfv4DXgKTt1+/MHqGwbtlyxQuaSlMeUZLxUKBaX8wdzlTfHkmnLw==", "signatures": [{"sig": "MEUCIQCI9gvOd0y2uW+pCdRLDTUN9oPqdFxzN/GITN23vkDh9wIgLD22ypM0Z52SHSDq2d/40Z7oEGKbE+FGZcSORuH3ok0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10238447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj87P4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouUg//di+51YnUoqcX4TAMyxpk3wMNLr/Csl25/QTSwphQynfdOzqH\r\nzr5zCl/MpJimAzWxHppalOMOHVSpB5IBXas4iij18YFtfsGxv1kGYRKdO+Dm\r\nWVq9jok+pIOaIOgzvS+vrhuB/x+4ydD4hBkP1QrZVbZlala7hmd3T3UFUVDe\r\nbQfng1ax6S0uwjqoteXY2shg5KHDSL/F934llRhlJLFECHt1WlnTkQ/Vt+28\r\nn7iKBcebDIa54cX/AUFWnjFi+ie+NnCNb+LKFLS+GRN+ShG0h0Yqg4JvfQAL\r\nd9fd9PM4wjHbf3U100x0oYhj5eBReZhAFnuad5mFrIxCmS9tn8qxN/MH2NqA\r\nNSFAXUL8rtfp1cy1wPFTbF+dCqoUi1kFNNBejaCRBgamj1A2gymq0wvQhPiI\r\nfmlPhsfbD6h6aohQ7Li5M6xQkFtq72y9y7Ag2Q9KivMJVVCKo1h+ge11eCrR\r\n08vkoukPrUMJ9bKPf5iiuKvA70AX5niPmVJti5kdB6CyHsggXidHbxs042au\r\nIL5fdOb+Tx0cPV/U56iRL6iuSMX598B1W3fCd6ZQSbANL+EHm3GfLLpssPZx\r\nwDT4d5pn7bOdZ9QRwLlL3ElDxBrwp7i7X1R9jtQzR/8zeox2OtMQdDPzv5b2\r\nO1YyQqed/EIgc4AdIxMpwOvysMJfKyWs4Q8=\r\n=470X\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "996d400a7ab25b67b80122e2d4a8515575918e79", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.10_1676915704506_0.34217625043334565", "host": "s3://npm-registry-packages"}}, "0.17.11": {"name": "@esbuild/android-arm", "version": "0.17.11", "license": "MIT", "_id": "@esbuild/android-arm@0.17.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f3fc768235aecbeb840d0049fdf13cd28592105f", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.11.tgz", "fileCount": 6, "integrity": "sha512-CdyX6sRVh1NzFCsf5vw3kULwlAhfy9wVt8SZlrhQ7eL2qBjGbFhRBWkkAzuZm9IIEOCKJw4DXA6R85g+qc8RDw==", "signatures": [{"sig": "MEUCIG1HZwS+GCkie/GVlowrdiaTepTN6ON7gJEiRoWRDyLiAiEA9MJKLtemWF+iYTtBXYSXjNex6hOxVQV4dlxB4+woyFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10242902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAndVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOARAAnRa/+r1ZqUvJJgEgvo7VKpBtlYldMVSCA0KuC/4pMkUhEOzX\r\nBWV8jjREaxEiyBpmQXhDm3Fvj3H9CYZy5pIL0J7KIKR7AAdcnEzauiOPJqyY\r\nw7i8Fn9RnJQIV59U3dGZOJhZ6GDq8gzydiOhAm07oWVJlYDVTTg+Holfmezt\r\nPzPkzs6rI746pEKyJdn1bTeS2Anbgh3Ag9Obzxkyt0zdQz2qpE+5MrZ8TDG7\r\ngiY/tINjH28R3NhKjlhigl8blvSS5Ih5IDEb84F1+8mPOAL+bhTlmLoycFXI\r\nC/+K8/VrEBVZlz1DIYbcTwIW0RiOf19cwMn4y+HMKb+GqgIR++X0qsNrFcBX\r\ng5XZlE4H062ogGataJXBlLUF25NS3b6tcYm9OkmyyrXbwHmelVYfC36L7LxC\r\nBa1k+9gP+ftzGLWK+XPP3K/ZS0P5okOZK9VK33d4fDN5DELSdJANQ1fArKij\r\nktpZkn+tIx1+iUs0pIrD8jg19Jt0PmE9VowcVrGpSu4lvZWf2AHBO5TROL/1\r\n3FL3WXL73OhKcwGL6EDKbSdt9n75jyTaNIfAFb8tcItUuak4Z7l1K/V0e+vE\r\nTczyBptlFv6Nsd+qfU0+nMqc4HSzwCAAKOG38WVJlhSXyN+gLaOMzTLDGo7f\r\nUBr66nUjAuansNbs6ePhKuMP+hRsFSmbPXw=\r\n=Fh3f\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "38cfd53020d9e0454bae0956e549546c55a66aed", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.11_1677883220880_0.26228028892371036", "host": "s3://npm-registry-packages"}}, "0.17.12": {"name": "@esbuild/android-arm", "version": "0.17.12", "license": "MIT", "_id": "@esbuild/android-arm@0.17.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "677a09297e1f4f37aba7b4fc4f31088b00484985", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.12.tgz", "fileCount": 6, "integrity": "sha512-E/sgkvwoIfj4aMAPL2e35VnUJspzVYl7+M1B2cqeubdBhADV4uPon0KCc8p2G+LqSJ6i8ocYPCqY3A4GGq0zkQ==", "signatures": [{"sig": "MEUCIH5mjN5McImKykcPkeRYj62EHVQVC2F9F4YLpxnZg7QgAiEA4JYMSnaukR6qZyTqb+wgPBK1pleQFIjrh3OA6LYrcKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10625826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFAXsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrC9xAAgW4C2T9XmRmmXp91AF6HZaCllQLgIbug2jY/ubjhTaehy83s\r\n7DEGoVNZKT4luFLujedCaivuzMJduFMSECPcSCRwcyoHksZFbEufe0EHYzXO\r\nRAhp4C2pXEty8g743IHL8DZ5eEMLq5fh0ObueNyDHGc8lBtDu/TK0KoabIUb\r\nuQ4UzgXZGZTq7B1zQEf/+Ri6cC+QhyZ+ufsjTLr5s8aLCyXwvTzMgqdKPxHr\r\nj0quxcMD5zgmPmte+X6jkjmHdlcd8PR2cgGDYZdWC8AmPUDDTkBAjezPBkhj\r\nH42G5AugHLcVl45eSZ5CXMGqa9GP4RzrtTKM6TVT9S6BvW6lPnJcRBqjKd8z\r\n+AXwGbsrnb+ZIvY5+AWAWABJcqAhFs76oUEGEQXxie+oSWesXclgyjP3NVZw\r\nKb5zV6YJ8WT7bl0zEUBZA/vC2Ogiv0j2rHWjv4nR9vDA+bGDLi7ISVshowMU\r\nIS4QBFGvalZpZpPcvgRko3uLYNX7yhosxMUCWf3WFNL0jz44BiKIt+B7+vsh\r\nY5Pu9Il78SC2o7oXltAVKCpBbUnzkg1CFjMCvf5eLA8/bI2ByKG0LVmQP/af\r\npAwSBhMzLbqwVMZD51lBxvSsXPgeMbm8K5LJ2f9+PV5UxIPHDpEn7Gq2xozu\r\ndMmsT3PaQK6b05WGLM+maS4+D6TK11dt1FM=\r\n=Pihs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "495216dbea685cd0e10172a866eeb8ca4764a0bf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.12_1679033835628_0.9547875248654902", "host": "s3://npm-registry-packages"}}, "0.17.13": {"name": "@esbuild/android-arm", "version": "0.17.13", "license": "MIT", "_id": "@esbuild/android-arm@0.17.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5a1cfd20453eb1c8a64602069d3e2187a2278b34", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.13.tgz", "fileCount": 6, "integrity": "sha512-5tZZ/hLIfBmt7E8JsE5KbsknoAFmoElkg+A/gjyPtmSQvJjPf+9GsSJihid8VMa08lrsYyaEXOT9RLh3xXQONw==", "signatures": [{"sig": "MEUCIBSIEEfMzjKQsSBKeV2Rb2+YK3CNFFvRSqkce4Hr+M/IAiEA5Ve0pHw7E01V/YU6l3qacLwJhAA+6Z7PFfQlc/Y0ezc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10632743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfKKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrzmw/+MWWXpWlb69pgq2RvVfZZuN1KvlqXboSgRejTJy9nX5aZGD69\r\nwoS70K+6fEOs7CxbMpo3RGK4gpQamh7gBaj3qk/gYTq8fXASi2Iyv3TqnEFO\r\nwWPRMlJQKgPqFz+LK8+yEVif2o4VEM8z09Z0RYWDEDWaCKHeKR7XeLZBTsKm\r\nH3CiTEX7dbaPnVFqiqKP2DNcE5x2iNwBk56EPExs8YRguFALDAv2aqhhxPKw\r\nJil015ncdOchkVbfNIw5IHB18kHxZ/02YFWJZ2Hoc/i4SpZJfNQb8ohtwSt5\r\n4Sq+LWKtS+iFpNck7L/5hh0KCVUPWEJ7ut0oWxhvZrg5VC0RlsFjV9W17qas\r\nilAMy3VhMA8bgpq+ZQXBHOYd7BYsw1lovyFJC57n6EAsAspDR5FOGzmdTsY3\r\nKREv1ZUvQ8ODdQIYnY5tmn//+mMwRInXunyZeVbFlmP4Q+6LpwS/0f20UjqF\r\nAgHvu6Ad1/KHHhbWvnb3vqw0+KmNT74xjLz8n6khkW3UYVa3dvuJJWR8XlQ4\r\nUx4RIdVjGcJ++tEc6i0X75MeZNv1HCYbE+cSRnFf2EHGfely8mypaSlxd5+9\r\n00p66Ksagh1+oImkEewXe0kUqtDOuOWGqVHQ9nuwW/XSPy3crYD1D3tgrw/H\r\nAx1JEu5k31pQ5trDn4URYGgjMawBvdNsCMQ=\r\n=HrxG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "8dbb85531a9971058f4d8739cb52e98fa43b40a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.13_1679684234511_0.2160169359676043", "host": "s3://npm-registry-packages"}}, "0.17.14": {"name": "@esbuild/android-arm", "version": "0.17.14", "license": "MIT", "_id": "@esbuild/android-arm@0.17.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "74fae60fcab34c3f0e15cb56473a6091ba2b53a6", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.14.tgz", "fileCount": 6, "integrity": "sha512-0CnlwnjDU8cks0yJLXfkaU/uoLyRf9VZJs4p1PskBr2AlAHeEsFEwJEo0of/Z3g+ilw5mpyDwThlxzNEIxOE4g==", "signatures": [{"sig": "MEUCIQCdZb/pEBg2CWUFZgFlCxYqcL52ldxatz63pFaATeLQAQIgP03lQ0LucQzKIZlCFS1ZCZffrFtSHXutqiG4F2xw5YI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10700700, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH7JXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo55A//exZPa5b9isYinHIlGlbCpujoRQKdpEVd+wQL4pKPema85DfS\r\n/ObBVuQ9LE9RyUHgwL/+QPR4SIno5PQcoNV+vFp6zk8Ht0oB2e6wOslbTJXn\r\nnAw0bYWXMx/pgbsSlZ7N8RxrNEM4ijSkOl/McRXLrmNtGVc+e2bp5iKxrGfe\r\nEfWGSaZazWLSNcFq/D7sNB0aqSxcNTiO2IpqdpN4mbbNxJ5g6lzmbwwngWKB\r\nGADW9gKmfoRvjQDotO/Q1k63SGwvE4UphXxhET7nyf/uwu18iF2VkPK+1K3+\r\nUqvac7+w2Is4yOPcxPH7kkiA7F4O/Go5SZ7cKXk+ZhqVW9ZlxUqqA2WHOP8K\r\nu71V4vF4h7HmTqVmwZDMwFYzXXdSh8BpglDxi90Ho+WDZ4UurEgH1G4m4fcA\r\nE2HswgfvPeTO/6xTlLKZH9gxOSnX+BpnYotnwnlYAcWWDOFXwQdLxnnE2Hzf\r\nD0/xWSaoOMdXk08e5eU3udAmtSiQsZfTS+kSK/lonHSHXqeJPNfPhcqo/1qL\r\nTPhdZ0sZWh0OP03mMDaEbqwSyvjIpNaqhfYv0gDg1PuRGOIoaT0Gm4bb4txn\r\nsb/YsVJaBvWpflIB8uVCccLkars595Ru2I0sjH/mXGstXmefH6BkhodVbmjM\r\n9Moaii7mykCzmGErkjcgn11iNw8mFTkZIsM=\r\n=L8uP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "b2b897870564a6b8e8bc802a140c55bf602de31b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.14_1679798871371_0.15475286224937213", "host": "s3://npm-registry-packages"}}, "0.17.15": {"name": "@esbuild/android-arm", "version": "0.17.15", "license": "MIT", "_id": "@esbuild/android-arm@0.17.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "143e0d4e4c08c786ea410b9a7739779a9a1315d8", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.15.tgz", "fileCount": 6, "integrity": "sha512-sRSOVlLawAktpMvDyJIkdLI/c/kdRTOqo8t6ImVxg8yT7LQDUYV5Rp2FKeEosLr6ZCja9UjYAzyRSxGteSJPYg==", "signatures": [{"sig": "MEQCIA+8MVRxNtB5yPRtdhQ3KewjxWdjjB51cPxn/0Eaxe4UAiBGjt5bHSYrGzrT8lZa2S4WIiQPDcKSF9AJoFWzTVOfWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10706588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKK+xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2jBAAntKxSFffC1/t/sp61eOlZct8JhSnIcn9GeHKMGfgvZh+k8kE\r\ncMkUfDbY0x7klsCUnJt1rB+INRHZHQgIGRl6bw/V4ac+AVdsrxruBpbc8udU\r\nraYu58TM6iHUkzvsiVVYphNu3pDNJwKBoWv5zTpzNK5BuZMrwEfnatEme9Ir\r\nDwxypZJrANf7lkqWCSnSGZHoBL0L6YorV541xulmyNGZeu3XygCBZBtXRCAG\r\ny3HCoV69GxjGKfqizfWjIFJaJwcR8zJWF2gmvel4305WtLsIQvTtxQlpaF3R\r\ny2JXu0LiXL9TK8UMoP5t3CiGOSu8PDYnidOYxLx9P7ig/Ux/7+rRzCmoFUXw\r\n/edsnt9O/KEwcSoNc9VhZBF78PsnZyUTxdFtjyDrF6m8dJkeXQa8gZp9TdtD\r\nmwTnahHE4BieoJ37Zt1bdoGXMjH/+y+rQInJTGfLrDU67fk1Gy4GmTSEQtBL\r\nV724QQD0WsRNRUtCNF1MwmQ0tmgVzHXUP/64w4fHfdNN2fI/hzZ05yE7Ooks\r\nYsJ7YaoE7xCMjpMMegqwZeyc7dE8FJxh5IGTVhm4H+uPvJe6IAWNLWiwG7kH\r\n1klELqplLeSNc804By4Cu6wnzF+DJAmRmlGZaSBdvRn50oyomag0IZ0oOcs/\r\nf10Vw6+2My1AUO3bn9z1vR9jWC3e+cBkRms=\r\n=hjXF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ef912892181e27386205d110c622c55c4c1df856", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.15_1680388016711_0.08276192686722905", "host": "s3://npm-registry-packages"}}, "0.17.16": {"name": "@esbuild/android-arm", "version": "0.17.16", "license": "MIT", "_id": "@esbuild/android-arm@0.17.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5c47f6a7c2cada6ed4b4d4e72d8c66e76d812812", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.16.tgz", "fileCount": 6, "integrity": "sha512-baLqRpLe4JnKrUXLJChoTN0iXZH7El/mu58GE3WIA6/H834k0XWvLRmGLG8y8arTRS9hJJibPnF0tiGhmWeZgw==", "signatures": [{"sig": "MEUCIQCxTLPLJvTcO30P2QGFiDJtGy2JeE/H9cIMws3Pm7iP/gIgJBvf10ZELwnTuHwr9UYjXXTtvDJuHV2SoWQfFSHrPxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10711838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM5H+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogwA//RGx0c55WlEkyEJbyrdyQlkngS4oNwKTHK86ot51i+z1dUSRC\r\nR3E3RUQlN4xF2ieRRLSm86AxkouFCF47b9jjCG93Sp37ytcAdPImY4ts5ACj\r\nvzEduemEPPopCFcLKgjfB0zaCEp+FjLNFBXaneaO9WXEjXi2ZnlXwxyktc+8\r\nQ2saoO23yGGRbdbvJuQKk85ZHkUicfiStA08VjpBU1SxnZLMN3beVBuxJP9Q\r\n7BuRewOkNzze2b8Qc/R6MdGpbcQXEkdpOXcjh2bGP9TgueK137wyr5tBICaG\r\nns9yh1dTy1ah+uRRTn/vgmEYoFu2jlHII1Vn0v1p0rOUDU7sAmrPlL+2GCkl\r\np4sSa1Dew+MD7DbgKS/2ghRLsqgxNTKjmSNjw3Ax/YQOiXNjGDSgAIt5DRmV\r\nJf8GCLjOEeN30NpVVbIKd9iIcyUV6RztpwjymLFsDKZAciH/ojmTGknVCo+x\r\nVdyUUUdxvHbnG9gjRMvGSQd5acas4iqGbEGxZxUOB0cwNiYSih0WKQiWCw5X\r\no4xqxR2zTCOncfW/lXAMR6kM8cgqtrAfOlFK6WEtZ//JFGsTydr+H1vdRaNw\r\n/wNPsf+LdFb2p7HKbpkIqFG4yw3f+geelzIlqw19Arx5IcxwEC1ZpV7WP+Kj\r\nK6E8WnLBXgwJ4kdYDhT1l3IkKJGra7+2Z6A=\r\n=lArd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "f0704baa38ef9a808b7e34cac75aa4ac285bb087", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.16_1681101310123_0.5905139748217001", "host": "s3://npm-registry-packages"}}, "0.17.17": {"name": "@esbuild/android-arm", "version": "0.17.17", "license": "MIT", "_id": "@esbuild/android-arm@0.17.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "1b3b5a702a69b88deef342a7a80df4c894e4f065", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.17.tgz", "fileCount": 6, "integrity": "sha512-E6VAZwN7diCa3labs0GYvhEPL2M94WLF8A+czO8hfjREXxba8Ng7nM5VxV+9ihNXIY1iQO1XxUU4P7hbqbICxg==", "signatures": [{"sig": "MEYCIQCbk0RTEsMjE0emndikrw7KS+RSHsdeN8DRWYIeKbUp9gIhAIEhDw2QtnLtzgrltmpvGKcsgn9WBg2zwYmsAWqHz2vb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10722107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPGdjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpO7A//bV9YD2pHug1UthFgHrtFc93awTpuVKqDrehHoUKzCd3PgsZg\r\n0hRn9fv059XNt3pVonxybSoEiFX1Ia9UzlRKyRTOVhVQK52bApDR887zFyeg\r\nARjuxJkXxSv6CSU+w6H+kDJOnd2v/cNLmH5G9r9zsqW8SPGlE2Uz40qcURiY\r\nR3eH6NQT0luAZ4O9x5f5xVumozLA8XzrnBHpPkEeUCGsZc61FYxzocGzZLey\r\njEtCuRu1qfhTN3DJ0leh1dgqfHyJ0/VAWvyJtKzYIpKQBPwV9D8t5Bg91SZ7\r\nsOqbKCrN4CL8arTnvRwxCDYMla458Ocl/uZzlM9iRXsSGrpseUhyHtLNl1i4\r\nBSZjLe8k+OIS+VFi6pxMMesgh/BJDmOz4c0fzt8OVzwMuhsb3n2HhzEao6vN\r\nn9332f2z7f0X+e0mlESuwgmPPQoVPs5xSNFTXtoY9O+622w8F9rfTkkw0ARB\r\n3fd+TghcJkufbm86X9NkZ+6HxBHGJw63Oo6micpAPoZGKnM+ZOulR2eTYAk6\r\nzJaelQAv/NiTVGBDqS9nIhnCsyebADAhrplHxpQmsoRuRvGEXdxzdHsCZrXI\r\nTg92vvCGIbfzwLFMd2QVfE14PR3ZjrS6JBNlC+T7RzDeRzfyq5auDSz50dm7\r\nTI0VFomsBivoNf5pY/MsFwEG2xKAhPLtYMs=\r\n=9p8d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "0776a4be2bb80980482b123a9a05dbf55cc35683", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.17_1681680226835_0.7260104239054002", "host": "s3://npm-registry-packages"}}, "0.17.18": {"name": "@esbuild/android-arm", "version": "0.17.18", "license": "MIT", "_id": "@esbuild/android-arm@0.17.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "74a7e95af4ee212ebc9db9baa87c06a594f2a427", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.18.tgz", "fileCount": 6, "integrity": "sha512-EmwL+vUBZJ7mhFCs5lA4ZimpUH3WMAoqvOIYhVQwdIgSpHC8ImHdsRyhHAVxpDYUSm0lWvd63z0XH1IlImS2Qw==", "signatures": [{"sig": "MEUCIQDIJRMgL4c6zQTzLMX5SoiUVcDqd9GumyQ7dfNHJjedhgIgPPFE52FdAw6IWjiMg9Ji+XUfu1fjS9JO6YcOXuUzDuI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10727791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkREZ9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq13Q/+Kk+qWalSWdWSxBuE2GYJp5KQ6a6ZJodV4ek2ZKewC7083gIS\r\nMyXYGr6jedZWQXtMt2aog2cHu6xWSrDD7ryXQ9FlUwSx42IgQyEl9Ur6vNFV\r\nMe/VDZqgfuTm3B397spJphOThaaJzUaOgSdJg2wgijIKW1GhSAzLXHz08QZ0\r\nqqqLqOnTknSC7nr747sy6e62M8J0eAy2qYzeqn7HutqlG31TuMGjX43MHyDh\r\nZ9MNlzfs7I2fmm5Y5Exh6wjIpxZns5vk0hD2++QcUx/ZLOoS0j62AgG+CVEm\r\n7I0QPGYzzupQMaN3SJm9T3doMx3MnIJaKqiyLjN6zEXg3s5u4KAZY6+syIYF\r\nMfyFyuzXicWaz4RQax/y57KtBT4a3DC/6Ok7ottJxQa91DsSMzb81xghWTDI\r\nA3c1/DiLBhHm8PUbNCgcRbncIEQN4/IafGHhsZtzJW9ytqm7kiav8ds/Pasb\r\nwk4oLcEWngny8eru4y1n6ici3tWgwcdy5a3XBoaVXBMJpS/oV0DCAA1Z+hR/\r\nwQoH6QoqktuSwhksTR5bszFSSJhPph8W6rljLzbVtzgCT/1BCSBmlAEBiJ/a\r\nm6YC4oMHoPX59+1rILGN0YdT6bH/pZ29MlA54wdqNDP0TAemPViCrQLt7dgU\r\nwuSojgVclwGjlCaNhqKDwBpe5K9V2PxeHzA=\r\n=zJ/i\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "gitHead": "ee646b4ed8d3b9567e1a0ce2e478241b68a2a2e4", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.18_1682196093441_0.12293237535541168", "host": "s3://npm-registry-packages"}}, "0.17.19": {"name": "@esbuild/android-arm", "version": "0.17.19", "license": "MIT", "_id": "@esbuild/android-arm@0.17.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5898f7832c2298bc7d0ab53701c57beb74d78b4d", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.17.19.tgz", "fileCount": 6, "integrity": "sha512-rIKddzqhmav7MSmoFCmDIb6e2W57geRsM94gV2l38fzhXMwq7hZoClug9USI2pFRGL06f4IOPHHpFNOkWieR8A==", "signatures": [{"sig": "MEQCIFKVYhPrLLH8ykZhVd1gfQaNgANcK2KK2IOiI4gK5OAdAiANB6svqR/67yHIMh+jsGPwghnaLkipuzBzJocAsY58Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10735177}, "engines": {"node": ">=12"}, "gitHead": "d47ab43980c457db27d2671ab618cd5c40a618a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.17.19_1683936400966_0.6072675777548002", "host": "s3://npm-registry-packages"}}, "0.18.0": {"name": "@esbuild/android-arm", "version": "0.18.0", "license": "MIT", "_id": "@esbuild/android-arm@0.18.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "8410aa4c5fe16426cf53fd26ad3805c679407f7b", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.0.tgz", "fileCount": 6, "integrity": "sha512-+uLHSiWK3qOeyDYCf/nuvIgCnQsYjXWNa3TlGYLW1pPG7OYMawllU+VyBgHQPjF2aIUVFpfrvz5aAfxGk/0qNg==", "signatures": [{"sig": "MEQCIC2/yi53MMgPSugiBUOOMljHEJpGvHXz7sdxLSqKonHLAiAUv0f4RHMhliiMWDL761ZjRzebf5uYDRjKBp6tLFD83Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10737148}, "engines": {"node": ">=12"}, "gitHead": "4dda49d4ea86afcbe715bdca5e8f4b13659e0c2f", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.0_1686345866520_0.20274424538900404", "host": "s3://npm-registry-packages"}}, "0.18.1": {"name": "@esbuild/android-arm", "version": "0.18.1", "license": "MIT", "_id": "@esbuild/android-arm@0.18.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "da0b674fc68a698f1657a9d9d9fd0b496d1bf725", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.1.tgz", "fileCount": 6, "integrity": "sha512-8+QS98jqdreHLvCojIke8NjcuelB+Osysazr15EhkUIuG0Ov5WK26XgPYWViCTjHnKQxbpS86/JryBOkEpyrBA==", "signatures": [{"sig": "MEUCIA/DZ9KI53DsgclZaFKJS+dmQiQXRjs2VrdlIAXw9qW0AiEA9wJhHGpHv4mvxSnShpsqeDlT6YNet0xgn1TZlbt099Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10749746}, "engines": {"node": ">=12"}, "gitHead": "3aa3ec2da489dad64d90aa965c9782984defc904", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.1_1686545512258_0.17198351880841978", "host": "s3://npm-registry-packages"}}, "0.18.2": {"name": "@esbuild/android-arm", "version": "0.18.2", "license": "MIT", "_id": "@esbuild/android-arm@0.18.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "3882a9b68807c5f62fe310e37c7c22fa93c46ec6", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.2.tgz", "fileCount": 6, "integrity": "sha512-YAnQBHlY0IvYtvY0avnXjI8ywW23emEjk5XExqbFmypath+Snq9MgY1IS47rnqBKVSqnl0ElDt221ZgaeRrkXg==", "signatures": [{"sig": "MEUCIQDTR4whew8JwxLOzJvXN/6+5BD549er0TadHnpIWBiViAIgI0sDPrpYVKQTPehQjFuv2OoyqHoSCni424C8/Rk5JkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10756763}, "engines": {"node": ">=12"}, "gitHead": "a7a909605b1387b5c74d1bd0217af3fb4a843461", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.2_1686624038825_0.8518449744376986", "host": "s3://npm-registry-packages"}}, "0.18.3": {"name": "@esbuild/android-arm", "version": "0.18.3", "license": "MIT", "_id": "@esbuild/android-arm@0.18.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "7904d8326fd7168debfd4a80b9bf867486d4204c", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.3.tgz", "fileCount": 6, "integrity": "sha512-QOn3VIlL6Qv1eHBpQB/s7simaZgGss2ASyxDOwYSLmc6vD0uuizZkuYawHmuLjWEm5wPwp0JQWhbpaYwwGevYw==", "signatures": [{"sig": "MEUCIQD2brHeqb63q2Ybj/UblEEvgFZbb3aeCdJQ5rGvaJBYZQIgfCHNZXNUf+3aq7iSF5IHHyoESI3FKfWG17fbJ/RZ4ms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10755976}, "engines": {"node": ">=12"}, "gitHead": "9224cce93632b0fc6db8767676211fb44ac642a7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.3_1686831675666_0.33996769616379696", "host": "s3://npm-registry-packages"}}, "0.18.4": {"name": "@esbuild/android-arm", "version": "0.18.4", "license": "MIT", "_id": "@esbuild/android-arm@0.18.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "b8173173fa779b807e41146328f36324bf4513c1", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.4.tgz", "fileCount": 6, "integrity": "sha512-yKmQC9IiuvHdsNEbPHSprnMHg6OhL1cSeQZLzPpgzJBJ9ppEg9GAZN8MKj1TcmB4tZZUrq5xjK7KCmhwZP8iDA==", "signatures": [{"sig": "MEQCIG7Xp1ILRFBgHGSIRBWxIcTyaz52N0FNoqiOGnE9KDIUAiApSXY1B+aU5YOfKNdRS+C8SIRjRxFN9Hd6O5b9tNCsKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10765777}, "engines": {"node": ">=12"}, "gitHead": "bfc5a0fe07b6f6855ff3ff11a91894066378c5dd", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.4_1686929920143_0.49841489423041585", "host": "s3://npm-registry-packages"}}, "0.18.5": {"name": "@esbuild/android-arm", "version": "0.18.5", "license": "MIT", "_id": "@esbuild/android-arm@0.18.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "7d3fb83951f06344dd18541fe1483e9e87216d05", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.5.tgz", "fileCount": 6, "integrity": "sha512-+8GXQzuASxGg/rb47Z5zJe3vjOfL7RRce/DILuk6kbB/8HO0p3CPo72CbR349P2K8YP1h5NvNqU+2GDRbNJylw==", "signatures": [{"sig": "MEUCIQDXz4CR2NFQ5RMLIY6phpYwWoAdRxkL6SR4kV2zn5cGSgIgY/jrRrEKeKipeXKEQX4iuJKpb6+H8pkXbeSR5yNO8Sk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10803156}, "engines": {"node": ">=12"}, "gitHead": "931be1b9b2312609c5214812671b0d2a21cfe92b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.5_1687222369461_0.754861650367282", "host": "s3://npm-registry-packages"}}, "0.18.6": {"name": "@esbuild/android-arm", "version": "0.18.6", "license": "MIT", "_id": "@esbuild/android-arm@0.18.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5f355ae54027360113e6aadcb25291d0d97976e0", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.6.tgz", "fileCount": 6, "integrity": "sha512-J3lwhDSXBBppSzm/LC1uZ8yKSIpExc+5T8MxrYD9KNVZG81FOAu2VF2gXi/6A/LwDDQQ+b6DpQbYlo3VwxFepQ==", "signatures": [{"sig": "MEUCIFU2bzbZGJE3DLyWQ3ney0NrcT8Hjf2cJQ8y4XdlZhxTAiEA4lHswvAdKltWPxAcJvwSFmctlb1/c49sl/QHNkN1tkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10818892}, "engines": {"node": ">=12"}, "gitHead": "f0b5803694c3d74c1d84851a518e3e25916ec005", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.6_1687303500901_0.6061751170661209", "host": "s3://npm-registry-packages"}}, "0.18.7": {"name": "@esbuild/android-arm", "version": "0.18.7", "license": "MIT", "_id": "@esbuild/android-arm@0.18.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "dcdbaa2c56d3ca6de7601f2df2c5bc4e6893c325", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.7.tgz", "fileCount": 6, "integrity": "sha512-nDQ7AC5WVKOqxaip/E5YbaRgnilBMsoGKjoGLCeCOYNePaBCBDwJsLfa2fn3FEtktiLaAF990W592avsHjJ/Vg==", "signatures": [{"sig": "MEUCIQCTzxbHCfJJmyYu/EyoSv7g43lJ45E/6lBa64jxodNsXwIgKj5kW/amhc9KqzlrPXyMDCo5ktG1UAd11ddFrbpTdXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10881982}, "engines": {"node": ">=12"}, "gitHead": "adb8d19b56d2ae2d65128305c875b577476fac93", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.7_1687574791180_0.8309257543927526", "host": "s3://npm-registry-packages"}}, "0.18.8": {"name": "@esbuild/android-arm", "version": "0.18.8", "license": "MIT", "_id": "@esbuild/android-arm@0.18.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5e7c2efeda019d6884d4320a11a43e2f93ac12bf", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.8.tgz", "fileCount": 6, "integrity": "sha512-xDncukyW2b/JU04AZJ6cSAp4FaoAMyxFFTgmmNEKbjn2MwThw/ekHwt3d84Nm0fJG2KqKBS3D6uGDo2jzDN/uQ==", "signatures": [{"sig": "MEUCIB7+kAGwDT194le2VVsPyMwhyOJX6VX+Fyk9EoJbeIy6AiEAknNBhuIcIqodut8WfvsAwBcIqmOeyM+PXeefQ6YECAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10887152}, "engines": {"node": ">=12"}, "gitHead": "9b233a4f670a73173dcc7e83ebf7648d0007b082", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.8_1687663160354_0.08287132997931135", "host": "s3://npm-registry-packages"}}, "0.18.9": {"name": "@esbuild/android-arm", "version": "0.18.9", "license": "MIT", "_id": "@esbuild/android-arm@0.18.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f0cfe4e981f901d290b32f3b3d322d867dc8b42f", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.9.tgz", "fileCount": 6, "integrity": "sha512-v1cr0l0RZOzIgLtTe8M1cRFFP0ICRdymPPa8HCPUpgZ+XasQrd5Mxyp9KlDqXLLyGmnZpzhufKEThLIihQL53A==", "signatures": [{"sig": "MEUCIA1Y8jruSWkzRMP5E5XLJyYw3Jt0pBpEFd4zkQXm3GsdAiEA4Vxhcxo4IJ2mvzW+5SSR1x48EpVuYJsM//MEEMJdQEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10921662}, "engines": {"node": ">=12"}, "gitHead": "d568ff038d012a7894a9d4334b75f8559bf2532e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.9_1687757290238_0.4935949361211003", "host": "s3://npm-registry-packages"}}, "0.18.10": {"name": "@esbuild/android-arm", "version": "0.18.10", "license": "MIT", "_id": "@esbuild/android-arm@0.18.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "aff8cfebd2ed3a52f4d4a923360e7abd0817c34c", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.10.tgz", "fileCount": 6, "integrity": "sha512-3KClmVNd+Fku82uZJz5C4Rx8m1PPmWUFz5Zkw8jkpZPOmsq+EG1TTOtw1OXkHuX3WczOFQigrtf60B1ijKwNsg==", "signatures": [{"sig": "MEYCIQCMKxQpBdlKzszrhZc3I9O6aUnhAWq9sNfjIlYXjqohZwIhAKKLeTKlhoVyRWvf8zR3+AJHc7ByF4mMU9l966fjBIk4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10921759}, "engines": {"node": ">=12"}, "gitHead": "cdb6c7ce3f9419e51bd855fd61d07d2c615fb30e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.10_1687814435968_0.5729680829350317", "host": "s3://npm-registry-packages"}}, "0.18.11": {"name": "@esbuild/android-arm", "version": "0.18.11", "license": "MIT", "_id": "@esbuild/android-arm@0.18.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ae84a410696c9f549a15be94eaececb860bacacb", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.11.tgz", "fileCount": 6, "integrity": "sha512-q4qlUf5ucwbUJZXF5tEQ8LF7y0Nk4P58hOsGk3ucY0oCwgQqAnqXVbUuahCddVHfrxmpyewRpiTHwVHIETYu7Q==", "signatures": [{"sig": "MEYCIQCiHrjWrdeitRfMAE8dIriaIWgw7cakuhQiuMXhKgNnaAIhALYgWNsR7c12JHBYZuFWI5KmblQfL047bqWeq/rqr6bD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10921933}, "engines": {"node": ">=12"}, "gitHead": "2703f90d47fd96f425fedcfd2a5c318a43b04d45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "16.13.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.11_1688191441974_0.7181088208116548", "host": "s3://npm-registry-packages"}}, "0.18.12": {"name": "@esbuild/android-arm", "version": "0.18.12", "license": "MIT", "_id": "@esbuild/android-arm@0.18.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "b91c893170ef45b3a094795b5a44ee519c23aed6", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.12.tgz", "fileCount": 6, "integrity": "sha512-LIxaNIQfkFZbTLb4+cX7dozHlAbAshhFE5PKdro0l+FnCpx1GDJaQ2WMcqm+ToXKMt8p8Uojk/MFRuGyz3V5Sw==", "signatures": [{"sig": "MEUCICD4HcsT/4kFpipC+pys8nMPQ5uehZoKn35gR72T+L2VAiEAw7ipqA8VTyJhn6FNkEO2YstByHGBRLZuY8wCJGZ2oK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10925021}, "engines": {"node": ">=12"}, "gitHead": "d196e4c4898fc46dc553124e28b1b29829ef7f7d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.12_1689212057304_0.8324706509252895", "host": "s3://npm-registry-packages"}}, "0.18.13": {"name": "@esbuild/android-arm", "version": "0.18.13", "license": "MIT", "_id": "@esbuild/android-arm@0.18.13", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "15db83099855fc4193658a40687893ee5c95d7a9", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.13.tgz", "fileCount": 6, "integrity": "sha512-KwqFhxRFMKZINHzCqf8eKxE0XqWlAVPRxwy6rc7CbVFxzUWB2sA/s3hbMZeemPdhN3fKBkqOaFhTbS8xJXYIWQ==", "signatures": [{"sig": "MEUCIQCfJ2t+AXv4RV0nCFgStWcUg7Cbx5E8RyCs2NPQ2cvprQIgb7ebWDtMqXPEsGV5mC61+8uLQNjLfZOFZmglHXyv0l0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10912728}, "engines": {"node": ">=12"}, "gitHead": "12a8a25b4ca8b650d7c96046b8a3e76491a119f5", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.13_1689388643874_0.5124492184541511", "host": "s3://npm-registry-packages"}}, "0.18.14": {"name": "@esbuild/android-arm", "version": "0.18.14", "license": "MIT", "_id": "@esbuild/android-arm@0.18.14", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ed59310c0e6ec6df8b17e363d33a954ecf870f4f", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.14.tgz", "fileCount": 6, "integrity": "sha512-blODaaL+lngG5bdK/t4qZcQvq2BBqrABmYwqPPcS5VRxrCSGHb9R/rA3fqxh7R18I7WU4KKv+NYkt22FDfalcg==", "signatures": [{"sig": "MEYCIQDdKy37vrgvN9bDusLbQFfnWyUt67/1jx3G2gWk2Ig45wIhAPBwfT/oAOms5OgZ2VmV1eTvO0oBm/NrLN+6iE0h0Ukz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10961210}, "engines": {"node": ">=12"}, "gitHead": "af0fe32eaea8112de45e17cb8d0ad487b2123132", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.14_1689656427998_0.1003102647314793", "host": "s3://npm-registry-packages"}}, "0.18.15": {"name": "@esbuild/android-arm", "version": "0.18.15", "license": "MIT", "_id": "@esbuild/android-arm@0.18.15", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "6afedd79c68d5d4d1e434e20a9ab620bb5849372", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.15.tgz", "fileCount": 6, "integrity": "sha512-wlkQBWb79/jeEEoRmrxt/yhn5T1lU236OCNpnfRzaCJHZ/5gf82uYx1qmADTBWE0AR/v7FiozE1auk2riyQd3w==", "signatures": [{"sig": "MEUCIFsafhDERsCrKTcWitE52wxnyr7VDRdaD5wfQpdkbiG3AiEAlhV0qS/vp+sT7a1p+/rHys8ilxH0xzCQmqxi1ewxeRI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10971600}, "engines": {"node": ">=12"}, "gitHead": "daf64732be2e05d1258023b5b7d5389e08e291a9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.15_1689857602507_0.5063690776580376", "host": "s3://npm-registry-packages"}}, "0.18.16": {"name": "@esbuild/android-arm", "version": "0.18.16", "license": "MIT", "_id": "@esbuild/android-arm@0.18.16", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ef6f9aa59a79a9b9330a2e73f7eb402c6630c267", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.16.tgz", "fileCount": 6, "integrity": "sha512-gCHjjQmA8L0soklKbLKA6pgsLk1byULuHe94lkZDzcO3/Ta+bbeewJioEn1Fr7kgy9NWNFy/C+MrBwC6I/WCug==", "signatures": [{"sig": "MEUCIQD9kkF5DbWIecddudyV+c1tvnG/TaWzjSbkwCxDBcKdYwIgOlyXAHhk1tdr2awqVi7yT5g28YxDryTa3uf3iHiYGDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10975347}, "engines": {"node": ">=12"}, "gitHead": "22920366954b4d18aed77dfc2b5961f339d4e318", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.16_1690087692480_0.5363266683653805", "host": "s3://npm-registry-packages"}}, "0.18.17": {"name": "@esbuild/android-arm", "version": "0.18.17", "license": "MIT", "_id": "@esbuild/android-arm@0.18.17", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "1aa013b65524f4e9f794946b415b32ae963a4618", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.17.tgz", "fileCount": 6, "integrity": "sha512-wHsmJG/dnL3OkpAcwbgoBTTMHVi4Uyou3F5mf58ZtmUyIKfcdA7TROav/6tCzET4A3QW2Q2FC+eFneMU+iyOxg==", "signatures": [{"sig": "MEQCIE4tT0nzYZYp5BZ81lSvVOnN0wQNUSE8z/kuZjdVgYZmAiBwf6uKIBpLj+4s7DKLhd2V69qhH8MFZF1QxrS2MvBJVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11015579}, "engines": {"node": ">=12"}, "gitHead": "1771c7109f7f5d17d96543d6b17a6ab12d9d38d0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.17_1690335660781_0.7997603760353649", "host": "s3://npm-registry-packages"}}, "0.18.18": {"name": "@esbuild/android-arm", "version": "0.18.18", "license": "MIT", "_id": "@esbuild/android-arm@0.18.18", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ffd591b956ced1c96e1224edfbed1001adadf2ae", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.18.tgz", "fileCount": 6, "integrity": "sha512-oBymf7ZwplAawSxmiSlBCf+FMcY0f4bs5QP2jn43JKUf0M9DnrUTjqa5RvFPl1elw+sMfcpfBRPK+rb+E1q7zg==", "signatures": [{"sig": "MEQCIA4aCseREdJlDgNdcRXCHO6xB6BFZY4zZNhsYkbkMJlVAiB2PG0qx1RP1JhbcefUa2kyQcNFxkKybF9uNSy0bbbzXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11044564}, "engines": {"node": ">=12"}, "gitHead": "e8e43ad19359f0b29d84607c89c6aa95a4d1637d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.18_1691255193730_0.11455593748914028", "host": "s3://npm-registry-packages"}}, "0.18.19": {"name": "@esbuild/android-arm", "version": "0.18.19", "license": "MIT", "_id": "@esbuild/android-arm@0.18.19", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "efd1f33583a893c0cc57f25b1d081af8cdc6bfd9", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.19.tgz", "fileCount": 6, "integrity": "sha512-1uOoDurJYh5MNqPqpj3l/TQCI1V25BXgChEldCB7D6iryBYqYKrbZIhYO5AI9fulf66sM8UJpc3UcCly2Tv28w==", "signatures": [{"sig": "MEQCIFd9wQHlOrTE61LsAnPcR9XThsRkAt/x6O/Kia36l1+jAiANkJ9P+6+s1Iv8U9p+zxr6eqKXU0QIBSqd8QIZvwa9tQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11094194}, "engines": {"node": ">=12"}, "gitHead": "e08ee8990905f24b987a7ddffde89e20cbf3cf6a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.19_1691376685536_0.8912898626344208", "host": "s3://npm-registry-packages"}}, "0.18.20": {"name": "@esbuild/android-arm", "version": "0.18.20", "license": "MIT", "_id": "@esbuild/android-arm@0.18.20", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "fedb265bc3a589c84cc11f810804f234947c3682", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.18.20.tgz", "fileCount": 6, "integrity": "sha512-fyi7TDI/ijKKNZTUJAQqiG5T7YjJXgnzkURqmGj13C6dCqckZBLdl4h7bkhHt/t0WP+zO9/zwroDvANaOqO5Sw==", "signatures": [{"sig": "MEQCIEtwvjdx2T92C8bvvIAOWbAeqTi8JoY6Kb5aCS2ki5sPAiBSe+hT8rzdx4ETC5m4+6I+UKxqAl5KQeo/cH+q07ym2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11108974}, "engines": {"node": ">=12"}, "gitHead": "22f0818cf81024b63752d815c51fe737612b43ec", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.18.20_1691468109054_0.8533674630086703", "host": "s3://npm-registry-packages"}}, "0.19.0": {"name": "@esbuild/android-arm", "version": "0.19.0", "license": "MIT", "_id": "@esbuild/android-arm@0.19.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "6eb6e1fbc0dbfafa035aaef8b5ecde25b539fcf9", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.0.tgz", "fileCount": 6, "integrity": "sha512-GAkjUyHgWTYuex3evPd5V7uV/XS4LMKr1PWHRPW1xNyy/Jx08x3uTrDFRefBYLKT/KpaWM8/YMQcwbp5a3yIDA==", "signatures": [{"sig": "MEYCIQDiZzrc+1ctkbVd01X/EuOZHwmN+g2OD6SLTMueTqzACgIhAM1M6ntAmNjcnu8QflqwNOBHsO5xSuaqM+3MxfCo9d1N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11194107}, "engines": {"node": ">=12"}, "gitHead": "c337498cdad8cac87517ec49c923441b2dc67bf2", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.0_1691509959397_0.7949908558022203", "host": "s3://npm-registry-packages"}}, "0.19.1": {"name": "@esbuild/android-arm", "version": "0.19.1", "license": "MIT", "_id": "@esbuild/android-arm@0.19.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "131a3bf5681fb50e1e873b79fce8eae98043606b", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.1.tgz", "fileCount": 6, "integrity": "sha512-yjTucwcOua52z14RL30JMwmCdylsQ5WrErGkAb6VL0MWPbnwJyLejydaRcUqkPO6g0MowlzavdxrR7AcfCW+yA==", "signatures": [{"sig": "MEUCIQDWlNDaddUtTQMNPpBD5nIQUDKEbQytd/IAUB7ZOJDpegIgfuPVNFbnz3dmQ86FknYQ5cAW5XYWvUH5YK0pQ8ci8uA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11220931}, "engines": {"node": ">=12"}, "gitHead": "49801f761347d53bd1f6a88767bb79e257f9fbb9", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.1_1691769460508_0.755520614733602", "host": "s3://npm-registry-packages"}}, "0.19.2": {"name": "@esbuild/android-arm", "version": "0.19.2", "license": "MIT", "_id": "@esbuild/android-arm@0.19.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "edd1c8f23ba353c197f5b0337123c58ff2a56999", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.2.tgz", "fileCount": 6, "integrity": "sha512-tM8yLeYVe7pRyAu9VMi/Q7aunpLwD139EY1S99xbQkT4/q2qa6eA4ige/WJQYdJ8GBL1K33pPFhPfPdJ/WzT8Q==", "signatures": [{"sig": "MEYCIQDSfIeBflT9pBhAcdS5d5bZ8OU4p7YaEBwtA5sdV1gvFwIhALxMIemBshag4sBdIBFeJxiUxqeKJBWUVQS76lXMAhp5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11208322}, "engines": {"node": ">=12"}, "gitHead": "09a100124e9daef9e0be57d21cc7729c6f3516e7", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.2_1691978307177_0.8186208741944583", "host": "s3://npm-registry-packages"}}, "0.19.3": {"name": "@esbuild/android-arm", "version": "0.19.3", "license": "MIT", "_id": "@esbuild/android-arm@0.19.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "08bd09f2ebc312422f4e94ae954821f9cf37b39e", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.3.tgz", "fileCount": 6, "integrity": "sha512-Lemgw4io4VZl9GHJmjiBGzQ7ONXRfRPHcUEerndjwiSkbxzrpq0Uggku5MxxrXdwJ+pTj1qyw4jwTu7hkPsgIA==", "signatures": [{"sig": "MEUCIF+6xV/QIRSzuvsI03r/mlpTkVTy6+glIsKrkHp6ZWXFAiEA4pVpYoqOwzBylfuYo0I8RuQSwaxPqETTBBhJCXdY7Rs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11219150}, "engines": {"node": ">=12"}, "gitHead": "673ad10ff752486aa90749b63ebeb952c29106a1", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.3_1694653947693_0.10011460327695354", "host": "s3://npm-registry-packages"}}, "0.19.4": {"name": "@esbuild/android-arm", "version": "0.19.4", "license": "MIT", "_id": "@esbuild/android-arm@0.19.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "c27363e1e280e577d9b5c8fa7c7a3be2a8d79bf5", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.4.tgz", "fileCount": 6, "integrity": "sha512-uBIbiYMeSsy2U0XQoOGVVcpIktjLMEKa7ryz2RLr7L/vTnANNEsPVAh4xOv7ondGz6ac1zVb0F8Jx20rQikffQ==", "signatures": [{"sig": "MEUCIHGUQtGgfd4HZhFMRAm7w13uII196c4rndwn0Etx/E+3AiEAokZL4wLOgHYHEueXKKHr0lYyHJVQh+9XE7kzPZADrsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11219518}, "engines": {"node": ">=12"}, "gitHead": "a75b16ec09e76a050cea8ad43588172dc297784d", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.4_1695865640073_0.6569960902587342", "host": "s3://npm-registry-packages"}}, "0.19.5": {"name": "@esbuild/android-arm", "version": "0.19.5", "license": "MIT", "_id": "@esbuild/android-arm@0.19.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "4a3cbf14758166abaae8ba9c01a80e68342a4eec", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.5.tgz", "fileCount": 6, "integrity": "sha512-bhvbzWFF3CwMs5tbjf3ObfGqbl/17ict2/uwOSfr3wmxDE6VdS2GqY/FuzIPe0q0bdhj65zQsvqfArI9MY6+AA==", "signatures": [{"sig": "MEUCIQDKn6V2m1/4kWjzFWiPDESvtSfazilQBQRKoVun1Aro4QIgO9SpMP7QmdHxzSpBsx+vOKzi/A8j6n+kF7PHVa9jeGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11223776}, "engines": {"node": ">=12"}, "gitHead": "a7fcc43fdb6b6edc58f781fe96328f4867f4b33e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.5_1697519440036_0.39794878230406394", "host": "s3://npm-registry-packages"}}, "0.19.6": {"name": "@esbuild/android-arm", "version": "0.19.6", "license": "MIT", "_id": "@esbuild/android-arm@0.19.6", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "68898d949672c56f10451f540fd92301dc713fb3", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.6.tgz", "fileCount": 6, "integrity": "sha512-muPzBqXJKCbMYoNbb1JpZh/ynl0xS6/+pLjrofcR3Nad82SbsCogYzUE6Aq9QT3cLP0jR/IVK/NHC9b90mSHtg==", "signatures": [{"sig": "MEYCIQCr4f9o16zLAzHWH+Y/9Jue7VQVwnZ5Elzt/vLEjpYLvgIhALPHQ2d8sUBcgYZ7A0Jy4yAi5yoVg2ls9LjfFzsinzmj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11259849}, "engines": {"node": ">=12"}, "gitHead": "6073a3a9a02909d54cedbaf9c06f5fa501f9f337", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.6_1700377901101_0.6677076502716854", "host": "s3://npm-registry-packages"}}, "0.19.7": {"name": "@esbuild/android-arm", "version": "0.19.7", "license": "MIT", "_id": "@esbuild/android-arm@0.19.7", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "0827b49aed813c33ea18ee257c1728cdc4a01030", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.7.tgz", "fileCount": 6, "integrity": "sha512-YGSPnndkcLo4PmVl2tKatEn+0mlVMr3yEpOOT0BeMria87PhvoJb5dg5f5Ft9fbCVgtAz4pWMzZVgSEGpDAlww==", "signatures": [{"sig": "MEYCIQDtF7Gmgzn4wotmNL/ZcCeZ3sxKjOed+oCiJoLUBwWW0QIhAN6xdxhAGz/cfV6iIfN2XglniwEsMEuS3kzms5eU5BHq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11303982}, "engines": {"node": ">=12"}, "gitHead": "a7773b340bb216d053df91b7479b5aa2a152b0de", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.7_1700528468850_0.8244861364684535", "host": "s3://npm-registry-packages"}}, "0.19.8": {"name": "@esbuild/android-arm", "version": "0.19.8", "license": "MIT", "_id": "@esbuild/android-arm@0.19.8", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "b46e4d9e984e6d6db6c4224d72c86b7757e35bcb", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.8.tgz", "fileCount": 6, "integrity": "sha512-31E2lxlGM1KEfivQl8Yf5aYU/mflz9g06H6S15ITUFQueMFtFjESRMoDSkvMo8thYvLBax+VKTPlpnx+sPicOA==", "signatures": [{"sig": "MEUCIQCn/MJ5t6S9bhndNuxVrmHyja9IszNnnOr8a4V89OVYcwIgNACdFo3+RPmnmDx6uWrDtjgHi9hskXz/DGcC/Eu/wB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11300680}, "engines": {"node": ">=12"}, "gitHead": "e97bd6706c7aaddb3770ae31b164d7ccaec8056c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.8_1701040092100_0.03264770395527172", "host": "s3://npm-registry-packages"}}, "0.19.9": {"name": "@esbuild/android-arm", "version": "0.19.9", "license": "MIT", "_id": "@esbuild/android-arm@0.19.9", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "21a4de41f07b2af47401c601d64dfdefd056c595", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.9.tgz", "fileCount": 6, "integrity": "sha512-jkYjjq7SdsWuNI6b5quymW0oC83NN5FdRPuCbs9HZ02mfVdAP8B8eeqLSYU3gb6OJEaY5CQabtTFbqBf26H3GA==", "signatures": [{"sig": "MEYCIQCTFxJt6fO2FB/FPfPjcWqF8Lhqjf6PTeMb/3AU1jUaXgIhAKm0BuzSyEwV95awSo2tWft4AcPjte64rXrfS/it13Yj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11463314}, "engines": {"node": ">=12"}, "gitHead": "9edc9d44c3d0480c27f68a71365f18e688b6184a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.9_1702184972613_0.7581246303981926", "host": "s3://npm-registry-packages"}}, "0.19.10": {"name": "@esbuild/android-arm", "version": "0.19.10", "license": "MIT", "_id": "@esbuild/android-arm@0.19.10", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "1c23c7e75473aae9fb323be5d9db225142f47f52", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.10.tgz", "fileCount": 6, "integrity": "sha512-7W0bK7qfkw1fc2viBfrtAEkDKHatYfHzr/jKAHNr9BvkYDXPcC6bodtm8AyLJNNuqClLNaeTLuwURt4PRT9d7w==", "signatures": [{"sig": "MEUCIQCFvaND+7hnnmNCxfrRXWn/GFu4tPyDLHfYO/olIgf7jwIgYuQyTj9bu0/oiLPLXRWZcKQwnzOK7hXyCl/Xt3HkRdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11470596}, "engines": {"node": ">=12"}, "gitHead": "55e1127a49db0c26f1abd97f1b180bbc728aa95a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.10_1702945302881_0.10046068101288119", "host": "s3://npm-registry-packages"}}, "0.19.11": {"name": "@esbuild/android-arm", "version": "0.19.11", "license": "MIT", "_id": "@esbuild/android-arm@0.19.11", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "f46f55414e1c3614ac682b29977792131238164c", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.11.tgz", "fileCount": 6, "integrity": "sha512-5OVapq0ClabvKvQ58Bws8+wkLCV+Rxg7tUVbo9xu034Nm536QTII4YzhaFriQ7rMrorfnFKUsArD2lqKbFY4vw==", "signatures": [{"sig": "MEMCHwqGKl4jTrp9Rhj7YpmGHojHADObkbNsagUGplgQ6PkCIAgXq8/y3vxSq8DIiRpOXxVD+TrW2yqnaKWm0+Ris7WX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11471593}, "engines": {"node": ">=12"}, "gitHead": "6ee82255bdfdffef2de60827e9d35a425a7cbff6", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "18.16.1", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.11_1703881922342_0.5216846567972495", "host": "s3://npm-registry-packages"}}, "0.19.12": {"name": "@esbuild/android-arm", "version": "0.19.12", "license": "MIT", "_id": "@esbuild/android-arm@0.19.12", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "b0c26536f37776162ca8bde25e42040c203f2824", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.19.12.tgz", "fileCount": 6, "integrity": "sha512-qg/Lj1mu3CdQlDEEiWrlC4eaPZ1KztwGJ9B6J+/6G+/4ewxJg7gqj8eVYWvao1bXrqGiW2rsBZFSX3q2lcW05w==", "signatures": [{"sig": "MEUCIHb29eb8xPQJOgTbSqIb1uohdpAPJ4Tp47HncSZGc4GGAiEA1/eag02NmKBYk2ETwEGAY+MKCwSmHOPlypoBUeX8iqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11471298}, "engines": {"node": ">=12"}, "gitHead": "d7fd1ad35715cda76eb33343b7c07b275e402a2e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.19.12_1706031633941_0.3815107762308416", "host": "s3://npm-registry-packages"}}, "0.20.0": {"name": "@esbuild/android-arm", "version": "0.20.0", "license": "MIT", "_id": "@esbuild/android-arm@0.20.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "1397a2c54c476c4799f9b9073550ede496c94ba5", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.20.0.tgz", "fileCount": 6, "integrity": "sha512-3bMAfInvByLHfJwYPJRlpTeaQA75n8C/QKpEaiS4HrFWFiJlNI0vzq/zCjBrhAYcPyVPG7Eo9dMrcQXuqmNk5g==", "signatures": [{"sig": "MEUCIAIaaHTeb3gSLB0TrBMm2tX92+XzWjxnxhLspMPEZSNKAiEAuOcgKRA7qXfY9UcMxg3x9Y9LojIge60/V7K1ogwfemc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11472922}, "engines": {"node": ">=12"}, "gitHead": "2af5ccf478812d2d7226ad4435d46fbbb3419a8c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.20.0_1706374179202_0.08086049125896633", "host": "s3://npm-registry-packages"}}, "0.20.1": {"name": "@esbuild/android-arm", "version": "0.20.1", "license": "MIT", "_id": "@esbuild/android-arm@0.20.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "38c91d8ee8d5196f7fbbdf4f0061415dde3a473a", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.20.1.tgz", "fileCount": 6, "integrity": "sha512-4j0+G27/2ZXGWR5okcJi7pQYhmkVgb4D7UKwxcqrjhvp5TKWx3cUjgB1CGj1mfdmJBQ9VnUGgUhign+FPF2Zgw==", "signatures": [{"sig": "MEYCIQDEt4jz1ggTimgiPRowzV8L8ONvqfPSPnPsugggfmUjiwIhAJANiQI3HN15djXN3WGMt3fSiTukzLxeSUo7psTu18bh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11496438}, "engines": {"node": ">=12"}, "gitHead": "9f9e4f85e6e28a58727531458663afd157b8b415", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.20.1_1708324699836_0.32472565427894695", "host": "s3://npm-registry-packages"}}, "0.20.2": {"name": "@esbuild/android-arm", "version": "0.20.2", "license": "MIT", "_id": "@esbuild/android-arm@0.20.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "3b488c49aee9d491c2c8f98a909b785870d6e995", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.20.2.tgz", "fileCount": 6, "integrity": "sha512-t98Ra6pw2VaDhqNWO2Oph2LXbz/EJcnLmKLGBJwEwXX/JAN83Fym1rU8l0JUWK6HkIbWONCSSatf4sf2NBRx/w==", "signatures": [{"sig": "MEYCIQCNqCReXTl78RYv/7g0RpwZfbcVI3mXTrD/HyM/X4i/+wIhAPOIx5JQXw4Q7PZJXY8Qgz/oThaurAlfUcwRmEHCnzW4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11502339}, "engines": {"node": ">=12"}, "gitHead": "617eddaa32b7649ad23ddd15257816df3f0f544c", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.20.2_1710445788276_0.41718863111404825", "host": "s3://npm-registry-packages"}}, "0.21.0": {"name": "@esbuild/android-arm", "version": "0.21.0", "license": "MIT", "_id": "@esbuild/android-arm@0.21.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "87df91b68b1bc8630448ad34cd0316a21d12dca8", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.21.0.tgz", "fileCount": 6, "integrity": "sha512-8OvDALSbmoLJ79KCs0hxoki5I3qJA7JQMhJO6aq5O8G+pi7TPnGICdQRQcgdzwZaVc4ptp5SX7Phg6jKzvSEBg==", "signatures": [{"sig": "MEYCIQC0ODCVMh7+DfJqInaa+PWKR3dDKT+hSfI+Kt31xJaYGwIhAIMeQ/tU+lc8QECMM8tL/NV3HURKFXIxsGaMTU1bRvfw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11661274}, "engines": {"node": ">=12"}, "gitHead": "c6da2c3aa2b1321be3fdacd1e53566c5f24ee702", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.21.0_1715050351394_0.425721659035472", "host": "s3://npm-registry-packages"}}, "0.21.1": {"name": "@esbuild/android-arm", "version": "0.21.1", "license": "MIT", "_id": "@esbuild/android-arm@0.21.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "8247c5aef933a212bca261290f6e43a9dca07cc5", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.21.1.tgz", "fileCount": 6, "integrity": "sha512-hh3jKWikdnTtHCglDAeVO3Oyh8MaH8xZUaWMiCCvJ9/c3NtPqZq+CACOlGTxhddypXhl+8B45SeceYBfB/e8Ow==", "signatures": [{"sig": "MEUCIQDCQOXds78pRbp0fPTA1d/B7j3vJddzw2WljbpBwfN5fwIgQuVmuceB+PUSlneylCEs3cwXvAGBLhgUGY7wIZdlybo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11661348}, "engines": {"node": ">=12"}, "gitHead": "e87639417e47ba5db160f105785dc10bde0999cf", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.21.1_1715100910625_0.020785689302203147", "host": "s3://npm-registry-packages"}}, "0.21.2": {"name": "@esbuild/android-arm", "version": "0.21.2", "license": "MIT", "_id": "@esbuild/android-arm@0.21.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "99f3a3c90bf8ac37d1881af6b87d404a02007164", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.21.2.tgz", "fileCount": 6, "integrity": "sha512-G1ve3b4FeyJeyCjB4MX1CiWyTaIJwT9wAYE+8+IRA53YoN/reC/Bf2GDRXAzDTnh69Fpl+1uIKg76DiB3U6vwQ==", "signatures": [{"sig": "MEQCIFziz5Pwz8GoadzY5CFPfLepVsjS17qCypUUtodUp9vjAiA+dSFSoVOtr7fklbIO1bqMToWnVoY2ijXGexYzzwCtIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11662985}, "engines": {"node": ">=12"}, "gitHead": "b24180e4fbd07504e91cb922948870d5467072e0", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.21.2_1715545979654_0.07482585814089227", "host": "s3://npm-registry-packages"}}, "0.21.3": {"name": "@esbuild/android-arm", "version": "0.21.3", "license": "MIT", "_id": "@esbuild/android-arm@0.21.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "7fda92e3231043c071ea6aa76c92accea86439fd", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.21.3.tgz", "fileCount": 6, "integrity": "sha512-bviJOLMgurLJtF1/mAoJLxDZDL6oU5/ztMHnJQRejbJrSc9FFu0QoUoFhvi6qSKJEw9y5oGyvr9fuDtzJ30rNQ==", "signatures": [{"sig": "MEUCIQDxwJg/sM6zmWrVb59Q5WaxYPlZeqnvmAjnRKg0MA2hoQIgck9wF6XeJ87vERs65mVgMXB7TdKopnX5lmXWfubg1mI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11655816}, "engines": {"node": ">=12"}, "gitHead": "efa3dd2d8e895f7f9a9bef0d588560bbae7d776e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.21.3_1715806358641_0.674737280627463", "host": "s3://npm-registry-packages"}}, "0.21.4": {"name": "@esbuild/android-arm", "version": "0.21.4", "license": "MIT", "_id": "@esbuild/android-arm@0.21.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "76767a989720a97b206ea14c52af6e4589e48b0d", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.21.4.tgz", "fileCount": 6, "integrity": "sha512-E7H/yTd8kGQfY4z9t3nRPk/hrhaCajfA3YSQSBrst8B+3uTcgsi8N+ZWYCaeIDsiVs6m65JPCaQN/DxBRclF3A==", "signatures": [{"sig": "MEUCIFc7Yau/zSEcq4CSx80lz4U38wNJfEG8kNnxQVabNxGkAiEAzRO2v2yEMq9wJjc8NVgKX2rwP7he8SdCIRzoMuhUcCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11672535}, "engines": {"node": ">=12"}, "gitHead": "67cbf87a4909d87a902ca8c3b69ab5330defab0a", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.21.4_1716603054691_0.4826045623933197", "host": "s3://npm-registry-packages"}}, "0.21.5": {"name": "@esbuild/android-arm", "version": "0.21.5", "license": "MIT", "_id": "@esbuild/android-arm@0.21.5", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "9b04384fb771926dfa6d7ad04324ecb2ab9b2e28", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.21.5.tgz", "fileCount": 6, "integrity": "sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==", "signatures": [{"sig": "MEYCIQCFrmp4YNWKSIjZqLYZ1xFy4BGNvCx0gDOfUwFfpO31DwIhAJBxzMl1JHO/guVBdHPQ/Khp3ZpXWqZ58utZEY0VWWGP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11678220}, "engines": {"node": ">=12"}, "gitHead": "fc37c2fa9de2ad77476a6d4a8f1516196b90187e", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.21.5_1717967820659_0.03206839372622006", "host": "s3://npm-registry-packages"}}, "0.22.0": {"name": "@esbuild/android-arm", "version": "0.22.0", "license": "MIT", "_id": "@esbuild/android-arm@0.22.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "dd26ec407db736eee0eb060195a43aa13f618013", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.22.0.tgz", "fileCount": 6, "integrity": "sha512-PBnyP+r8vJE4ifxsWys9l+Mc2UY/yYZOpX82eoyGISXXb3dRr0M21v+s4fgRKWMFPMSf/iyowqPW/u7ScSUkjQ==", "signatures": [{"sig": "MEUCIDBg+yRWMV2xMvj5ZX5AnKStWdM4ChhK3N1w8uCDOiGbAiEA6BDkT7Mkl4KL7xGjltAMvIfh+3cabEeNsx21yDRyw6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11653464}, "engines": {"node": ">=18"}, "gitHead": "80c6e6ea094a71691ab1644ab61494cc67729365", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.22.0_1719779872798_0.1631119911827028", "host": "s3://npm-registry-packages"}}, "0.23.0": {"name": "@esbuild/android-arm", "version": "0.23.0", "license": "MIT", "_id": "@esbuild/android-arm@0.23.0", "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "26c806853aa4a4f7e683e519cd9d68e201ebcf99", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.23.0.tgz", "fileCount": 6, "integrity": "sha512-+KuOHTKKyIKgEEqKbGTK8W7mPp+hKinbMBeEnNzjJGyFcWsfrXjSTNluJHCY1RqhxFurdD8uNXQDei7qDlR6+g==", "signatures": [{"sig": "MEUCIDo8+8WhG7VxiyGRdVjfSv11ScCAG7a3WZOrKEJ64x6qAiEA0IfeWTlwexV3BRWECCL6GLj35LYFK++LVel4X/OEI88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11653780}, "engines": {"node": ">=18"}, "gitHead": "9d506806bdd963b02b3d6edf45e717e03dcba785", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.23.0_1719891229466_0.01963644217722016", "host": "s3://npm-registry-packages"}}, "0.23.1": {"name": "@esbuild/android-arm", "version": "0.23.1", "license": "MIT", "_id": "@esbuild/android-arm@0.23.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5eb8c652d4c82a2421e3395b808e6d9c42c862ee", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.23.1.tgz", "fileCount": 6, "integrity": "sha512-uz6/tEy2IFm9RYOyvKl88zdzZfwEfKZmnX9Cj1BHjeSGNuGLuMD1kR8y5bteYmwqKm1tj8m4cb/aKEorr6fHWQ==", "signatures": [{"sig": "MEUCIQCy50x3hY2z5pHAv1V5Sds3Mx5p6sahbLvirXNsr8a80gIgE7PywEC7wuAUpES5dtpjjnC0igRPw0bnIyaZNYFOnrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11660557}, "engines": {"node": ">=18"}, "gitHead": "332727499e62315cff4ecaff9fa8b86336555e46", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.23.1_1723846402743_0.6439080209231962", "host": "s3://npm-registry-packages"}}, "0.24.0": {"name": "@esbuild/android-arm", "version": "0.24.0", "license": "MIT", "_id": "@esbuild/android-arm@0.24.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ab7263045fa8e090833a8e3c393b60d59a789810", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.24.0.tgz", "fileCount": 6, "integrity": "sha512-arAtTPo76fJ/ICkXWetLCc9EwEHKaeya4vMrReVlEIUCAUncH7M4bhMQ+M9Vf+FFOZJdTNMXNBrWwW+OXWpSew==", "signatures": [{"sig": "MEUCIQC1UtwRg4Zo1KP2JL35DHaVtm6QTuHr5iLrbvaTihJLhgIgPYRJbpAWBkiMouloHdva2s4Ol0Z+SP8I9EULacVTrCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11915829}, "engines": {"node": ">=18"}, "gitHead": "d34e79e2a998c21bb71d57b92b0017ca11756912", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.24.0_1726970788017_0.6089324699715073", "host": "s3://npm-registry-packages"}}, "0.24.1": {"name": "@esbuild/android-arm", "version": "0.24.1", "license": "MIT", "_id": "@esbuild/android-arm@0.24.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "db64da18f4be8180e2acb4c2ba8aa44dc04c39bf", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.24.1.tgz", "fileCount": 6, "integrity": "sha512-gKHsqtULVpVfsffGLaU/W4Jx+DLU8BLOQtvBrg+R22tz422VMgBK5moQ/ELDTRPR7Tqt9gLNk13XlvOFinXSzQ==", "signatures": [{"sig": "MEUCIAVr7q1FyHGJSysHCVjSaQzFUkXnahnARA36S3AaTO1aAiEA+4Ey+bM52SmAhN5W2ZL075elOUIeLYkl77SMTifzVSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11927028}, "engines": {"node": ">=18"}, "gitHead": "de9598f42dc3ffc395e3fd3672a4804f6b4e5c09", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.24.1_1734673275519_0.5822813405280387", "host": "s3://npm-registry-packages-npm-production"}}, "0.24.2": {"name": "@esbuild/android-arm", "version": "0.24.2", "license": "MIT", "_id": "@esbuild/android-arm@0.24.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "72d8a2063aa630308af486a7e5cbcd1e134335b3", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.24.2.tgz", "fileCount": 6, "integrity": "sha512-tmwl4hJkCfNHwFB3nBa8z1Uy3ypZpxqxfTQOcHX+xRByyYgunVbZ9MzUUfb0RxaHIMnbHagwAxuTL+tnNM+1/Q==", "signatures": [{"sig": "MEUCIAphUseJ9VTBvlosZbQC4+cUgIJMi7o8ygdWQl29VByZAiEAvpn7HeY3w7Dvz1RWihMne2YgTPzOppWLQfga1YyJXaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11929387}, "engines": {"node": ">=18"}, "gitHead": "745abd9f0c06f73ca40fbe198546a9bc36c23b81", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.24.2_1734717394055_0.8775801224283459", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.0": {"name": "@esbuild/android-arm", "version": "0.25.0", "license": "MIT", "_id": "@esbuild/android-arm@0.25.0", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "ca6e7888942505f13e88ac9f5f7d2a72f9facd2b", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.0.tgz", "fileCount": 6, "integrity": "sha512-PTyWCYYiU0+1eJKmw21lWtC+d08JDZPQ5g+kFyxP0V+es6VPPSUhM6zk8iImp2jbV6GwjX4pap0JFbUQN65X1g==", "signatures": [{"sig": "MEUCICm+hwJ8YAfq0bRBVS0toFn/1EtnCWBK4mBmd5tWGIHzAiEAv2NhSCBNh3Gr9Pe91Hl/rpUsJ5PzoqVGfUJB0hTBfQU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12140532}, "engines": {"node": ">=18"}, "gitHead": "e9174d671b1882758cd32ac5e146200f5bee3e45", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.25.0_1738983742685_0.30766081228646214", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.1": {"name": "@esbuild/android-arm", "version": "0.25.1", "license": "MIT", "_id": "@esbuild/android-arm@0.25.1", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "e84d2bf2fe2e6177a0facda3a575b2139fd3cb9c", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.1.tgz", "fileCount": 6, "integrity": "sha512-dp+MshLYux6j/JjdqVLnMglQlFu+MuVeNrmT5nk6q07wNhCdSnB7QZj+7G8VMUGh1q+vj2Bq8kRsuyA00I/k+Q==", "signatures": [{"sig": "MEQCIErJeJQsc7XKvKOjrSnzN7rvvIMjU//+YmscI87R6j6GAiBVqmu4+16/Q8SWNvE6Evs+Ytz3Lk1XCh8KuR7T6NaI8g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12147106}, "engines": {"node": ">=18"}, "gitHead": "6bfc1c13b4d986b86e8bc2035f00c337b0c1d007", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.25.1_1741578332720_0.5778934202717394", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.2": {"name": "@esbuild/android-arm", "version": "0.25.2", "license": "MIT", "_id": "@esbuild/android-arm@0.25.2", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "3c49f607b7082cde70c6ce0c011c362c57a194ee", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.2.tgz", "fileCount": 6, "integrity": "sha512-NQhH7jFstVY5x8CKbcfa166GoV0EFkaPkCKBQkdPJFvo5u+nGXLEH/ooniLb3QI8Fk58YAx7nsPLozUWfCBOJA==", "signatures": [{"sig": "MEUCIGLp85F5MKAAP3ubeORqa9Svc1W9768BaszejUr6nMeQAiEAlDzCdbZJFAdhM+RW46cMZNLNWyEBg8+MR6UwtIDVJmY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12153577}, "engines": {"node": ">=18"}, "gitHead": "4475787eef4c4923b92b9fa37ebba1c88b9e1d9b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.25.2_1743355981433_0.7530806180913778", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.3": {"name": "@esbuild/android-arm", "version": "0.25.3", "license": "MIT", "_id": "@esbuild/android-arm@0.25.3", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "8a0f719c8dc28a4a6567ef7328c36ea85f568ff4", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.3.tgz", "fileCount": 6, "integrity": "sha512-PuwVXbnP87Tcff5I9ngV0lmiSu40xw1At6i3GsU77U7cjDDB4s0X2cyFuBiDa1SBk9DnvWwnGvVaGBqoFWPb7A==", "signatures": [{"sig": "MEQCIFnPyqwRq+pVeB3ToEEJPruERXU8ntFPBLTqebhdo1kWAiAG6wtX8hRLp1x/Sqn307Lcf+xVzRSNZtikIp9mdFSZJw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12162525}, "engines": {"node": ">=18"}, "gitHead": "677910b073194b64d5ae01aefd7a7465bbf5b27b", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.25.3_1745380573611_0.7207523816524584", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.4": {"name": "@esbuild/android-arm", "version": "0.25.4", "license": "MIT", "_id": "@esbuild/android-arm@0.25.4", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "homepage": "https://github.com/evanw/esbuild#readme", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "os": ["android"], "cpu": ["arm"], "dist": {"shasum": "5660bd25080553dd2a28438f2a401a29959bd9b1", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.4.tgz", "fileCount": 6, "integrity": "sha512-QNdQEps7DfFwE3hXiU4BZeOV68HHzYwGd0Nthhd3uCkkEKK7/R6MTgM0P7H7FAs5pU/DIWsviMmEGxEoxIZ+ZQ==", "signatures": [{"sig": "MEUCIHaO8mgZiVxNwTjSpuQDrrBNPBZo0Fd+sM3Jg+7zh1/JAiEAgQjjPv71hv/AR9c2uvvQ81IJkKCHdo+niNvbQdQ3jbo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12166594}, "engines": {"node": ">=18"}, "gitHead": "218d29e9da018d60cf87b8fb496bb8167936ff54", "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/evanw/esbuild.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "A WebAssembly shim for esbuild on Android ARM.", "directories": {}, "_nodeVersion": "22.14.0", "_hasShrinkwrap": false, "preferUnplugged": true, "_npmOperationalInternal": {"tmp": "tmp/android-arm_0.25.4_1746491452356_0.28055135543765286", "host": "s3://npm-registry-packages-npm-production"}}, "0.25.5": {"name": "@esbuild/android-arm", "version": "0.25.5", "description": "A WebAssembly shim for esbuild on Android ARM.", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "license": "MIT", "preferUnplugged": true, "engines": {"node": ">=18"}, "os": ["android"], "cpu": ["arm"], "_id": "@esbuild/android-arm@0.25.5", "gitHead": "ea453bf687c8e5cf3c5f11aae372c5ca33be0c98", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "homepage": "https://github.com/evanw/esbuild#readme", "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA==", "shasum": "4290d6d3407bae3883ad2cded1081a234473ce26", "tarball": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.5.tgz", "fileCount": 6, "unpackedSize": 12171867, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCgdFBrl4ESNp3KhmCrxOogh8YeKFM6H1Tqk8m5WZfDfAIhANMqCmwUQzB77sIX/thcPjM1uVzgPXz6sRQNYE4gAPmv"}]}, "_npmUser": {"name": "evanw", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/android-arm_0.25.5_1748315574664_0.46327315752343634"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-09-12T01:30:47.087Z", "modified": "2025-05-27T03:12:55.080Z", "0.15.7": "2022-09-12T01:30:47.410Z", "0.15.8": "2022-09-18T18:20:42.661Z", "0.15.9": "2022-09-22T19:52:59.823Z", "0.15.10": "2022-09-29T16:13:25.694Z", "0.15.11": "2022-10-14T14:21:41.250Z", "0.15.12": "2022-10-19T18:24:49.124Z", "0.15.13": "2022-11-03T05:36:38.958Z", "0.15.14": "2022-11-15T04:46:40.964Z", "0.15.15": "2022-11-21T04:48:40.839Z", "0.15.16": "2022-11-27T16:25:08.398Z", "0.15.17": "2022-12-04T01:27:38.663Z", "0.15.18": "2022-12-05T01:55:49.620Z", "0.16.0": "2022-12-07T03:55:10.085Z", "0.16.1": "2022-12-07T04:48:19.067Z", "0.16.2": "2022-12-08T06:59:56.364Z", "0.16.3": "2022-12-08T20:13:10.930Z", "0.16.4": "2022-12-10T03:50:42.302Z", "0.16.5": "2022-12-13T17:47:47.126Z", "0.16.6": "2022-12-14T05:23:22.557Z", "0.16.7": "2022-12-14T22:47:06.123Z", "0.16.8": "2022-12-16T23:38:50.513Z", "0.16.9": "2022-12-18T04:31:29.072Z", "0.16.10": "2022-12-19T23:26:45.816Z", "0.16.11": "2022-12-27T01:39:15.015Z", "0.16.12": "2022-12-28T02:07:51.074Z", "0.16.13": "2023-01-02T22:57:25.856Z", "0.16.14": "2023-01-04T20:13:09.625Z", "0.16.15": "2023-01-07T04:19:10.630Z", "0.16.16": "2023-01-08T22:43:57.831Z", "0.16.17": "2023-01-11T21:58:07.610Z", "0.17.0": "2023-01-14T04:36:10.637Z", "0.17.1": "2023-01-16T18:05:48.241Z", "0.17.2": "2023-01-17T06:39:49.444Z", "0.17.3": "2023-01-18T19:14:40.662Z", "0.17.4": "2023-01-22T06:13:46.452Z", "0.17.5": "2023-01-27T16:37:55.156Z", "0.17.6": "2023-02-06T17:00:53.645Z", "0.17.7": "2023-02-09T22:26:50.216Z", "0.17.8": "2023-02-13T06:35:47.164Z", "0.17.9": "2023-02-19T17:45:27.274Z", "0.17.10": "2023-02-20T17:55:04.758Z", "0.17.11": "2023-03-03T22:40:21.184Z", "0.17.12": "2023-03-17T06:17:15.992Z", "0.17.13": "2023-03-24T18:57:14.796Z", "0.17.14": "2023-03-26T02:47:51.621Z", "0.17.15": "2023-04-01T22:26:56.985Z", "0.17.16": "2023-04-10T04:35:10.452Z", "0.17.17": "2023-04-16T21:23:47.086Z", "0.17.18": "2023-04-22T20:41:33.677Z", "0.17.19": "2023-05-13T00:06:41.281Z", "0.18.0": "2023-06-09T21:24:26.790Z", "0.18.1": "2023-06-12T04:51:52.519Z", "0.18.2": "2023-06-13T02:40:39.116Z", "0.18.3": "2023-06-15T12:21:15.969Z", "0.18.4": "2023-06-16T15:38:40.460Z", "0.18.5": "2023-06-20T00:52:49.811Z", "0.18.6": "2023-06-20T23:25:01.243Z", "0.18.7": "2023-06-24T02:46:31.585Z", "0.18.8": "2023-06-25T03:19:20.630Z", "0.18.9": "2023-06-26T05:28:10.538Z", "0.18.10": "2023-06-26T21:20:36.254Z", "0.18.11": "2023-07-01T06:04:02.305Z", "0.18.12": "2023-07-13T01:34:17.610Z", "0.18.13": "2023-07-15T02:37:24.231Z", "0.18.14": "2023-07-18T05:00:28.301Z", "0.18.15": "2023-07-20T12:53:22.806Z", "0.18.16": "2023-07-23T04:48:12.812Z", "0.18.17": "2023-07-26T01:41:01.111Z", "0.18.18": "2023-08-05T17:06:34.110Z", "0.18.19": "2023-08-07T02:51:25.779Z", "0.18.20": "2023-08-08T04:15:09.355Z", "0.19.0": "2023-08-08T15:52:39.696Z", "0.19.1": "2023-08-11T15:57:40.726Z", "0.19.2": "2023-08-14T01:58:27.437Z", "0.19.3": "2023-09-14T01:12:28.068Z", "0.19.4": "2023-09-28T01:47:20.481Z", "0.19.5": "2023-10-17T05:10:40.348Z", "0.19.6": "2023-11-19T07:11:41.417Z", "0.19.7": "2023-11-21T01:01:09.197Z", "0.19.8": "2023-11-26T23:08:12.371Z", "0.19.9": "2023-12-10T05:09:32.896Z", "0.19.10": "2023-12-19T00:21:43.168Z", "0.19.11": "2023-12-29T20:32:02.610Z", "0.19.12": "2024-01-23T17:40:34.222Z", "0.20.0": "2024-01-27T16:49:39.436Z", "0.20.1": "2024-02-19T06:38:20.095Z", "0.20.2": "2024-03-14T19:49:48.534Z", "0.21.0": "2024-05-07T02:52:31.626Z", "0.21.1": "2024-05-07T16:55:10.883Z", "0.21.2": "2024-05-12T20:32:59.970Z", "0.21.3": "2024-05-15T20:52:38.960Z", "0.21.4": "2024-05-25T02:10:54.950Z", "0.21.5": "2024-06-09T21:17:01.003Z", "0.22.0": "2024-06-30T20:37:53.008Z", "0.23.0": "2024-07-02T03:33:49.716Z", "0.23.1": "2024-08-16T22:13:23.135Z", "0.24.0": "2024-09-22T02:06:28.349Z", "0.24.1": "2024-12-20T05:41:15.782Z", "0.24.2": "2024-12-20T17:56:34.298Z", "0.25.0": "2025-02-08T03:02:22.944Z", "0.25.1": "2025-03-10T03:45:32.971Z", "0.25.2": "2025-03-30T17:33:01.714Z", "0.25.3": "2025-04-23T03:56:13.863Z", "0.25.4": "2025-05-06T00:30:52.790Z", "0.25.5": "2025-05-27T03:12:54.910Z"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "license": "MIT", "homepage": "https://github.com/evanw/esbuild#readme", "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "description": "A WebAssembly shim for esbuild on Android ARM.", "maintainers": [{"name": "evanw", "email": "<EMAIL>"}, {"name": "esbuild", "email": "<EMAIL>"}], "readme": "# esbuild\n\nThis is a WebAssembly shim for esbuild on Android ARM. See https://github.com/evanw/esbuild for details.\n", "readmeFilename": "README.md"}