export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          username: string | null
          full_name: string | null
          avatar_url: string | null
          character_class: string | null
          level: number
          experience: number
          gold: number
          reputation: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          character_class?: string | null
          level?: number
          experience?: number
          gold?: number
          reputation?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          username?: string | null
          full_name?: string | null
          avatar_url?: string | null
          character_class?: string | null
          level?: number
          experience?: number
          gold?: number
          reputation?: number
          created_at?: string
          updated_at?: string
        }
      }
      herbs: {
        Row: {
          id: string
          name: string
          scientific_name: string
          description: string
          rarity: 'common' | 'uncommon' | 'rare' | 'legendary'
          base_price: number
          effects: Json
          image_url: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          scientific_name: string
          description: string
          rarity?: 'common' | 'uncommon' | 'rare' | 'legendary'
          base_price: number
          effects?: Json
          image_url?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          scientific_name?: string
          description?: string
          rarity?: 'common' | 'uncommon' | 'rare' | 'legendary'
          base_price?: number
          effects?: Json
          image_url?: string | null
          created_at?: string
        }
      }
      herb_discoveries: {
        Row: {
          id: string
          user_id: string
          herb_id: string
          location: string
          date_found: string
          notes: string | null
          quality: number
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          herb_id: string
          location: string
          date_found: string
          notes?: string | null
          quality?: number
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          herb_id?: string
          location?: string
          date_found?: string
          notes?: string | null
          quality?: number
          created_at?: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          description: string
          category: 'herbs' | 'potions' | 'decorations' | 'equipment'
          price: number
          original_price: number | null
          rarity: 'common' | 'rare' | 'legendary'
          in_stock: boolean
          effects: Json | null
          image_url: string | null
          seller_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          category: 'herbs' | 'potions' | 'decorations' | 'equipment'
          price: number
          original_price?: number | null
          rarity?: 'common' | 'rare' | 'legendary'
          in_stock?: boolean
          effects?: Json | null
          image_url?: string | null
          seller_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          category?: 'herbs' | 'potions' | 'decorations' | 'equipment'
          price?: number
          original_price?: number | null
          rarity?: 'common' | 'rare' | 'legendary'
          in_stock?: boolean
          effects?: Json | null
          image_url?: string | null
          seller_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      tasks: {
        Row: {
          id: string
          title: string
          description: string
          type: 'herb_collection' | 'exploration' | 'trading' | 'crafting'
          difficulty: 'easy' | 'medium' | 'hard' | 'legendary'
          progress: number
          max_progress: number
          reward_gold: number
          reward_experience: number
          reward_items: Json | null
          time_limit: string | null
          status: 'available' | 'in_progress' | 'completed' | 'expired'
          assigned_to: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          type: 'herb_collection' | 'exploration' | 'trading' | 'crafting'
          difficulty?: 'easy' | 'medium' | 'hard' | 'legendary'
          progress?: number
          max_progress: number
          reward_gold: number
          reward_experience: number
          reward_items?: Json | null
          time_limit?: string | null
          status?: 'available' | 'in_progress' | 'completed' | 'expired'
          assigned_to?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          type?: 'herb_collection' | 'exploration' | 'trading' | 'crafting'
          difficulty?: 'easy' | 'medium' | 'hard' | 'legendary'
          progress?: number
          max_progress?: number
          reward_gold?: number
          reward_experience?: number
          reward_items?: Json | null
          time_limit?: string | null
          status?: 'available' | 'in_progress' | 'completed' | 'expired'
          assigned_to?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      exploration_areas: {
        Row: {
          id: string
          name: string
          description: string
          difficulty: 'easy' | 'medium' | 'hard' | 'legendary'
          coordinates: Json
          discovered_by: string[]
          rewards: Json
          requirements: string | null
          time_required: number
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          difficulty?: 'easy' | 'medium' | 'hard' | 'legendary'
          coordinates: Json
          discovered_by?: string[]
          rewards: Json
          requirements?: string | null
          time_required: number
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          difficulty?: 'easy' | 'medium' | 'hard' | 'legendary'
          coordinates?: Json
          discovered_by?: string[]
          rewards?: Json
          requirements?: string | null
          time_required?: number
          created_at?: string
        }
      }
      transactions: {
        Row: {
          id: string
          buyer_id: string
          seller_id: string | null
          product_id: string
          quantity: number
          total_amount: number
          transaction_type: 'purchase' | 'sale' | 'trade'
          status: 'pending' | 'completed' | 'cancelled'
          created_at: string
        }
        Insert: {
          id?: string
          buyer_id: string
          seller_id?: string | null
          product_id: string
          quantity: number
          total_amount: number
          transaction_type?: 'purchase' | 'sale' | 'trade'
          status?: 'pending' | 'completed' | 'cancelled'
          created_at?: string
        }
        Update: {
          id?: string
          buyer_id?: string
          seller_id?: string | null
          product_id?: string
          quantity?: number
          total_amount?: number
          transaction_type?: 'purchase' | 'sale' | 'trade'
          status?: 'pending' | 'completed' | 'cancelled'
          created_at?: string
        }
      }
      user_inventory: {
        Row: {
          id: string
          user_id: string
          product_id: string
          quantity: number
          acquired_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          quantity: number
          acquired_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          quantity?: number
          acquired_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}